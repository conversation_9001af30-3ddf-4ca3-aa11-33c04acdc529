<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_2.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"端口管理");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script>

function showTrunkPorts(trunkIdNo,ltime)
{
	window.location.href="lacp_dynamic.asp?trunkIdNoDel="+trunkIdNo+"&ltime="+ltime;
}

function messageCheckdelPort()
{
	var hid = document.webForm;
	hid.action="/goform/DeletePortFromDynamicChannel"
	hid.submit();
	return 0;
}
function delChan(v1)
{
	var hid = document.webForm;
		hid.idx1.value=v1;
		hid.action="/goform/delLacpDynamicPort"
		hid.submit();
	return 0;
}
function ShowDynamicChan(v1,v2)
{
	var tmp;
	var i=0;
	var k=0;
	var hid = document.webForm;
	for(i=1;i<129;i++)
		{
			tmp="po"+i;
			if(v1!=tmp)
				continue;
			else
				break;
		}
//	hid.idx1.value=v1;
	hid.idx2.value=v2;
    document.write("<tr height='30'><td align='left' width='15%' class='crons'>"+v1+"</td><td align='left' width='75%' class='crons'>"+v2+"</td><td align='left' width='10%' class='crons'><input name='Submit' type='button' class='button'"+"value='"+putmsg(<% write(lang); %>,"删  除")+ "' onclick='delChan("+i+")'></td></tr>");
	return 0;
}
function showTrunkcfg(trunkIdNo,ltime)
{
	window.location.href="lacp_dynamic.asp?trunkIdNo="+trunkIdNo+"&ltime="+ltime;
}
function messageCheck1()
{
	var hid = document.webForm;
	if(hid.trunkid.value.length==0)
	{
		alert(putmsg(<% write(lang); %>,"Trunk ID不能为空!"));
		return ;
	}

	if(!DataScope(hid.trunkid.value, 32, 1))
	{
		alert(putmsg(<% write(lang); %>,"组ID必须在1到32之间!"));
		return ;
	}
	hid.action="/goform/creatChannelGroup"
	hid.submit();
	return true;
}
function messageCheck()
{
	var hid = document.webForm;
		hid.action="/goform/setLacpLoadBalance"
		hid.submit();
	return true;
}
function SHOWLACPLINKTRAFFIC(v1,v2,v3,v4,v5,v6,v7)
{
	document.write("<tr height='30'><td align='left' class='crons'>"+v1+"</td><td	 align='left' class='crons'>"+v2+"</td><td 	align='left' class='crons'>"+v3+"</td><td	 align='left' class='crons'>"+v4+"</td><td 	align='left' class='crons'>"+v5+"</td><td	 align='left' class='crons'>"+v6+"</td><td align='left' class='crons'>"+v7+"</td>");
	return true;
}
function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm?name=lacp_dynamic";
	tf.submit();
}

function refreshpage()
{
  location.href="lacp_dynamic.asp?ltime="+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp("lacp_dynamic",<% write(lang); %>);
}

</script>
</head>
<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="">
<input type="hidden" name="idx1">
<input type="hidden" name="ltime" value=<% write(lltime); %> >
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="idx2">
<% var errorcode,syspriority,portpriority,porttimeout,trunkid,trunkmode;getLacpDynamicCfg(); %>
<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
            <tr>
              <td colspan="2" align="left" class="cword09">&nbsp;LACP&nbsp<b><font color="#FF7F00">&gt;&gt;</font></b> &nbsp;<script>writemsg(<% write(lang); %>,"动态汇聚");</script></td>
            </tr>
        </table></td>
      </tr>     
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
            <tr height="25">
              <td colspan="6" align="left">&nbsp;<script>writemsg(<% write(lang); %>,"LACP配置");</script></td>
            </tr>
         <!--   <tr height="25">
              <td  align="left" class="crons">&nbsp;LACP系统优先级:</td>
              <td colspan="5" align="left" class="crons">
              	<input type="text" name="syspriority" value=<% write(syspriority); %> >
               </td> 
            </tr>-->
             <tr height="25">
              <td width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script>:
                 <% showPortForDynamicLacp(); %>
                 </td> 
              <td  width="10%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"组ID");</script>:</td>
              <td  width="15%" align="left" class="crons">
              	<input type="text" name="trunkid" style="width:50%">(1-32)
               </td> 
              <td  width="10%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"模式");</script>:</td>
              <td  width="35%" align="left" class="crons">
              	<input type="radio" name="trunkmode" value="active" checked>Active
          		<input type="radio" name="trunkmode" value="passive" >Passive
               </td>
             <td width="15%" align="middle" class="crons">
             <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"创  建","button","button","modify","messageCheck1()");</script>
             </td></tr>
             <% showDynamicChannelAndPortsFordel(); %>
        </table></td>
      </tr>
		<tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">      
      <tr>     
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
            <tr height="25">
				<td width="100%">
					<table width="100%">
					<tr height="25"><td colspan="3" align="left" width="100%">&nbsp;<script>writemsg(<% write(lang); %>,"动态汇聚组列表");</script></td></tr>
					<tr><td align="left" width="18%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"汇聚组ID");</script></td><td align="left" width="75%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"端口成员");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"操作");</script></td></tr>
					<script>
					<% showDynamicChannelGroup(); %>
					</script>
					</table>
				</td>                     
            </tr>
        </table></td>
      </tr>      
    </td>
  	</tr> 
      <tr>
        <td colspan="6"><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
            <tr height="25">
              <td colspan="7" align="left">&nbsp;<script>writemsg(<% write(lang); %>,"汇聚组信息");</script></td>
            </tr>
            <tr><td width="12%"></td><td width="13%"></td><td width="13%"></td><td width="13%"></td><td width="13%"></td><td width="13%"></td><td width="13%"></td></tr>
             <% showLacpTrunkPortsTable(); %>    
        </table></td>
      </tr>
    <tr>
	    <td align="center" height="35">
	    <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
        &nbsp;
        &nbsp;
        </td>
		</tr>
  			</table>
			</td></tr>
        </table></td>
      </tr>
    </table></td>
  </tr> 
</table>
</td></tr>
<tr><td>
</td></tr></table>
</form>
<script>
changebgcolor();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</html>

