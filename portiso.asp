<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<meta http-equiv="Content-Type" content="text/html; charset=utf8">
		<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
		<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
		<script language="javascript" type="text/javascript" src="js/alpinejs.min.js" defer></script>
		<link href="css/display_web.css" rel="stylesheet" type="text/css" />
		<title>&nbsp;
			<script>writemsg(<% write(lang); %>, "端口隔离");</script>
		</title>
		<script language="JavaScript">
			var boardType = <% getSysCfg(); %>;
		</script>
		<% var mirrorEnable; getmirrorEnable(); %>
			<script>
				retValue = <% var responseJsonStr; jw_get_portPortisoConfig(); %>;
				const data = <% write(responseJsonStr); %>;
				var portIsoArr
				if (retValue) {
					portIsoArr = data.PortPortiso.map(e => { e.portModel = e.portPortiso.split('|'); return e })
				} else {
					portIsoArr = new Array(10).fill(undefined).map((e, index) => {
						return {
							portName: `ge${index + 1}`,
							portPortiso: ''
						}
					})
				}
				function handleSubmit(p) {
					const form = document.portisosetting
					const PortPortiso = p.map(e => {
						return {
							portName: e.portName,
							portPortiso: e.portModel.join('|')
						}
					})
					const params = {
						pageName: "portiso.asp",
						PortPortiso
					}
					form.param1.value = JSON.stringify(params)
					form.action = "/goform/jw_set_portPortisoConfig";
					form.submit();
				}
			</script>
</head>

<body><br>
	<% var authmode; checkCurMode(); %>
		<script>
			checktop(<% write(lang); %>);
		</script>
		<form name="portisosetting" method="post" action="/goform/portiso" class="formContain">
			<!--<input type="hidden" name="left_menu_id" value="@left_menu_id#">-->
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input type="hidden" name="param1" id="param1">
			<table width="96%" align="center" border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td height="30px">
						<font size="5" color="#404040">
							<div class="bot">端口隔离</div>
						</font>
					</td>
				</tr>
			</table>
			<div x-data="{}">
				<table width="96%" align="center" border="0" cellspacing="0" cellpadding="0" class="tablebord">
					<thead>
						<tr>
							<th class="all_tables">端口</th>
							<th class="all_tables">转发端口</th>
						</tr>
					</thead>
					<tbody>
						<template x-for="(port,index) in portIsoArr" :key="index">
							<tr style="text-align: center;">
								<td class="all_tables" x-text="port.portName"></td>
								<td class="all_tables" style="display: flex; justify-content: center;">
									<template x-for="(port2, port2Index) in portIsoArr" :key="port2Index">
										<label style="display: flex; margin-right: 5px;">
											<input type="checkbox" :name="port2.portName" :value="port2.portName"
												x-model="port.portModel">
											<span x-text="port2.portName"></span>
										</label>
									</template>
								</td>
							</tr>
						</template>
						<tr>
							<th class="all_tables"></th>
							<th class="all_tables">
								<div style="display: flex; justify-content: center;">
									<div style="line-height: 35px; cursor: pointer; font-weight: normal" class="inpBtn3"
										@click="handleSubmit(portIsoArr)">修改</div>
								</div>
							</th>
						</tr>
					</tbody>
				</table>
			</div>
		</form>
		<script>
				<% if (errorcode != "") { write_errorcode(errorcode); } %>
		</script>

</body>

</html>