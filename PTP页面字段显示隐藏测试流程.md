# PTP页面字段显示/隐藏测试流程

## 测试目标
验证PTP高级设置页面的字段显示/隐藏逻辑是否完全符合PTP协议文档要求。

## 测试环境准备
1. 打开PTP配置页面
2. 切换到"PTP高级设置"标签页
3. 确保页面完全加载，所有字段可见

---

## 测试1：portType（端口角色）控制测试

### 测试1.1：SLAVE模式测试
**操作步骤：**
1. 设置 `端口角色` = `SLAVE`
2. 观察页面字段变化

**预期结果：**
✅ **应该显示的字段：**
- 时钟模式选项：只有`OC`和`TC`可选（`BC`和`MIX-CLK`选项被隐藏）
- RX延迟补偿、TX延迟补偿、RX非对称补偿、TX非对称补偿
- Announce超时秒数、Announce超时微秒
- DELAY间隔、PREQ间隔、PRESP间隔
- REQ发送间隔秒、REQ发送间隔微秒

❌ **应该隐藏的字段：**
- SYNC间隔、ANNOUNCE间隔
- SYNC发送间隔秒/微秒、ANNOUNCE发送间隔秒/微秒
- UTC偏移、优先级1、优先级2、跳数、时间源
- 时钟等级、时钟精度、时钟稳定度、时钟ID高位、时钟ID低位

### 测试1.2：MASTER模式测试
**操作步骤：**
1. 设置 `端口角色` = `MASTER`
2. 观察页面字段变化

**预期结果：**
✅ **应该显示的字段：**
- 时钟模式选项：`OC`、`BC`、`TC`、`MIX-CLK`全部可选
- SYNC间隔、ANNOUNCE间隔
- SYNC发送间隔秒/微秒、ANNOUNCE发送间隔秒/微秒
- UTC偏移、优先级1、优先级2、跳数、时间源
- 时钟等级、时钟精度、时钟稳定度、时钟ID高位、时钟ID低位

❌ **应该隐藏的字段：**
- RX延迟补偿、TX延迟补偿、RX非对称补偿、TX非对称补偿
- Announce超时秒数、Announce超时微秒
- DELAY间隔、PREQ间隔、PRESP间隔
- REQ发送间隔秒、REQ发送间隔微秒

---

## 测试2：clockType（时钟模式）控制测试

### 测试2.1：TC模式测试
**操作步骤：**
1. 设置 `端口角色` = `SLAVE`（确保TC选项可见）
2. 设置 `时钟模式` = `TC`
3. 观察页面字段变化

**预期结果：**
✅ **应该显示的字段：**
- Pdelay透传使能

❌ **应该隐藏的字段（几乎所有其他字段）：**
- 接收封装类型、接收透传使能
- SYNC包步模式、PDELAY-RESP包步模式
- 域编号、标志字段、时钟ID高位、时钟ID低位、端口号
- PDELAY TTL、NON-PDELAY TTL
- 所有消息间隔字段
- 所有发送间隔字段
- Announce超时配置
- 所有Announce相关字段
- VLAN检查使能、VLAN检查值

### 测试2.2：非TC模式测试
**操作步骤：**
1. 设置 `时钟模式` = `OC`
2. 观察页面字段变化

**预期结果：**
✅ **应该显示的字段：**
- 所有tc-hidden类字段重新显示
- 根据portType显示相应的slave-only或master-only字段

❌ **应该隐藏的字段：**
- Pdelay透传使能

### 测试2.3：MIX-CLK模式测试
**操作步骤：**
1. 设置 `端口角色` = `MASTER`（确保MIX-CLK选项可见）
2. 设置 `时钟模式` = `MIX-CLK`
3. 观察页面字段变化

**预期结果：**
✅ **应该显示的字段：**
- Pdelay透传使能（因为clockType=3属于tc-only）
- 所有master-only字段
- 所有非tc-hidden字段

---

## 测试3：encapType（封装类型）控制测试

### 测试3.1：IP封装模式测试
**操作步骤：**
1. 设置 `封装类型` = `IPV4`
2. 确保 `时钟模式` ≠ `TC`（避免tc-hidden影响）
3. 观察页面字段变化

**预期结果：**
✅ **应该显示的字段：**
- PDELAY TTL
- NON-PDELAY TTL

### 测试3.2：以太网封装模式测试
**操作步骤：**
1. 设置 `封装类型` = `ETH`
2. 观察页面字段变化

**预期结果：**
❌ **应该隐藏的字段：**
- PDELAY TTL
- NON-PDELAY TTL

---

## 测试4：复合依赖关系测试

### 测试4.1：SLAVE + TC模式
**操作步骤：**
1. 设置 `端口角色` = `SLAVE`
2. 设置 `时钟模式` = `TC`
3. 观察页面字段变化

**预期结果：**
✅ **应该显示的字段：**
- 仅Pdelay透传使能

❌ **应该隐藏的字段：**
- 几乎所有其他字段（slave-only字段被tc-hidden覆盖）

### 测试4.2：MASTER + 非TC + IP封装
**操作步骤：**
1. 设置 `端口角色` = `MASTER`
2. 设置 `时钟模式` = `OC`
3. 设置 `封装类型` = `IPV4`
4. 观察页面字段变化

**预期结果：**
✅ **应该显示的字段：**
- 所有master-only字段
- 所有ip-only字段（TTL配置）
- 所有基础字段

---

## 测试5：动态切换测试

### 测试5.1：portType切换时clockType自动调整
**操作步骤：**
1. 设置 `端口角色` = `MASTER`
2. 设置 `时钟模式` = `BC`
3. 切换 `端口角色` = `SLAVE`
4. 观察时钟模式选项变化

**预期结果：**
- 时钟模式自动重置为`OC`
- BC和MIX-CLK选项从下拉列表中消失

### 测试5.2：clockType切换时字段动态显示/隐藏
**操作步骤：**
1. 设置 `时钟模式` = `OC`（观察字段状态）
2. 切换 `时钟模式` = `TC`（观察字段隐藏）
3. 切换回 `时钟模式` = `OC`（观察字段重新显示）

**预期结果：**
- 字段应该实时动态显示/隐藏
- 切换过程中不应有页面刷新或错误

---

## 完整测试矩阵

| 测试场景 | 端口角色 | 时钟模式 | 封装类型 | 主要显示字段 | 主要隐藏字段 |
|---------|---------|---------|---------|-------------|-------------|
| 场景1 | SLAVE | OC | ETH | 延迟补偿、DELAY间隔、REQ发送间隔 | MASTER字段、IP字段 |
| 场景2 | SLAVE | TC | ETH | Pdelay透传使能 | 几乎所有其他字段 |
| 场景3 | MASTER | OC | ETH | Announce配置、SYNC间隔 | SLAVE字段、IP字段 |
| 场景4 | MASTER | BC | IPV4 | Announce配置、SYNC间隔、TTL | SLAVE字段 |
| 场景5 | MASTER | TC | IPV4 | Pdelay透传使能 | 几乎所有其他字段 |

---

## 测试通过标准

1. **字段显示正确性**：所有字段的显示/隐藏状态符合预期
2. **动态响应性**：字段状态随用户选择实时变化
3. **依赖关系正确性**：复合条件下的字段状态正确
4. **用户体验**：切换过程流畅，无页面错误

---

## 常见问题排查

1. **字段未按预期隐藏**：检查CSS类是否正确添加
2. **字段未按预期显示**：检查JavaScript显示逻辑是否正确执行
3. **动态切换不生效**：检查onchange事件是否正确绑定
4. **复合条件错误**：检查多个CSS类的优先级和组合逻辑

---

## 测试完成确认

- [ ] 所有单一条件测试通过
- [ ] 所有复合条件测试通过  
- [ ] 所有动态切换测试通过
- [ ] 测试矩阵中所有场景验证通过
- [ ] 无JavaScript错误或页面异常
