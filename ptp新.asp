<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

    <head>
        <% var lltime,lang; getltime_lanflag(); %>
        <meta http-equiv="Content-Type" content="text/html; charset=utf8">
        <script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
        <script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
        <script src="js/alpinejs.min.js" defer></script>
        <title>
            <script>writemsg(<% write(lang); %>, "端口管理");</script>
        </title>
        <link href="css/display_web.css" rel="stylesheet" type="text/css"/>
        <style>
            .select1 {
                width: 197px;
            }

            /* PTP表格样式优化 */
            #table_ptp {
                font-size: 11px;
                width: 100%;
                table-layout: fixed;
            }

            #table_ptp th, #table_ptp td {
                padding: 3px 1px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                text-align: center;
            }

            /* 设置各列的具体宽度 */
            #table_ptp th:nth-child(1), #table_ptp td:nth-child(1) { width: 5%; }   /* 选择 */
            #table_ptp th:nth-child(2), #table_ptp td:nth-child(2) { width: 7%; }   /* 端口号 */
            #table_ptp th:nth-child(3), #table_ptp td:nth-child(3) { width: 8%; }   /* 映射通道号 */
            #table_ptp th:nth-child(4), #table_ptp td:nth-child(4) { width: 8%; }   /* 端口角色 */
            #table_ptp th:nth-child(5), #table_ptp td:nth-child(5) { width: 10%; }  /* 封装类型 */
            #table_ptp th:nth-child(6), #table_ptp td:nth-child(6) { width: 8%; }   /* 延迟机制 */
            #table_ptp th:nth-child(7), #table_ptp td:nth-child(7) { width: 8%; }   /* 使能状态 */
            #table_ptp th:nth-child(8), #table_ptp td:nth-child(8) { width: 8%; }   /* 时钟模式 */
            #table_ptp th:nth-child(9), #table_ptp td:nth-child(9) { width: 12%; }  /* 协议类型 */
            #table_ptp th:nth-child(10), #table_ptp td:nth-child(10) { width: 8%; } /* 接收封装 */
            #table_ptp th:nth-child(11), #table_ptp td:nth-child(11) { width: 12%; } /* 步模式 */
            #table_ptp th:nth-child(12), #table_ptp td:nth-child(12) { width: 6%; } /* 操作 */

            #table_ptp .text-success {
                color: #28a745;
                font-weight: bold;
            }

            #table_ptp .text-muted {
                color: #6c757d;
            }

            /* 表格容器样式 */
            .table-container {
                width: 100%;
                overflow-x: auto;
            }

            /* 针对较小屏幕的响应式调整 */
            @media (max-width: 1200px) {
                #table_ptp {
                    font-size: 10px;
                }
                #table_ptp th, #table_ptp td {
                    padding: 2px 1px;
                }
            }

            /* 高级设置表单样式 */
            #table_advanced {
                font-size: 12px;
            }

            #table_advanced .input_board4 {
                width: 150px;
            }

            #table_advanced .select1 {
                width: 150px;
            }

            /* 动态显示控制已改为Alpine.js的x-show指令 */
        </style>
        <script language="JavaScript">
            var boardType = <% getSysCfg(); %>;

            // 获取PTP配置数据
            var ptpRetValue =
            <% var responseJsonStr; jw_get_portPtpConfig(); %>
            var ptpResponseStr = <% write(responseJsonStr); %>;

            // 打印返回值到控制台
            console.log('PTP配置返回值:', ptpRetValue)
            console.log('PTP响应字符串:', ptpResponseStr)

            // 处理PTP配置数据
            if (ptpResponseStr && ptpResponseStr.ptpConfig)
            {
                localStorage.setItem('ptpConfigList', JSON.stringify(ptpResponseStr.ptpConfig))
                console.log('PTP配置已存储到localStorage:', ptpResponseStr.ptpConfig)

                // 全局变量，供Alpine.js使用
                window.ptpConfigData = ptpResponseStr.ptpConfig
                console.log('PTP配置数据:', window.ptpConfigData)
            }
            else
            {
                console.log('PTP配置数据格式:', typeof ptpResponseStr, ptpResponseStr)
                window.ptpConfigData = []
            }
        </script>
        <script language="JavaScript">
            var tabActive = 'tab1'

            function isValidString(str) {
                var validc = "/'%`\"\\><"
                var a,
                    c
                for (i = 0; i < validc.length; i++)
                {
                    c = validc.substring(i, i + 1)
                    if (str.indexOf(c) > -1)
                    {
                        return true
                    }
                }
                return false
            }







            function checking() {
                var tf = document.ptp_setting
                //tf.action = "/goform/ringmodify";
                tf.tabName.value = "tab1"
                console.log(ptpData, 123)
                //tf.submit();

            }

            // 测试函数：手动获取和打印PTP配置
            function testPtpConfig() {
                console.log('=== 手动测试PTP配置获取 ===')
                console.log('ptpRetValue:', ptpRetValue)
                console.log('ptpResponseStr:', ptpResponseStr)

                // 检查localStorage中的数据
                var storedPtpConfig = localStorage.getItem('ptpConfigList')
                if (storedPtpConfig)
                {
                    console.log('localStorage中的PTP配置:', JSON.parse(storedPtpConfig))
                }
                else
                {
                    console.log('localStorage中没有找到PTP配置数据')
                }

                // 如果响应数据存在，尝试解析
                if (ptpResponseStr)
                {
                    try
                    {
                        var parsedData = typeof ptpResponseStr === 'string' ? JSON.parse(ptpResponseStr) : ptpResponseStr
                        console.log('解析后的PTP数据:', parsedData)
                    }
                    catch (e)
                    {
                        console.log('解析PTP数据时出错:', e)
                        console.log('原始数据类型:', typeof ptpResponseStr)
                        console.log('原始数据内容:', ptpResponseStr)
                    }
                }
            }

            // PTP配置操作函数 - 参考erps.asp的addToERPS函数
            function selectPtpConfig(item, event) {
                console.log('选择PTP配置:', item, event.target.checked)

                if (event.target.checked) {
                    // 填充高级设置表单（只在高级设置标签页时填充）
                    if (tabActive === 'tab2') {
                        fillAdvancedPtpForm(item)
                    }
                }
            }

            // 填充高级设置表单
            function fillAdvancedPtpForm(item) {
                console.log('填充高级设置表单:', item)

                // 基本配置
                document.getElementById('adv_portNum').value = item.portNum || ''
                document.getElementById('adv_enable').value = item.enable || 0
                document.getElementById('adv_mapChn').value = item.mapChn || ''
                document.getElementById('adv_mapChnEnable').value = item.mapChnEnable || 0
                document.getElementById('adv_portType').value = item.portType || 0
                document.getElementById('adv_tranSpec').value = item.tranSpec || 0
                document.getElementById('adv_encapType').value = item.encapType || 0
                document.getElementById('adv_clockType').value = item.clockType || 0
                document.getElementById('adv_delayMechanism').value = item.delayMechanism || 0

                // 步模式
                if (item.stepMode && item.stepMode.length >= 2) {
                    document.getElementById('adv_stepMode_sync').value = item.stepMode[0] || 0
                    document.getElementById('adv_stepMode_pdelay').value = item.stepMode[1] || 0
                }

                document.getElementById('adv_pdelayTcEn').value = item.pdelayTcEn || 0
                document.getElementById('adv_rxBypassEn').value = item.rxBypassEn || 0
                document.getElementById('adv_rxEncapType').value = item.rxEncapType || 0

                // 延迟补偿值
                if (item.delayValue && item.delayValue.length >= 4) {
                    document.getElementById('adv_delayValue_rx').value = item.delayValue[0] || 0
                    document.getElementById('adv_delayValue_tx').value = item.delayValue[1] || 0
                    document.getElementById('adv_delayValue_rxAsym').value = item.delayValue[2] || 0
                    document.getElementById('adv_delayValue_txAsym').value = item.delayValue[3] || 0
                }

                // PTP头部配置
                if (item.ptpHead) {
                    document.getElementById('adv_domainNumber').value = item.ptpHead.domainNumber || 0
                    document.getElementById('adv_flagField').value = item.ptpHead.flagField || 0

                    if (item.ptpHead.sourcePortIdentity) {
                        document.getElementById('adv_clockId0').value = item.ptpHead.sourcePortIdentity['clock_id[0]'] || 0
                        document.getElementById('adv_clockId1').value = item.ptpHead.sourcePortIdentity['clock_id[1]'] || 0
                        document.getElementById('adv_portNumber').value = item.ptpHead.sourcePortIdentity.port_number || 0
                    }

                    if (item.ptpHead.msgInterval && item.ptpHead.msgInterval.length >= 5) {
                        document.getElementById('adv_msgInterval_sync').value = item.ptpHead.msgInterval[0] || 0
                        document.getElementById('adv_msgInterval_announce').value = item.ptpHead.msgInterval[1] || 0
                        document.getElementById('adv_msgInterval_delay').value = item.ptpHead.msgInterval[2] || 0
                        document.getElementById('adv_msgInterval_preq').value = item.ptpHead.msgInterval[3] || 0
                        document.getElementById('adv_msgInterval_presp').value = item.ptpHead.msgInterval[4] || 0
                    }

                    if (item.ptpHead.ttl && item.ptpHead.ttl.length >= 2) {
                        document.getElementById('adv_ttl_pdelay').value = item.ptpHead.ttl[0] || 0
                        document.getElementById('adv_ttl_nonPdelay').value = item.ptpHead.ttl[1] || 0
                    }
                }

                // announceReceiptTimeout配置
                if (item.announceReceiptTimeout) {
                    document.getElementById('adv_timeoutSecond').value = item.announceReceiptTimeout.timeoutSecond || 0
                    document.getElementById('adv_timeoutMicrosecond').value = item.announceReceiptTimeout.timeoutMicrosecond || 0
                }

                // txInterval配置
                if (item.txInterval && item.txInterval.length >= 3) {
                    document.getElementById('adv_txInterval_sync_sec').value = item.txInterval[0] ? item.txInterval[0].txIntervalSecond || 0 : 0
                    document.getElementById('adv_txInterval_sync_usec').value = item.txInterval[0] ? item.txInterval[0].txIntervalMicrosecond || 0 : 0
                    document.getElementById('adv_txInterval_announce_sec').value = item.txInterval[1] ? item.txInterval[1].txIntervalSecond || 0 : 0
                    document.getElementById('adv_txInterval_announce_usec').value = item.txInterval[1] ? item.txInterval[1].txIntervalMicrosecond || 0 : 0
                    document.getElementById('adv_txInterval_req_sec').value = item.txInterval[2] ? item.txInterval[2].txIntervalSecond || 0 : 0
                    document.getElementById('adv_txInterval_req_usec').value = item.txInterval[2] ? item.txInterval[2].txIntervalMicrosecond || 0 : 0
                }

                // selfPortIdentityValue配置
                if (item.selfPortIdentityValue) {
                    document.getElementById('adv_selfClockId0').value = item.selfPortIdentityValue.clock_id?.[0] || 0
                    document.getElementById('adv_selfClockId1').value = item.selfPortIdentityValue.clock_id?.[1] || 0
                    document.getElementById('adv_selfPortNumber').value = item.selfPortIdentityValue.port_number || 0
                }

                // dMacmode配置
                if (item.dMacmode && item.dMacmode.length >= 4) {
                    document.getElementById('adv_dMacmode_req').value = item.dMacmode[0] || 0
                    document.getElementById('adv_dMacmode_resp').value = item.dMacmode[1] || 0
                    document.getElementById('adv_dMacmode_pdelay').value = item.dMacmode[2] || 0
                    document.getElementById('adv_dMacmode_sync').value = item.dMacmode[3] || 0
                }

                // dMac配置
                if (item.dMac && item.dMac.length >= 4) {
                    document.getElementById('adv_dMac_req').value = item.dMac[0] ? item.dMac[0].join(':') : ''
                    document.getElementById('adv_dMac_resp').value = item.dMac[1] ? item.dMac[1].join(':') : ''
                    document.getElementById('adv_dMac_pdelay').value = item.dMac[2] ? item.dMac[2].join(':') : ''
                    document.getElementById('adv_dMac_sync').value = item.dMac[3] ? item.dMac[3].join(':') : ''
                }

                // dIpmode配置
                if (item.dIpmode && item.dIpmode.length >= 4) {
                    document.getElementById('adv_dIpmode_sync').value = item.dIpmode[0] || 0
                    document.getElementById('adv_dIpmode_announce').value = item.dIpmode[1] || 0
                    document.getElementById('adv_dIpmode_req').value = item.dIpmode[2] || 0
                    document.getElementById('adv_dIpmode_resp').value = item.dIpmode[3] || 0
                }

                // dIp配置
                if (item.dIp && item.dIp.length >= 4) {
                    document.getElementById('adv_dIp_sync').value = item.dIp[0] || ''
                    document.getElementById('adv_dIp_announce').value = item.dIp[1] || ''
                    document.getElementById('adv_dIp_req').value = item.dIp[2] || ''
                    document.getElementById('adv_dIp_resp').value = item.dIp[3] || ''
                }

                // unicastFlag配置
                if (item.unicastFlag && item.unicastFlag.length >= 4) {
                    document.getElementById('adv_unicastFlag_sync').value = item.unicastFlag[0] || 0
                    document.getElementById('adv_unicastFlag_announce').value = item.unicastFlag[1] || 0
                    document.getElementById('adv_unicastFlag_req').value = item.unicastFlag[2] || 0
                    document.getElementById('adv_unicastFlag_resp').value = item.unicastFlag[3] || 0
                }

                // pktCheck配置
                if (item.pktCheck) {
                    document.getElementById('adv_rxVlanCheckEn').value = item.pktCheck.rxVlanCheckEn || 0
                    document.getElementById('adv_rxVlanCheckValue').value = item.pktCheck.rxVlanCheckValue || 0
                    document.getElementById('adv_rxDipCheckEn').value = item.pktCheck.rxDipCheckEn || 0
                    document.getElementById('adv_rxDipCheckValue').value = item.pktCheck.rxDipCheckValue || ''
                    document.getElementById('adv_rxDmacCheckEn').value = item.pktCheck.rxDmacCheckEn || 0
                    document.getElementById('adv_rxDmacCheckValue').value = item.pktCheck.rxDmacCheckValue ? item.pktCheck.rxDmacCheckValue.join(':') : ''

                    if (item.pktCheck.domainCheckEn && item.pktCheck.domainCheckEn.length >= 3) {
                        document.getElementById('adv_domainCheckEn_nonreq').value = item.pktCheck.domainCheckEn[0] || 0
                        document.getElementById('adv_domainCheckEn_req').value = item.pktCheck.domainCheckEn[1] || 0
                        document.getElementById('adv_domainCheckEn_announce').value = item.pktCheck.domainCheckEn[2] || 0
                    }

                    if (item.pktCheck.domainValue && item.pktCheck.domainValue.length >= 3) {
                        document.getElementById('adv_domainValue_nonreq').value = item.pktCheck.domainValue[0] || 0
                        document.getElementById('adv_domainValue_req').value = item.pktCheck.domainValue[1] || 0
                        document.getElementById('adv_domainValue_announce').value = item.pktCheck.domainValue[2] || 0
                    }

                    document.getElementById('adv_transportCheckEn').value = item.pktCheck.transportCheckEn || 0

                    if (item.pktCheck.portIdentityCheckEn && item.pktCheck.portIdentityCheckEn.length >= 3) {
                        document.getElementById('adv_portIdentityCheckEn_pdelay').value = item.pktCheck.portIdentityCheckEn[0] || 0
                        document.getElementById('adv_portIdentityCheckEn_sync').value = item.pktCheck.portIdentityCheckEn[1] || 0
                        document.getElementById('adv_portIdentityCheckEn_announce').value = item.pktCheck.portIdentityCheckEn[2] || 0
                    }

                    if (item.pktCheck.portIdentityValue && item.pktCheck.portIdentityValue.length >= 2) {
                        document.getElementById('adv_portIdentityValue1_clockId0').value = item.pktCheck.portIdentityValue[0]?.clock_id?.[0] || 0
                        document.getElementById('adv_portIdentityValue1_clockId1').value = item.pktCheck.portIdentityValue[0]?.clock_id?.[1] || 0
                        document.getElementById('adv_portIdentityValue1_portNumber').value = item.pktCheck.portIdentityValue[0]?.port_number || 0
                        document.getElementById('adv_portIdentityValue2_clockId0').value = item.pktCheck.portIdentityValue[1]?.clock_id?.[0] || 0
                        document.getElementById('adv_portIdentityValue2_clockId1').value = item.pktCheck.portIdentityValue[1]?.clock_id?.[1] || 0
                        document.getElementById('adv_portIdentityValue2_portNumber').value = item.pktCheck.portIdentityValue[1]?.port_number || 0
                    }
                }

                // Announce配置
                if (item.announce) {
                    document.getElementById('adv_currentUtcOffset').value = item.announce.currentUtcOffset || 0
                    document.getElementById('adv_priority1').value = item.announce.priority1 || 0
                    document.getElementById('adv_priority2').value = item.announce.priority2 || 0
                    document.getElementById('adv_stepsRemoved').value = item.announce.stepsRemoved || 0
                    document.getElementById('adv_timeSource').value = item.announce.timeSource || 0
                    document.getElementById('adv_identity1').value = item.announce.identity1 || 0
                    document.getElementById('adv_identity2').value = item.announce.identity2 || 0

                    if (item.announce.clockQuality) {
                        document.getElementById('adv_clockClass').value = item.announce.clockQuality.clockClass || 0
                        document.getElementById('adv_clockAccuracy').value = item.announce.clockQuality.clockAccuracy || 0
                        document.getElementById('adv_clockVariance').value = item.announce.clockQuality.clockVariance || 0
                    }
                }

                // 动态显示逻辑已改为Alpine.js的x-show指令
            }

            function deletePtpConfig(item) {
                console.log('删除PTP配置:', item)
                // 确认删除
                if (confirm('确定要删除端口' + item.portNum + ' 的PTP配置吗？'))
                {
                    console.log('执行删除操作 - 端口:', item.portNum)

                    // 构建删除参数，格式与快速配置相同，但使用portNum替代ptpConfig
                    var deleteConfig = {
                        pageName: "ptp.asp",
                        portNum: item.portNum
                    }

                    console.log('发送删除请求:', deleteConfig)

                    // 使用param1传递删除参数
                    var tf = document.ptp_setting
                    tf.param1.value = JSON.stringify(deleteConfig)
                    tf.action = "/goform/jw_del_portPtpConfig"
                    tf.submit()
                }
            }

            function fillPtpForm(item) {
                console.log('填充PTP表单:', item)
                // 使用Alpine.js的数据绑定来填充表单
                // 这里需要触发Alpine.js的数据更新
                if (window.Alpine && window.Alpine.store)
                {
                    // 如果使用Alpine.js store
                    window.Alpine.store('ptpForm', item)
                }
                else
                {
                    // 直接更新表单字段
                    document.getElementById('portNum').value = item.portNum
                    document.getElementById('mapChn').value = item.mapChn
                    // ... 更多字段
                }
            }





            // 动态显示逻辑已改为Alpine.js的x-show指令，不再需要此函数

            // 高级设置应用函数 - 新增配置（不是修改）
            function applyAdvancedPtpConfig() {
                console.log('应用PTP高级设置')

                // 获取用户输入的端口号和映射通道号
                var portNum = parseInt(document.getElementById('adv_portNum').value)
                var mapChn = parseInt(document.getElementById('adv_mapChn').value)

                // 验证必填字段
                if (!portNum || portNum < 1 || portNum > 28) {
                    alert('请输入有效的端口号 (1-28)')
                    return
                }

                if (isNaN(mapChn) || mapChn < 0 || mapChn > 31) {
                    alert('请输入有效的映射通道号 (0-31)')
                    return
                }

                // 检查重复配置（参考快速配置的逻辑）
                var existingConfig = window.ptpConfigData.find(function(config) {
                    return config.portNum === portNum && config.mapChn === mapChn
                })

                if (existingConfig) {
                    alert('端口号 ' + portNum + ' 和映射通道号 ' + mapChn + ' 的组合已存在，请选择其他值')
                    return
                }

                // 获取所有高级设置表单数据
                var formData = {
                    portNum: portNum,
                    enable: parseInt(document.getElementById('adv_enable').value) || 0,
                    mapChn: mapChn,
                    mapChnEnable: parseInt(document.getElementById('adv_mapChnEnable').value) || 0,
                    portType: parseInt(document.getElementById('adv_portType').value) || 0,
                    tranSpec: parseInt(document.getElementById('adv_tranSpec').value) || 0,
                    encapType: parseInt(document.getElementById('adv_encapType').value) || 0,
                    clockType: parseInt(document.getElementById('adv_clockType').value) || 0,
                    delayMechanism: parseInt(document.getElementById('adv_delayMechanism').value) || 0,
                    rxEncapType: parseInt(document.getElementById('adv_rxEncapType').value) || 0,
                    rxBypassEn: parseInt(document.getElementById('adv_rxBypassEn').value) || 0,
                    pdelayTcEn: parseInt(document.getElementById('adv_pdelayTcEn').value) || 0,
                    stepMode: [
                        parseInt(document.getElementById('adv_stepMode_sync').value) || 0,
                        parseInt(document.getElementById('adv_stepMode_pdelay').value) || 0
                    ],
                    delayValue: [
                        parseInt(document.getElementById('adv_delayValue_rx').value) || 0,
                        parseInt(document.getElementById('adv_delayValue_tx').value) || 0,
                        parseInt(document.getElementById('adv_delayValue_rxAsym').value) || 0,
                        parseInt(document.getElementById('adv_delayValue_txAsym').value) || 0
                    ],
                    ptpHead: {
                        domainNumber: parseInt(document.getElementById('adv_domainNumber').value) || 0,
                        flagField: parseInt(document.getElementById('adv_flagField').value) || 0,
                        sourcePortIdentity: {
                            "clock_id[0]": parseInt(document.getElementById('adv_clockId0').value) || 0,
                            "clock_id[1]": parseInt(document.getElementById('adv_clockId1').value) || 0,
                            "port_number": parseInt(document.getElementById('adv_portNumber').value) || 0
                        },
                        msgInterval: [
                            parseInt(document.getElementById('adv_msgInterval_sync').value) || 0,
                            parseInt(document.getElementById('adv_msgInterval_announce').value) || 0,
                            parseInt(document.getElementById('adv_msgInterval_delay').value) || 0,
                            parseInt(document.getElementById('adv_msgInterval_preq').value) || 0,
                            parseInt(document.getElementById('adv_msgInterval_presp').value) || 0
                        ],
                        ttl: [
                            parseInt(document.getElementById('adv_ttl_pdelay').value) || 0,
                            parseInt(document.getElementById('adv_ttl_nonPdelay').value) || 0
                        ]
                    },
                    announce: {
                        currentUtcOffset: parseInt(document.getElementById('adv_currentUtcOffset').value) || 0,
                        priority1: parseInt(document.getElementById('adv_priority1').value) || 0,
                        priority2: parseInt(document.getElementById('adv_priority2').value) || 0,
                        clockQuality: {
                            clockClass: parseInt(document.getElementById('adv_clockClass').value) || 0,
                            clockAccuracy: parseInt(document.getElementById('adv_clockAccuracy').value) || 0,
                            clockVariance: parseInt(document.getElementById('adv_clockVariance').value) || 0
                        },
                        identity1: parseInt(document.getElementById('adv_identity1').value) || 0,
                        identity2: parseInt(document.getElementById('adv_identity2').value) || 0,
                        stepsRemoved: parseInt(document.getElementById('adv_stepsRemoved').value) || 0,
                        timeSource: parseInt(document.getElementById('adv_timeSource').value) || 0
                    },
                    announceReceiptTimeout: {
                        timeoutSecond: parseInt(document.getElementById('adv_timeoutSecond').value) || 0,
                        timeoutMicrosecond: parseInt(document.getElementById('adv_timeoutMicrosecond').value) || 0
                    },
                    txInterval: [
                        {
                            txIntervalSecond: parseInt(document.getElementById('adv_txInterval_sync_sec').value) || 0,
                            txIntervalMicrosecond: parseInt(document.getElementById('adv_txInterval_sync_usec').value) || 0
                        },
                        {
                            txIntervalSecond: parseInt(document.getElementById('adv_txInterval_announce_sec').value) || 0,
                            txIntervalMicrosecond: parseInt(document.getElementById('adv_txInterval_announce_usec').value) || 0
                        },
                        {
                            txIntervalSecond: parseInt(document.getElementById('adv_txInterval_req_sec').value) || 0,
                            txIntervalMicrosecond: parseInt(document.getElementById('adv_txInterval_req_usec').value) || 0
                        }
                    ],
                    selfPortIdentityValue: {
                        clock_id: [
                            parseInt(document.getElementById('adv_selfClockId0').value) || 0,
                            parseInt(document.getElementById('adv_selfClockId1').value) || 0
                        ],
                        port_number: parseInt(document.getElementById('adv_selfPortNumber').value) || 0
                    },
                    dMacmode: [
                        parseInt(document.getElementById('adv_dMacmode_req').value) || 0,
                        parseInt(document.getElementById('adv_dMacmode_resp').value) || 0,
                        parseInt(document.getElementById('adv_dMacmode_pdelay').value) || 0,
                        parseInt(document.getElementById('adv_dMacmode_sync').value) || 0
                    ],
                    dMac: [
                        document.getElementById('adv_dMac_req').value ? document.getElementById('adv_dMac_req').value.split(':').map(x => parseInt(x, 16)) : [0,0,0,0,0,0],
                        document.getElementById('adv_dMac_resp').value ? document.getElementById('adv_dMac_resp').value.split(':').map(x => parseInt(x, 16)) : [0,0,0,0,0,0],
                        document.getElementById('adv_dMac_pdelay').value ? document.getElementById('adv_dMac_pdelay').value.split(':').map(x => parseInt(x, 16)) : [0,0,0,0,0,0],
                        document.getElementById('adv_dMac_sync').value ? document.getElementById('adv_dMac_sync').value.split(':').map(x => parseInt(x, 16)) : [0,0,0,0,0,0]
                    ],
                    dIpmode: [
                        parseInt(document.getElementById('adv_dIpmode_sync').value) || 0,
                        parseInt(document.getElementById('adv_dIpmode_announce').value) || 0,
                        parseInt(document.getElementById('adv_dIpmode_req').value) || 0,
                        parseInt(document.getElementById('adv_dIpmode_resp').value) || 0
                    ],
                    dIp: [
                        document.getElementById('adv_dIp_sync').value || '********',
                        document.getElementById('adv_dIp_announce').value || '********',
                        document.getElementById('adv_dIp_req').value || '********',
                        document.getElementById('adv_dIp_resp').value || '********'
                    ],
                    unicastFlag: [
                        parseInt(document.getElementById('adv_unicastFlag_sync').value) || 0,
                        parseInt(document.getElementById('adv_unicastFlag_announce').value) || 0,
                        parseInt(document.getElementById('adv_unicastFlag_req').value) || 0,
                        parseInt(document.getElementById('adv_unicastFlag_resp').value) || 0
                    ],
                    pktCheck: {
                        rxVlanCheckEn: parseInt(document.getElementById('adv_rxVlanCheckEn').value) || 0,
                        rxVlanCheckValue: parseInt(document.getElementById('adv_rxVlanCheckValue').value) || 0,
                        rxDipCheckEn: parseInt(document.getElementById('adv_rxDipCheckEn').value) || 0,
                        rxDipCheckValue: document.getElementById('adv_rxDipCheckValue').value || '********',
                        rxDmacCheckEn: parseInt(document.getElementById('adv_rxDmacCheckEn').value) || 0,
                        rxDmacCheckValue: document.getElementById('adv_rxDmacCheckValue').value ? document.getElementById('adv_rxDmacCheckValue').value.split(':').map(x => parseInt(x, 16)) : [0,0,0,0,0,0],
                        domainCheckEn: [
                            parseInt(document.getElementById('adv_domainCheckEn_nonreq').value) || 0,
                            parseInt(document.getElementById('adv_domainCheckEn_req').value) || 0,
                            parseInt(document.getElementById('adv_domainCheckEn_announce').value) || 0
                        ],
                        domainValue: [
                            parseInt(document.getElementById('adv_domainValue_nonreq').value) || 0,
                            parseInt(document.getElementById('adv_domainValue_req').value) || 0,
                            parseInt(document.getElementById('adv_domainValue_announce').value) || 0
                        ],
                        transportCheckEn: parseInt(document.getElementById('adv_transportCheckEn').value) || 0,
                        portIdentityCheckEn: [
                            parseInt(document.getElementById('adv_portIdentityCheckEn_pdelay').value) || 0,
                            parseInt(document.getElementById('adv_portIdentityCheckEn_sync').value) || 0,
                            parseInt(document.getElementById('adv_portIdentityCheckEn_announce').value) || 0
                        ],
                        portIdentityValue: [
                            {
                                clock_id: [
                                    parseInt(document.getElementById('adv_portIdentityValue1_clockId0').value) || 0,
                                    parseInt(document.getElementById('adv_portIdentityValue1_clockId1').value) || 0
                                ],
                                port_number: parseInt(document.getElementById('adv_portIdentityValue1_portNumber').value) || 0
                            },
                            {
                                clock_id: [
                                    parseInt(document.getElementById('adv_portIdentityValue2_clockId0').value) || 0,
                                    parseInt(document.getElementById('adv_portIdentityValue2_clockId1').value) || 0
                                ],
                                port_number: parseInt(document.getElementById('adv_portIdentityValue2_portNumber').value) || 0
                            }
                        ]
                    }
                }

                console.log('高级设置数据:', formData)

                // 使用实际表单数据构建PTP配置对象
                var ptpConfig = {
                    pageName: "ptp.asp",
                    ptpConfig: [{
                        portNum: formData.portNum,
                        enable: formData.enable,
                        mapChn: formData.mapChn,
                        mapChnEnable: formData.mapChnEnable,
                        portType: formData.portType,
                        tranSpec: formData.tranSpec,
                        encapType: formData.encapType,
                        clockType: formData.clockType,
                        delayMechanism: formData.delayMechanism,
                        rxEncapType: formData.rxEncapType,
                        rxBypassEn: formData.rxBypassEn,
                        pdelayTcEn: formData.pdelayTcEn,
                        stepMode: formData.stepMode,
                        delayValue: formData.delayValue,
                        ptpHead: formData.ptpHead,
                        dMac: formData.dMac,
                        dMacmode: formData.dMacmode,
                        dIp: formData.dIp,
                        dIpmode: formData.dIpmode,
                        unicastFlag: formData.unicastFlag,
                        selfPortIdentityValue: formData.selfPortIdentityValue,
                        announceReceiptTimeout: formData.announceReceiptTimeout,
                        txInterval: formData.txInterval,
                        pktCheck: formData.pktCheck,
                        announce: formData.announce
                    }]
                }

                // 使用param1传递JSON数据
                var tf = document.ptp_setting
                tf.param1.value = JSON.stringify(ptpConfig)
                tf.action = "/goform/jw_set_portPtpConfig"
                tf.submit()
            }

            // 快速配置PTP - 使用动态参数并检查重复
            function applyPtpConfig() {
                console.log('快速配置PTP')

                // 获取表单数据
                var formData = {
                    portNum: parseInt(document.getElementById('portNum').value) || 0,
                    mapChn: parseInt(document.getElementById('mapChn').value) || 0,
                    portType: parseInt(document.getElementById('portType').value) || 0,
                    encapType: parseInt(document.getElementById('encapType').value) || 0,
                    delayMechanism: parseInt(document.getElementById('delayMechanism').value) || 0
                }

                // 验证必填字段
                if (!formData.portNum || formData.portNum < 1 || formData.portNum > 28)
                {
                    alert('请输入有效的端口号 (1-28)')
                    return
                }

                if (formData.mapChn < 0 || formData.mapChn > 31)
                {
                    alert('请输入有效的映射通道号 (0-31)')
                    return
                }

                // 检查端口号和映射通道号是否与现有配置重复
                if (window.ptpConfigData && window.ptpConfigData.length > 0) {
                    var existingPorts = window.ptpConfigData.map(item => item.portNum)
                    var existingMapChns = window.ptpConfigData.map(item => item.mapChn)

                    if (existingPorts.includes(formData.portNum)) {
                        alert('端口号 ' + formData.portNum + ' 已经配置过PTP，请选择其他端口号')
                        return
                    }

                    if (existingMapChns.includes(formData.mapChn)) {
                        alert('映射通道号 ' + formData.mapChn + ' 已经被使用，请选择其他通道号')
                        return
                    }
                }

                // 使用动态参数构建PTP配置对象
                var ptpConfig = {
                    pageName: "ptp.asp",
                    ptpConfig: [{
                        "portNum": formData.portNum,
                        "enable": 1,  // 默认启用
                        "mapChn": formData.mapChn,
                        "mapChnEnable": 1,  // 默认启用映射通道
                        "portType": formData.portType,
                        "tranSpec": 0,  // 默认IEEE 1588
                        "encapType": formData.encapType,
                        "clockType": 0,  // 默认OC
                        "delayMechanism": formData.delayMechanism,
                        "stepMode": [0, 0],  // 默认ONE-STEP
                        "pdelayTcEn": 0,
                        "rxBypassEn": 0,
                        "rxEncapType": formData.encapType,  // 与发送封装类型相同
                        "delayValue": [0, 0, 0, 0],
                        "ptpHead": {
                            "domainNumber": 0,
                            "flagField": 0,
                            "sourcePortIdentity": {
                                "clock_id[0]": 0,
                                "clock_id[1]": 0,
                                "port_number": formData.portNum
                            },
                            "msgInterval": [0, 0, 0, 0, 0],
                            "ttl": [0, 0]
                        },
                        "dMac": [
                            [0, 0, 0, 0, 0, 0],
                            [0, 0, 0, 0, 0, 0],
                            [0, 0, 0, 0, 0, 0],
                            [0, 0, 0, 0, 0, 0]
                        ],
                        "dMacmode": [0, 0, 0, 0],
                        "dIp": [],
                        "dIpmode": [0, 0, 0, 0],
                        "unicastFlag": [0, 0, 0, 0],
                        "selfPortIdentityValue": {
                            "clock_id[0]": 0,
                            "clock_id[1]": 0,
                            "port_number": formData.portNum
                        },
                        "announceReceiptTimeout": {
                            "timeoutSecond": 0,
                            "timeoutMicrosecond": 0
                        },
                        "txInterval": [
                            {
                                "txIntervalSecond": 0,
                                "txIntervalMicrosecond": 0
                            },
                            {
                                "txIntervalSecond": 0,
                                "txIntervalMicrosecond": 0
                            },
                            {
                                "txIntervalSecond": 0,
                                "txIntervalMicrosecond": 0
                            }
                        ],
                        "pktCheck": {
                            "rxVlanCheckEn": 0,
                            "rxVlanCheckValue": 0,
                            "rxDipCheckEn": 0,
                            "rxDipCheckValue": "0.0.0.0",
                            "rxDmacCheckValue": [0, 0, 0, 0, 0, 0],
                            "domainCheckEn": [0, 0, 0],
                            "domainValue": [0, 0, 0],
                            "transportCheckEn": 0,
                            "transportValue": 0,
                            "portIdentityCheckEn": [0, 0, 0],
                            "portIdentityValue": [
                                {
                                    "clock_id[0]": 0,
                                    "clock_id[1]": 0,
                                    "port_number": 0
                                },
                                {
                                    "clock_id[0]": 0,
                                    "clock_id[1]": 0,
                                    "port_number": 0
                                }
                            ]
                        },
                        "announce": {
                            "currentUtcOffset": 0,
                            "priority1": 0,
                            "priority2": 0,
                            "clockQuality": {
                                "clockClass": 0,
                                "clockAccuracy": 0,
                                "clockVariance": 0
                            },
                            "identity1": 0,
                            "identity2": 0,
                            "stepsRemoved": 0,
                            "timeSource": 0
                        }
                    }]
                }

                console.log('发送PTP配置:', ptpConfig)

                // 参考mirror.asp的提交方式，使用param1传递JSON
                var tf = document.ptp_setting
                tf.param1.value = JSON.stringify(ptpConfig)
                tf.action = "/goform/jw_set_portPtpConfig"
                tf.submit()
            }

            function fastchecking() {
                var tf = document.erps_setting
                var traffic_vlan = document.getElementById("traffic_vlan2").value

                if (traffic_vlan == "")
                {
                    alert(putmsg(<% write(lang); %>, "配置不能为空!"))
                    return
                }

                if (!checkavidhybrid(traffic_vlan))
                {
                    alert(putmsg(<% write(lang); %>, "traffic vlan格式必须为: X,X,X,X . "))
                    return 0
                }

                tf.action = "/goform/fasterps"
                tf.submit()

            }





            function refreshpage() {
                location.href = 'ptp.asp?ltime=' + <% write(lltime); %> +'&tab=' + tabActive
            }

            function getUrlParamTab() {
                let urlSearch = window.location.search
                const search = new URLSearchParams(urlSearch)

                const params = Object.fromEntries(search.entries())

                try
                {
                    if (params.tab)
                    {
                        tabActive = params.tab
                        return params.tab
                    }
                }
                catch
                {
                }
                return 'tab1'
            }



        </SCRIPT>
    </head>

    <body onload="" x-data="{active:'tab1'}" x-init="active=getUrlParamTab()"><br>
        <% var authmode; checkCurMode(); %>
        <script>
            checktop(<% write(lang); %>)



            // 页面加载完成后初始化高级设置表单
            document.addEventListener('DOMContentLoaded', function() {
                // 动态显示逻辑已改为Alpine.js的x-show指令
            })
        </script>
        <div>
            <ul class="tabmenu">
                <li id="tab1" :class="active==='tab1'?'tab':'tab-disable'">
                    <a herf="#" x-on:click="active='tab1',tabActive='tab1'">PTP快速设置</a>
                </li>
                <li id="tab2" :class="active==='tab2'?'tab':'tab-disable'">
                    <a herf="#" x-on:click="active='tab2',tabActive='tab2'">PTP高级设置</a>
                </li>

            </ul>
        </div>
        <form name="ptp_setting" method="POST" action="" class="formContain" style="min-height: 560px;">
            <input type="hidden" name="left_menu_id" value="@left_menu_id#">
            <input type="hidden" name="ltime" value=<% write(lltime); %>>
            <input type="hidden" name="lastts" value=<% write(serverts); %>>
            <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
            <!-- <input type="hidden" name="erps_id"> -->
            <input type="hidden" name="tabName" id="tabName">
            <table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
                <tr>
                    <td>
                        <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
                            <tr>
                                <td valign="top">
                                    <table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"
                                           class="cword09">
                                        <tr>
                                            <td>
                                                <table width="100%" align="center" border="0" cellspacing="0"
                                                       cellpadding="0">
                                                    <tr>
                                                        <td height="30px">
                                                            <font size="5" color="#404040">
                                                                <div class="bot" x-text="active==='tab1'?'PTP快速设置':'PTP高级设置'"></div>
                                                            </font>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>


                        </table>
                    </td>
                </tr>
            </table>
            <input type="hidden" name="todo" value="save">
            <INPUT type="hidden" name="this_file" value="ptp.asp">
            <INPUT type="hidden" name="next_file" value="ptp.asp">
            <input type="hidden" name="message" value="@msg_text#">
            <input type="hidden" name="param1" id="param1">
            <div class="" x-show="active==='tab1'">
                <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table2"
                       class="tablebord">

                    <tr height="30">
                        <td width="15%" align="left" class="td7">&nbsp;
                            <script>writemsg(<% write(lang); %>, "端口号:")</script>
                        </td>
                        <td width="35%" align="left" class="td7">
                            <input id="portNum" type="text" class="input_board4"/>&nbsp;
                            <script>writemsg(<% write(lang); %>, "(1-28)")</script>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;
                            <script> writemsg(<% write(lang); %>, "映射通道号:") </script>
                        </td>
                        <td width="35%" align="left" class="td7">
                            <input type="text" class="input_board4" id="mapChn"/>&nbsp;
                            <script>writemsg(<% write(lang); %>, "(0-31)")</script>
                        </td>
                    </tr>

                    <tr height="30">
                        <td width="15%" align="left" class="td7">&nbsp;
                            <script>writemsg(<% write(lang); %>, "端口角色:")</script>
                        </td>
                        <td class="td7" width="35%" align="left">
                            <select id="portType">
                                <option value="0">SLAVE</option>
                                <option value="1">MASTER</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;
                            封装类型:
                        </td>
                        <td class="all_tables" width="35%" align="left" class="td7">
                            <select id="encapType">
                                <option value="0">ETH</option>
                                <option value="1">ETH&VLAN</option>
                                <option value="2">IPV4</option>
                                <option value="3">IPV4&VLAN</option>
                            </select>
                        </td>
                    </tr>

                    <tr height="30">
                        <td width="15%" align="left" class="td7">&nbsp;
                            延迟机制:
                        </td>
                        <td class="all_tables" width="35%" align="left" class="td7">
                            <select id="delayMechanism">
                                <option value="0">P2P</option>
                                <option value="1">E2E</option>
                            </select>
                        </td>
                        <td width="35%" align="left" class="td7" colspan="2">
                            <!-- 空白区域 -->
                        </td>
                    </tr>

                    <tr height="30">
                        <td colspan="4" align="center" class="td7" style="text-align: center;">
                            <script>writebutton(<% write(authmode); %>, <% write(lang); %>, "快速配置", "buttons_add", "button", "modify4", "applyPtpConfig()")</script>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- PTP高级设置标签页 -->
            <div class="" x-show="active==='tab2'"
                 x-data="{
                     portType: 0,
                     clockType: 0,
                     encapType: 0,
                     rxEncapType: 0,
                     dMacmode_req: 0,
                     dMacmode_resp: 0,
                     dMacmode_pdelay: 0,
                     dMacmode_sync: 0,
                     dIpmode_sync: 0,
                     dIpmode_announce: 0,
                     dIpmode_req: 0,
                     dIpmode_resp: 0,
                     updateVisibility() {
                         this.portType = parseInt(document.getElementById('adv_portType').value) || 0;
                         this.clockType = parseInt(document.getElementById('adv_clockType').value) || 0;
                         this.encapType = parseInt(document.getElementById('adv_encapType').value) || 0;
                         this.rxEncapType = parseInt(document.getElementById('adv_rxEncapType').value) || 0;
                         this.dMacmode_req = parseInt(document.getElementById('adv_dMacmode_req')?.value) || 0;
                         this.dMacmode_resp = parseInt(document.getElementById('adv_dMacmode_resp')?.value) || 0;
                         this.dMacmode_pdelay = parseInt(document.getElementById('adv_dMacmode_pdelay')?.value) || 0;
                         this.dMacmode_sync = parseInt(document.getElementById('adv_dMacmode_sync')?.value) || 0;
                         this.dIpmode_sync = parseInt(document.getElementById('adv_dIpmode_sync')?.value) || 0;
                         this.dIpmode_announce = parseInt(document.getElementById('adv_dIpmode_announce')?.value) || 0;
                         this.dIpmode_req = parseInt(document.getElementById('adv_dIpmode_req')?.value) || 0;
                         this.dIpmode_resp = parseInt(document.getElementById('adv_dIpmode_resp')?.value) || 0;
                     },
                     resetClockTypeIfNeeded() {
                         const portType = parseInt(document.getElementById('adv_portType').value) || 0;
                         const clockTypeSelect = document.getElementById('adv_clockType');
                         const currentClockType = parseInt(clockTypeSelect.value) || 0;

                         // 如果切换到SLAVE模式，且当前选择的是BC(1)或MIX-CLK(3)，则重置为OC(0)
                         if (portType === 0 && (currentClockType === 1 || currentClockType === 3)) {
                             clockTypeSelect.value = 0;
                             this.updateVisibility(); // 重新更新显示状态
                             console.log('时钟模式已自动重置为OC，因为SLAVE模式不支持BC和MIX-CLK');
                         }
                     }
                 }"
                 x-init="updateVisibility()">
                <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table_advanced" class="tablebord">



                    <tr height="30">
                        <td width="15%" align="left" class="td7">&nbsp;端口号:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_portNum" type="text" class="input_board4"/>&nbsp;(1-28)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;使能状态:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_enable" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                    </tr>

                    <tr height="30">
                        <td width="15%" align="left" class="td7">&nbsp;映射通道号:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_mapChn" type="text" class="input_board4"/>&nbsp;(0-31)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;映射通道使能:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_mapChnEnable" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                    </tr>



                    <tr height="30">
                        <td width="15%" align="left" class="td7">&nbsp;端口角色:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_portType" class="select1" @change="updateVisibility(); resetClockTypeIfNeeded()">
                                <option value="0">SLAVE</option>
                                <option value="1">MASTER</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;协议类型:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_tranSpec" class="select1">
                                <option value="0">IEEE 1588</option>
                                <!--<option value="1">IEEE 802.1AS</option> -->
                            </select>
                        </td>
                    </tr>

                    <tr height="30">
                        <td width="15%" align="left" class="td7">&nbsp;时钟模式:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_clockType" class="select1" @change="updateVisibility()">
                                <option value="0">OC</option>
                                <option value="1" x-show="portType === 1">BC</option>
                                <option value="2">TC</option>
                                <option value="3" x-show="portType === 1">MIX-CLK</option>
                            </select>
                        </td>
                    </tr>

                    <!-- PTP报文基础配置分类 -->
                    <tr height="35" style="background-color: #f0f0f0;" x-show="clockType !== 2">
                        <td colspan="4" align="left" class="td7" style="font-weight: bold; font-size: 14px; padding-left: 10px;">
                            PTP报文基础配置
                        </td>
                    </tr>

                    <tr height="30">
                        <td width="15%" align="left" class="td7">&nbsp;封装类型:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_encapType" class="select1" @change="updateVisibility()">
                                <option value="0">ETH</option>
                                <option value="1">ETH&VLAN</option>
                                <option value="2">IPV4</option>
                                <option value="3">IPV4&VLAN</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;延迟机制:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_delayMechanism" class="select1">
                                <option value="0">P2P</option>
                                <option value="1">E2E</option>
                            </select>
                        </td>
                    </tr>
                    <tr height="30" class="tc-hidden">
                        <td width="15%" align="left" class="td7">&nbsp;域编号:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_domainNumber" type="text" class="input_board4"/>&nbsp;(0-255)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;标志字段:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_flagField" type="text" class="input_board4"/>&nbsp;(0-65535)
                        </td>
                    </tr>

                    <tr height="30" class="tc-hidden">
                        <td width="15%" align="left" class="td7">&nbsp;时钟ID高位:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_clockId0" type="text" class="input_board4"/>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;时钟ID低位:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_clockId1" type="text" class="input_board4"/>
                        </td>
                    </tr>

                    <tr height="30" class="tc-hidden">
                        <td width="15%" align="left" class="td7">&nbsp;端口号:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_portNumber" type="text" class="input_board4"/>&nbsp;(0-65535)
                        </td>
                        <td width="15%" align="left" class="td7" x-show="encapType === 2 || encapType === 3">&nbsp;PDELAY TTL:</td>
                        <td width="35%" align="left" class="td7" x-show="encapType === 2 || encapType === 3">
                            <input id="adv_ttl_pdelay" type="text" class="input_board4"/>&nbsp;(0-255)
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;本端时钟ID高位:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_selfClockId0" type="text" class="input_board4"/>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;本端时钟ID低位:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_selfClockId1" type="text" class="input_board4"/>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;本端端口号:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_selfPortNumber" type="text" class="input_board4"/>&nbsp;(0-65535)
                        </td>
                        <td width="35%" align="left" class="td7" colspan="2">
                            <!-- 空白区域 -->
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2 && (encapType === 2 || encapType === 3)">
                        <td width="15%" align="left" class="td7">&nbsp;NON-PDELAY TTL:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_ttl_nonPdelay" type="text" class="input_board4"/>&nbsp;(0-255)
                        </td>
                        <td width="35%" align="left" class="td7" colspan="2">
                            <!-- 空白区域 -->
                        </td>
                    </tr>



                    <tr height="30" x-show="portType === 1 && clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;SYNC间隔:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_msgInterval_sync" type="text" class="input_board4"/>&nbsp;(日志级别)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;ANNOUNCE间隔:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_msgInterval_announce" type="text" class="input_board4"/>&nbsp;(日志级别)
                        </td>
                    </tr>

                    <tr height="30" x-show="portType === 0 && clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;DELAY间隔:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_msgInterval_delay" type="text" class="input_board4"/>&nbsp;(日志级别)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;PREQ间隔:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_msgInterval_preq" type="text" class="input_board4"/>&nbsp;(日志级别)
                        </td>
                    </tr>

                    <tr height="30" x-show="portType === 0 && clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;PRESP间隔:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_msgInterval_presp" type="text" class="input_board4"/>&nbsp;(日志级别)
                        </td>
                        <td width="35%" align="left" class="td7" colspan="2">
                            <!-- 空白区域 -->
                        </td>
                    </tr>

                    <tr height="30" x-show="portType === 1 && clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;SYNC发送间隔秒:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_txInterval_sync_sec" type="text" class="input_board4"/>&nbsp;(秒)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;SYNC发送间隔微秒:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_txInterval_sync_usec" type="text" class="input_board4"/>&nbsp;(微秒)
                        </td>
                    </tr>

                    <tr height="30" x-show="portType === 1 && clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;ANNOUNCE发送间隔秒:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_txInterval_announce_sec" type="text" class="input_board4"/>&nbsp;(秒)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;ANNOUNCE发送间隔微秒:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_txInterval_announce_usec" type="text" class="input_board4"/>&nbsp;(微秒)
                        </td>
                    </tr>

                    <tr height="30" x-show="portType === 0 && clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;REQ发送间隔秒:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_txInterval_req_sec" type="text" class="input_board4"/>&nbsp;(秒)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;REQ发送间隔微秒:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_txInterval_req_usec" type="text" class="input_board4"/>&nbsp;(微秒)
                        </td>
                    </tr>


                    <!-- 报文发送配置分类 -->
                    <tr height="35" style="background-color: #f0f0f0;" x-show="clockType !== 2">
                        <td colspan="4" align="left" class="td7" style="font-weight: bold; font-size: 14px; padding-left: 10px;">
                            报文发送配置
                        </td>
                    </tr>


                    <tr height="30">

                        <td width="15%" align="left" class="td7 tc-hidden">&nbsp;接收封装类型:</td>
                        <td width="35%" align="left" class="td7 tc-hidden">
                            <select id="adv_rxEncapType" class="select1">
                                <option value="0">ETH</option>
                                <option value="1">IPV4</option>
                            </select>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;接收透传使能:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_rxBypassEn" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7" x-show="clockType === 2 || clockType === 3">&nbsp;Pdelay透传使能:</td>
                        <td width="35%" align="left" class="td7" x-show="clockType === 2 || clockType === 3">
                            <select id="adv_pdelayTcEn" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;SYNC包步模式:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_stepMode_sync" class="select1">
                                <option value="0">ONE-STEP</option>
                                <option value="1">TWO-STEP</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;PDELAY-RESP包步模式:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_stepMode_pdelay" class="select1">
                                <option value="0">ONE-STEP</option>
                                <option value="1">TWO-STEP</option>
                            </select>
                        </td>
                    </tr>





                    <tr height="30" x-show="portType === 0 && clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;Announce超时秒数:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_timeoutSecond" type="text" class="input_board4"/>&nbsp;(0-1023)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;Announce超时微秒:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_timeoutMicrosecond" type="text" class="input_board4"/>&nbsp;(0-1048576)
                        </td>
                    </tr>





                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;MAC地址模式REQ:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_dMacmode_req" class="select1" @change="updateVisibility()">
                                <option value="0">COPY</option>
                                <option value="1">CONFIG</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;MAC地址模式RESP:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_dMacmode_resp" class="select1" @change="updateVisibility()">
                                <option value="0">COPY</option>
                                <option value="1">CONFIG</option>
                            </select>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;MAC地址模式PDELAY:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_dMacmode_pdelay" class="select1" @change="updateVisibility()">
                                <option value="0">COPY</option>
                                <option value="1">CONFIG</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;MAC地址模式SYNC:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_dMacmode_sync" class="select1" @change="updateVisibility()">
                                <option value="0">COPY</option>
                                <option value="1">CONFIG</option>
                            </select>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2 && (dMacmode_req === 1 || dMacmode_resp === 1)">
                        <td width="15%" align="left" class="td7" x-show="dMacmode_req === 1">&nbsp;REQ包MAC地址:</td>
                        <td width="35%" align="left" class="td7" x-show="dMacmode_req === 1">
                            <input id="adv_dMac_req" type="text" class="input_board4" placeholder="00:11:22:33:44:55"/>
                        </td>
                        <td width="15%" align="left" class="td7" x-show="dMacmode_resp === 1">&nbsp;RESP包MAC地址:</td>
                        <td width="35%" align="left" class="td7" x-show="dMacmode_resp === 1">
                            <input id="adv_dMac_resp" type="text" class="input_board4" placeholder="00:AA:BB:CC:DD:EE"/>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2 && (dMacmode_pdelay === 1 || dMacmode_sync === 1)">
                        <td width="15%" align="left" class="td7" x-show="dMacmode_pdelay === 1">&nbsp;PDELAY包MAC地址:</td>
                        <td width="35%" align="left" class="td7" x-show="dMacmode_pdelay === 1">
                            <input id="adv_dMac_pdelay" type="text" class="input_board4" placeholder="11:22:33:44:55:66"/>
                        </td>
                        <td width="15%" align="left" class="td7" x-show="dMacmode_sync === 1">&nbsp;SYNC包MAC地址:</td>
                        <td width="35%" align="left" class="td7" x-show="dMacmode_sync === 1">
                            <input id="adv_dMac_sync" type="text" class="input_board4" placeholder="77:88:99:AA:BB:CC"/>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2 && (encapType === 2 || encapType === 3)">
                        <td width="15%" align="left" class="td7">&nbsp;IP地址模式SYNC:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_dIpmode_sync" class="select1" @change="updateVisibility()">
                                <option value="0">COPY</option>
                                <option value="1">CONFIG</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;IP地址模式ANNOUNCE:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_dIpmode_announce" class="select1" @change="updateVisibility()">
                                <option value="0">COPY</option>
                                <option value="1">CONFIG</option>
                            </select>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2 && (encapType === 2 || encapType === 3)">
                        <td width="15%" align="left" class="td7">&nbsp;IP地址模式REQ:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_dIpmode_req" class="select1" @change="updateVisibility()">
                                <option value="0">COPY</option>
                                <option value="1">CONFIG</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;IP地址模式RESP:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_dIpmode_resp" class="select1" @change="updateVisibility()">
                                <option value="0">COPY</option>
                                <option value="1">CONFIG</option>
                            </select>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2 && (encapType === 2 || encapType === 3) && (dIpmode_sync === 1 || dIpmode_announce === 1)">
                        <td width="15%" align="left" class="td7" x-show="dIpmode_sync === 1">&nbsp;SYNC包IP地址:</td>
                        <td width="35%" align="left" class="td7" x-show="dIpmode_sync === 1">
                            <input id="adv_dIp_sync" type="text" class="input_board4" placeholder="********"/>
                        </td>
                        <td width="15%" align="left" class="td7" x-show="dIpmode_announce === 1">&nbsp;ANNOUNCE包IP地址:</td>
                        <td width="35%" align="left" class="td7" x-show="dIpmode_announce === 1">
                            <input id="adv_dIp_announce" type="text" class="input_board4" placeholder="********"/>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2 && (encapType === 2 || encapType === 3) && (dIpmode_req === 1 || dIpmode_resp === 1)">
                        <td width="15%" align="left" class="td7" x-show="dIpmode_req === 1">&nbsp;REQ包IP地址:</td>
                        <td width="35%" align="left" class="td7" x-show="dIpmode_req === 1">
                            <input id="adv_dIp_req" type="text" class="input_board4" placeholder="********"/>
                        </td>
                        <td width="15%" align="left" class="td7" x-show="dIpmode_resp === 1">&nbsp;RESP包IP地址:</td>
                        <td width="35%" align="left" class="td7" x-show="dIpmode_resp === 1">
                            <input id="adv_dIp_resp" type="text" class="input_board4" placeholder="********"/>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2 && (encapType === 2 || encapType === 3)">
                        <td width="15%" align="left" class="td7">&nbsp;单播模式SYNC:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_unicastFlag_sync" class="select1">
                                <option value="0">MULTICAST</option>
                                <option value="1">UNICAST</option>
                                <option value="2">COPY</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;单播模式ANNOUNCE:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_unicastFlag_announce" class="select1">
                                <option value="0">MULTICAST</option>
                                <option value="1">UNICAST</option>
                                <option value="2">COPY</option>
                            </select>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2 && (encapType === 2 || encapType === 3)">
                        <td width="15%" align="left" class="td7">&nbsp;单播模式REQ:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_unicastFlag_req" class="select1">
                                <option value="0">MULTICAST</option>
                                <option value="1">UNICAST</option>
                                <option value="2">COPY</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;单播模式RESP:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_unicastFlag_resp" class="select1">
                                <option value="0">MULTICAST</option>
                                <option value="1">UNICAST</option>
                                <option value="2">COPY</option>
                            </select>
                        </td>
                    </tr>

                    <!-- 报文校验配置分类 -->
                    <tr height="35" style="background-color: #f0f0f0;" x-show="clockType !== 2">
                        <td colspan="4" align="left" class="td7" style="font-weight: bold; font-size: 14px; padding-left: 10px;">
                            报文校验配置
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;VLAN检查使能:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_rxVlanCheckEn" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;VLAN检查值:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_rxVlanCheckValue" type="text" class="input_board4"/>&nbsp;(1-4094)
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2 && rxEncapType === 1">
                        <td width="15%" align="left" class="td7">&nbsp;目的IP检查使能:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_rxDipCheckEn" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;目的IP检查值:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_rxDipCheckValue" type="text" class="input_board4" placeholder="********"/>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;目的MAC检查使能:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_rxDmacCheckEn" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;目的MAC检查值:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_rxDmacCheckValue" type="text" class="input_board4" placeholder="00:22:44:66:88:AA"/>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;域名检查使能NON-REQ:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_domainCheckEn_nonreq" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;域名检查使能REQ:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_domainCheckEn_req" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;域名检查使能ANNOUNCE:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_domainCheckEn_announce" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;传输层检查使能:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_transportCheckEn" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;域名检查值NON-REQ:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_domainValue_nonreq" type="text" class="input_board4" placeholder="1"/>&nbsp;(0-255)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;域名检查值REQ:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_domainValue_req" type="text" class="input_board4" placeholder="1"/>&nbsp;(0-255)
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;域名检查值ANNOUNCE:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_domainValue_announce" type="text" class="input_board4" placeholder="0"/>&nbsp;(0-255)
                        </td>
                        <td width="35%" align="left" class="td7" colspan="2">
                            <!-- 空白区域 -->
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;端口标识检查PDELAY:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_portIdentityCheckEn_pdelay" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;端口标识检查SYNC:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_portIdentityCheckEn_sync" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;端口标识检查ANNOUNCE:</td>
                        <td width="35%" align="left" class="td7">
                            <select id="adv_portIdentityCheckEn_announce" class="select1">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                        <td width="35%" align="left" class="td7" colspan="2">
                            <!-- 空白区域 -->
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;端口标识1时钟ID高位:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_portIdentityValue1_clockId0" type="text" class="input_board4" placeholder="0x11111111"/>
                            <input id="adv_portIdentityValue1_clockId1" type="text" class="input_board4" placeholder="0x22222222"/>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;端口标识1时钟ID低位:</td>
                        <td width="35%" align="left" class="td7">
                            
                        </td>

                        <td width="15%" align="left" class="td7">&nbsp;端口标识1端口号:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_portIdentityValue1_portNumber" type="text" class="input_board4" placeholder="200"/>&nbsp;(0-65535)
                        </td>
                        <td width="35%" align="left" class="td7" colspan="2">
                            <!-- 空白区域 -->
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">

                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;端口标识2时钟ID高位:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_portIdentityValue2_clockId0" type="text" class="input_board4" placeholder="0x33333333"/>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;端口标识2时钟ID低位:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_portIdentityValue2_clockId1" type="text" class="input_board4" placeholder="0x44444444"/>
                        </td>
                    </tr>

                    <tr height="30" x-show="clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;端口标识2端口号:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_portIdentityValue2_portNumber" type="text" class="input_board4" placeholder="300"/>&nbsp;(0-65535)
                        </td>
                        <td width="35%" align="left" class="td7" colspan="2">
                            <!-- 空白区域 -->
                        </td>
                    </tr>

                    <!-- 时间同步配置分类 -->
                    <tr height="35" style="background-color: #f0f0f0;" x-show="portType === 1 && clockType !== 2">
                        <td colspan="4" align="left" class="td7" style="font-weight: bold; font-size: 14px; padding-left: 10px;">
                            时间同步配置
                        </td>
                    </tr>

                    <tr height="30" x-show="portType === 1 && clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;UTC偏移:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_currentUtcOffset" type="text" class="input_board4"/>&nbsp;(0-65535)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;优先级1:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_priority1" type="text" class="input_board4"/>&nbsp;(0-255)
                        </td>
                    </tr>

                    <tr height="30" x-show="portType === 1 && clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;优先级2:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_priority2" type="text" class="input_board4"/>&nbsp;(0-255)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;跳数:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_stepsRemoved" type="text" class="input_board4"/>&nbsp;(0-65535)
                        </td>
                    </tr>

                    <tr height="30" x-show="portType === 1 && clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;时间源:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_timeSource" type="text" class="input_board4"/>&nbsp;(0-255)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;时钟等级:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_clockClass" type="text" class="input_board4"/>&nbsp;(0-255)
                        </td>
                    </tr>

                    <tr height="30" x-show="portType === 1 && clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;时钟精度:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_clockAccuracy" type="text" class="input_board4"/>&nbsp;(0-255)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;时钟稳定度:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_clockVariance" type="text" class="input_board4"/>&nbsp;(0-65535)
                        </td>
                    </tr>

                    <tr height="30" x-show="portType === 1 && clockType !== 2">
                        <td width="15%" align="left" class="td7">&nbsp;时钟ID高位:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_identity1" type="text" class="input_board4"/>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;时钟ID低位:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_identity2" type="text" class="input_board4"/>
                        </td>
                    </tr>
                    <!-- 报文其它配置分类 -->
                    <tr height="35" style="background-color: #f0f0f0;" x-show="clockType !== 2">
                        <td colspan="4" align="left" class="td7" style="font-weight: bold; font-size: 14px; padding-left: 10px;">
                            报文其它配置
                        </td>
                    </tr>

                    <tr height="30" x-show="portType === 0">
                        <td width="15%" align="left" class="td7">&nbsp;RX延迟补偿:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_delayValue_rx" type="text" class="input_board4"/>&nbsp;(纳秒)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;TX延迟补偿:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_delayValue_tx" type="text" class="input_board4"/>&nbsp;(纳秒)
                        </td>
                    </tr>

                    <tr height="30" x-show="portType === 0">
                        <td width="15%" align="left" class="td7">&nbsp;RX非对称补偿:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_delayValue_rxAsym" type="text" class="input_board4"/>&nbsp;(纳秒)
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;TX非对称补偿:</td>
                        <td width="35%" align="left" class="td7">
                            <input id="adv_delayValue_txAsym" type="text" class="input_board4"/>&nbsp;(纳秒)
                        </td>
                    </tr>


                    <tr height="30">
                        <td colspan="4" align="center" class="td7" style="text-align: center;">
                            <script>writebutton(<% write(authmode); %>, <% write(lang); %>, "应  用", "buttons_add", "button", "modify4", "applyAdvancedPtpConfig()")</script>
                        </td>
                    </tr>

                </table>
            </div>

            <div style="margin-top: 20px;" x-data="{ ptpList: window.ptpConfigData || [] }" x-init="console.log('PTP列表数据:', ptpList)">
                <font size="5" color="#404040">
                    <div class="bot">PTP信息</div>
                </font>

                <div class="table-container">
                    <table width="100%" border="0" cellpadding="0" cellspacing="0" class="tablebord " id="table_ptp">
                    <thead height="30" align="center" class="td7">
                        <th align="center" class="td2">选择</th>
                        <th align="center" class="td2">端口号</th>
                        <th align="center" class="td2">映射通道</th>
                        <th align="center" class="td2">端口角色</th>
                        <th align="center" class="td2">封装类型</th>
                        <th align="center" class="td2">延迟机制</th>
                        <th align="center" class="td2">使能状态</th>
                        <th align="center" class="td2">时钟模式</th>
                        <th align="center" class="td2">协议类型</th>
                        <th align="center" class="td2">接收封装</th>
                        <th align="center" class="td2">步模式</th>
                        <th align="center" class="td2">操作</th>
                    </thead>
                    <tbody>
                        <template x-for="(item, index) in ptpList" :key="index">
                            <tr height="30" class="td7" :id="'tr_ptp_' + item.portNum">
                                <td align="center" class="td2">
                                    <input type="checkbox" :value="index"
                                           @change="selectPtpConfig(item, $event)"/>
                                </td>
                                <td align="center" class="td2" x-text="item.portNum"></td>
                                <td align="center" class="td2" x-text="item.mapChn"></td>
                                <td align="center" class="td2" x-text="item.portType ? 'MASTER' : 'SLAVE'"></td>
                                <td align="center" class="td2">
                                    <span x-text="['ETH', 'ETH&V', 'IPV4', 'IPV4&V'][item.encapType] || 'ETH'"></span>
                                </td>
                                <td align="center" class="td2" x-text="item.delayMechanism ? 'E2E' : 'P2P'"></td>
                                <td align="center" class="td2">
                                    <span x-text="item.enable ? '启用' : '禁用'"
                                          :class="item.enable ? 'text-success' : 'text-muted'"></span>
                                </td>
                                <td align="center" class="td2">
                                    <span x-text="['OC', 'BC', 'TC', 'MIX'][item.clockType] || 'OC'"></span>
                                </td>
                                <td align="center" class="td2">
                                    <span x-text="item.tranSpec ? '802.1AS' : '1588'"></span>
                                </td>
                                <td align="center" class="td2">
                                    <span x-text="['ETH', 'IPV4'][item.rxEncapType] || 'ETH'"></span>
                                </td>
                                <td align="center" class="td2">
                                    <span x-text="item.stepMode && item.stepMode.length >= 2 ?
                                        (item.stepMode[0] ? '2S' : '1S') + '/' +
                                        (item.stepMode[1] ? '2S' : '1S') : 'N/A'"></span>
                                </td>
                                <td align="center" class="td2">
                                    <button type="button" class="botton_under_line"
                                            @click="deletePtpConfig(item)"
                                            style="color: white; background-color: #dc3545;">删除
                                    </button>
                                </td>
                            </tr>
                        </template>
                        <tr x-show="ptpList.length === 0">
                            <td colspan="12" align="center" class="td2" style="padding: 20px; color: #999;">
                                暂无PTP配置数据
                            </td>
                        </tr>
                    </tbody>
                </table>
                </div>
                <div align="center" style="padding: 5px;">
                    <script>writebutton(1, <% write(lang); %>, "刷  新", "button", "button", "Refresh", "refreshpage()")</script>
                </div>

            </div>
        </form>
        <script>
            <% if (errorcode != "") { write_errorcode(errorcode); } %>
            // changebgcolor();
            changebgcolor_name("table2")
            changebgcolor_name("table3")
            changebgcolor_name("table_ptp")
            changebgcolor_name("table_advanced")

            // 页面加载完成后初始化字段显示状态
            setTimeout(function() {
                console.log('初始化PTP字段显示状态 - 已改为Alpine.js的x-show指令')
            }, 100)
        </script>
    </body>

</html>