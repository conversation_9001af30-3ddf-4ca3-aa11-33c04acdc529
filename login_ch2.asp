<!DOCTYPE html>
<html>

<head>
  <title>Login</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf8">
  <link rel="stylesheet" href="./css/style.css">
  <script language=javascript>
    if (top.location != document.location) top.location.href = document.location.href;
  </script>
  <script language="javascript">
    function login() {
      var dplen;
      var dpvalue;
      var dpvalue_v1;
      if ("" == document.tF0.v1.value) {
        alert("请输入用户名!");
        return false;
      }
      if ("" == document.tF0.v2.value) {
        alert("请输入用户密码!");
        return false;
      }
      if (document.tF0.v2.value.indexOf(" ") > -1) {
        alert("密码中含有空格,请重新输入!");
        return false;
      }
      dplen = parseInt(document.tF0.v1.value.length / 2);
      dpvalue = document.tF0.v1.value;
      dpvalue_v1 = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
        + document.tF0.v1.value.length + ":E26bE809" + dpvalue.substring(dplen);
      dplen = parseInt(document.tF0.v2.value.length / 2);
      dpvalue = document.tF0.v2.value;
      document.tF0.v3.value = dpvalue_v1 + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
        + document.tF0.v2.value.length + ":E95ca4DA" + dpvalue.substring(dplen);
      document.tF0.v1.disabled = true;
      document.tF0.v2.disabled = true;
      document.tF0.v1.value = "soPkcS%RZ$810uUn";
      document.tF0.v2.value = "bORvaD#2IhU1HG6m";
      localStorage.clear('lasttimeHandle');
      return true;
    }
    function messageCheck(language_type) {
      switch (language_type) {
        case 1:
          location.href = "login_ch.asp";
          break;
        case 2:
          location.href = "login_en.asp";
          break;
        default:
          break;
      }
      return true;
    }
    function validteCode() {
      var validateCode = "";
      var codes = new Array(4);       // 用于存储随机验证码
      var colors = new Array("Red", "Green", "Gray", "Blue", "Maroon", "Aqua", "Fuchsia", "Lime", "Olive", "Silver");
      for (var i = 0; i < codes.length; i++) {
        //获取随机验证码
        codes[i] = Math.floor(Math.random() * 10);
      }
      //var spans = document.getElementById("divCode").all;
      document.getElementById("divCode1").value = codes[0];
      document.getElementById("divCode2").value = codes[1];
      document.getElementById("divCode3").value = codes[2];
      document.getElementById("divCode4").value = codes[3];
      for (var i = 0; i < 4; i++) {
        validateCode += codes[i];
      }
      //将验证码的值保存到一个隐藏控件
      document.getElementById("validateValue").value = validateCode;
      document.forms[0].v1.focus();
    }
  </script>
</head>

<body class="node-main-login" onload="validteCode()">
  <header>
    <div class="container">
      <span class="showSide"></span>
      <img src="./images/JOYWARE.png" alt="">
      <div class="pull-right">
      </div>
    </div>
  </header>
  <div class="main">
    <div class="main-right">
      <div id="maincontent">
        <div class="container">
          <form name="tF0" method="post" action="/goform/setLoginCfg" class="loginBox">
            <% var errorcode,serverts, alpha;getLoginCfg(); %>
              <% web_get_stat(); %>
                <input type="hidden" name="lastts" value=<% write(serverts); %>>
                <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
                <input type="hidden" name="v3" id="v3" value=<% write(alpha); %>>
                <input type="hidden" name="lang" value="ch">
                <div class="loginBox-header">
                  欢迎登录
                </div>
                <fieldset>
                  <div class="section">
                    <label class="input">
                      <i class="icon-append glyphicon glyphicon-user"></i>
                      <input name="v1" maxlength="16" class="cbi-input-user" type="text" name="luci_username"
                        value="admin">
                    </label>
                  </div>
                  <div class="section">
                    <label class="input">
                      <i class="icon-append glyphicon glyphicon-lock"></i>
                      <input name="v2" maxlength="16" class="cbi-input-password" type="password" name="luci_password">
                    </label>
                  </div>
                </fieldset>
                <div height="22" style="display:none">
                  <div id="divCode" onclick="JavaScript:validteCode()">
                    <input name="divCode1" type="text" id="divCode1" size="1" maxlength="1" disabled />
                    <input name="divCode2" type="text" id="divCode2" size="1" maxlength="1" disabled />
                    <input name="divCode3" type="text" id="divCode3" size="1" maxlength="1" disabled />
                    <input name="divCode4" type="text" id="divCode4" size="1" maxlength="1" disabled />
                  </div>
                </div>
                <div class="loginBox-footer">
                  <input type="submit" value="登录" class="cbi-button cbi-button-apply pull-right"
                    onClick="return login();">
                </div>
          </form>
        </div>
        <footer class="mobile-hide">
          <a href="http://www.joyware.com">Powered by www.joyware.com</a> /
          <a href="http://www.joyware.com">中威电子</a>
        </footer>
      </div>
    </div>
    <script type="text/javascript">
      var input = document.getElementsByName('luci_password')[0];
      if (input)
        input.focus();
    </script>
  </div>

  <script>
    <% if (errorcode == "1") { write_errorcode("Bad username or password, please login again!"); } %>
    <% if (errorcode == "2") { write_errorcode("Failed to change password!');"); } %>
    <% if (errorcode == "3") { write_errorcode("The number of users has reached the maximum!"); } %>
    <% if (errorcode == "4") { write_errorcode("This username has been locked for 5 minutes, please try again later!"); } %>
    <% if (errorcode == "5") { write_errorcode("rejected check error"); } %>
  </script>
</body>

</html>