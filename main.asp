<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<title>端口管理</title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script language="JavaScript">

function P(portId, admin,link,nego,cspeed,rspeed,cfl,rfl,learn,tport, plbd, plbdc)
{

    var narr=6;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	
    portId=portId;
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	            
    eport=portId;

    opt = document.createElement("OPTION");
    opt.text = eport;    
    opt.value = portId;

    // document.getElementById("PortNum").options.add(opt);   	
    
    tbtr.cells[0].abbr = 0;
    tbtr.cells[1].abbr = tport;
	
    tbtr.cells[0].innerHTML = eport;
    tbtr.cells[1].innerHTML = admin;
    tbtr.cells[2].innerHTML = link;
    tbtr.cells[3].innerHTML = nego;
    tbtr.cells[4].innerHTML = cspeed;
    tbtr.cells[5].innerHTML = rspeed;
}

</SCRIPT>

</head>

<body  onload=""><br>
<script>
checktop(<% write(lang); %>);
</script>
<form name="port_setting" method="POST" action="/goform/fcformTest">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
            <tr>
              <td colspan="2" align="left" class="cword09">端口设置 <b><font color="#FF7F00">&gt;&gt;</font></b> 端口管理</td>
            </tr>
        </table></td>
      </tr>
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
            <tr height="25">
              <td colspan="2" align="left">&nbsp;端口管理设置</th>
            </tr>
            <tr height="25">
              <td align="left" class="crons">&nbsp;端口范围</td>
              <td align="left" class="crons"><input type="text" name="port_range" class="input_board3"/>
                 </td>
            </tr>			
            <tr height="25">
              <td width="20%" align="left" class="crons">&nbsp;端口启用</td>
              <td align="left" class="crons">&nbsp;
                  <select name="port_enable" class="select1">
                    <option value="1" selected>启用</option>
                    <option value="2">禁用</option>
                  </select>              </td>
            </tr>
            <tr height="25">
              <td width="20%" align="left" class="crons">&nbsp;端口速率</td>
              <td align="left" class="crons">&nbsp;
                  <select name="port_speed" class="select1">
                    <option value="0" selected>自动协商</option>
                    <!--   <option value="1">1000M/全双工</option>  -->
                    <!--   <option value="2">1000M/半双工</option>  -->
                    <option value="3">100M/全双工</option>
                    <option value="4">100M/半双工</option>
                    <option value="5">10M/全双工</option>
                    <option value="6">10M/半双工</option>
                  </select>              </td>
            </tr>
            <tr height="25">
              <td width="20%" align="left" class="crons">&nbsp;流量控制</td>
              <td align="left" class="crons">&nbsp;
                  <select name="port_flow_s" class="select1">
                    <option value="1" selected>启用</option>
                    <option value="2">禁用</option>
                  </select>              </td>
            </tr>

			<tr><td colspan="2" align="middle" class="crons"> <input align="middle" name="modify"  type="submit" class="button" value="修 改"> </td></tr>
			
        </table></td>
      </tr>
      <tr>
        <td height="8"></td>
      </tr>
      <tr>
        <td><table id="table_port"  border="0" cellspacing="0" cellpadding="0" width="100%"  class="table2">
            <tr height="25" align="center" class="crons">
              <th align="center" class="td2" width="10%" >端口</th>
              <th align="center" class="td2" width="14%" >端口启用</th>
              <th align="center" class="td2" width="21%" >当前状态</th>
              <th align="center" class="td2" width="21%">端口自适应</th>
              <th align="center" class="td2" width="12%">端口速率</th>
              <th align="center" class="td2" width="12%">端口速率</th>
            </tr>
	
            <script>
				  <% fcTest("firstmile", "zzzzzzzzzzzzzzzzzzzzzz"); %>

                   //P(1,"Enabled","Up","Force","100M Full","100M Full","Off", "Off","Enabled");
                   //P(2,"Enabled","Up","Force","100M Full","100M Full","Off", "Off","Enabled");
                   //P(5,"Enabled","Down","Force","100M Full","-","Off", "-","Enabled");
                   //P(24,"Enabled","Down","Force","100M Full","-","Off", "-","Enabled");
	
                  </script>
        </table></td>
      </tr>
      <tr>
        <td align="center" height="35"><input name="Refresh" type="button" value="刷 新"  class="button" onClick="location.href='port.asp'"/>
          &nbsp;
          <input name="Submit" type="submit" class="button" value="保 存" onClick="checkData();">
          &nbsp;
          <input name="Help" type="button" class="button" id="Help" value="帮 助" /></td>
      </tr>
    </table></td>
  </tr> 
</table>
</td></tr>

</table>

<input type="hidden" name="todo" value="save">
<INPUT type="hidden" name="this_file" value="port.asp">
<INPUT type="hidden" name="next_file" value="port.asp">
<input type="hidden" name="message" value="@msg_text#">
</form>     
</body>

</html>

