<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_rtl.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"文件管理");</script></title>

<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 

<script language="JavaScript" type="text/JavaScript">

function X(ProName,VerInfo,SnmpOid,BuatRate,ManuInfo,SupRFC,CopyInfo,MACAddr,RunTime,CPURatio,MemRatio)
{
	
	var i,j;
	var buf = new Array(putmsg(<% write(lang); %>,'运行时间'),putmsg(<% write(lang); %>,'CPU利用率'),putmsg(<% write(lang); %>,'内存利用率'));
	var innr = buf.length;
	var info = new Array(RunTime,CPURatio,MemRatio);
		
	var tbtr;
	var tbtd;
	for(i=0;i<innr;i++)
	{
		tbtr = document.getElementById("sys_info").insertRow(-1);

    	tbtr.classname = "crons";
		tbtr.height = "30";

    	tbtr.setAttribute("height", "30");
    	tbtr.setAttribute("class", "crons");
    	tbtr.setAttribute("className", "crons");
			for(j=0;j<2;j++)
			{
				tbtd = document.createElement("td");
        		tbtd.align = "left";
				if(j==0)
					tbtd.setAttribute("width","20%");
				else
					tbtd.setAttribute("width","80%");
	    		tbtd.setAttribute("class", "crons");	
				tbtd.setAttribute("className", "crons");	
        		tbtr.appendChild(tbtd);
			}
		if(i==0){
			tbtr.cells[0].innerHTML = "&nbsp;"+buf[i];
			tbtr.cells[1].innerHTML = "&nbsp;"+info[i];
		}
		else
		{
			tbtr.cells[0].innerHTML = "&nbsp;"+buf[i];
			tbtr.cells[1].innerHTML = "&nbsp;"+info[i]+"%";
		}
	}
	
}

function refreshpage()
{
  location.href='runinfo.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('runinfo',<% write(lang); %>);
}

</script>
</head>

<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="rstpsetting" method="POST" action="setup.cgi">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="left_menu_id" value="@left_menu_id#">

    <table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
        <tr><td>
            <table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
            <tr>
				<td valign="top" >

				    <!-- 具体内容 begin-->
				    <table width="90%"   border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
				        <tr><td>
				        
				        <tr><td>
				        	<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
					         <tr>
					           <td colspan="2" align="left" class="cword09">&nbsp;<script>writemsg(<% write(lang); %>,"系统信息");</script><b><font color="#FF7F00">&gt;&gt;</font></b>&nbsp;<script>writemsg(<% write(lang); %>,"运行信息");</script></td>
					         </tr>
					        </table>
					       </td></tr>
					       
							    <tr>
								   	<td> 

								   	
							   	   <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
								     <tr height="25">
								       <th align="left" colspan="2">&nbsp;<script>writemsg(<% write(lang); %>,"运行信息");</script></td>
								     </tr>

 						             <script>
 						             	<%  var errorcode; sysInfo(); %>
									</script>
								     </table>
							   </td></tr>
							 
				        </td></tr>
				      <tr>
       				 	<td align="center" height="35"><script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>

</td>
     				 </tr>
				    </table>
					<!-- 具体内容 end -->

				</td>
            </tr>
        </td></tr>
        
    </table>
</form> 
<script>
changebgcolor();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</html>

