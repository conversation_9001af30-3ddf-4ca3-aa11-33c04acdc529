<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"SNMP管理");</script></title>

<script language="JavaScript" type="text/JavaScript">
<% var Allipaddr,errorcode; %>

function writebutton2(priv,lang,key,bclass,btype,bname,benable,bfunc)
{
	var outputstr ="";
	if((priv == 1)||(priv == 5))
	  outputstr="<input class="+bclass+" id='"+bname+"' "+benable+" name='"+bname+"' type='"+btype+"' value='"+putmsg(lang,key)+"' onClick='return "+bfunc+";'>";
	else
		outputstr="&nbsp;";
	document.write(outputstr);

}

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}


//[a-z A-Z 0-9 ] 字线和数字
function check_ingress_str_format(str) 
{
    var reg1 = /^([a-zA-Z]|\d)+$/; 
   
    if(!reg1.exec(str))
        return false;

    return true;
}


function isValidString(str)
{
	var validc = "/'%`\"\\><";
	var a,c;
	for(i = 0 ; i < validc.length; i ++)
	{
		c = validc.substring(i, i + 1);
		if(str.indexOf(c) > -1)
		{
			return true;
		}
	}
	return false;
}

/* by zjx  11-6-8 */
function IpCheck(ip_addr)
{
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])$/;
	if(myRE.test(ip_addr))
	{
        return true;
	}
	else
	{
		return false;
	}
}

function messageCheck(v)
{
    var hid=document.webForm;
    if(v==1)
    {
    	var vie = hid.snmp_view.value;
    	if(check_ingress_str_format(hid.community.value)!=true)
    	{
			alert(putmsg(<% write(lang); %>,"团体名只能为字母和数字!"));
			return false;
    	}
    	if(hid.community.value.length>32)
    	{
			alert(putmsg(<% write(lang); %>,"团体名长度不能大于32!"));
			return false;
		}
	 	else if(hid.community.value.length==0)
	    {
	    	alert(putmsg(<% write(lang); %>,"团体名不能为空!"));
	    	return false;
	    }
	    hid.flag.value=1;
		hid.submit();
		return true;
    }
    else if(v==2)
    {
    	if(hid.community.value.length==0)
	    {
	    	alert(putmsg(<% write(lang); %>,"团体名不能为空!"));
	    	return false;
	    }
	    hid.flag.value=2;
		hid.submit();
		return true;
    }
    else if(v==3)
    {
		if(hid.admininfo.value.length>=128)
		{
			alert(putmsg(<% write(lang); %>,"管理员标识长度小于128!"));
			return false;
		}
		else if(isValidString(hid.admininfo.value))
		{
			alert(putmsg(<% write(lang); %>,"输入中发现非法字符!"));
			return false;
		}
		
    	hid.flag.value=3;
    	hid.submit();
		return true;
    }
    else if(v==4)
    {
    	hid.flag.value=4;
    	hid.submit();
		return true;
    }
    else if(v==5)
    {
    	if(hid.trapsIp.value.length==0||hid.username.value.length==0)
				{
					alert(putmsg(<% write(lang); %>,"主机地址和团体名不能为空!"));
					return false;
				}
		else  if(check_ingress_str_format(hid.username.value)!=true)
		     	{
		 			alert(putmsg(<% write(lang); %>,"团体名只能为字母和数字!"));
		 			return false;
		     	}
		if(eval(document.webForm.trapval.value)!=1)
				{
					alert(putmsg(<% write(lang); %>,"此配置只有在traps功能打开的情况下有效!"));
					return false;
				}
		if(IpCheck(hid.trapsIp.value)!=true)
				{
					alert(putmsg(<% write(lang); %>,"IP地址的格式不合法!"));
					return false;
				}
				
		if(hid.username.value.length>32)
		{
			alert(putmsg(<% write(lang); %>,"团体名长度不能大于32!"));
			return false;
		}
				
	    hid.flag.value=5;
			hid.submit();
			return true;
    }
    else if(v==6)
    {
    	if(hid.trapsIp.value.length==0)
		{
			alert(putmsg(<% write(lang); %>,"主机地址不能为空!"));
			return false;
		}
		if(eval(document.webForm.trapval.value)!=1)
		{
			alert(putmsg(<% write(lang); %>,"此配置只有在traps功能打开的情况下有效!"));
			return false;
		}
	    hid.flag.value=6;
		hid.submit();
		return true;
    }
    else if(v==7)
    {
    	if(hid.devLocation.value.length==0)
		{
			alert(putmsg(<% write(lang); %>,"设备位置不能为空!"));
			return false;
		}
		else if(hid.devLocation.value.length > 256)
		{
			alert(putmsg(<% write(lang); %>,"设备位置长度不能大于256!"));
			return false;
		}
		else if(isValidString(hid.devLocation.value))
		{
			alert(putmsg(<% write(lang); %>,"输入中发现非法字符!"));
			return false;
		}
	    hid.flag.value=7;
		hid.submit();
		return true;
    }
    else if(v==8)
    {
    	if(hid.devLocation.value.length==0)
		{
			alert(putmsg(<% write(lang); %>,"设备位置不能为空!"));
			return false;
		}
		else if(hid.devLocation.value.length>256)
		{
			alert(putmsg(<% write(lang); %>,"设备位置长度不能大于256!"));
			return false;
		}
	    hid.flag.value=8;
		hid.submit();
		return true;
    }

    return ;
   
}
function DelSnmpCom(value)
{
    var hid=document.webForm;
    	hid.comname.value=value;
		hid.flag.value=2;
		hid.submit();
		return true;
}
function DelSnmpTrap(v1,v2)
{
    var hid=document.webForm;
		hid.flag.value=6;
		hid.trapiphid.value=v1;
		hid.trapnamehid.value=v2;
		hid.submit();
		return true;
}
function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm?name=snmp";
	tf.submit();
}
function refreshpage()
{
  location.href='snmp.asp?ltime='+<% write(lltime); %>;
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function showHelpinfo()
{
   showHelp('snmp',<% write(lang); %>);
}

<%  var authmode; checkCurMode(); %>

function display()
{
	if(document.getElementsByName("isauthA"))
	{
		if(<% write(authmode); %> != 1)
		{
			allitem = document.getElementsByName("isauthA");
			for(i=0;i<allitem.length;i++)
				allitem[i].style.display = "none";		
		}
			
	}

}

</script>
</head>
<body  onload="display()"><br>

<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setSnmpCfgComm">
<input type="hidden" name="flag">
<input type="hidden" name="comname">
<input type="hidden" name="trapiphid">
<input type="hidden" name="trapnamehid">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>


	        

 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>团体名</b></font></td></tr>
 </table>
 

<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">

    <tr height="25">
      <td width="15%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"团体名");</script>:</td>
      <td width="25%" align="left" class="crons">&nbsp;
          <input type="text" name="community" class="input_board3" value="">	               </td> 
      <td width="15%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"权 限");</script>:</td>
         <td width="23%" align="left" class="crons">&nbsp;
          <input type="radio" name="rw" value="rw" checked>rw
          <input type="radio" name="rw" value="ro" >ro	                 </td>
    </tr>
    <tr height="25" >
      <td align="left" class="crons">&nbsp;
          <script>writemsg(<% write(lang); %>,"视图名");</script>
        :</td>
      <td colspan="4" align="left" class="crons">&nbsp;
          <input type="text" name="snmp_view" readonly="readonly" class="input_board3" value="all">                  </td>
  </tr>
    <tr height="25">
      <td  colspan="4" align="center" class="crons">&nbsp;
      <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","modify1","messageCheck(1)");</script>	              </td>
  </tr> 
</table>

<br>
 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#ABD8EF" height="20px" ><font size="2" color="#FFFFFF"><b>团体名列表</b></font></td></tr>
 </table>


<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table2" class="tablebord">	        
								<% showSnmpComTable(); %>
</table>

<script>
changebgcolor();
changebgcolor2();

<% if (errorcode!="") { write_errorcode(errorcode); } %>

</script>
<br><br>
</form>
 

</body>
</html>
