<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"SNTP管理");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script  language="JavaScript">

function IpCheck(ip_addr)
{
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])$/;
	if(myRE.test(ip_addr))
	{
        return true;
	}
	else
	{
		return false;
	}
}
function TimeCheck(val)
{
	var str=/^((((19[7-9][1-9])|(1980)|(1990)|(1970)|(([2-9]\d)\d{2}))-(0[13578]|1[02])-(0[1-9]|[12]\d|3[01]))|(((19[7-9][1-9])|(1980)|(1990)|(1970)|(([2-9]\d)\d{2}))-(0[13456789]|1[012])-(0[1-9]|[12]\d|30))|(((19[7-9][1-9])|(1980)|(1990)|(1970)|(([2-9]\d)\d{2}))-02-(0[1-9]|1\d|2[0-8]))|(((19|[2-9]\d)(0[48]|[2468][048]|[13579][26])|((16|[2468][048]|[3579][26])00))-02-29-)) ((20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d) ?$/;
	if(str.test(val))
	{
		return true;
	}
	else
	{
		return false;
	}
}

function TimeZoneCheck(val)
{
	var str=/^(\+|\-)(([0-1]\d)|(2[0-3])|(\d)):([0-5]\d)$/;	
	if(str.test(val))
	{
		return true;
	}
	else
	{
		return false;
	}	
}

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables txtCent" ;
		}

     }

  }
}

function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			
			tab.rows[i].cells[j].className = "all_tables txtCent" ;
		}

     }

  }
}
function messageCheck(v)
{
    var hid=document.webForm;

    if(v==1)
    {
    	/*check time format*/
    	if(TimeCheck(hid.systime.value)==false)
    	{
    		alert(putmsg(<% write(lang); %>,"时间的格式不合法,应该为:yyyy-mm-dd hh:mm:ss!"));
    		return false;
    	}
    	hid.flag.value=1;
    	hid.submit();
    }
    else if(v==2)
    {
    	/*check timezone format*/
    	if(hid.timezone.value.length!=0)
    	{
	    	if(TimeZoneCheck(hid.timezone.value)==false)
	    	{
	    		alert(putmsg(<% write(lang); %>,"时区的格式不合法,格式如:+08:00!"));
	    		return false;
	    	}
    	}
    	hid.flag.value=2;
    	hid.submit();
    }
    else if(v==3)
    {
    	if(hid.sntppri.value.length!=0)
    	{
	    	if(IpCheck(hid.sntppri.value)!=true)
	    	{
	    		alert(putmsg(<% write(lang); %>,"IP地址格式错误!"));
	    	}
	    	else
	    	{
		    	hid.flag.value=3;
		    	hid.submit();
	    	}
    	}
    	else
    	{
    		hid.flag.value=3;
		    hid.submit();
    	}
    }
    else if(v==4)
    {
    	if(hid.sntpsec.value.length!=0)
    	{
	    	if(IpCheck(hid.sntpsec.value)!=true)
	    	{
	    		alert(putmsg(<% write(lang); %>,"IP地址格式错误!"));
	    	}
	    	else
	    	{
		    	hid.flag.value=4;
		    	hid.submit();
	    	}
    	}
    	else
    	{
    		hid.flag.value=4;
		    hid.submit();
    	}
    }
    else if (5 == v)
    {
		if(hid.sntpinterval.value >99999 || hid.sntpinterval.value < 30 && hid.sntpinterval.value != "")
		{
	    	alert(putmsg(<% write(lang); %>,"轮询间隔设置范围(30~99999s)"));
	    }
		else
		{
		    hid.flag.value = 5;
		    hid.submit();
		}
    }
    else if (6 == v)
    {
	    hid.flag.value = 6;
	    hid.submit();
    }
    else if (7 == v)
    {
	    hid.flag.value = 7;
	    hid.submit();
    }
	else if (8 == v)
    {
	    hid.flag.value = 9;
	    hid.submit();
    }	
    else if (11 == v)
    {
		if(hid.sntptport.value >99999 || hid.sntptport.value < 60 && hid.sntptport.value != "")
		{
	    	alert(putmsg(<% write(lang); %>,"中断间隔设置范围(60~99999s)"));
	    }
		else
		{
		    hid.flag.value = 11;
		    hid.submit();
		}
    }
	else if (12 == v)
    {
	    hid.flag.value = 12;
	    hid.submit();
    }	

	return true;
}
function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm?name=sntp";
	tf.submit();
}
function refreshpage()
{
  location.href='sntp.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('sntp',<% write(lang); %>);
}

function check_stnp_state()
{
	    var hid=document.webForm;
		if (hid.hsntpeanble.value == "enable")
		{
			document.getElementById("sntp_enable").value = 1;
			//document.getElementById("modify1").disabled = false;
			document.getElementById("modify2").disabled = false;
			document.getElementById("modify3").disabled = false;
			document.getElementById("modify4").disabled = false;
			document.getElementById("modify8").disabled = false;
			document.getElementById("modify11").disabled = false;
			document.getElementById("modify12").disabled = false;
		}
		else
		{
			document.getElementById("sntp_enable").value = 2;
			//document.getElementById("modify1").disabled = true;
			document.getElementById("modify2").disabled = true;
			document.getElementById("modify3").disabled = true;
			document.getElementById("modify4").disabled = true;
			document.getElementById("modify8").disabled = true;
			document.getElementById("modify11").disabled = true;
			document.getElementById("modify12").disabled = true;
		}
		if (hid.telgramselect.value == "unicast")
			document.getElementById("sntp_telgramselect").value = 1;
		else
			document.getElementById("sntp_telgramselect").value = 2;

		if (hid.hntpeanble.value == "enable")
			document.getElementById("ntp_enable").value = 1;
		else
			document.getElementById("ntp_enable").value = 2;
}


</script>
</head>
<body  onload="display()"><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
function display()
{
	if(document.getElementById("isauthA"))
	{
		if(<% write(authmode); %> != 1)
			document.getElementById("isauthA").style.display = "none";
		
	}

}checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setSysTime">
<input type="hidden" name="flag">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<% var errorcode,hntpeanble,hsntpeanble,systime,timezone,sntppri,sntpsec,sntpinterval, sntptport, telgramselect;getSysTime(); %>
<input type="hidden" name="hsntpeanble" value=<% write(hsntpeanble); %>>
<input type="hidden" name="telgramselect" value=<% write(telgramselect); %>>
<input type="hidden" name="hntpeanble" value=<% write(hntpeanble); %>>
<table  id="table111"   width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
	<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
		<tr><td class="tit" height="30px" ><font color="#0069d6"><div class="bot">SNTP及系统时间</div></font></td></tr>
	 </table>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" class="formContain">
  <tr>
    <td valign="top" >
		<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td>

        </td>
      </tr>

      	<tr>
        <td>
 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="20px" ><font size="5" color="#404040"><div class="bot">SNTP</div></font></td></tr>
 </table>
        </td>
      </tr>
	  
      <tr>
      
        <td>
			<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table1" class="tablebord">

            <tr height="25">
				<td width="20%" align="center" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"sntp启用");</script></td>
				<td  align="left" class="crons"> 
					<select id="sntp_enable" name="sntp_enable" class="select1">
						<option value="1" selected><script>writemsg("ch","开启");</script></option>
						<option value="2"><script>writemsg("ch","关闭");</script></option>
					</select>
				</td>
				<td  align="middle" class="crons"> 
					<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify5","messageCheck(6)");</script>
				</td>
            </tr>


			<% showSystime(); %>
            <tr height="25">
              <td width="20%" align="center" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"时区设置");</script></td>
              <td align="left" class="crons">
				<!--
		              <input type="text" name="timezone" class="input_board3" readonly="readonly" value=<% write(timezone); %>></td>
             	 	-->
             	 	&nbsp;+08:00
               <td  align="middle" class="crons"> 
               &nbsp;
               <!--
               	<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify1","messageCheck(2)");</script>
				-->
               </td>
            </tr>
            <tr height="25">
              <td width="20%" align="center" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"sntp server");</script></td>
              <td align="left" class="crons"><input type="text" name="sntppri" class="input_board3" value=<% write(sntppri); %>>
                 </td>
               <td  align="middle" class="crons"> 
               <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify2","messageCheck(3)");</script>
               </td>  
            </tr>
            <tr height="25" style="display:none">
              <td width="20%" align="center" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"sntp second server");</script></td>
              <td align="left" class="crons"><input type="text" name="sntpsec" class="input_board3" value=<% write(sntpsec); %>>
                 </td>
                <td  align="middle" class="crons"> 
                  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify3","messageCheck(4)");</script>
                </td>
            </tr>
            
            <tr height="25">
              <td width="20%" align="center" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"轮询间隔");</script></td>
              <td align="left" class="crons"><input type="text" name="sntpinterval" class="input_board3" value=<% write(sntpinterval); %>> &nbsp;(30~99999s)
                 </td>
                <td  align="middle" class="crons"> 
                  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify4","messageCheck(5)");</script>
                </td>
            </tr>         
            
            <tr height="25">
				<td width="20%" align="center" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"对时报文选择");</script></td>
				<td  align="left" class="crons"> 
					<select id="sntp_telgramselect" name="sntp_telgramselect" class="select1">
						<option value="1" selected><script>writemsg("ch","Unicast");</script></option>
						<option value="2"><script>writemsg("ch","Broadcast");</script></option>
					</select>
				</td>
				<td  align="middle" class="crons"> 
					<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify8","messageCheck(8)");</script>
				</td>
            </tr>    

          
            <tr height="25">
				<td width="20%" align="center" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"中断间隔");</script></td>
				<td align="left" class="crons">
              		<input type="text" name="sntptport" class="input_board3" value=<% write(sntptport); %>> &nbsp;(60~99999s)
				</td>
                <td  align="middle" class="crons"> 
                  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify11","messageCheck(11)");</script>
                </td>
            </tr>    
           
            <tr height="25">
				<td width="20%" align="center" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"sntp复位");</script></td>
				<td align="left" class="crons">
              		&nbsp;(重新对时)
				</td>
                <td  align="middle" class="crons"> 
                  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"复  位","buttons_apply","button","modify12","messageCheck(12)");</script>
                </td>
            </tr>    

           
            <tr  style='display:none'>
		    <td colspan="3" align="middle" ><script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
		      &nbsp;
		      &nbsp;
		      </td>
			</tr>
        </table>
	</td>
      </tr>



      	<tr>
        <td>
			
	<br />
	
 <table  width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="20px" ><font size="5" color="#404040"><div class="bot">NTP</div></font></td></tr>
 </table>
        </td>
      </tr>

      <tr>
      
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table2" class="tablebord">

            <tr height="25">
				<td width="20%" align="center" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"ntp启用");</script></td>
				<td  align="center" class="crons"> 
					<select id="ntp_enable" name="ntp_enable" class="select1">
						<option value="1" selected><script>writemsg("ch","开启");</script></option>
						<option value="2"><script>writemsg("ch","关闭");</script></option>
					</select>
				</td>
				<td  align="middle" class="crons"> 
					<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify5","messageCheck(7)");</script>
				</td>
            </tr>
        </table>
        </td>
      </tr>

    </table>
</td>

  </tr> 
  
</table>
</td></tr>
<tr><td>
</td></tr></table>

</form>

<script>
changebgcolor();
changebgcolor2();
check_stnp_state();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>

 
</html>

