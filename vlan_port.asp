<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"vlan端口配置");</script></title>

<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript">

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true;  
			 addToPortRange(objs[i]);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRange(objs[i]);
			}
             
        }
    } 
    
}


function checkavid(arguments)
{
	var arr = new Array();
	var arry = new Array();
	arry = arguments.split("");
	arr = arguments.split(",");
	var i;
	for(i=0;i<arry.length;i++)
	{
		if(arry[i]!="," && isNaN(arry[i]))
		{
			return false;	
		}
	}
	
	for(i=0;i<arr.length;i++)
	{
	    if(isNaN(arr[i]) || (arr[i]<1 || arr[i] > 4094))
		{	
			return false;
		}
	}	
	return true;
}

function checkavidhybrid(arguments)
{
	var arr = new Array();
	var arry = new Array();
	arry = arguments.split("");
	arr = arguments.split(",");
	var i;
	for(i=0;i<arry.length;i++)
	{
		if(arry[i]!="," && isNaN(arry[i]))
		{
			return false;	
		}
	}
	
	for(i=0;i<arr.length;i++)
	{
	    if(isNaN(arr[i]) || (arr[i]<1 || arr[i] > 4094))
		{	
			return false;
		}
	}	
	return true;
}


function settype()
{
 	var type=document.getElementById("porttype").value;		
	var avid=document.getElementById("avid");					
	var dvid=document.getElementById("dvid");
	var add = document.getElementById("add");
	var egress_tagged1 = document.getElementById("egress_tagged1");
	var egress_tagged2 = document.getElementById("egress_tagged2");
	var addavid=document.getElementById("addavid");
	var deleteavid = document.getElementById("deleteavid");
	var adddvid = document.getElementById("adddvid");
	//var deletedvid = document.getElementById("deletedvid");
	
	var tagged = document.getElementById("tagged");
	
	
		if(type == 2)
		{
			 tagged.style.display="";
		}
		else
		{
				 tagged.style.display="none";
		
		}
		
		
    if(type == 0)
	{
			 avid.disabled=true;
			 dvid.disabled=false;
			 egress_tagged1.disabled=true;
			 egress_tagged2.disabled=true;
			 addavid.disabled=true;
			 deleteavid.disabled=true;
			 adddvid.disabled=false;
			 //deletedvid.disabled=false;
			 //tagged.style.display="none";
	}
	else if (type == 1)
	{
			 avid.disabled=false;
			 //dvid.disabled=true;
			 egress_tagged1.disabled=true;
			 egress_tagged2.disabled=true;
			 addavid.disabled=false;
			 deleteavid.disabled=false;
			 adddvid.disabled=false;
			 //deletedvid.disabled=false;
			 //tagged.style.display="none";
	}
	else if (type == 2)
	{
			 avid.disabled=false;
			 dvid.disabled=false;
			 egress_tagged1.disabled=false;
			 egress_tagged2.disabled=false;
			 addavid.disabled=false;
			 deleteavid.disabled=false;
			 adddvid.disabled=false;
			 //deletedvid.disabled=false;
			 //tagged.style.display="";
	}
}

function check(type_v)
{
	var vid=document.getElementById("dvid").value;
	var avid=document.getElementById("avid").value;	
	var type_value = document.getElementById("type_value").value;
	var tf=document.vlan_port;
	var i,t=0,a=0,h=0;
	var target = document.getElementById("port_range").value;
	var checkbox_index = document.getElementsByName("checkbox_index");
	var table_port_vlan = document.getElementById("table_port_vlan");
	
	var egress_tagged = document.getElementById("egress_tagged");
	
	

	document.getElementById("flag").value=type_v;
	
	if(target=="")
	{
		alert(putmsg(<% write(lang); %>,"请选择端口!"));
		return 0;
	}
	for(i=1;i<table_port_vlan.rows.length;i++)
	{
	  if(checkbox_index[i-1].checked)
	  {
	    if(table_port_vlan.rows[i].cells[2].innerHTML=="trunk")
	    {  t++;  }
	    if(table_port_vlan.rows[i].cells[2].innerHTML=="access")
	    {  a++;  }
		if(table_port_vlan.rows[i].cells[2].innerHTML=="hybrid")
	    {  h++;  }
	  }	
	}

		if(type_v==2)
		{

			if(isNaN(vid)||(vid>4094) || (vid<1))
			{
				alert(putmsg("ch","Default VLAN ID的范围必须在1-4094之间 且必须为数字"));
				return 0;
			}

		}

	if((a==0 && t==0) || (h==0&&t==0)||(h==0&&a==0)){
	if(type_value == 0)
	{

   	    tf.submit();
	}
	
	else if(type_value == 1)
	{
  		//if(checkavid(avid))
   		{
   		 	tf.submit();
   		}
		/*
   		else
   		{
   		 	alert(putmsg(<% write(lang); %>,"allow VLAN ID只能输入 , 与 数字 , 数字范围在1-4094, 格式: X,X,X,X "));
			return 0;
		}
		*/
	}
	else if(type_value == 2)
	{
	/*
	    if((vid>4094) || (vid<1))
		{
	   		alert(putmsg(<% write(lang); %>,"Default VLAN ID的范围必须在1-4094之间 且必须为数字"));
			return 0;
		}
	*/	
		if(type_v==1)
		{
			if(document.getElementById("c1").checked)
				egress_tagged.value = "enable";
			else
				egress_tagged.value = "disable";	
						
  		  if(checkavidhybrid(avid))
   		  {
   	    	 	tf.submit();
   		  }
   		  else
   		  {
   		  	alert(putmsg(<% write(lang); %>,"allow VLAN ID格式必须为: X,X,X,X . "));
   		 	//alert(putmsg(<% write(lang); %>,"allow VLAN ID只能输入 , 与 数字 , 数字范围在1-4094, 格式: X,X,X,X. "));
			return 0;
   		  }
	    }
		else
		{
		    tf.submit();
		}
	}
	else
	{
		alert(putmsg(<% write(lang); %>,"链路类型不一样，不能添加"));
		return 0;
	}
 }
}

function checkdel(type_v)
{
	var tf = document.vlan_port;
	var i,j=1,k=1,z;
	var vid=document.getElementById("dvid").value;
	var avid=document.getElementById("avid").value;
	var type_value = document.getElementById("type_value").value;
	var target = document.getElementById("port_range").value;
	var arr = new Array();
	var arr1 = new Array();
	var t=0,a=0,h=0;
	var checkbox_index = document.getElementsByName("checkbox_index");
	var table_port_vlan = document.getElementById("table_port_vlan");
	
	document.getElementById("flag").value=type_v;

	if(target=="")
	{
		alert(putmsg(<% write(lang); %>,"请选择端口!"))
		return 0;
	}
	
	for(i=1;i<table_port_vlan.rows.length;i++)
	{
	  if(checkbox_index[i-1].checked)
	  {
	    if(table_port_vlan.rows[i].cells[2].innerHTML=="trunk")
	    {  t++;  }
	    if(table_port_vlan.rows[i].cells[2].innerHTML=="access")
	    {  a++;  }
		if(table_port_vlan.rows[i].cells[2].innerHTML=="hybrid")
	    {  h++;  }
	  }	
	}
/*表示只有一种链路类型*/
	if((a==0 && t==0) || (h==0&&t==0)||(h==0&&a==0)){
	if(type_value == 0)
	{
		
		/*if(isNaN(vid))
		{
			alert(putmsg(<% write(lang); %>,"default vlan id 必须为数字"));
		}else
		*/
		{
			tf.action = "/goform/delVlanPort";
			tf.submit();
		}
	}

	else if(type_value == 1)
	{
		//if(checkavid(avid))
		{
				tf.action = "/goform/delVlanPort";
				tf.submit();
		}
		/*
		else
		{
			alert(putmsg(<% write(lang); %>,"你输入的格式或范围不正确 "));
			return 0;
		}
		*/
	}
	else if(type_value == 2)
	{
	
		
			if(type_v==1)
			{
				if(checkavidhybrid(avid))
				{
					tf.action = "/goform/delVlanPort";
					tf.submit();
				}
				else
				{
					alert(putmsg(<% write(lang); %>,"allow VLAN ID只能输入 , 与 数字 , 数字范围在1-4094, 格式: X,X,X,X， "));
					return 0;
   				}
			}
			else
			{
				tf.action = "/goform/delVlanPort";
				tf.submit();
			}
		}
	}
	else
		{
		alert(putmsg(<% write(lang); %>,"你要删除的链路类型不一样，不能删除!"));
		return 0;
	}

}  

function checkup()
{
	var tf = document.vlan_port;
	tf.action = "/goform/selVlanPort";
	tf.submit();
}

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var target = document.getElementById("port_range");
	var avid = document.getElementById("avid");
	var dvid = document.getElementById("dvid");
	var porttype = document.getElementById("porttype");
	var filter = document.getElementById("filter");
	var type_value = document.getElementById("type_value");
	var filter_value = document.getElementById("filter_value");
	var vlan_port_id = document.getElementById("vlan_port_id");
	var p = obj.value;

	if(obj.checked){
		target.value =target.value + p + " ";
		vlan_port_id.value =vlan_port_id.value + p + " , ";
		
		avid.value=(trobj.cells[6].innerHTML=="-"?"":trobj.cells[6].innerHTML);
		dvid.value=trobj.cells[5].innerHTML;
		filter_value.value = trobj.cells[3].innerHTML ;
		if(trobj.cells[2].innerHTML== "access")
			type_value.value=0;
		else if(trobj.cells[2].innerHTML== "trunk")
			type_value.value=1;
		else if(trobj.cells[2].innerHTML== "hybrid")
			type_value.value=2;
	/*	type_value.value = (trobj.cells[2].innerHTML== "access" ? 0 : 1);*/
		if("access"== trobj.cells[2].innerHTML)
		{
			porttype.options[0].selected=true;
			porttype.options[1].selected=false;
			porttype.options[2].selected=false;

		}else if("trunk" == trobj.cells[2].innerHTML)
		{
					porttype.options[0].selected=false;
					porttype.options[1].selected=true;
			porttype.options[2].selected=false;
		}else if("hybrid" == trobj.cells[2].innerHTML)
				{
					porttype.options[0].selected=false;
					porttype.options[1].selected=false;
					porttype.options[2].selected=true;
				}

		if(filter.options[0].value == trobj.cells[3].innerHTML)
		{
			filter.options[0].selected=true;
			filter.options[1].selected=false;
		}
		else
		{
			filter.options[0].selected=false;
			filter.options[1].selected=true;
		}
		settype();
	}
	else
	{
		target.value = target.value.replace(p + " ","");
		vlan_port_id.value = vlan_port_id.value.replace(p + " , ","");
	}

}

function P(portId,name,enable,instance,interface,portbmp)
{
    var narr=7;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+portId);	

    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	

/*		
		if(i==3||i==4)
		{
			tbtd.setAttribute("style","display:none");
		}
*/
        tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
    tbtr.cells[1].abbr = portId;
	/*
	if((portId=="sa1")||(portId=="sa2")||(portId=="sa3")||(portId=="sa4")||(portId=="sa5")||(portId=="sa6")||(portId=="sa7")||(portId=="sa8"))
	{
		tbtr.cells[0].innerHTML = "&nbsp;";
	}
	else*/	
	{
		tbtr.cells[0].innerHTML = "<input type=\"checkbox\" id=\"input_"+portId+"\" name=\"checkbox_index\" value=\""+portId+"\" onchange=\"addToPortRange(this)\"/>";
	}
	
		tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = name;
    //tbtr.cells[3].innerHTML = enable;	
    //tbtr.cells[4].innerHTML = instance;
    tbtr.cells[3].innerHTML = interface;		
    tbtr.cells[4].innerHTML = (portbmp==""?"-":portbmp);
}

function checkData()
{
	var tf=document.vlan_port;
	tf.action = "/goform/saveComm?name=vlan_port";
	tf.submit();
}

function refreshpage()
{
  location.href='vlan_port.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('vlan_port',<% write(lang); %>);
}

</script>
</head>

<body  ><br>

<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="vlan_port" method="POST" action="/goform/vlanPortConfig">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="vlan_port_id" id="vlan_port_id" >
<input type="hidden" name="flag" id="flag" >
<input type="hidden" name="type_value" id = "type_value"  value="@trunk_config#">
<input type="hidden" name="filter_value" id = "filter_value"  value="@trunk_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="pvid_config"  value="@pvid_config#">
<input type="hidden" name="egress_tagged" id="egress_tagged" value="@egress_tagged#">

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">

<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>

     	 
 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>VLAN设置/端口VLAN设置</b></font></td></tr>
 </table>
 
<!--
    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
	     <tr height="25">
	       <td align="left" colspan="4"  class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"端口VLAN");</script></td>
	     </tr>
        </table>
-->
        
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%" id="table1" class="tablebord">

		<tr height="25">
              <td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"端口范围");</script></td>
              <td align="left" class="crons" colspan="3">&nbsp;
                <input name="port_range" type="text" class="input_x" id="port_range" readonly="true"/></td>
         </tr>
	     <tr height="25">
	     	  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"模式");</script></td>
	     	  <td class="crons" colspan="3">&nbsp;
	     	    <select name="porttype" id="porttype" class="select1" onChange="settype()">
                  <option value="0" >Access</option>
                  <option value="1" >Trunk</option>
                  <option value="2" >Hybrid</option>
                </select>
                </td>
	     </tr>		 
		  
            <tr height="25" style="display:none">
              <td width="18%" align="left" class="crons"> &nbsp;Ingress Filter</td>
              <td align="left" class="crons" colspan="3">&nbsp;
                  <select name="filter" id="filter" class="select1">
                    <option value="enable" >Enable</option>				  
                    <option value="disable">Disable</option>
                  </select></td>
            </tr>			 
		 
	     <tr height="25">

	     	  <td colspan="4" align="middle" class="crons">
	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify","checkup()");</script>
&nbsp;&nbsp;&nbsp;</td>
	     </tr>
	   
<tr><td colspan="3" height="8"></td></tr>
   <tr>
	    	<td colspan="4">
	    		<table id="table_port_vlan1" border="0" cellspacing="0" cellpadding="0" width="100%" class="tablebord">
	    		 	 <tr height="25">
	     	  				<td width="18%" height="27" class="crons">&nbsp;Allowed VLAN ID </td>
	     	  				<td width="23%" class="crons">
								&nbsp;
							  <input type="text" name="avid"  id="avid" class="input_board3" maxlength="25"/>&nbsp;(1-4094)
							  &nbsp;&nbsp;<div style="display:none">
                Egress-tagged: &nbsp;Enable:<input type="radio" id="egress_tagged1" checked name="egress_tagged111" value="enable">
                Disable:<input type="radio" id="egress_tagged2" name="egress_tagged111" value="disable"></div></td>

    		  				
	     	  		        <td width="59%" class="crons"><span id="tagged" style="display:none"><input type="checkbox" name="checkbox" value="checkbox" id="c1">Egress-tagged</span></td>
	    		 	 </tr>	
					<tr height="25">
						<td align="middle" colspan="3" class="crons">&nbsp;
						       <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","addavid","check(1)");</script>
    		    				&nbsp;
                				<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","deleteavid","checkdel(1)");</script>               </td>
					</tr>
					<tr height="25">
	     	  			<td class="crons">&nbsp;缺省VLAN ID </td>
	     	 			<td colspan="2" class="crons">
							&nbsp;
							<input type="text" name="dvid" id="dvid" class="input_board3" maxlength="25"/>&nbsp;(1-4094)</td>
	     			</tr>	
					<tr height="25">
						<td align="middle" colspan="3" class="crons">&nbsp;
						<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","adddvid","check(2)");</script>
    		    				&nbsp;
               				<script>
							//writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","deletedvid","checkdel(2)");
							</script>					</tr>	
	    		</table>	    	</td>
   </tr>	   
	   
<tr><td colspan="3" height="8"></td></tr>
   <tr>
	    	<td colspan="4">
	    		<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
	    		 	<tr align="center" height="25" class="crons">
	    		 		<th class="td2" width="10%"><input type="checkbox" name="check_all" value="all" onClick="selectToAll()" /></th>
	    		 		<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></b></font></th>
	    		 		<th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"模式");</script></b></font></th>
	
	    		 		<th class="td2" width="15%"><font color="#333333"><b>缺省Vid</b></font></th>						
	    		 		<th class="td2" width="50%"><font color="#333333"><b>Vlan ID</b></font></th>						
	    		 	</tr>
					<script>  <%  var errorcode; showVlanPort(); %></script>
	    		</table>	    	</td>
   </tr>
     </table>
   </td></tr>
   <tr>
	  	   <td colspan="2" align="center" height="35">
	  	   <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
	  		</td>
   </tr>
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>

</form>  
<script>
changebgcolor();
changebgcolor_name("table_port_vlan");
changebgcolor_name("table_port_vlan1");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>


</body>
</html>

