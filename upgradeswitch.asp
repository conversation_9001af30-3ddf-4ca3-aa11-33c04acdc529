<HTML>

<HEAD>
  <% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
    <TITLE>page</TITLE>
    <META http-equiv=Content-Type content="text/html; charset=utf8">
    <script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
    <script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
    <link href="css/display_web.css" rel="stylesheet" type="text/css" />
    <script>
<% var Allipaddr, errorcode; getIpAddr(); %>

        function tdIpCheck(textValue) {
          re1 = /(\d+)(\W)(\d+)(\W)(\d+)(\W)(\d+)/
          Check = textValue.search(re1);
          if (Check == -1) {
            return false;
          }
          else {
            ipSplit = textValue.split('.');
            if (ipSplit.length != 4) {
              return false;
            }

            for (i = 0; i < ipSplit.length; i++) {
              if (isNaN(ipSplit[i])) return false;
              if (ipSplit[i].length > 1 && ipSplit[i].charAt(0) == "0") return false;
            }

            for (i = 0; i < ipSplit.length; i++) {
              if (ipSplit[i] > 255) {
                return false;
              }
              if (ipSplit[i] < 0) {
                return false;
              }
            }
            if ((ipSplit[0] == 255) && (ipSplit[1] == 255) && (ipSplit[2] == 255) && (ipSplit[3] == 255)) {
              return false;
            }

            if ((ipSplit[0] == 0) || (ipSplit[3] == 0) || (ipSplit[3] == 255)) {
              return false;
            }

            if (ipSplit[0] >= 224) {
              return false;
            }
            return true;
          }
        }

      function IpCheckAndMask(ip_addr) {
        /*by FC-fcy 2012-4-23 start*/
        var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])(\u002F)(([0-9])|([1-2][0-9])|(3[0-2]))$/;
        /*by FC-fcy 2012-4-23 end*/
        if (myRE.test(ip_addr)) {
          return true;
        }
        else {
          return false;
        }
      }

      function showHelp(helpname, lang) {
        var tmp = lang + "_help.html#" + helpname;
        window.open(tmp);
      }

      function changebgcolor() {
        var tab = document.all.table1;
        var len = tab.rows.length;
        for (var i = 0; i < len; i++) {
          var lencol = tab.rows[i].cells.length
          for (var j = 0; j < lencol; j++) {
            if (j % 2 == 1) {

              tab.rows[i].cells[j].className = "all_tables all_tables2";
            }
            else {
              tab.rows[i].cells[j].className = "all_tables";
            }

          }

        }
      }

    async function messageCheck() {
        var hid = document.webForm;
        var fName = hid.upgrade_filename.value;
        var dplen;
        var dpvalue;
        var reauthn;
        var reauthpd;

        if (tdIpCheck(hid.upgrade_ip.value) == false) {
          alert("IP地址输入非法！格式：A.B.C.D");
          return false;
        }


        if (fName.indexOf(".zip") != fName.length - 4) {
          alert("文件必须以 .zip 为扩展名！");
          return false;
        }

        if (confirm("确定要升级吗?")) {

         // var name = prompt("操作认证-用户名", "");
          document.getElementById("reauthn").value = await userNamePrompt();
          // var pwd = prompt("操作认证-密码", "");
          // document.getElementById("reauthpd").value = pwd;
          document.getElementById("reauthpd").value = await testThePrompt();

          reauthn = document.getElementById("reauthn").value;
          reauthpd = document.getElementById("reauthpd").value;

          dplen = parseInt(reauthn.length / 2);
          dpvalue = reauthn;
          reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
            + reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

          dplen = parseInt(reauthpd.length / 2);
          dpvalue = reauthpd;
          document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
            + reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
          document.getElementById("reauthn").value = "XD!Zb3BjRCCoPExe";

          const fileInput = document.getElementById('upgrade_filename');

          const file = fileInput.files[0]; // 获取用户选择的文件


          if (file) {
            // if (file.size > 6 * 1024 * 1024) {
            //   alert("文件过大不支持");
            //   window.location.reload();
            //   return false;
            // }
            const pro = document.getElementsByClassName("progress")[0];
            pro.style.display = "block";


            uploadFile(file); // 调用上传文件的函数
          }

          // hid.submit();
          return true;
        }
        else
          return false;

        return false;
      }

      function uploadFile(file) {
        const formData = new FormData(); // 创建FormData对象
        formData.append('upgrade_filename', file); // 将文件添加到FormData中，可以设置自定义的键名
        formData.append('upgrade_ip', '127.0.0.1');
        formData.append('reauthn', 'XD!Zb3BjRCCoPExe');
        formData.append('reauthpd', document.getElementById("reauthpd").value);
        formData.append('ltime', document.getElementsByName("ltime")[0].value);
        formData.append('lastts', document.getElementsByName("lastts")[0].value);
        formData.append('alpha', document.getElementsByName("alpha")[0].value);

        const progress = document.getElementById("progress");
        const span = document.getElementById("span");
        const timer = setInterval(() => {
          if (progress.value < 99) {
            progress.value++;
            span.innerHTML = progress.value + "%";
          }
          else {
            clearInterval(timer);
          }

        }, 300)
        // 发送POST请求
        fetch('/goform/updateSwitch', {
          method: 'POST',
          body: formData
        })
          .then(response => {
            if (response.url.indexOf('error') != -1) {
              document.getElementsByClassName("progress")[0].style.display = "none";
              alert('不具备执行该操作的权限')
              window.location.reload();
              return
            }
            if (response.status == 200) {
              progress.value = 100;
              span.innerHTML = progress.value + "%";
              document.getElementsByClassName("progress")[0].style.display = "none";
              setTimeout(function () {
                alert("系统软件升级成功，重启后生效！");
                window.location.reload();
              }, 100);



            }
            else {
              document.getElementsByClassName("progress")[0].style.display = "none";
              alert("升级失败！");
              window.location.reload();
            }
          })
          .catch(error => {
            // 处理错误

            window.location.reload();
          });
      }
      function showHelpinfo() {
        showHelp('ip',<% write(lang); %>);
      }


    </script>
    <style>
      .progress {
        display: none;

      }

      .mask {
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
      }

      .pro {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    </style>
</HEAD>

<BODY>
  <div class="progress">
    <div class="mask"></div>
    <div class="pro"><label for="progress" style="color: aliceblue;">上传进度:<span id="span">70%</span> </label>
      <progress id="progress" value="0" max="100"> </progress>
    </div>

  </div>
  <br>
  <% var authmode; checkCurMode(); %>
    <script>
      checktop(<% write(lang); %>);
    </script>

    <form name="webForm" method="post" action="/goform/updateSwitch" enctype="multipart/form-data" id="webForm">
      <input type="hidden" name="ltime" value=<% write(lltime); %>>
      <input type="hidden" id="reauthn" name="reauthn" value="">
      <input type="hidden" id="reauthpd" name="reauthpd" value="">
      <input type="hidden" name="lastts" value=<% write(serverts); %>>
      <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

      <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
        <tr>
          <td class="tit" height="30px">
            <font color="#0069d6">
              <div>软件升级</div>
            </font>
          </td>
        </tr>
      </table>
      <br />


      <div class="formContain">
        <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td height="20px">
              <font size="5" color="#404040">
                <div class="bot">SWITCH</div>
              </font>
            </td>
          </tr>
        </table>

        <TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
          <TBODY>
            <TR height=22 style="display: none;">
              <TD width="42%" align="right" valign="top" style="font-weight: bold;">&nbsp;&nbsp;TFTP Server IP:</TD>
              <td><span class="crons">
                  <input type="text" name="upgrade_ip" id="upgrade_ip" value="***********">
                </span></td>
            </TR>
            <TR height=22>
              <TD align="right" style="font-weight: bold;font-size: 16px;" width="50%">&nbsp;&nbsp;
                <script>writemsg(<% write(lang); %>, "文件名:");</script>
              </TD>
              <td><input type="file" name="upgrade_filename" id="upgrade_filename" value=""></td>
            </TR>
            <TR height=22>

              <TD colspan="2" valign="top">

                <div align="center">
                  <script>writebutton(<% write(authmode); %>,<% write(lang); %>, "升  级", "buttons_apply", "button", "aaa", "messageCheck()");</script>
                </div>
              </TD>
            </TR>
        </TABLE>
      </div>

      <script>
        changebgcolor();

<% if (errorcode == "3") { write("alert(putmsg("); write(lang); write(",'不具备执行该操作的权限!'));"); } %>
<% if (errorcode != "") { if (errorcode != "3") { write_errorcode(errorcode); } } %>

      </script>

    </form>



</BODY>
<script>


</script>

</HTML>