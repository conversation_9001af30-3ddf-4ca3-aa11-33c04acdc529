<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<script language="javascript" type="text/javascript" src="js/alpinejs.min.js" defer></script>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"端口管理");</script></title>

<script  language="JavaScript" type="text/JavaScript">

function isINT(str){
var re = /^[0-9]*[0-9][0-9]*$/ ;

var result = re.test(str);   //测试 返回true或false

return result;

}

function checkMessage()
{	
	var hid=document.webForm;
	var target = document.getElementById("portNumm");

	if(hid.timeout.value<60||hid.timeout.value>1800)
	{
		alert(putmsg(<% write(lang); %>,"超时时间应该在范围(60-1800)之间!"));
		return false;
	}
	target.value = target.value.replace(/\s/g, "|");
	
	hid.submit();
	return true;
}
function addToPortRange(obj)
{
	var target = document.getElementById("portNumm");
	var p = obj.value;
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);


	if(obj.checked)
	{
		target.value = target.value  + p + " ";

		document.webForm.timeout.value=trobj.cells[2].innerHTML;
		
		if(trobj.cells[3].innerHTML == "enable")
			document.getElementById("e1").checked = true;
		else 
			document.getElementById("e2").checked = true;

	}
	else{

		target.value = target.value.replace(p+" ", "");
	}
	
	return true;
}

/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;


	if (cf.check_all.checked == true)
    {
		for (i = 0; i < objs.length; i++) 
        {    
        	if (objs[i].disabled==false && objs[i].checked==false)
		    {
            	objs[i].checked = true;  
				addToPortRange(objs[i]);
			}
        }
    }
    else
    {
        for (i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true)
			{
				objs[i].checked = false;  
			 	addToPortRange(objs[i]);
			}             
        }
    }     
}


function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm?name=setautodowncfg";
	tf.submit();
}

function refreshpage()
{
  location.href='autodown.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('setautodowncfg',<% write(lang); %>);
}


function P(aa,bb,cc,dd)
{
    var narr=5;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+aa);	

    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
	    tbtr.appendChild(tbtd);
    }
	            
	//tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+aa+"\" onclick=\"addToPortRange(this)\"/>";
	tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"checkbox_index\" value=\""+aa+"\" onchange=\"addToPortRange(this)\"/>";

	tbtr.cells[1].innerHTML = aa;
	tbtr.cells[2].innerHTML = bb;


	if("disable"==cc)
	tbtr.cells[3].innerHTML = "disable";
	else
	tbtr.cells[3].innerHTML = "enable";

	tbtr.cells[4].innerHTML = dd;


}
retValue = <% var responseJsonStr; jw_get_portCloseConfig(); %>
responseStr = <% write(responseJsonStr); %>;
const appData = {
	headers: ['端口', '超过时间', '使能', '状态'],
};

function checkMessage(row) {
	var obj={
		pageName: 'autodown.asp',
		PortAotoClose:[
			{
				portName: row.portName,
				portEnable: row.portEnable,
				timeout: Number(row.timeout),
			}
		]
	}
	var tf=document.webForm;
	tf.param1.value = JSON.stringify(obj)
	tf.action = "/goform/jw_set_portCloseConfig";
	tf.submit();
}

</script>
</head>
<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="webForm" method="post" action="/goform/setautodowncfg">
	<input type="hidden" name="param1" id="param1" />
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
	<input type="hidden" name="lastts" id="lastts" value=<% write(serverts); %>>
	<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
</form>
<div class="formContain">
	<table
          width="96%"
          align="center"
          border="0"
          cellspacing="0"
          cellpadding="0"
        >
          <tr>
            <td height="30px">
              <font size="5" color="#404040"><div class="bot">端口自动关闭</div></font>
            </td>
          </tr>
	</table>
	<div x-data="appData">
		<table
		  class="tab1 tablebord"
		  width="96%"
		  border="0"
		  align="center"
		  cellpadding="0"
		  cellspacing="0"
		>
		  <thead>
			<tr>
			  <template x-for="(header, index) in headers" :key="index">
				<th class="all_tables" x-text="header"></th>
			  </template>
			</tr>
		  </thead>
		  <tbody>
			<template
			  x-for="(row, rowIndex) in responseStr.PortAotoClose"
			  :key="rowIndex"
			>
			  <tr style="text-align: center">
				<td class="all_tables" x-text="row.portName"></td>
				<td class="all_tables">
					<input type="text" @change="checkMessage(row)" x-model="row.timeout" />
					(60-1800 s)
				</td>
				<td class="all_tables">
					<div>
						<label>
							<input type="radio" @change="checkMessage(row)" :name="'fruit'+rowIndex" value="enalbe" x-model="row.portEnable"/>
							开启
						</label>
						<label>
							<input type="radio" @change="checkMessage(row)" :name="'fruit'+rowIndex" value="disable" x-model="row.portEnable"/>
							关闭
						</label>
					</div>
				</td>
				<td class="all_tables">
					<div x-text="row.portState"></div>
				</td>
			  </tr>
			</template>
			
		  </tbody>
		</table>
	  </div>
</div>

<script>

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</body>
</html>
