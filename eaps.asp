<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>

<title>&nbsp;<script>writemsg(<% write(lang); %>,"EAPS设置");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>

<script language="JavaScript" type="text/JavaScript">

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length;
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function check()
{
	var ring=document.getElementById("ring").value;
	var vid=document.getElementById("cvlan").value;
	var ftime=document.getElementById("ftime").value;	
	var htime=document.getElementById("htime").value;
	var pport=document.getElementById("pport").value;
	var sport=document.getElementById("sport").value;
	var tf=document.eaps;
	var i,j=0;
	var pvlan = new Array();
	
	pvlan=document.getElementById("pvlan").value.split(',');	
	
	if((ring>16) || (ring<1))
	{
	   alert(putmsg(<% write(lang); %>,"ring id的范围必须在1-16之间"));
	   return false;
    }

	var table_eaps = document.getElementById("table_eaps");
/*
		for(i=1;i<table_eaps.rows.length;i++){
			if(table_eaps.rows[i].cells[1].innerHTML==ring && table_eaps.rows[i].cells[11].innerHTML==pvlan ){
				j=8;
				break;
			}
			j=0;
		}
*/
	if(pport == sport)
	{
		alert(putmsg(<% write(lang); %>,"源端口与次端口不能相同!"));
		return;
	}


	if(vid<2||vid>4094)
	{
		alert(putmsg(<% write(lang); %>,"控制VLAN的范围必须在2-4094之间"));
	}

	for(i=0; i<pvlan.length; i++)
	{
		if(pvlan[i]<1||pvlan[i]>4094)
		{
			alert(putmsg(<% write(lang); %>,"保护VLAN的范围在1-4094之间"));
		}
	
		if(vid == pvlan[i])
		{
			alert(putmsg(<% write(lang); %>,"控制VLAN与保护VLAN不能配成相同的VLAN"));
			return;
		}
	}


    if((ftime<=65535) && (ftime>=2))
 	{
	  if((htime<=65535) && (htime>=1))
	  {
	     if(ftime>htime)
		 {
	       tf.submit();
		 }
		 else
		 {
		   alert(putmsg(<% write(lang); %>,"fail-time必须大于hello-time"));
		 }
	  }
	  else
	  {
	    alert(putmsg(<% write(lang); %>,"hello-time的范围必须在1-65535之间"));
	  }
	}
	else
	{
		alert(putmsg(<% write(lang); %>,"fail-time的范围必须在2-65535之间"));
	}

}

function checkdel()
{
	var tf=document.eaps;
	var ring=document.getElementById("ring").value;
	var vid=document.getElementById("cvlan").value;	
	var del = document.getElementById("del");
    var i,j=0;
	var table_eaps = document.getElementById("table_eaps");
	for(i=1;i<table_eaps.rows.length;i++){
		if(table_eaps.rows[i].cells[1].innerHTML==ring){
			j=8;
			break;
		}
		j=0;
	}
	if(j==8){
		del.value = "1";
   		tf.submit();
	}
	else
	{
		alert(putmsg(<% write(lang); %>,"未选择eaps环ID!"));
	}

}  

function chang(x)
{
	var enables =document.getElementById("enable1");
	var ring=document.getElementById("ring").value;
	var del = document.getElementById("del");
	var ftime=document.getElementById("ftime").value;	
	var htime=document.getElementById("htime").value;
	var pport=document.getElementById("pport").value;
	var sport=document.getElementById("sport").value;
	var vid=document.getElementById("cvlan").value;
	var tf=document.eaps;
	var i,j=0;
	var pvlan = new Array();
	
	pvlan=document.getElementById("pvlan").value.split(',');	
	
	switch(x)
	{
		case 1:
			var table_eaps = document.getElementById("table_eaps");
			for(i=1;i<table_eaps.rows.length;i++){
				if(table_eaps.rows[i].cells[1].innerHTML==ring){
					j=8;
					break;
				}
				j=0;
			}

			if(j==8){
				del.value="1";
				tf.action = "/goform/ChangEaps?name=eaps";
   				tf.submit();
			}else{
				alert(putmsg(<% write(lang); %>,"未选择eaps环ID!"));
			}
			break;
		case 2:
			if(pport == sport)
			{
				alert(putmsg(<% write(lang); %>,"源端口与次端口不能相同!"));
				return;
			}

			for(i=0; i<pvlan.length; i++)
			{
				if(pvlan[i]<1||pvlan[i]>4094)
				{
					alert(putmsg(<% write(lang); %>,"保护VLAN的范围在1-4094之间"));
				}
			
				if(vid == pvlan[i])
				{
					alert(putmsg(<% write(lang); %>,"控制VLAN与保护VLAN不能配成相同的VLAN"));
					return;
				}
			}
			
			if(enables.options[0].selected)
			{
				alert(putmsg(<% write(lang); %>,"配置时,ring使能必须关闭"));
			}else
			{	
					    if((ftime<=65535) && (ftime>=2))
					 	{
						  if((htime<=65535) && (htime>=1))
						  {
						     if(ftime>htime)
							 {
							       del.value = "2";
								tf.action = "/goform/ChangEaps?name=eaps";
				   				tf.submit();
							 }
							 else
							 {
							   alert(putmsg(<% write(lang); %>,"fail-time必须大于hello-time"));
							 }
						  }
						  else
						  {
						    alert(putmsg(<% write(lang); %>,"hello-time的范围必须在1-65535之间"));
						  }
						}
						else
						{
							alert(putmsg(<% write(lang); %>,"fail-time的范围必须在2-65535之间"));
						}
				
			}
			break;
	}
	
}
function AddOption(port_name){
	var selectObject = document.getElementById("pport");
	var selectObject2 = document.getElementById("sport");
	var y=document.createElement('option');
	var y2=document.createElement('option');
	y.text=port_name;
	y.value=port_name;
	y2.text=port_name;
	y2.value=port_name;
	try
	{
		selectObject.add(y,null); // standards compliant3
		selectObject2.add(y2,null); // standards compliant3
	}
	catch(ex)
	{
		selectObject.add(y); // IE only
		selectObject2.add(y2); // IE only
	}
}
function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var target = document.getElementById("ring");
	var pport = document.getElementById("pport");
	var sport = document.getElementById("sport");
	var cvlan = document.getElementById("cvlan");
	var pvlan = document.getElementById("pvlan");
	var htime =document.getElementById("htime");
	var ftime =document.getElementById("ftime");
	var enables =document.getElementById("enable1");
	var mode =document.getElementById("mode");
	var extreme =document.getElementById("extreme");

	var pvlanl =document.getElementById("pvlanl");

	var p = obj.value;
	var i;

	if(obj.checked){
		target.value =p;
		pport.value=trobj.cells[6].innerHTML;
		sport.value=trobj.cells[8].innerHTML;
		cvlan.value=trobj.cells[10].innerHTML;
		pvlan.value=trobj.cells[11].innerHTML;
		htime.value=trobj.cells[13].innerHTML;
		ftime.value=trobj.cells[12].innerHTML;
		pvlanl.value=trobj.cells[11].innerHTML;
		if(mode.options[0].text==trobj.cells[5].innerHTML)
		{
			mode.options[0].selected=true;
			mode.options[1].selected=false;
		}else
		{
			mode.options[1].selected=true;
			mode.options[0].selected=false;
		}
		if(enables.options[0].text==trobj.cells[2].innerHTML)
		{
			enables.options[0].selected=true;
			enables.options[1].selected=false;
		}
		else
		{
			enables.options[1].selected=true;
			enables.options[0].selected=false;
		}

		if(((extreme.options[0].text=="Enable")?"on":"off")==trobj.cells[4].innerHTML)
		{
			extreme.options[0].selected=true;
			extreme.options[1].selected=false;
		}else
		{
			extreme.options[1].selected=true;
			extreme.options[0].selected=false;
		}
		}
}

function P(portId,name,state,enable,instance,interface,pport,portbmp,sport,cvlan,pvlan,ftime,htime)
{
    var narr=14;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_eaps").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+portId);	

    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
    tbtr.cells[1].abbr = portId;
	
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+portId+"\" onclick=\"addToPortRange(this)\"/>";
	tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = name;
	tbtr.cells[3].innerHTML = state;
    tbtr.cells[4].innerHTML = enable;	
    tbtr.cells[5].innerHTML = instance;
    tbtr.cells[6].innerHTML = interface;		
	tbtr.cells[7].innerHTML = pport;	
    tbtr.cells[8].innerHTML = portbmp;
    tbtr.cells[9].innerHTML = sport;	
    tbtr.cells[10].innerHTML = cvlan;
    tbtr.cells[11].innerHTML = pvlan;	
    tbtr.cells[12].innerHTML = ftime;
    tbtr.cells[13].innerHTML = htime;		
}


function checkData()
{
	var tf=document.eaps;
	tf.action = "/goform/saveComm?name=eaps";
	tf.submit();
}

function refreshpage()
{
  location.href='eaps.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('eaps',<% write(lang); %>);
}

</script>
</head>

<body  >
<br>

<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="eaps" method="POST" action="/goform/eapsConfig">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
		<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
			<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>EAPS设置</b></font></td></tr>
		</table>

       </td></tr>
             
   <tr><td>  	
   	 
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%"  id="table1" class="tablebord">
	  
		 <tr  height="25">
              <td width="18%" align="left" class="crons">&nbsp;Ring ID</td>
              <td colspan="3" align="left" class="crons">&nbsp;
                <input name="ring" id="ring" type="text" >&nbsp;(1-16)&nbsp;			</td>
	     	  </tr>		 

	     <tr height="25">
	     	  <td class="crons">&nbsp;Mode</td>
	     	  <td width="31%" class="crons">&nbsp;
	   	        <select name="mode" id="mode" class="select1">
	   	             <option value="0">Master</option>
	   	             <option value="1">Transit</option>
	   	             </select>	     	  </td>

	     	  <td width="25%" class="crons">&nbsp;Extreme-Interoperability</td>
	     	  <td width="26%" class="crons">&nbsp;
	   	        <select name="extreme" id="extreme" class="select1">
	   	             <option value="0">Enable</option>
	   	             <option value="1">Disable</option>
	   	             </select>	     	  </td>
	     </tr>	 	 

	     <tr height="25">
	     	  <td class="crons">&nbsp;Primary-Port </td>
			  <td class="crons">&nbsp;&nbsp;<select style="width:70px;" id="pport" name="pport" ></select></td>

	     	  <td class="crons">&nbsp;Secondary-Port </td>
	     	  <td class="crons">&nbsp;&nbsp;<select style="width:70px;" id="sport" name="sport" ><script><% var errorcode;AppendOption(); %></script></select></td>

	     </tr>	 		 
	     <tr height="25">
	     	  <td class="crons">&nbsp;Control-VLAN </td>
	     	  <td class="crons">
&nbsp;
<input type="text" name="cvlan" id="cvlan" class="input_board3" maxlength="25"/></td>

	     	  <td colspan="2" class="crons">&nbsp;<span class="STYLE1">*控制VLAN 仅包含主端口 与次端口 ,不能再加入其它端口</span></td>
	     	  </tr>	 
		 
	     <tr height="25">
	       <td class="crons">&nbsp;Protected-VLAN </td>
	       <td class="crons">&nbsp;
               <input type="text" name="pvlan" id="pvlan" class="input_board3" maxlength="25"/>&nbsp;<script>writemsg(<% write(lang); %>,"(1,2,4094)");</script></td>
	       <td colspan="2" class="crons">&nbsp;</td>
	       </tr>
	     <tr height="25">
	     	  <td class="crons">&nbsp;Fail-Time </td>
	     	  <td class="crons">
&nbsp;
<input type="text" name="ftime" id="ftime" value="3" class="input_board3" maxlength="25"/></td>

	     	  <td class="crons">&nbsp;Hello-Time </td>
	     	  <td class="crons">
&nbsp;
<input type="text" name="htime" id="htime" value="1" class="input_board3" maxlength="25"/></td>
	     </tr>	 		 		 
		 
	     <tr height="25">

	     	  <td colspan="4" align="middle" class="crons">
	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","buttons","button","add","check()");</script>
	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify","chang(2)");</script>
	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","delete","checkdel()");</script>	     </tr>
	   
<tr><td colspan="4" height="8"></td></tr>

	<tr>
			<td class="crons"><span >&nbsp;<script>writemsg(<% write(lang); %>,"Ring使能");</script></span></td>
			<td colspan="3" class="crons">&nbsp;
					<select name="enable1" id="enable1">
						 <option value="0">Enable</option>
						 <option value="1">Disable</option>
					</select> 
						 &nbsp;
	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify2","chang(1)");</script>			</td>
	</tr>


   <tr>
	    	<td colspan="4">
	    		<table id="table_eaps" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
	    		 	<tr align="center" height="25" class="crons">
	    		 		<th class="td2" width="5%"></th>
	    		 		<th class="td2" width="5%"><font color="#333333"><b>Ring ID</b></font></th>
					    <th class="td2" width="5%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"Ring使能");</script></b></font></th>	
	    		 		<th class="td2" width="5%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"Ring状态");</script></b></font></th>						
	    		 		<th class="td2" width="10%"><font color="#333333"><b>Extreme Interoperability</b></font></th>
	    		 		<th class="td2" width="10%"><font color="#333333"><b>Mode</b></font></th>						
	    		 		<th class="td2" width="10%" colspan="2"><font color="#333333"><b>Primary Port</b></font></th>												
	    		 		<th class="td2" width="10%" colspan="2"><font color="#333333"><b>Secondary Port</b></font></th>									
				    	<th class="td2" width="10%"><font color="#333333"><b>Control VLAN</b></font></th>						
	    		 		<th class="td2" width="10%"><font color="#333333"><b>Protected VLAN</b></font></th>
						<th class="td2" width="10%"><font color="#333333"><b>Fail Time</b></font></th>						
	    		 		<th class="td2" width="10%"><font color="#333333"><b>Hello Time</b></font></th>							
	    		 	</tr>
					<script>  <%  var errorcode; showEaps(); %></script>
	    		</table>	    	</td>
   </tr>
     </table>
   </td></tr>
   <tr>
	  	   <td colspan="2" align="center" height="35">
	  		    <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
              &nbsp;
	  		</td>
   </tr>
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>
<INPUT type="hidden" name="del" id= "del" value="0">
<INPUT type="hidden" name="pvlanl" id= "pvlanl" value="0">
</form>    
<script>
changebgcolor();

changebgcolor_name("table_eaps");


<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</html>
