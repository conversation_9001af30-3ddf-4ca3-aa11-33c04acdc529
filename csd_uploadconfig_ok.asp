<HTML>
<HEAD>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>

<TITLE>page</TITLE>
<META http-equiv=Content-Type content="text/html; charset=utf8">

<style>
td {font-family : Verdana, Arial, Helvetica;font-size : 12px;color:000000}

.tablebord {
	BORDER-TOP: #8CACBB 1px solid; MARGIN-TOP: 10px; BORDER-LEFT: #8CACBB 1px solid;
}

.all_tables  {  border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #4891c6;
border-right-style: solid; border-right-width: 1px; border-right-color: #4891c6
}

.menuTitle {
	FONT-WEIGHT: bold; FONT-SIZE: 12px; COLOR: #164674; FONT-FAMILY: Verdana, Arial, Helvetica, sans-serif; HEIGHT: 22px
}

.update_ok{font-size: 14px;font-weight: bold;color: #DC143C; text-decoration: none;}
</style>


<script language="javascript">

function display(){
//changebgcolor();
setTimeout("startUp()",30000);

}
function startUp()
{
	var hid=document.form1;
	hid.submit();
	return true;
}
</script>
</HEAD>

<BODY  onload="display()"  >
<!--	
 <table width="95%" border="0" cellspacing="0" cellpadding="0" style="margin-left:30px;">
	<tr><td bgcolor="#5893B0" height="30px" ><font size="3" color="#FFFFFF"><b>软件升级成功</b></font></td></tr>
 </table>
 配置备份成功
-->
  <form id="form1" name="form1" method="post" action="/goform/configsetting_csd_ok">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table width="95%" borderColor=#4891C6 cellSpacing=0 border=1 cellpadding=0 id="table1" style="margin-left:30px;" class="tablebord">
    
    <TR height=22>
      <td align="center"  bgcolor="#efefef" >
        <br>
        <br>
        <br>
         <span  class="update_ok" >Upload file successfully</span><br>
        <br>
        <br>
        <br></td>
    </TR>
</TABLE>
  </FORM>
</BODY>
</HTML>


