<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"MMS通信参数");
</script></title>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<% var errorcode, mmsenable, piedname, measurementperiod_tmp; getMMSConfigInfo(); %>

<script>

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor3(){
 var tab = document.all.table3;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function IpCheck(ip_addr)
{
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])$/;
	if(myRE.test(ip_addr))
	{
        return true;
	}
	else
	{
		return false;
	}
}

//[a-z A-Z 0-9 ] 字线和数字
function check_ingress_str_format(str)
{
    var reg1 = /^([a-zA-Z]|\d)+$/;

    if(!reg1.exec(str))
        return false;

    return true;
}

function isValidString(str)
{
	var validc = "/'%`\"\\><";
	var a,c;
	for(i = 0 ; i < validc.length; i ++)
	{
		c = validc.substring(i, i + 1);
		if(str.indexOf(c) > -1)
		{
			return true;
		}
	}
	return false;
}

/* by zjx  11-6-8 */
function IpCheck(ip_addr)
{
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])$/;
	if(myRE.test(ip_addr))
	{
        return true;
	}
	else
	{
		return false;
	}
}

function messageCheck(v)
{
    var hid=document.webForm;
    if(v==1)
    {
    	hid.flag.value=1;
    	hid.submit();
		return true;
    }
    else if (v==2)
    {
		hid.flag.value=2;
		hid.submit();
		return true;
    }
    else if (v==3)
    {
		hid.flag.value=3;
		hid.submit();
		return true;
    }

    return ;
}
function DelSnmpCom(value)
{
    var hid=document.webForm;
    	hid.comname.value=value;
		hid.flag.value=2;
		hid.submit();
		return true;
}
function DelSnmpTrap(v1,v2)
{
    var hid=document.webForm;
		hid.flag.value=6;
		hid.trapiphid.value=v1;
		hid.trapnamehid.value=v2;
		hid.submit();
		return true;
}
function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm?name=snmp";
	tf.submit();
}
function refreshpage()
{
  location.href='snmp.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
//   showHelp('snmp',<% write(lang); %>);
}


function display()
{
 	var hid=document.webForm;

	tmp = "<% write(piedname); %>";
	hid.iedname.value = tmp.split(",")[0];


	tmp1 = "<% write(measurementperiod_tmp); %>";
	hid.measurementperiod.value = tmp1.split(",")[0];
}


</script>

</head>
<body  onload="display()"><br>

<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);


</script>
<form  name="webForm" method="post" action="/goform/setMMSCfg">
<input type="hidden" name="flag">
<input type="hidden" name="comname">
<input type="hidden" name="trapiphid">
<input type="hidden" name="trapnamehid">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>





 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>MMS通信参数</b></font></td></tr>
 </table>

<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">

    <tr height="25">
		<td width="20%" align="left" class="crons">&nbsp;MMS使能</td>

		<td width="30%" align="left" class="crons">&nbsp;
			<input type="hidden" name="mmsenable" value=<% write(mmsenable); %>>
			<input type="radio" name="trap" value="1" <% if (mmsenable==1) write("checked"); %>>Enable
			<input type="radio" name="trap" value="0" <% if (mmsenable==0) write("checked"); %> >Disable
		</td>

		<td  align="left" class="crons">&nbsp;
			<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","buttons_apply","button","modify4","messageCheck(1)");</script>
		</td>
    </tr>


    <tr height="25">
		<td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"IED name");</script>:</td>
		<td align="left" class="crons">&nbsp;
			<input type="text" name="iedname" id="iedname" class="input_board3" readonly>
		</td>

		<td align="left" class="crons">&nbsp;
<!--
			<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","buttons_apply","button","modify5","messageCheck(2)");</script>
-->
		</td>
    </tr>

    <tr height="25">
		<td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"Measurement Period");</script>:</td>
		<td align="left" class="crons">&nbsp;
			<input type="text" name="measurementperiod" id="measurementperiod" class="input_board3"> (单位：ms, 0-30000)
		</td>

		<td  align="left" class="crons">&nbsp;
		<!--
			<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","buttons_apply","button","modify6","messageCheck(3)");</script>
		-->
		</td>
    </tr>
</table>



<script>
changebgcolor();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
<br><br>
</form>

</body>


</html>

