<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"端口管理");</script></title>

<script  language="JavaScript" type="text/JavaScript">

function isINT(str){
var re = /^[0-9]*[0-9][0-9]*$/ ;

var result = re.test(str);   //测试 返回true或false

return result;

}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}



function checkMessage()
{	
	var hid=document.webForm;
	var target = document.getElementById("portNumm");


	if(!isINT(hid.pathcost.value))
	{
	    alert(putmsg(<% write(lang); %>,"路径开销必须是整数!"));
		return false;
	}
	if(hid.pathcost.value<1||hid.pathcost.value>200000000)
	{
		alert(putmsg(<% write(lang); %>,"路径开销应该在范围(1-200000000)之间!"));
		return false;
	}
	if(!isINT(hid.priority.value))
	{
	    alert(putmsg(<% write(lang); %>,"优先级必须是整数!"));
		return false;
	}
	if((hid.priority.value<0||hid.priority.value>240)||((hid.priority.value%16)!=0))
	{
		alert(putmsg(<% write(lang); %>,"优先级应该在范围(0-240)之间并且是16的倍数!"));
		return false;
	}

	target.value = target.value.replace(/\s/g, "|");
	
	hid.submit();
	return true;
}
function showportcfg(portNo)
{
	window.location.href="mstpportcfg.asp?portNo="+portNo;
}
function showportcfg2(v)
{
	var portNum=document.webForm.portNum.value;
	window.location.href="mstpportcfg.asp?portNo="+portNum+"&fastoredgeIdx="+v;
}
function addToPortRange(obj)
{
	var target = document.getElementById("portNumm");
	var p = obj.value;
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);


	if(obj.checked)
	{
		//target.value = p ;
		target.value = target.value  + p + " ";

		document.webForm.pathcost.value=trobj.cells[2].innerHTML;
		document.webForm.priority.value=trobj.cells[3].innerHTML;
		document.webForm.linktype.value=trobj.cells[5].innerHTML;

		//document.webForm.fastoredge.value=trobj.cells[4].innerHTML;
		document.webForm.fastoredge.value="portfast";
		
		if(trobj.cells[4].innerHTML == "enable")
			document.getElementById("k1").checked = true;
		else 
			document.getElementById("k2").checked = true;
		
		if(trobj.cells[6].innerHTML == "RSTP")
		document.webForm.forceversion.value=2;
		else if(trobj.cells[6].innerHTML == "STP")
		document.webForm.forceversion.value=0;

		if(trobj.cells[7].innerHTML == "enable")
			document.getElementById("pf1").checked = true;
		else 
			document.getElementById("pf2").checked = true;

		if(trobj.cells[8].innerHTML == "enable")
			document.getElementById("en1").checked = true;
		else 
			document.getElementById("en2").checked = true;
	}
	else{

		target.value = target.value.replace(p+" ", "");
	}
	
	return true;
}

/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;


	if (cf.check_all.checked == true)
    {
		for (i = 0; i < objs.length; i++) 
        {    
        	if (objs[i].disabled==false && objs[i].checked==false)
		    {
            	objs[i].checked = true;  
				addToPortRange(objs[i]);
			}
        }
    }
    else
    {
        for (i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true)
			{
				objs[i].checked = false;  
			 	addToPortRange(objs[i]);
			}             
        }
    }     
}


function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm?name=mstpportcfg";
	tf.submit();
}

function refreshpage()
{
  location.href='mstpportcfg.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('mstpportcfg',<% write(lang); %>);
}


function P(aa,bb,cc,dd,ee,ff,en, pf)
{
    var narr=9;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+aa);	


    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		
	        tbtr.appendChild(tbtd);
    }
	            
	//tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+aa+"\" onclick=\"addToPortRange(this)\"/>";
	tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"checkbox_index\" value=\""+aa+"\" onchange=\"addToPortRange(this)\"/>";

		tbtr.cells[1].innerHTML = aa;
    tbtr.cells[2].innerHTML = bb;
    tbtr.cells[3].innerHTML = cc;
   // tbtr.cells[4].innerHTML = dd;
	
		if("disable"==dd)
   	 tbtr.cells[4].innerHTML = "disable";
	 else
   	 tbtr.cells[4].innerHTML = "enable";



    tbtr.cells[5].innerHTML = ee;
	if(2==ff)
   	 tbtr.cells[6].innerHTML = "RSTP";
	 else
   	 tbtr.cells[6].innerHTML = "STP";
	 
	if(2==pf)
   	 tbtr.cells[7].innerHTML = "enable";
	 else
   	 tbtr.cells[7].innerHTML = "disable";

	if("disable"==en)
   	 tbtr.cells[8].innerHTML = "disable";
	 else
   	 tbtr.cells[8].innerHTML = "enable";

}


</script>
</head>
<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setMstpPortcfg">


<input type="hidden" name="portIdx" value= >

<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td>

 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>spanning tree端口</b></font></td></tr>
 </table>
 
<!--
        <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
            <tr>
              <td colspan="2" align="left" class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"端口配置");</script></td>
            </tr>
        </table>
        -->

        </td>
      </tr>
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">
    
      <tr  height="25">
        <td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"端口列表");</script>:          </td>
        <td align="left" class="crons">&nbsp;
          			<input type="text" name="portNumm" id="portNumm" readonly="true"></td>
        <td align="left" class="crons">&nbsp;</td>
        <td align="left" class="crons">&nbsp;</td>
      </tr>
      
        <tr height="25">
              <td  width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"路径开销");</script>&nbsp;:</td>
              <td   width="30%" align="left" class="crons">&nbsp;
          			<input type="text" size=15 name="pathcost" value="200000">(1-200000000)               </td> 
              <td  width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"优先级");</script>&nbsp;:</td>
              <td   width="30%" align="left" class="crons">&nbsp;
          			<input type="text" name="priority" size=15 value="128">(0-240)               </td>
            </tr>
            <tr height="25"  style='display:none'>
             <td  width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"快速转发特性");</script>&nbsp;:</td>
            <td  align="left" class="crons" colspan="3">&nbsp;
            <select name="fastoredge" >
							<option value="portfast" selected>Portfast
							<option value="edgeport" >Edgeport
			  		</select>
              &nbsp;
              <!--
          			<input type="radio" name="fastoredgesw" value="enable" >Enable
          			<input type="radio" name="fastoredgesw" value="disable" checked>Disable               
          			--></td> 
            </tr>
            <tr height="25"  style='display:none'>
              <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"BPDU过滤");</script>&nbsp;:</td>
              <td   width="30%" align="left" class="crons">&nbsp;
          			<select name="bpdufilter">
									<option value="default" >Default
									<option value="disable" >Disable
									<option value="enable"  >Enable
          			</select>               </td> 
              <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"BPDU保护");</script>&nbsp;:</td>
              <td   width="30%" align="left" class="crons">&nbsp;
          			<select name="bpduguard">
									<option value="default" >Default
									<option value="disable" >Disable
									<option value="enable"  >Enable
          			</select>               </td>
            </tr>
            <tr height="25">
              <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"自动边界");</script>&nbsp;:</td>
              <td   width="30%" align="left" class="crons">&nbsp;
          			<input type="radio" name="autoedge" value="enable" id="k1" >Enable
          			<input type="radio" name="autoedge" value="disable"  id="k2"  checked>Disable               </td> 
              <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"链路类型");</script>&nbsp;:</td>
              <td   width="30%" align="left" class="crons">&nbsp;
          			<select name="linktype" >
          				<option value="shared" >Shared
									<option value="point-to-point" selected>Point To Point
          			</select>               </td> 
            </tr>
            <tr height="25">
              <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"协议版本");</script>&nbsp;:</td>
              <td   width="30%" align="left" class="crons">&nbsp;
          			<select name="forceversion" >
								<option value=0 >STP
								<option value=2 selected >RSTP
          			</select>               
          	  </td> 
              <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"portfast");</script>&nbsp;:</td>
              <td   width="30%" align="left" class="crons">&nbsp;
          			<input type="radio" name="fastoredgesw" value="enable" id="pf1" >Enable
          			<input type="radio" name="fastoredgesw" value="disable" id="pf2" checked>Disable               </td> 
          	  </td> 

          	  <tr height="25">

          	                <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"state");</script>&nbsp;:</td>
              <td   width="30%" align="left" class="crons">&nbsp;
          			<input type="radio" name="portstate" value="enable" id="en1" >Enable
          			<input type="radio" name="portstate" value="disable" id="en2" checked>Disable               </td> 
          	  </td> 
        <td align="left" class="crons">&nbsp;</td>
        <td align="left" class="crons">&nbsp;</td>
          	  </tr>
<!--          	  
              <td  align="left" class="crons">&nbsp;&nbsp;</td>
               <td   width="30%" align="left" class="crons">
               &nbsp;<div  style='display:none'>
          			<input type="radio" name="rootguard" value="enable" >Enable
          			<input type="radio" name="rootguard" value="disable" checked>Disable </div>               
          	</td>
-->          	
            </tr>
        </table></td>
      </tr>
    <tr>
    <td align="center" height="35">			 	 
    <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify","checkMessage()");</script>
    &nbsp;
    <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
<!--
      &nbsp;
	<script>writebutton(1,<% write(lang); %>,"帮  助","button","button","Help","showHelpinfo()");</script>
-->
	</td>
	</tr>
	
	<br>


			  <tr>
	    	<td colspan="4">
	    		<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
	    		 	<tr align="center" height="25" class="crons">
	    		 	<!--
	    		 		<th class="td2" width="10%"><font color="#333333"><b>&nbsp;</b></font></th>
	    		 		-->
	    		 		<th class="td2" width="10%"><input type="checkbox" name="check_all" value="all" onClick="selectToAll()" /></th>
	    		 		<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></b></font></th>
	    		 		<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"路径开销");</script></b></font></th>
	    		 		<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"优先级");</script></b></font></th>						
	    		 		<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"自动边界");</script></b></font></th>						
	    		 		<th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"链路类型");</script></b></font></th>						
	    		 		<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"协议版本");</script></b></font></th>						
	    		 		<th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"portfast");</script></b></font></th>	
	    		 		<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"state");</script></b></font></th>
	    		 	</tr>


		  <script>
		  		<%  var errorcode; showStpSetPort();%>
		  </script>
		  	    		</table>	    	</td>
   </tr>
        </table></td>
      </tr>
  			</table>
			</td></tr>
			
			
      <tr>
        <td height="8"></td>
      </tr>

    </table></td>
  </tr> 
</table>
</td></tr>
<tr><td>
</td></tr></table>
</form>
<script>
changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</body>
</html>
