<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<meta http-equiv="Content-Type" content="text/html; charset=utf8">
		<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
		<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
		<script language="javascript" type="text/javascript" src="js/alpinejs.min.js" defer></script>
		<link href="css/display_web.css" rel="stylesheet" type="text/css" />
		<title>&nbsp;
			<script>writemsg(<% write(lang); %>, "风暴抑制");</script>
		</title>

		<script language="JavaScript">
			var boardType = <% getSysCfg(); %>;
		</script>
		<script language="JavaScript" type="text/JavaScript">


/*by LuoM  11-5-18*/
function DataScope(value,max,min)
{
	if(!isNaN(value) && value>=min && value<=max)
		return true;
	else
		return false;
}


function getRadioCheckedValue(radio_object)   // value of selected item, "" if none
{
	var index = 0;
	if (!radio_object)
		return "";
	var size = radio_object.length;
	if(isNaN(size)) 
	{
		if (radio_object.checked == true)
			return radio_object.value;
		else 
			return ""; 
	}
	for (var i = 0; i < size; i++)
	{
		if(!(radio_object[i])) 
			continue;
		if (radio_object[i].checked == true)
			return(radio_object[i].value);
	}
	if (radio_object.checked == true)
		return radio_object.value;
	else 
		return ""; 
}

function setDisabled(OnOffFlag,formFields)
{
	for (var i = 1; i < setDisabled.arguments.length; i++)
		setDisabled.arguments[i].disabled = OnOffFlag;
}

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

 function selectToAllx() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true; 
			 addToPortRangeX(objs[i]);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRangeX(objs[i]);
			}
             
        }
    } 
    
}


function addToPortRangeX(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var target = document.getElementById("port_range");
	var broadcast = document.getElementById("broadcast");
	var multicast = document.getElementById("multicast");
	var unknow = document.getElementById("unknow");
	
	var checkbox_broadcast = document.getElementById("checkbox_broadcast");
	var checkbox_multicast = document.getElementById("checkbox_multicast");
	var checkbox_unknow = document.getElementById("checkbox_unknow");
	
	
	var p = obj.value;
	var i;
	if(obj.checked){	
		target.value = target.value  + p + " ";
		
		//alert(trobj.cells[2].innerHTML);
		//alert(trobj.cells[3].innerHTML);
		//alert(trobj.cells[4].innerHTML);
		
		
		if(trobj.cells[2].innerHTML == "Disable")
			checkbox_broadcast.checked = true;
		else
			broadcast.value=trobj.cells[2].innerHTML;
			
		if(trobj.cells[3].innerHTML == "Disable")
			checkbox_multicast.checked = true;
		else
			multicast.value=trobj.cells[3].innerHTML;
			
		if(trobj.cells[4].innerHTML == "Disable")
			checkbox_unknow.checked = true;
		else
			unknow.value=trobj.cells[4].innerHTML;
		
		
		
	}else{
		target.value = target.value.replace(p+" ","");
	}

}


function set_storm_enable()
{
	var cf = document.forms[0];
	var dflag = (getRadioCheckedValue(cf.storm_enable) == "disable");
	
	if((dflag == true) || (arguments[0] == "1"))
	  dflag = true;
	else
		dflag = false;
		setDisabled(dflag, cf.control_rate[0], cf.control_rate[1], cf.control_rate[2],cf.control_rate[3],cf.control_rate[4],cf.control_rate[5],cf.control_pkts,cf.control_pkts2,cf.warning_type,cf.way[0],cf.way[1]);
		
}
function StormOut(portId,Broadcast,Multicast,Unknow)
{
    var narr=5;
    var tbtd;
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port").insertRow(-1);
	
    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");
	tbtr.setAttribute("id", "tr_"+portId);
	
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"checkbox_index\" value=\""+portId+"\" onchange=\"addToPortRangeX(this)\"/>";
    tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = Broadcast;
    tbtr.cells[3].innerHTML = Multicast;
    tbtr.cells[4].innerHTML = Unknow;
}



function saveData()
{
	var tf=document.stormseting;
	tf.action = "/goform/saveComm?name=storm";
	tf.submit();
}

function stormJudgment()
{
	var st=document.stormseting;
	var broadcast = document.getElementById("broadcast").value;
	var multicast = document.getElementById("multicast").value;
	var unknow = document.getElementById("unknow").value;
	
		var checkbox_broadcast = document.getElementById("checkbox_broadcast");
	var checkbox_multicast = document.getElementById("checkbox_multicast");
	var checkbox_unknow = document.getElementById("checkbox_unknow");
	
	
	

	 
		if((DataScope(broadcast,100000,0)==true ||checkbox_broadcast.checked == true) && (DataScope(multicast,100000,0)==true||checkbox_multicast.checked == true) &&( DataScope(unknow,100000,0)==true||checkbox_unknow.checked == true))
		{
				if(checkbox_broadcast.checked == true)
					document.getElementById("broadcast").value= -1;
					
					if(checkbox_multicast.checked == true)
					document.getElementById("multicast").value= -1;
				if(checkbox_unknow.checked == true)
					document.getElementById("unknow").value= -1;
				
				
					//	alert(document.getElementById("broadcast").value);
					//	alert(document.getElementById("multicast").value);
					//	alert(document.getElementById("unknow").value);
		
			
			st.submit();
		}
		else
		{
			alert(putmsg(<% write(lang); %>,"取值范围为0-100000!"));
		}



}

function DelStorm()
{
	var st=document.stormseting;
	st.action= "/goform/stormClean";
	st.submit();
}

function refreshpage()
{
  location.href='storm.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('storm',<% write(lang); %>);
}



</script>
</head>

<body>
	<br>
	<% web_get_stat(); %>
		<% var authmode; checkCurMode(); %>
			<script>
				checktop(<% write(lang); %>);
				retValue = <% var responseJsonStr; jw_get_portStormConfig(); %>
					responseStr = <% write(responseJsonStr); %>;
				var headers = ["端口", "广播报文", "未知组播报文", "未知单播报文",]
				var selectOpt = JSON.parse(localStorage.getItem('portList')).map(item => item.portName)
				var tabDatas = {
					bcastPps: "",
					dlfBcastPps: "",
					mcastPps: "",
					portName: ""
				}
				function optSelect1() {
					for (let i = 0; i < responseStr.PortStorm.length; i++) {
						if (tabDatas.portName == responseStr.PortStorm[i].portName) {
							if (responseStr.PortStorm[i].bcastPps == "Disable") {
								window.form2.bcastPps1.checked = true
							} else {
								window.form2.bcastPps.value = responseStr.PortStorm[i].bcastPps
								window.form2.bcastPps1.checked = false
							}
							if (responseStr.PortStorm[i].dlfBcastPps == "Disable") {
								window.form2.dlfBcastPps1.checked = true
							} else {
								window.form2.dlfBcastPps.value = responseStr.PortStorm[i].dlfBcastPps
								window.form2.dlfBcastPps1.checked = false
							}
							if (responseStr.PortStorm[i].mcastPps == "Disable") {
								window.form2.mcastPps1.checked = true
							} else {
								window.form2.mcastPps.value = responseStr.PortStorm[i].mcastPps
								window.form2.mcastPps1.checked = false
							}
						}
					}
				}
				function checkMultipleOf16(number) {
					if (number % 16 === 0) {
						return number
					} else {
						const closestMultiple = Math.round(number / 16) * 16;
						return closestMultiple;
					}
				}
				function change1() {
					if (!window.form2.mcastPps1.checked) {
						if (checkMultipleOf16(window.form2.mcastPps.value) > 1000000) {

							window.form2.mcastPps.value = 1000000
						} else {
							window.form2.mcastPps.value = checkMultipleOf16(window.form2.mcastPps.value)
						}

					}
					if (!window.form2.dlfBcastPps1.checked) {
						if (checkMultipleOf16(window.form2.dlfBcastPps.value) > 1000000) {

							window.form2.dlfBcastPps.value = 1000000
						} else {
							window.form2.dlfBcastPps.value = checkMultipleOf16(window.form2.dlfBcastPps.value)
						}

					}
					if (!window.form2.bcastPps1.checked) {
						console.log(checkMultipleOf16(window.form2.bcastPps.value),123)
						if (checkMultipleOf16(window.form2.bcastPps.value) > 1000000) {
							window.form2.bcastPps.value = 1000000
						}
						else {
							window.form2.bcastPps.value = checkMultipleOf16(window.form2.bcastPps.value)
						}

					}
				}
				function apply1() {
					if (tabDatas.portName == '') {
						alert('请选择端口')
						return
					}
					var obj = {
						pageName: "storm.asp",
						PortStorm: [
							{
								portName: tabDatas.portName,
								bcastPps: '',
								mcastPps: '',
								dlfBcastPps: ''
							}
						]
					}
					if (window.form2.bcastPps1.checked) {
						obj.PortStorm[0].bcastPps = 'Disable'
					} else {
						if (window.form2.bcastPps.value == '') {
							obj.PortStorm[0].bcastPps = 'Disable'
						} else {
							// if (window.form2.bcastPps.value % 16 != 0) {
							// 	alert('广播报文必须是16的倍数')
							// 	return
							// }
							obj.PortStorm[0].bcastPps = window.form2.bcastPps.value
						}
					}
					if (window.form2.mcastPps1.checked) {
						obj.PortStorm[0].mcastPps = 'Disable'
					} else {
						if (window.form2.mcastPps.value == '') {
							obj.PortStorm[0].mcastPps = 'Disable'
						} else {
							// if (window.form2.mcastPps.value % 16 != 0) {
							// 	alert('未知组播报文必须是16的倍数')
							// 	return
							// }
							obj.PortStorm[0].mcastPps = window.form2.mcastPps.value
						}
					}
					if (window.form2.dlfBcastPps1.checked) {
						obj.PortStorm[0].dlfBcastPps = 'Disable'
					} else {
						if (window.form2.dlfBcastPps.value == '') {
							obj.PortStorm[0].dlfBcastPps = 'Disable'
						} else {
							// if (window.form2.dlfBcastPps.value % 16 != 0) {
							// 	alert('未知单播报文必须是16的倍数')
							// 	return
							// }
							obj.PortStorm[0].dlfBcastPps = window.form2.dlfBcastPps.value
						}
					}
					var tf = document.stormseting;
					tf.param1.value = JSON.stringify(obj)
					tf.action = "/goform/jw_set_portStormConfig";
					tf.submit();
				}
			</script>
			<div class="formContain">
				<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td height="30px">
							<font size="5" color="#0069d6">
								<div class="bot">风暴抑制显示</div>
							</font>
						</td>
					</tr>
				</table>
				<div x-data="{}">
					<table width="96%" class="tablebord" align="center" border="0" cellspacing="0" cellpadding="0">
						<thead>
							<tr>
								<template x-for="(item,index) in headers" :key="index">
									<th class="all_tables" x-text="item"></th>
								</template>
							</tr>
						</thead>
						<tbody>
							<template x-for="(row,rowIndex) in responseStr.PortStorm">
								<tr style="text-align: center;">
									<td style="font-weight: normal" class="all_tables" x-text="row.portName"></td>
									<td style="font-weight: normal" class="all_tables" x-text="row.bcastPps"></td>
									<td style="font-weight: normal" class="all_tables" x-text="row.mcastPps"></td>
									<td style="font-weight: normal" class="all_tables" x-text="row.dlfBcastPps"></td>
								</tr>
							</template>
						</tbody>
					</table>
				</div>
			</div>
			<br /><br />
			<form action="" name="form2" class="formContain">
				<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td height="30px">
							<font size="5" color="#0069d6">
								<div class="bot">风暴抑制配置</div>
							</font>
						</td>
					</tr>
				</table>
				<div x-data="tabDatas">
					<table width="96%" class="tablebord" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td width="42%" class="all_tables all_tables1">端口选择:</td>
							<td class="all_tables all_tables2" style="text-align: left;">
								<select x-model="portName" @change="optSelect1">
									<option value="" disabled></option>
									<template x-for="(opt,optIndex) in selectOpt" :key="optIndex">
										<option x-text="opt"></option>
									</template>
								</select>
							</td>
						</tr>
						<tr>
							<td class="all_tables all_tables1">广播报文:</td>
							<td class="all_tables all_tables2" style="text-align: left; font-weight: normal;">
								<label>
									<input name="bcastPps1" type="checkbox" />
									禁用
								</label>
								<input name="bcastPps" type="number" @change="change1"/>
								(kbits,16倍数,最大1000000)
							</td>
						</tr>
						<tr>
							<td class="all_tables all_tables1">未知组播报文:</td>
							<td class="all_tables all_tables2" style="text-align: left; font-weight: normal;">
								<label>
									<input name="mcastPps1" type="checkbox" />
									禁用
								</label>
								<input name="mcastPps" type="number" @change="change1" />
								(kbits,16倍数,最大1000000)
							</td>
						</tr>
						<tr>
							<td class="all_tables all_tables1">未知单播报文:</td>
							<td class="all_tables all_tables2" style="text-align: left; font-weight: normal;">
								<label>
									<input name="dlfBcastPps1" type="checkbox" />
									禁用
								</label>
								<input name="dlfBcastPps" type="number" @change="change1" />
								(kbits,16倍数,最大1000000)
							</td>
						</tr>
						<tr>
							<td class="all_tables"></td>
							<td class="all_tables" style="text-align: left;">
								<input type="button" class="inpBtn1" value="添加" @click="apply1" />
							</td>
						</tr>
					</table>
				</div>
			</form>
			<form name="stormseting" method="POST" action="/goform/stromComm">
				<input type="hidden" name="param1" id="param1">
				<input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
				<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			</form>
			<script>
<% if (errorcode != "") { write_errorcode(errorcode); } %>
			</script>

</body>

</html>