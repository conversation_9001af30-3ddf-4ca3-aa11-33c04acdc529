<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"端口管理");</script></title>
<script>

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function changebgcolor3(){
 var tab = document.all.table3;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function MSTPCISTBRIBASEINFO(v1, v2, v3, v4, v5, v6, v7, v8, v9)
{
	document.write("<tr height='30'><td align=left' class='crons'>"+v2+"</td><td align=left' class='crons'>"+v3+"</td><td align=left' class='crons'>"+v4+"</td><td align=left' class='crons'>"+v5+"</td><td align=left' class='crons'>"+v6+"</td><td align=left' class='crons'>"+v8+"</td><td align=left' class='crons'>"+v9+"</td></tr>");
	return ;
}



function P(m1,m2,m3,m4)
{
    var narr=4;
    var tbtd;
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table3").insertRow(-1);
	
    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");
	//tbtr.setAttribute("id", "tr_"+portId);
	
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	
	
    tbtr.cells[0].innerHTML = m1;
    tbtr.cells[1].innerHTML = m2;
    tbtr.cells[2].innerHTML = m3;
    tbtr.cells[3].innerHTML = m4;

}





function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm";
	tf.submit();
}

function refreshpage()
{
  location.href='ring_port.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('ring_port',<% write(lang); %>);
}

</script>
</head>
<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setRingPortCfg">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0"><tr><td>
	<table width="98%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" ><tr><td valign="top" >
		<table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
	      <tr>
	        <td>
				 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
					<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>RING/RING信息</b></font></td></tr>
				 </table>

	        </td>
	      </tr>     
    
		    </td>
		  </tr> 
		 
		  <tr>
		    <td valign="top" >
			
			<br>
	       
	<table width="100%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table3"  >
		<TR align="center" height=22>
		<td class="all_tables_list" width="14%"  align="center">端口</td>
		<td width="14%"  class="all_tables_list">状态</td>
		<td width="14%"  class="all_tables_list">模式</td>
		<td width="14%"  class="all_tables_list">转发状态</td>

		</TR>
		<script>
		<% RingPortShow(); %>
		</script>
	</table>		
			
			
			
	</td></tr>
  		<tr>
	        <td align="center" height="35"><script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
		        &nbsp;
		        &nbsp;
		        </td>
	      </tr>
        </table>
     </td></tr></table>
</form>
<br>


<script>

changebgcolor3();

</script>


</body>
</html>
