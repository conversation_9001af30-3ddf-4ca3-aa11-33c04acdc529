// Help and Message ==========================================================


function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function addstr(input_msg)
{
	var last_msg = "";
	var str_location;
	var temp_str_1 = "";
	var temp_str_2 = "";
	var str_num = 0;
	temp_str_1 = addstr.arguments[0];
	while(1)
	{
		str_location = temp_str_1.indexOf("%s");
		if(str_location >= 0)
		{
			str_num++;
			temp_str_2 = temp_str_1.substring(0,str_location);
			last_msg += temp_str_2 + addstr.arguments[str_num];
			temp_str_1 = temp_str_1.substring(str_location+2,temp_str_1.length);
			continue;
		}
		if(str_location < 0)
		{
			last_msg += temp_str_1;
			break;
		}
	}
	return last_msg;
}

//  High-level test functions - generate messages

function checkBlank(fieldObj, fname)
{
	var msg = "";
	if (fieldObj.value.length < 1){
		msg = addstr(msg_blank,fname);
        }
	return msg;
}

function checkNoBlanks(fObj, fname)
{
	var space = " ";
 	if (fObj.value.indexOf(space) >= 0 )
		return addstr(msg_space, fname);
	else return "";
}

function checkMail(fobj, fname)
{  
   var tmp_str = fobj.value;
   var msg = "";

   //matching Email address format(regular expression)
   var pattern = /^\w+([-+.]\w+)*@\w+([-.]\\w+)*\w+([-.]\w+)*$/;
   
   if(!pattern.test(tmp_str))
     msg = addstr(msg_invalid_email, fname);
   
   return msg;
} 

function checkAllSpaces(fieldObj, fname)
{
	var msg = "";
	if(fieldObj.value.length == 0)
		return "";
	var tstr = makeStr(fieldObj.value.length," ");
	if (tstr == fieldObj.value)
		msg = addstr(msg_allspaces,fname);
	return msg;
}

function checkValid(text_input_field, field_name, Valid_Str, max_size, mustFill)
{
	var error_msg= "";
	var size = text_input_field.value.length;
	var str = text_input_field.value;

	if ((mustFill) && (size != max_size) )
		error_msg = addstr(msg_blank_in,field_name);
 	for (var i=0; i < size; i++)
  	{
    	if (!(Valid_Str.indexOf(str.charAt(i)) >= 0))
    	{
			error_msg = addstr(msg_invalid,field_name,Valid_Str);
			break;
    	}
  	}
  	return error_msg;
}

function checkInt(text_input_field, field_name, min_value, max_value, required)
// NOTE: Doesn't allow negative numbers, required is true/false
{
	var str = text_input_field.value;
	var error_msg= "";

	if (text_input_field.value.length==0) // blank
	{
		if (required)
			error_msg = addstr(msg_blank,field_name);
	}
	else // not blank, check contents
	{
		for (var i=0; i < str.length; i++)
		{
			if ((str.charAt(i) < '0') || (str.charAt(i) > '9'))
				error_msg = addstr(msg_check_invalid,field_name);
		}
		if (error_msg.length < 2) // don't parse if invalid
		{
			var int_value = parseInt(str,10);
			if (int_value < min_value || int_value > max_value)
				error_msg = addstr(msg_valid_range,field_name,min_value,max_value);
		}
	}
	return(error_msg);
}

function checkMAC(fObj, fname, removeSeparators)
{
	var msg = "";
	if(badMac(fObj, removeSeparators))
		msg = addstr(msg_invalid_mac, fname);
	return msg;
}	


// Low-level test functions - return true or false ============================


function blankIP(ip1, ip2, ip3, ip4) // ip fields, true if 0 or blank
{
return ((ip1.value == "" || ip1.value == "0")
	 && (ip2.value == "" || ip2.value == "0")
	 && (ip3.value == "" || ip3.value == "0")
	 && (ip4.value == "" || ip4.value == "0"))
}

function badIP(ip1, ip2, ip3, ip4, max)   // ip fields, ******* to 254.255.255.max
{
	if(!(isInteger(ip1.value,1,254,false))) return true;
	if(!(isInteger(ip2.value,0,255,false))) return true;
	if(!(isInteger(ip3.value,0,255,false))) return true;
	if(!(isInteger(ip4.value,1,max,false))) return true;
   	return false;
}
function badSubnetIP(ip1, ip2, ip3, ip4, max)   // ip fields *******. to 255.255.255.max
{
	if(!(isInteger(ip1.value,1,254,false))) return true;
	if(!(isInteger(ip2.value,0,255,false))) return true;
	if(!(isInteger(ip3.value,0,255,false))) return true;
	if(!(isInteger(ip4.value,0,max,false))) return true;
   	return false;
}


function badMask(ip1, ip2, ip3, ip4)   // mask fields 0 to 255
{
	if(!(isInteger(ip1.value,0,255,false))) return true;
	if(!(isInteger(ip2.value,0,255,false))) return true;
	if(!(isInteger(ip3.value,0,255,false))) return true;
	if(!(isInteger(ip4.value,0,255,false))) return true;
   	return false;
}


function badMac(macfld, removeSeparators) // macfld is form field, removeSeparators true/false
{
	var myRE = /[0-9a-fA-F]{12}/;
	var MAC = macfld.value;	
	
	MAC = MAC.replace(/:/g,"");
	MAC = MAC.replace(/-/g,"");
	if (removeSeparators)
		macfld.value = MAC;	
	if((MAC.length != 12) || (MAC == "000000000000")||(myRE.test(MAC)!=true))
		return true;
	else
	 	return false;
}

function ValidMacAddress(macAddr)
{
//	alert("ValidMacAddress(): Use badMac(macfld, removeSeparators) instead!");
//	return;
	
	var i;
	if ((macAddr.indexOf(':')!=-1)||(macAddr.indexOf('-')!=-1))
	{     
        macAddr = macAddr.replace(/:/g,"");
		macAddr = macAddr.replace(/-/g,"");
	}
	
	if ((macAddr.length == 12) && (macAddr != "000000000000"))
	{
		for(i=0; i<macAddr.length;i++) 
		{
			var c = macAddr.substring(i, i+1);
			if(("0" <= c && c <= "9") || ("a" <= c && c <= "f") || ("A" <= c && c <= "F")) 
				continue;
			else
				return false;
		}

		return true;
	}

	return false;	  
}



function badIpRange(from1,from2,from3,from4,to1,to2,to3,to4)
// parameters are form fields, returns true if invalid ( from > to )
{
    var total1 = 0;
    var total2 = 0;
    
    total1 += parseInt(from4.value,10);
    total1 += parseInt(from3.value,10)*256;
    total1 += parseInt(from2.value,10)*256*256;
    total1 += parseInt(from1.value,10)*256*256*256;
    
    total2 += parseInt(to4.value,10);
    total2 += parseInt(to3.value,10)*256;
    total2 += parseInt(to2.value,10)*256*256;
    total2 += parseInt(to1.value,10)*256*256*256;
    if(total1 >= total2)
        return true;
    return false;
}


function isBlank(str) 
{
	return (str.length == 0 );
}


function isBigger(str_a, str_b)
//  true if a bigger than b
{
	var int_value_a = parseInt(str_a);
	var int_value_b = parseInt(str_b);
	return (int_value_a > int_value_b);
}

function isInteger(str,min_value,max_value,allowBlank)  // allowBlank = true or false
// return true if positive Integer, false otherwise
{
	if(str.length == 0)
		if(allowBlank)
			return true;
		else
			return false;
	for (var i=0; i < str.length; i++)
	{
		if ((str.charAt(i) < '0') || (str.charAt(i) > '9'))
				return false;
	}
	var int_value = parseInt(str,10);
	if ((int_value < min_value) || (int_value > max_value))
		return false;
	return true;
}


function isHex(str) {
    var i;
    for(i = 0; i<str.length; i++) {
        var c = str.substring(i, i+1);
        if(("0" <= c && c <= "9") || ("a" <= c && c <= "f") || ("A" <= c && c <= "F")) {
            continue;
        }
        return false;
    }
    return true;
}

function CheckSpaceInName(text_input_field)
//not allow space in name,
{
	if (text_input_field.value.length>1)
	{
		for (var i=0;i<text_input_field.value.length;i++)
		{
			if (text_input_field.value.charAt(i) == ' ')
				return false;
		}
	}
	return true;
}

// Utility & Misc functions ===================================================
//false: valide ip address, true: invalid ip address
function checkIPAddress(ipbox, max, bAllowBlank)
{
	if(bAllowBlank == true)
	{
		if((eval("document.forms[0]."+ipbox+"1").value == "0"||eval("document.forms[0]."+ipbox+"1").value == "")
		&& (eval("document.forms[0]."+ipbox+"2").value == "0"||eval("document.forms[0]."+ipbox+"2").value == "")
		&& (eval("document.forms[0]."+ipbox+"3").value == "0"||eval("document.forms[0]."+ipbox+"3").value == "")
		&& (eval("document.forms[0]."+ipbox+"4").value == "0"||eval("document.forms[0]."+ipbox+"4").value == ""))
		     return false;  
	}
	
	return check_vip(	eval("document.forms[0]."+ipbox+"1"),
					eval("document.forms[0]."+ipbox+"2"),
					eval("document.forms[0]."+ipbox+"3"),
					eval("document.forms[0]."+ipbox+"4"), max);
}

function check_vip(ip1, ip2, ip3, ip4, max) {
    if(checkIPMain(ip1,255)) return true; 
    if(checkIPMain(ip2,255)) return true;
    if(checkIPMain(ip3,255)) return true;
    if(checkIPMain(ip4,max)) return true;
    if((parseInt(ip1.value)==0)||(parseInt(ip1.value)==0)&&(parseInt(ip2.value)==0)&&(parseInt(ip3.value)==0)&&(parseInt(ip4.value)==0))
    	return true;
    return false;
}

/* Check IP Address Format*/
function checkIPMain(ip,max) 
{
    if( false == isNumeric(ip, max) ) 
    {
        ip.focus();
        return true;
    }
    
    return false;
}

/* Check Numeric*/
function isNumeric(str, max) {
		if(str.value.length <= 3){
				str.value = str.value.replace(/^000/g,"0");
				str.value = str.value.replace(/^00/g,"0");
				if(str.value.length > 1)
						str.value = str.value.replace(/^0/g,"");
		}
		
    if(str.value.length == 0 || str.value == null || str.value == "") {
        str.focus();
        return false;
    }
    
    var i = parseInt(str.value);
    
    if(i>max) {
        str.focus();
        return false;
    }
    for(i=0; i<str.value.length; i++) {
        var c = str.value.substring(i, i+1);
        if("0" <= c && c <= "9") {
            continue;
        }
        str.focus();
        return false;
    }
    return true;
}


function setDisabled(OnOffFlag,formFields)
{
	for (var i = 1; i < setDisabled.arguments.length; i++)
		setDisabled.arguments[i].disabled = OnOffFlag;
}

var showit = "block";
var hideit = "none";

function show_hide(el,shownow)  // IE & NS6; shownow = true, false
{
//	alert("el = " + el);
	if (document.all)
		document.all(el).style.display = (shownow) ? showit : hideit ;
	else if (document.getElementById)
		document.getElementById(el).style.display = (shownow) ? showit : hideit ;
}

function isIP(strIP) 
{
       var re=/^(\d+)\.(\d+)\.(\d+)\.(\d+)$/g;
	   re.lastIndex = 0;
       if(re.test(strIP))
       {
            if(RegExp.$1 >0 && RegExp.$1 <255 && RegExp.$2>=0 && RegExp.$2<256 && RegExp.$3>=0 && RegExp.$3<256 && RegExp.$4>0 && RegExp.$4<255) return true;
       }
       
       return false; 
}

function check_ip(fobj) 
{ 	
		var tmp = fobj.value;
    var ip = new RegExp("^([0-9]+).([0-9]+).([0-9]+).([0-9]+)$");
    if (tmp.match(ip) == null)
    		return false;
	
    var ipaddr = tmp.split(".");
    if(ipaddr[0] >0 && ipaddr[0] <255 && ipaddr[1]>=0 && ipaddr[1]<=255 && ipaddr[2]>=0 && ipaddr[2]<=255 && ipaddr[3]>0 && ipaddr[3]<255) 
        return true;
   
    return false; 
}

function check_mask(fobj) 
{ 	
		var tmp = fobj.value;
    var ip = new RegExp("^([0-9]+).([0-9]+).([0-9]+).([0-9]+)$");
    if (tmp.match(ip) == null)
    		return false;
	
    var ipaddr = tmp.split(".");  
    if(ipaddr[0] >0 && ipaddr[0] <=255 && ipaddr[1]>=0 && ipaddr[1]<=255 && ipaddr[2]>=0 && ipaddr[2]<=255 && ipaddr[3]>=0 && ipaddr[3]<=255) 
        return true;
   
    return false; 
}

function check_intf(fobj) 
{ 	
    var tmp = fobj.value;
    var ip = new RegExp("^[0-9]{1,4}$");
    if (tmp.match(ip) == null)
    		return false;
	
    if(parseInt(tmp,10)>0 && parseInt(tmp,10)<=4000) 
        return true;
   
    return false; 
}


function isMulticastMac(mac)
{
	var head = mac.substring(0,2);
	if(parseInt(head,10)%2 ==1)
		return true;
	return false;
}

function isUnicastMac(mac)
{
	var head = mac.substring(0,2);
	if(parseInt(head,10)%2 ==0)
		return true;
	return false;
}

var selectedColor = "#fb8615";

function addItem() 
{
    var cf = document.forms[0];
    var tbl = document.getElementById(arguments[0]);
    
    if(tbl.rows.length > (parseInt(arguments[1],10))) 
    {
        alert(addstr(msg_max_number, arguments[1]));  
        return false;
    }
 
    if(checkForm())
    {
       if(false == formData2TableData("add", 0))  
            return false;
       else     
            return true;
    }
    
    return false; 
}

function check_item_rp()
{
    var cf = document.forms[0];
    var tbname = arguments[0];
    var tbl = document.getElementById(tbname);
    
    for(i = 1; i < tbl.rows.length; i++)
    {
        var repeate = true;
        
        for(var j = 1; j < (arguments.length - 1); j++) 
        {    
        	if(tbl.rows[i].cells[j].innerHTML != arguments[j])
        	{
        	    repeate = false;  
        	    break;
            }
        }
        
        if(true == repeate)
        {
            alert(addstr(arguments[arguments.length-1], i));
            return  false;  
        }            
    } 
       
    return true; 
}

function add_item()
{
    var cf = document.forms[0];
    var tbname = arguments[0];
    var tbl = document.getElementById(tbname);
    
    var rowsLen = tbl.rows.length;
    var row = tbl.insertRow(rowsLen);   
    if(rowsLen % 2 == 0)
       row.style.background = "#FFE1B5";
    else
       row.style.background = "#FFFFFF"; 
       
    row.setAttribute("height", "30");
    row.style.color = "black";
    row.setAttribute("align", "center");
   // row.style.font-weight = "200";
    var cell;
    var textNode; 
    
  //  var chkbox= document.createElement("td");
  //  chkbox.innerHTML = "<input type='checkbox' name='checkbox_index' value='" + rowsLen + "'/>";
    
    var chkbox = document.createElement('input');
    chkbox.type = 'checkbox';
    chkbox.name = 'checkbox_index';
    chkbox.value = rowsLen;
    cell = row.insertCell(0);
    cell.appendChild(chkbox);
    cell.className = "td2";
    row.appendChild(cell); 
    var temp = row.style.background;
         
    for(var i = 1; i < arguments.length; i++) 
    {    
    	textNode = document.createTextNode(arguments[i]);
        cell = row.insertCell(-1);
        cell.appendChild(textNode);
        cell.className = "td2";
        row.appendChild(cell);  
    }

       row.onclick = function() {
        irowClick(this, tbname);      
     };      
}


function irowClick(obj, tbname) {
    var cf = document.forms[0];
    var tbl = document.getElementById(tbname);
    var findindex, num = 0;
  
    selectedRowIndex = obj.rowIndex;

    if(cf.checkbox_index.length == undefined)
    {
        if(cf.checkbox_index.checked == true)
        {
            obj.style.background = selectedColor; 
            tableData2FormData(1);
        }
        else
           obj.style.background = "#FFFFFF"; 
        return;
    }

    var indexcon = tbl.rows[selectedRowIndex].cells[0].innerHTML;
    var splitcode = indexcon.indexOf('"');
    if(splitcode > 0)
        findindex = indexcon.split('"');
    else
        findindex = indexcon.split('=');

    for(var vindex = 0; vindex < findindex.length; vindex++)   
    {
        if(findindex[vindex].indexOf('value') >= 0)
        {
            var findex = parseInt(findindex[++vindex], 10);
            break;
        }    
    } 
    
    if(document.title == "端口管理")
    {
        if(cf.checkbox_index[findex - 2].checked == false) 
        {    
            if(selectedRowIndex % 2 == 0)
                obj.style.background = "#FFE1B5";
            else
                obj.style.background = "#FFFFFF";
            tableData2FormData(-1);
        }
        else
        {
            obj.style.background = selectedColor; 
            selectedAclIndex = cf.checkbox_index[selectedRowIndex - 2].value;
            tableData2FormData(selectedAclIndex);
        }
    }
    else
    {
        if(cf.checkbox_index[findex - 1].checked == false) 
        {    
            if(selectedRowIndex % 2 == 0)
                obj.style.background = "#FFE1B5";
            else
                obj.style.background = "#FFFFFF";
        }
        else
        {
            obj.style.background = selectedColor; 
            selectedAclIndex = cf.checkbox_index[selectedRowIndex - 1].value; 
            tableData2FormData(selectedAclIndex);
        }
    }         
}


function editItem()
{
    var cf = document.forms[0];
    var tbname = arguments[0];
    var tbl = document.getElementById(tbname);
    
    var num = 0;
    var chk_index = new Array();
    
    if(tbl.rows.length == 1)
    {
        //alert("列表为空，没有可修改的记录！");
        alert(msg_acl_no_edit_entry);
        return false;
    } 
      
    if(cf.checkbox_index.length == undefined)
    {
        if(cf.checkbox_index.checked == true)
        {
            chk_index[0] = 1;
            num = 1;
        }
        
    }
    else
    {
        for(i = 1; i < tbl.rows.length; i++)
        {
            var indexcon = tbl.rows[i].cells[0].innerHTML;
            var splitcode = indexcon.indexOf('"');
            if(splitcode > 0)
                findindex = indexcon.split('"');
            else
                findindex = indexcon.split('=');
                
            for(var vindex = 0; vindex < findindex.length; vindex++)   
            {
                if(findindex[vindex].indexOf('value') >= 0)
                {
                    var findex = parseInt(findindex[++vindex], 10);
                    break;
                }    
            } 

            if(cf.checkbox_index[findex - 1].checked == true)
            {
                chk_index[num] = i;
                num++;
            }
        }
    }
   
        
    if(num == 0)
    {
         //alert("请选择一条记录!");
         alert(msg_acl_choose_entry);
         return false;
    }
    
    if(num > 1)
    {
         //alert("一次只能选择一条记录修改!");
         alert( msg_acl_edit);
         return false;
    }
    
    if(checkForm())
    {
        if(false == formData2TableData("edit", chk_index[0]))  
            return false;
        else     
            return true;
    }
    
    return false;    
}

function edit_item()
{
    var i;
    var tbname = arguments[0];

    var tbl = document.getElementById(tbname);
    var tr = parseInt(arguments[1],10);
  
    for(i = 1; i < (arguments.length-1); i++)  
        tbl.rows[tr].cells[i].innerHTML = arguments[i+1];    
}

function delItem()
{
    var cf = document.forms[0];
    var tbname = arguments[0];
    var tbl = document.getElementById(tbname);
    
    var num = 0, lnum = 0;
    var chk_index = new Array();
    
    if(tbl.rows.length - 1 == 0)
    {
        //alert("列表为空，没有可删除的记录！");
        alert(msg_acl_no_del_entry);
        return false;
    }
    
    if(cf.checkbox_index.length == undefined)
    {
        if(cf.checkbox_index.checked == true)
        {
            chk_index[0] = 1;
            num = 1;
        }
        
    }
    else
    {
        for(i = 1; i < tbl.rows.length; i++)
        {
            var indexcon = tbl.rows[i].cells[0].innerHTML;
            var splitcode = indexcon.indexOf('"');
            if(splitcode > 0)
                findindex = indexcon.split('"');
            else
                findindex = indexcon.split('=');
                
            for(var vindex = 0; vindex < findindex.length; vindex++)   
            {
                if(findindex[vindex].indexOf('value') >= 0)
                {
                    var findex = parseInt(findindex[++vindex], 10);
                    break;
                }    
            } 

            if(cf.checkbox_index[findex - 1].checked == true)
            {
                chk_index[num] = i;
                num++;
            }
        }
    }
            
    if(num == 0)
    {
         //alert("请选择一条记录!");
         alert(msg_acl_choose_entry);
         return false;
    }

    for(i = num-1; i >= 0; i--)
      tbl.deleteRow(chk_index[i]); 

   for(i = 1; i < tbl.rows.length; i++)
   {
   	   tbl.rows[i].cells[1].innerHTML = i;
       if(i % 2 == 0)
         tbl.rows[i].style.background = "#FFE1B5";
       else
         tbl.rows[i].style.background = "#FFFFFF"; 
       
       if(cf.checkbox_index.length == undefined) 
         cf.checkbox_index.value = 1;
       else
         cf.checkbox_index[i-1].value = i;
   }

   cf.check_all.checked = false;
   
   return true;
}


function selectAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByTagName("input"); 
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if((objs[i].type.toLowerCase() == "checkbox") && objs[i].disabled==false )
             objs[i].checked = true;  
        }
        setColorAll(arguments[0], true);
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].type.toLowerCase() == "checkbox" )
             objs[i].checked = false;  
        }
        setColorAll(arguments[0], false);
    } 
    
}

function setColorAll(tbname, flag)
{
    var cf = document.forms[0];
    var tbl = document.getElementById(tbname);
    if(document.title == "端口管理")
        var len = tbl.rows.length - 4;
    else
        var len = tbl.rows.length;
    
    if(flag)
    {   
      for(i = 1; i < len; i++)         
        tbl.rows[i].style.background = selectedColor;    
    }
    else
    {
      for(i = 1; i < len; i=i+2)
        tbl.rows[i].style.background = "#FFFFFF";  
      for(i = 2; i < len; i=i+2)
        tbl.rows[i].style.background = "#FFE1B5"; 
    }
}


function insertItem()
{
    var cf = document.forms[0];
    var tbname = arguments[0];
    var tbl = document.getElementById(tbname);
    var num = 0;
    var chk_index = new Array();
    
    if(tbl.rows.length - 1 == 0)
    {
        alert(msg_acl_no_insert_entry);
        return false;
    } 
      
    if(cf.checkbox_index.length == undefined)
    {
        if(cf.checkbox_index.checked == true)
        {
            chk_index[0] = 1;
            num = 1;
        }
        
    }
    else
    {
       for(i = 1; i < tbl.rows.length; i++)
       {
            var indexcon = tbl.rows[i].cells[0].innerHTML;
            var splitcode = indexcon.indexOf('"');
            if(splitcode > 0)
                findindex = indexcon.split('"');
            else
                findindex = indexcon.split('=');
                      
            for(var vindex = 0; vindex < findindex.length; vindex++)   
            {
                if(findindex[vindex].indexOf('value') >= 0)
                {
                    var findex = parseInt(findindex[++vindex], 10);
                    break;
                }    
            } 

            if(cf.checkbox_index[findex - 1].checked == true)
            {
                chk_index[num] = i;
                num++;
            }
       }
    }
   
        
    if(num == 0)
    {
         alert(mag_acl_insert_place);
         return false;
    }
    
    if(num > 1)
    {
         alert(msg_acl_insert_more);
         return false;
    }
    
    if(checkForm())
    {
        if(false == formData2TableData("insert", chk_index[0]))  
            return false;

        for(i = 1; i < tbl.rows.length; i++)
        {
           if(cf.checkbox_index.length == undefined) 
           {
             cf.checkbox_index.value = 1;
           }
           else
             cf.checkbox_index[i-1].value = i;
        }

        for(i = 1; i < tbl.rows.length; i++)
        {
            var indexcon = tbl.rows[i].cells[0].innerHTML;
            
            var splitcode = indexcon.indexOf('"');
            if(splitcode > 0)
                findindex = indexcon.split('"');
            else
                findindex = indexcon.split('=');
        
            for(var vindex = 0; vindex < findindex.length; vindex++)   
            {
                if(findindex[vindex].indexOf('value') >= 0)
                {
                    var findex = parseInt(findindex[++vindex], 10);
                    break;
                }    
            }            

            if(cf.checkbox_index[findex - 1].checked == true)
            {
                tbl.rows[i].style.background = selectedColor;
            }else if(i % 2 == 0)
               tbl.rows[i].style.background = "#FFE1B5";
            else
                tbl.rows[i].style.background = "#FFFFFF";
        }
        
        return true;
    }
    
    return false;    
}

function insert_item()
{
    var cf = document.forms[0];
    var tbname = arguments[0];
    var tbl = document.getElementById(tbname);
    
    var rowsLen = parseInt(arguments[1], 10);
    var row = tbl.insertRow(rowsLen);   
    if(rowsLen % 2 == 0)
      row.style.background = "#FFE1B5";
    else
       row.style.background = "#FFFFFF"; 
       
    row.setAttribute("height", "22");
    row.style.color = "black";
    row.setAttribute("align", "center");
   // row.style.font-weight = "200";
    var cell;
    var textNode; 
    
    var chkbox= document.createElement("td");
    chkbox.innerHTML = "<input type='checkbox' name='checkbox_index' value='" + rowsLen + "'/>";
    cell = row.insertCell(0);
    cell.appendChild(chkbox);
    row.appendChild(cell); 
    var temp = row.style.background;
         
    for(var i = 2; i < arguments.length; i++) 
    {    
    	textNode = document.createTextNode(arguments[i]);
        cell = row.insertCell(-1);
        cell.appendChild(textNode);
        row.appendChild(cell);  
    }
        
     row.onclick = function() {
        irowClick(this, tbname);      
    };
}

function replacePos(strObj, start, end)
{
	var i, replaceTxt = "";
	
	if((start>strObj.length) || (end>strObj.length))
	  return -1;
	  
	for(i=0; i<=(end-start); i++)
	  replaceTxt += "1";
	  
  var str = strObj.substr(0, start-1) + replaceTxt + strObj.substring(end, strObj.length);
  
  return str;
}

function check_range(str, minNumber, maxNumber)  
{	 	
    //只能是数字，"-", ","  
    var reg1 = /^(\d|-|\,)*$/;
    if(!reg1.exec(str))
    {
        alert(msg_range_invalid + minNumber + "至" + maxNumber);
        return false;
    }
   
    var comma_split = str.split(",");
    
    for(var i=0; i<comma_split.length; i++)
    {
        if(comma_split[i].indexOf("-") >=0)
        {
            var dash_split = comma_split[i].split("-");
            if(!isInteger(dash_split[0], minNumber, maxNumber, false) || 
               !isInteger(dash_split[1], minNumber, maxNumber, false))
            {
                alert(msg_range_invalid + minNumber + "至" + maxNumber);
                return false;
            }
        else
        {
            if(parseInt(dash_split[0]) > parseInt(dash_split[1]))
            {
                alert(msg_range_invalid + minNumber + "至" + maxNumber);
                return false;
            }
        }
    }
    else
    {
        if(!isInteger(comma_split[i], minNumber, maxNumber, false))
        {
            alert(msg_range_invalid + minNumber + "至" + maxNumber);
            return false;
        }
    }
 }
    return true;
}

function setBodyWidth()
{
	 var clientWidth = document.body.clientWidth;
	 var tblWidth = (clientWidth<900) ? (clientWidth-10) : 980; 	
 	 

	 document.body.style.position = "absolute";
	// document.body.style.left = ((clientWidth-tblWidth)/2) + "px";
	 document.body.style.left = ((screen.width-tblWidth)/2) + "px";
	 document.body.style.top = "0px";
	 
	 document.getElementById("mainTbl").style.width = tblWidth + "px";
}

function addseparatortomac(str)
{
	var tmp = "";
	str = str.replace(/[:]/g, "");
	str = str.replace(/[-]/g, "");
	tmp += str.substring(0,2);   tmp += ":";
	tmp += str.substring(2,4);   tmp += ":";
	tmp += str.substring(4,6);   tmp += ":";
	tmp += str.substring(6,8);   tmp += ":";
	tmp += str.substring(8,10);  tmp += ":";
	tmp += str.substring(10,12);
	
	return tmp;	
}

//d_str:  1,5,7,5-9
function range2Array(d_str)
{
	 var i, j;
	 var data_array = new Array();
	 
	 var comma_split = d_str.split(",");
	 	  
	 for(i=0; i<comma_split.length; i++)
     {
   	    var dash_split = "";
   	 
   	    if(comma_split[i].indexOf("-") >= 0)
   	    {
   	 	   dash_split = comma_split[i].split("-"); 
   	 	       
   	 	   for(j=0; j<=(parseInt(dash_split[1])-parseInt(dash_split[0])); j++) 	
   	 	      data_array.push(parseInt(dash_split[0])+j);
   	    }
   	    else 
   	 	   data_array.push(parseInt(comma_split[i]));
     }
	 
	 data_array.sort(function(a,b){return (a-b);});	 
	 Array.prototype.unique = array_unique;  	 
     data_array.unique(); 
   
     return data_array;
}

function array_unique()
{
   var obj = new Object();
   for (var i=0,j=0; i<this.length; i++)
   {
      if (typeof obj[this[i]] == 'undefined')
        obj[this[i]] = this[i];
   }
   
   this.length = 0;
   
   for (var key in obj)
    this[key] = obj[key];

   return this;
}

//check ports if they belong to trunks members
function check_belong_to_trunk_mem(trunk_array, p_array)
{
  var i,j;
   	
  for(i in trunk_array)
  {
  	if(!isNaN(i))
  	{
  	  var temp = 0;
  	  var num = 0;
  	  for(j in trunk_array[i])
  	  {
  	 	  if(!isNaN(j))
  	 	  {
  	 	  	temp++;
  	 	 	  var p_key = trunk_array[i][j];
  	 	 	  if(typeof p_array[p_key] != 'undefined')  	 	 	     
  	 	 	    num++;
  	 	  }
  	  }
  	  
  	  if((num!=0) && (num != temp))
  	    return false;
  	}  	
  }  	
  return true;
}

 //parse trunk_config string
function parse_trunk_cfg_str()
{
    var cf = document.forms[0];
	var semicolon_split = cf.trunk_config.value.split(";");
	var len = semicolon_split.length - 1;
			
	for(var i = 0; i < len; i++)
	{
	    var colon_split = semicolon_split[i].split(":");
	    trunk_name[i] = colon_split[1];
		trunk_array[i] = new Array();
		trunk_array[i]= range2Array(colon_split[2]);
	}
}

//check port belongs to which trunk
function check_trunk_id_by_port(portId)
{   
  var i;
  for(i in trunk_array)
  {
    if(!isNaN(i))
    {
        var t_key = portId;
        if(typeof trunk_array[i][t_key] != 'undefined')
           return i;
    }
  } 
  return "";
}

//parse port array to port range with ',' && '-'
function parse_ports_to_range(port_array)
{
    var p_range = "";
    var temp = new Array();
    var num = 0;
    
    for(var i in port_array)
    {
       if(!isNaN(i))
       {
          if((typeof port_array[parseInt(i)+1] == "undefined") &&
             (typeof port_array[parseInt(i)-1] == "undefined"))
          {
             p_range += port_array[i]+",";             
          }
          else
          {
            temp[num] = port_array[i];
            num++;
            
            if(typeof port_array[parseInt(i)+1] == "undefined")
            {
            	p_range += temp[0] + "-" + temp[num-1] + ",";
            	temp = new Array();
            	num = 0;
            }           
          }
       }
    }
    
    p_range = p_range.substring(0, p_range.length-1);  
       
    return p_range;
}

//[a-z A-Z 0-9 -]  字母数字和中划线
function check_str_format(str) 
{
    var reg1 = /^([a-zA-Z]|\d|[-])+$/; 
   
    if(!reg1.exec(str))
        return false;

    return true;
}

//[a-z A-Z 0-9 ] 字线和数字
function check_ingress_str_format(str) 
{
    var reg1 = /^([a-zA-Z]|\d)+$/; 
   
    if(!reg1.exec(str))
        return false;

    return true;
}

/*by LuoM  11-5-18*/
function MacCheck(mac_addr)
{
	var myRE =/^[0-9a-fA-F]{1,4}\.[a-fA-F0-9]{1,4}\.[a-fA-F0-9]{1,4}$/;
	if(myRE.test(mac_addr))
	{
		var val = mac_addr.split(".",1);
		var vals = val[0].split("");
			if(val[0].length==3){
				if(vals[0]<10 && vals[0]>=0){
					if(vals[0]%2==1){
						return false;
					}
				}
				else{
					if((vals[0].charCodeAt()+1)%2==1){
						return false;
					}
				}
			}
			if(val[0].length==4){
				if(vals[1]<10 && vals[1]>=0){
					if(vals[1]%2==1){
						return false
					}
				}
				else{
					if((vals[1].charCodeAt()+1)%2==1){
						return false
					}
				}
			}
			return true;
	}
	else
	{
		return false;
	}
	
}

/* by zjx  11-6-8 */
function IpCheck(ip_addr)
{
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])$/;
	if(myRE.test(ip_addr))
	{
        return true;
	}
	else
	{
		return false;
	}
}
function TimeCheck(val)
{
	var str=/^((((19[7-9][1-9])|(1980)|(1990)|(1970)|(([2-9]\d)\d{2}))-(0[13578]|1[02])-(0[1-9]|[12]\d|3[01]))|(((19[7-9][1-9])|(1980)|(1990)|(1970)|(([2-9]\d)\d{2}))-(0[13456789]|1[012])-(0[1-9]|[12]\d|30))|(((19[7-9][1-9])|(1980)|(1990)|(1970)|(([2-9]\d)\d{2}))-02-(0[1-9]|1\d|2[0-8]))|(((19|[2-9]\d)(0[48]|[2468][048]|[13579][26])|((16|[2468][048]|[3579][26])00))-02-29-)) ((20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d) ?$/;
	if(str.test(val))
	{
		return true;
	}
	else
	{
		return false;
	}
}
function TimeZoneCheck(val)
{
	var str=/^(\+|\-)(([0-1]\d)|(2[0-3])|(\d)):([0-5]\d)$/;	
	if(str.test(val))
	{
		return true;
	}
	else
	{
		return false;
	}	
}
function tdIpCheck(textValue)
{
    re1=/(\d+)(\W)(\d+)(\W)(\d+)(\W)(\d+)/
    Check=textValue.search(re1);
    if(Check==-1)
    {
        return false;
    }
    else
    {
        ipSplit=textValue.split('.');
        if(ipSplit.length!=4)
        {
            return false;
        }

        for(i=0; i<ipSplit.length; i++)
        {
            if(isNaN(ipSplit[i]))  return false;
			if(ipSplit[i].length > 1 && ipSplit[i].charAt(0) == "0") return false;
        }
    
        for(i=0; i<ipSplit.length; i++)
        {
            if(ipSplit[i] > 255)
            {
                return false;
            }
            if(ipSplit[i] < 0)
            {
                return false;
            }
        }
        if((ipSplit[0]==255) && (ipSplit[1]==255) && (ipSplit[2]==255) && (ipSplit[3]==255))
        {
            return false;
        }

        if((ipSplit[0]==0) || (ipSplit[0]==127) || (ipSplit[3]==0) || (ipSplit[3]==255))
        {
            return false;
        }

        if(ipSplit[0] >= 224)
        {
            return false;
        }
        return true;
    }
}

function IpCheckAndMask(ip_addr)
{
/*by FC-fcy 2012-4-23 start*/
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])(\u002F)(([0-9])|([1-2][0-9])|(3[0-2]))$/;
/*by FC-fcy 2012-4-23 end*/
	if(myRE.test(ip_addr))
	{
        return true;
	}
	else
	{
		return false;
	}
}

/*by LuoM  11-5-18*/
function DataScope(value,max,min)
{
	if(!isNaN(value) && value>=min && value<=max)
		return true;
	else
		return false;
}

function isINT(str){
var re = /^[0-9]*[0-9][0-9]*$/ ;

var result = re.test(str);   //测试 返回true或false

return result;

}

/*一位小数*/
function isFloat1(val)
{
   var re=/^[0-9]{1,3}(.[0-9]{1})?$/;
   if(re.test(val))
     return true;
   else
     return false;
  
}
/*两位小数*/
function isFloat2(val)
{
   var re=/^[0-9]{1,3}(.[0-9]{2})?$/;
   if(re.test(val))
     return true;
   else
     return false;
  
}

function IntFormatStorm(val,max,min)
{
  var reint=/^[0-9]{1,3}?$/;
  if(val>max||val<min)
    return false;
  if(reint.test(val))
    return true;
  else if(isFloat1(val))
    return true;
  else if(isFloat2(val))
    return true;
  else
    return false;
}

function isValidString(str)
{
	var validc = "/'%`\"\\><";
	var a,c;
	for(i = 0 ; i < validc.length; i ++)
	{
		c = validc.substring(i, i + 1);
		if(str.indexOf(c) > -1)
		{
			return true;
		}
	}
	return false;
}

