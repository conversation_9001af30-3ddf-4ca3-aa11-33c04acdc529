<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
  <% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
    <meta http-equiv="Content-Type" content="text/html; charset=utf8">
    <script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
    <script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
    <script language="javascript" type="text/javascript" src="js/alpinejs.min.js"></script>
    <link href="css/display_web.css" rel="stylesheet" type="text/css" />
    <title>&nbsp;
      <script>writemsg(<% write(lang); %>, "802.1Q VLAN设置");</script>
    </title>

    <script language="JavaScript">
    </script>

    <script language="JavaScript" type="text/JavaScript">

function changebgcolor(){
 var tab = document.all.table1;
 var tab2= document.all.table2;
 var len2 = tab2.rows.length ;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
  for (var i=0; i<len2; i++)
  {
	var lencol = tab2.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab2.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab2.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}
function getUrlParamTab() {
        let urlSearch = window.location.search
        const search = new URLSearchParams(urlSearch)
		
        const params = Object.fromEntries(search.entries())
		
        try {
          if (params.tab) return params.tab
        } catch { }
        return 'tab1'
      }
function fucCheckNUM(NUM)
{
 var i,j,strTemp;
 strTemp="0123456789";
 if ( NUM.length== 0)
  return 0
 for (i=0;i<NUM.length;i++)
 {
  j=strTemp.indexOf(NUM.charAt(i)); 
  if (j==-1)
  {
  //说明有字符不是数字
   return false;
  }
 }
 //说明是数字
 return true;
} 

function display(){
  changebgcolor();
  var wValue = document.getElementById("qos_cfg").value.split(',');
  var qosprimode =	document.getElementById("qosprimode").value;

  document.getElementById("QosPortConfigPriorityTrust").value = qosprimode;

  if(wValue[0] == "enable")
  {
    document.getElementById("c1").checked = true;
    document.getElementById("QosConfigSMode").value = wValue[1];
    if(wValue[1] == "wrr")
    {
      document.forms[0].scheduler_weight1.value = wValue[2];
      document.forms[0].scheduler_weight2.value = wValue[3];
      document.forms[0].scheduler_weight3.value = wValue[4];
      document.forms[0].scheduler_weight4.value = wValue[5];
      document.forms[0].scheduler_weight5.value = wValue[6];
      document.forms[0].scheduler_weight6.value = wValue[7];
      document.forms[0].scheduler_weight7.value = wValue[8];
      document.forms[0].scheduler_weight8.value = wValue[9];
    }
    else
    {
      document.forms[0].scheduler_weight1.value = 1;
      document.forms[0].scheduler_weight2.value = 2;
      document.forms[0].scheduler_weight3.value = 3;
      document.forms[0].scheduler_weight4.value = 4;
      document.forms[0].scheduler_weight5.value = 5;
      document.forms[0].scheduler_weight6.value = 6;
      document.forms[0].scheduler_weight7.value = 7;
      document.forms[0].scheduler_weight8.value = 8;
    }
  }
  else
  {
    document.getElementById("c2").checked = true;
  }
  showQDisable();
  cosdisplay();

}
function checkSub()
{
	var idName= "";
	var quee;
	var cmd= "";

	document.getElementById("qosprimode").value=document.getElementById("QosPortConfigPriorityTrust").value;

	if(document.getElementById("c1").checked&&(document.getElementById("QosConfigSMode").value == "wrr"))
	{
		
			for(var j=1;j<9;j++)
			{
					quee = j - 1 ;
					idName = "scheduler_weight"+j;
					var wValue = document.getElementById(idName).value;
					
					if(isNaN(wValue) || wValue<1 || wValue>10 || !fucCheckNUM(wValue))
					{
						alert("队列"+quee+"权重设置错误，范围：1-10");
						return;
					}
					cmd += " " + wValue;
			}
		
	}
	if(document.getElementById("c1").checked)
	{
			if(document.getElementById("QosConfigSMode").value == "wrr")
				cmd = "mls qos scheduler wrr " + cmd;
			//else if(document.getElementById("QosConfigSMode").value == "wfq")
			//	cmd = "mls qos scheduler wfq" + cmd;
			else
				cmd = "no mls qos scheduler";

	}
	
	document.forms[0].qos_cmd.value = cmd;
	
			//	alert(cmd);


	
	document.form1.submit();
}
function switchIpt(){
  checkSub()
  //document.form1.submit();
}
function showQDisable()
{
  
	var QMode = document.getElementById("QosConfigSMode").value;
	
	if(QMode == "wrr")
	{
		document.getElementById("scheduler_cfg").style.display="";
    document.getElementById("application").style.display = "none";
	
	}
	else
	{
		document.getElementById("scheduler_cfg").style.display="none";
    document.getElementById("application").style.display = "table-row";
	}

}

//==================================================================================
// var boardType = <% getSysCfg(); %>;
// var portStaList=[<%QosPriShow();%>];

retValue = <% var responseJsonStr; jw_get_qosPortPriorityConfig(); %>
portStaList = <% write(responseJsonStr); %>;

var data=portStaList.PortQosCos
function writeLines()

{
  var j = 0;
  for(var i=0;i<data.length;i++)
  if(i%2==0){
      {
    document.write(" <tr  class='tables_all'>");
      document.write("    <td class='inputsyslog1'>"+data[i]['portName']+"</td>");
    document.write("    <td class='inputsyslog1'><select value="+data[i]['qosCos']+" data-portName="+data[i]['portName']+" class='selectCos' onchange=selectqosCos("+i+")> <option value='0' >0</option><option value='1' >1</option><option value='2' >2</option><option value='3' >3</option><option value='4' >4</option><option value='5' >5</option><option value='6' >6</option><option value='7' >7</option></select></td>")
   
    
   
    document.write("  </tr>");
  }
  }else{
    {
      document.write(" <tr  class='tables_all tables_allLLDP'>");
      document.write("    <td class='inputsyslog1'>"+data[i]['portName']+"</td>");
    document.write("    <td class='inputsyslog1'><select value="+data[i]['qosCos']+" data-portName="+data[i]['portName']+" class='selectCos' onchange=selectqosCos("+i+")> <option value='0' >0</option><option value='1' >1</option><option value='2' >2</option><option value='3' >3</option><option value='4' >4</option><option value='5' >5</option><option value='6' >6</option><option value='7' >7</option></select></td>")
   
    
   
    document.write("  </tr>");
  }
  }

}
var boardType = <% getSysCfg(); %>;
var portStaList1=[<%QosPriShow();%>];

function writeLinesDSCP()
{
  var j = 0;
  for(var i=0;i<(portStaList1.length-1)/2;i++)
  {
    if(i%2==0){
        document.write(" <tr  class='tables_all'>");
    document.write("    <td class='inputsyslog1'><input type='checkbox' name='checkbox_index' onchange=addToPortRange('"+i+"') ></td>");
    document.write("    <td class='inputsyslog1'>"+portStaList1[j]+"</td>");
    j++
    document.write("    <td  class='inputsyslog1'>"+portStaList1[j]+"</td>");
    j++;
    document.write("  </tr>");
    }else{
      document.write(" <tr  class='tables_all tables_allLLDP'>");
    document.write("    <td class='inputsyslog1'><input type='checkbox' name='checkbox_index' onchange=addToPortRange('"+i+"') ></td>");
    document.write("    <td class='inputsyslog1'>"+portStaList1[j]+"</td>");
    j++
    document.write("    <td  class='inputsyslog1'>"+portStaList1[j]+"</td>");
    j++;
    document.write("  </tr>");
    }
  
  }
}
 function  selectqosCos(row) {
  checking2(row)
  
 }
/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[1];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true;  
			 addToPortRange(i);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRange(i);
			}
             
        }
    } 
    
}

/*MTU judgment*/
function checking2(row)
{
  const pagePath = 'qos_portConfig.asp?ltime=' +<% write(lltime); %>;
 	// var port_range = document.getElementById("port_range").value;
 	// var port_txrate =document.getElementById("qos_prio").value;
 	//var port_rxrate= document.getElementById("rx_linerate").value;
	// var table_port11 = document.getElementById("table_port11");
	var tf=document.port_setting;
  let obj={
    pageName:'qos_portConfig.asp',
    pagePath:pagePath+ '&tab=tab2',
    PortQosCos:[
      {
        portName:row.portName,
        iQosCos:Number(row.qosCos) 
      }
    ]
  }
  tf.param1.value=JSON.stringify(obj);
  
  tf.action="/goform/jw_set_qosPortPriorityConfig";
  tf.submit();
	//if(port_txrate.length==0)port_txrate="-";
	////if(port_txburst.length==0)port_txburst="-";
	//if(port_rxrate.length==0)port_rxrate="-";	    
	//if(port_rxburst.length==0)port_rxburst="-";	
}

/*display function*/
function P(portId, admin,links,nego,cspeed,flowS,flowR,setmtu,portDesc,txlinerate,txburst,rxlinerate,rxburst)
{
    var narr=7;
	var arr="";
	var speed="";
	var Desc="";
    var tbtd;
    var i;
    var opt;
    var tbtr = document.getElementById("table_port11").insertRow(-1);
	
    tbtr.classname = "td7";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "td7");
    tbtr.setAttribute("className", "td7");
	tbtr.setAttribute("id", "tr_"+portId);

	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
	/*	
		if(i==4 || i==11 )
		{
			tbtd.setAttribute("style","display:none");
		}
		*/
        tbtr.appendChild(tbtd);
    }


	arr=cspeed.split(".");
	if(arr[1]=="full")
	{
		if(arr[0]!=0)
			speed=arr[0]+putmsg(<% write(lang); %>,"/全双工");
		else
			speed=putmsg(<% write(lang); %>,"自动协商");
	}
	else
	{
		if(arr[0]!=0)
			speed=arr[0]+putmsg(<% write(lang); %>,"/半双工");
		else
			speed=putmsg(<% write(lang); %>,"自动协商");
	}
		
	/*display*/
	tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"checkbox_index\" value=\""+portId+"\" onclick=\"addToPortRange(this)\"/>";
	tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = putmsg(<% write(lang); %>,(admin=="up")?"开启":"关闭");
    tbtr.cells[3].innerHTML = links;
    //tbtr.cells[4].innerHTML = putmsg(<% write(lang); %>,speed);
	tbtr.cells[4].innerHTML = putmsg(<% write(lang); %>,(flowS=="up")?"开启":"关闭");
	tbtr.cells[5].innerHTML = putmsg(<% write(lang); %>,(flowR=="up")?"开启":"关闭");
	//tbtr.cells[7].innerHTML = txlinerate;
	//tbtr.cells[8].innerHTML = txburst;
	//tbtr.cells[9].innerHTML = rxlinerate;
	//tbtr.cells[10].innerHTML = rxburst;
	//tbtr.cells[7].innerHTML = setmtu;
	if(portDesc.length>10){
		Desc=portDesc.substring(0,portDesc.length-(portDesc.length-10));
		tbtr.cells[6].innerHTML = "<font title=\""+portDesc+"\">"+Desc+"......</font>";
	}
	else
		tbtr.cells[6].innerHTML = "<font title=\""+portDesc+"\">"+portDesc+"</font>";

}

/*
	Show all check true port, and will last a port data displayed
*/


function checkData()
{
	var tf=document.port_setting;
	tf.action = "/goform/saveComm?name=port";
	tf.submit();
}

function refreshpage(tab)
{
  location.href='qos_portConfig.asp?ltime='+<% write(lltime); %>+'&tab='+tab;
}

function changebgcolor11(){
 var tab = document.all.table11;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}

function changebgcolor_port11(){
 var tab = document.all.table_port11;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function showHelpinfo()
{
   showHelp('port',<% write(lang); %>);
}

//==================================cos

function changebgcolor21(){
 var tab = document.all.table21;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}

function cosdisplay()
{
	changebgcolor21();

	var wValue = document.getElementById("cos_qos_cfg").value.split(',');
  let forDom=document.forms["cmsForm"]
	// if(wValue[0] == "disable")
	// {
	// 	document.getElementById("modify4").disabled = true;
	// 	alert("QoS未开启!");
	// }
	// else
	// {
   
	// }

		forDom.QosCosMap1.value = wValue[0];
		forDom.QosCosMap2.value = wValue[1];
    forDom.QosCosMap3.value = wValue[2];
		forDom.QosCosMap4.value = wValue[3];
		forDom.QosCosMap5.value = wValue[4];
		forDom.QosCosMap6.value = wValue[5];
		forDom.QosCosMap7.value = wValue[6];
		forDom.QosCosMap8.value = wValue[7];
}


function cosCheckSub()
{
	var idName= "";
	var quee;
	var cmd= "";
	var wValue ="";
	var tf=document.cmsForm;
	var qosprimode =	document.getElementById("qosprimode").value;
  if(qosprimode == "dscp" || qosprimode == "base")
  {
    alert("非DOT1P模式,不能修改DOT1P映射！！！");
    return;
  }

	for(var j=1;j<9;j++)
	{
		idName = "QosCosMap"+j;
		wValue = document.getElementById(idName).value;
		if(wValue == 0)
		cmd += " 0 ";
		else
		cmd += " " + wValue;
	}

	cmd = "mls qos cos-map " + cmd;

	document.forms["cmsForm"].cos_qos_cmd.value = cmd;
  tf.tabName.value="tab4";
	//alert(cmd);
	tf.submit();
}

//===============================dscp
var dscpStaList=[<%QosDscpShow();%>];

/*select ALL*/
function DscpselectToAll() 
{  
    var cf = document.forms["qos_dscp"];
    var objs = document.getElementsByName("checkbox_index"); 
	  var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true;  
			 addToPortRange(i);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRange(i);
			}
             
        }
    } 
    
}

/*MTU judgment*/
function checking4()
{
	var tf=document.qos_dscp;
  tf.tabName.value="tab3";
	tf.submit();
}

/*
	Show all check true port, and will last a port data displayed
*/
function addToPortRange(index){
	//alert(index);
	var target = document.getElementById("dscp_range");
	var qos_cos=document.getElementById("qos_cos");
  var objs = document.getElementsByName("checkbox_index"); 

	if(objs[index].checked){
		target.value = target.value  + dscpStaList[2*index] + " ";
		qos_cos.value= dscpStaList[2*index+1];
	}else{
		target.value = target.value.replace(dscpStaList[2*index]+" ","");
	}

}


function changebgcolor41(){
 var tab = document.all.table41;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}

function changebgcolor_port(){
 var tab = document.all.table_port;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables " ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}




</script>
</HEAD>

<BODY onload="display()" x-data="{active:'tab1'}">
  <br>

  <% web_get_stat(); %>
    <% var authmode;checkCurMode(); %>
      <% var errorcode, qoscfg; getQoSCosMap(); %>
        <script>
          retValue1 = <% var responseJsonStr; jw_get_qosDscpConfig(); %>
            responseStr = <% write(responseJsonStr); %>;
          var data1 = responseStr.PortQosDscp

          retValue = <% var responseJsonStr; jw_get_qosPortPriorityConfig(); %>
            portStaList = <% write(responseJsonStr); %>;

          var data = portStaList.PortQosCos
          const appData = {

            data: [
              {
                portName: '',
                qosCos: '',

              },

            ],
            data1: [
              {
                qosCos: '',
                qosDscp: '',

              },
            ]


          };
          appData.data = [...data]
          appData.data1 = [...data1]
          // 初始化 Alpine.js
          Alpine.data('myApp', () => ({
            ...appData,

          }));
          function changeDscp(row) {
            var tf = document.qos_dscp;

            tf.tabName.value = "tab3";
            var obj = {
              pageName: 'qos_portConfig.asp',
              pagePath: 'qos_portConfig.asp?ltime=' +<% write(lltime); %> +'&tab=tab3',
              PortQosDscp: [
                {
                  qosCos: Number(row.qosCos),
                  qosDscp: Number(row.qosDscp)
                }
              ]
          }

          tf.param1.value = JSON.stringify(obj);
          tf.action = "/goform/jw_set_qosDscpConfig";
          tf.submit();
          }
        </script>
        <div>
          <ul class="tabmenu">
            <li id="tab1" :class="active==='tab1'?'tab':'tab-disable'">
              <a herf="#" x-on:click="active='tab1'">QOS模式设置</a>
            </li>
            <li id="tab2" :class="active==='tab2'?'tab':'tab-disable'">
              <a herf="#" x-on:click="active='tab2'">端口优先级</a>
            </li>
            <li id="tab3" :class="active==='tab3'?'tab':'tab-disable'" @click="">
              <a herf="#" x-on:click="active='tab3'">DSCP映射</a>
            </li>
            <li id="tab4" :class="active==='tab4'?'tab':'tab-disable'" @click="">
              <a herf="#" x-on:click="active='tab4'">DOT1P映射</a>
            </li>
          </ul>
        </div>
        <form x-show="active==='tab1'" name="form1" method="post" action="/goform/setQoSConfig"
          style="min-height: 580px;" x-init="active=getUrlParamTab()">
          <% var errorcode, qoscfg,qosprimode; getQoSCfg(); %>
            <input type="hidden" name="qos_cfg" id="qos_cfg" value="<% write(qoscfg); %>">
            <input type="hidden" name="qosprimode" id="qosprimode" value="<% write(qosprimode); %>">
            <input type="hidden" name="qos_cmd" id="qos_cmd" value="">
            <input type="hidden" name="ltime" value=<% write(lltime); %>>
            <input type="hidden" name="lastts" value=<% write(serverts); %>>
            <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
            <div class="formContain">
              <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
                <tr>
                  <td height="30px">
                    <font size="5" color="#404040">
                      <div class="bot">QOS模式设置</div>
                    </font>
                  </td>

              </table>

              <TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
                <TBODY>

                  <TR height=22>
                    <TD valign="top" width="50%" style="text-align: right;">&nbsp;&nbsp;QoS模式</TD>
                    <td colspan="3"><span class="crons">
                        <input type="radio" name="qosSysEnable" value="enable" id="c1" >
                        开启
                        <input type="radio" name="qosSysEnable" value="disable" id="c2">
                        关闭

                      </span></td>
                  </TR>
                  <TR height=22>
                    <TD width="22%" width="50%" style="text-align: right;">
                      <div>&nbsp;&nbsp;<span class="td25">调度算法</span></div>
                    </TD>
                    <td colspan="3"><span class="crons">
                        <select name="QosConfigSMode" id="QosConfigSMode" onchange="showQDisable()"
                          style="width: 200px;">
                          <!--<option value="wfq">WFQ</option>-->
                          <option value="wrr">WRR</option>
                          <option value="sp" selected>SP</option>
                        </select>
                      </span></td>
                  </TR>
                  <TR height=22>
                    <TD valign="top" width="50%" style="text-align: right;">&nbsp;&nbsp;<span class="td25">优先级模式</span>
                    </TD>
                    <td colspan="3"><span class="crons">
                        <select name="QosPortConfigPriorityTrust" id="QosPortConfigPriorityTrust" style="width: 200px;">
                          <option value="dot1p" selected>DOT1P</option>
                          <option value="dscp">DSCP</option>
                          <option value="base">端口优先级</option>
                        </select>
                      </span></td>
                  </TR>
                  <TR height=22 id="application">
                    <TD colspan="4" valign="top">
                      <div align="center">
                        <script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "buttons_apply", "button", "modify2", "checkSub()");</script>

                      </div>
                    </TD>
                  </TR>
              </TABLE>
            </div>

            <div class="formContain" style="margin-top: 15px;min-height: 350px" id='scheduler_cfg'
              style='display:none;'>
              <div class="">
                <font size="5" color="#404040">
                  <div class="bot">调度算法配置</div>
                </font>
              </div>
              <table width="98%" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table2">
                <tbody>
                  <TR height=22>
                    <TD valign="top" width="20%">&nbsp;&nbsp;队列<span class="td25">0<span class="crons">权重</span></span>
                    </TD>
                    <td class="crons"><input name="scheduler_weight1" type="text" id="scheduler_weight1" size="4"
                        maxlength="2" / VALUE="" />
                      (1-10)</td>
                    <td class="crons">&nbsp;&nbsp;队列1权重</td>
                    <td class="crons"><input name="scheduler_weight2" type="text" id="scheduler_weight2" size="4"
                        maxlength="2" / VALUE="" />
                      (1-10)</td>
                  </TR>
                  <TR height=22>
                    <TD valign="top" width="20%">&nbsp;&nbsp;队列2<span class="crons">权重</span></TD>
                    <td class="crons"><input name="scheduler_weight3" type="text" id="scheduler_weight3" size="4"
                        maxlength="2" / VALUE="" />
                      (1-10)</td>
                    <td class="crons">&nbsp;&nbsp;队列3权重</td>
                    <td class="crons"><input name="scheduler_weight4" type="text" id="scheduler_weight4" size="4"
                        maxlength="2" / VALUE="" />
                      (1-10)</td>
                  </TR>

                  <TR height=22>
                    <TD valign="top" width="20%">&nbsp;&nbsp;队列<span class="td25">4<span class="crons">权重</span></span>
                    </TD>
                    <td class="crons"><input name="scheduler_weight5" type="text" id="scheduler_weight5" size="4"
                        maxlength="2" / VALUE="" />
                      (1-10)</td>
                    <td class="crons">&nbsp;&nbsp;队列5权重</td>
                    <td class="crons"><input name="scheduler_weight6" type="text" id="scheduler_weight6" size="4"
                        maxlength="2" / VALUE="" />
                      (1-10)</td>
                  </TR>
                  <TR height=22>
                    <TD valign="top" width="20%">&nbsp;&nbsp;队列6<span class="crons">权重</span></TD>
                    <td class="crons"><input name="scheduler_weight7" type="text" id="scheduler_weight7" size="4"
                        maxlength="2" / VALUE="" />
                      (1-10)</td>
                    <td class="crons">&nbsp;&nbsp;队列7权重</td>
                    <td class="crons"><input name="scheduler_weight8" type="text" id="scheduler_weight8" size="4"
                        maxlength="2" / VALUE="" />
                      (1-10)</td>
                  </TR>
                  <TR height=22>
                    <TD colspan="4" valign="top">
                      <div align="center">
                        <script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "buttons_apply", "button", "modify2", "checkSub()");</script>

                      </div>
                    </TD>
                  </TR>
                </tbody>
              </table>
            </div>
        </form>


        <form x-show="active==='tab2'" name="port_setting" method="POST" action="/goform/setAlarmPortrate"
          class="formContain">

          <input type="hidden" name="lastts" value=<% write(serverts); %>>
          <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
          <input type="hidden" name="left_menu_id" value="@left_menu_id#">
          <input type="hidden" name="ltime" value=<% write(lltime); %>>
          <input type="hidden" name="param1" id="param1">
          <table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
            <tr>
              <td>
                <table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0">
                  <tr>
                    <td valign="top">
                      <table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
                        <tr>
                          <td>

                            <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
                              <tr>
                                <td height="30px">
                                  <font size="5" color="#404040">
                                    <div class="bot">端口优先级</div>
                                  </font>
                                </td>
                              </tr>
                            </table>

                            <!--
  
          <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
              <tr>
                <td colspan="2" align="left"  class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"端口优先级");</script></td>
              </tr>
          </table>
  -->
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <!-- <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table11" class="tablebord">
              <tr height="30">
                <td align="left" class="td7">&nbsp;<script>writemsg(<% write(lang); %>,"端口选择");</script></td>
                <td width="81%" align="left" class="td7">&nbsp;
                  <input name="port_range" type="text" class="input_x" id="port_range" readonly="true"/></td>
              </tr>
     <tr height="30"  >
                <td width="20%" align="left" class="td7"  >&nbsp;<script>writemsg(<% write(lang); %>,"端口优先级");</script></td>
                <td align="left" class="td7">&nbsp;			  <span class="crons">
                  <select name="qos_prio" id="qos_prio" >
                    <option value="0" >0</option>
                    <option value="1" >1</option>
                    <option value="2" >2</option>
                    <option value="3" >3</option>
                    <option value="4" >4</option>
                    <option value="5" >5</option>
                    <option value="6" >6</option>
                    <option value="7" >7</option>
                 </select>
                  </span></td>
              </tr>
    
  
              
   
        <tr>
           <td colspan="2" align="center" class="td7">
           <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"应  用","buttons_apply","button","modify","checking2()");</script>				</td>
         </tr>
        
          </table> -->
                          </td>
                        </tr>
                        <tr>
                          <td height="8"></td>
                        </tr>
                        <tr>
                          <td x-data="myApp">

                            <table width="100%" height="35" border="0" cellpadding="0" cellspacing="0" class="tablebord"
                              id="table_port11">
                              <tr height="30" align="center" class="td7">

                                <th align="center" class="td2" width="31%">
                                  <script>writemsg(<% write(lang); %>, "端口");</script>
                                </th>
                                <th align="center" class="td2" width="51%">
                                  <script>writemsg(<% write(lang); %>, "队列");</script>
                                </th>
                              </tr>
                              <template x-for="(row,index) in appData.data" :key="index">
                                <tr class='tables_all'>
                                  <td class='inputsyslog1' x-text="row.portName"></td>
                                  <td class='inputsyslog1'>
                                    <select x-model="row.qosCos" class='selectCos' @change=selectqosCos(row)>
                                      <option value='0'>0</option>
                                      <option value='1'>1</option>
                                      <option value='2'>2</option>
                                      <option value='3'>3</option>
                                      <option value='4'>4</option>
                                      <option value='5'>5</option>
                                      <option value='6'>6</option>
                                      <option value='7'>7</option>
                                    </select>
                                  </td>



                                </tr>
                              </template>

                              <!-- <script language="javascript">
  writeLines();
  
  </script> -->
                            </table>
                          </td>
                        </tr>
                        <tr>
                          <td align="center" height="35">
                            <script>writebutton(1,<% write(lang); %>, "刷  新", "button", "button", "Refresh", "refreshpage(`tab2`)");</script>
                            &nbsp;
                            <!-- <input name="Submit" type="button" class="button" value="保 存" onclick="checkData()">
            &nbsp;
            <script>writebutton(1,<% write(lang); %>,"帮  助","button","button","Help","showHelpinfo()");</script>
  -->
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>


          </table>

          <input type="hidden" name="todo" value="save">
          <INPUT type="hidden" name="this_file" value="port.asp">
          <INPUT type="hidden" name="next_file" value="port.asp">
          <input type="hidden" name="message" value="@msg_text#">
        </form>
        <br>
        <br>
        <form x-show="active==='tab4'" name="cmsForm" method="post" action="/goform/setQoSCosMap" class="formContain">
          <% var errorcode, qoscfg; getQoSCosMap(); %>
            <input type="hidden" name="cos_qos_cfg" id="cos_qos_cfg" value="<% write(qoscfg); %>">
            <input type="hidden" name="cos_qos_cmd" id="cos_qos_cmd" value="">
            <input type="hidden" name="ltime" value=<% write(lltime); %>>
            <input type="hidden" name="lastts" value=<% write(serverts); %>>
            <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
            <input type="hidden" name="tabName" id="tabName">

            <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td height="30px">
                  <font size="5" color="#404040">
                    <div class="bot">DOT1P映射</div>
                  </font>
                </td>
              </tr>
            </table>

            <!--
  
  <table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
  <tr>
      <td  class=Tablelist id=tabs name=tabs><nobr>802.1P优先级队列</nobr></td>
   
      
    </tr>
  </table>
  -->

            <TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table21" class="tablebord">
              <TBODY>
                <TR height=22>
                  <TD width="25%" valign="top">
                    <div align="center">&nbsp;&nbsp;<span class="td25">内部优先级0的队列值</span></div>
                  </TD>
                  <TD width="25%"><span class="crons">
                      <select name="QosCosMap1" id="QosCosMap1">
                        <option value="0">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                      </select>
                    </span></TD>
                  <TD width="25%">&nbsp;&nbsp;<span class="td25">内部优先级1的队列值</span></TD>
                  <TD width="25%"><span class="crons">
                      <select name="QosCosMap2" id="QosCosMap2">
                        <option value="0">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                      </select>
                    </span></TD>
                </TR>
                <TR height=22>
                  <TD valign="top">&nbsp;&nbsp;<span class="td25">内部优先级2的队列值</span></TD>
                  <TD><span class="crons">
                      <select name="QosCosMap3" id="QosCosMap3">
                        <option value="0">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                      </select>
                    </span></TD>
                  <TD>&nbsp;&nbsp;<span class="td25">内部优先级3的队列值</span></TD>
                  <TD><span class="crons">
                      <select name="QosCosMap4" id="QosCosMap4">
                        <option value="0">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                      </select>
                    </span></TD>
                </TR>
                <TR height=22>
                  <TD valign="top">&nbsp;&nbsp;<span class="td25">内部优先级4的队列值</span></TD>
                  <TD><span class="crons">
                      <select name="QosCosMap5" id="QosCosMap5">
                        <option value="0">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                      </select>
                    </span></TD>
                  <TD>&nbsp;&nbsp;<span class="td25">内部优先级5的队列值</span></TD>
                  <TD><span class="crons">
                      <select name="QosCosMap6" id="QosCosMap6">
                        <option value="0">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                      </select>
                    </span></TD>
                </TR>
                <TR height=22>
                  <TD valign="top">&nbsp;&nbsp;<span class="td25">内部优先级6的队列值</span></TD>
                  <TD><span class="crons">
                      <select name="QosCosMap7" id="QosCosMap7">
                        <option value="0">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                      </select>
                    </span></TD>
                  <TD>&nbsp;&nbsp;<span class="td25">内部优先级7的队列值</span></TD>
                  <TD><span class="crons">
                      <select name="QosCosMap8" id="QosCosMap8">
                        <option value="0">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                      </select>
                    </span></TD>
                </TR>
                <TR height=22>
                  <TD colspan="4" valign="top">
                    <div align="center">
                      <script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "buttons_apply", "button", "modify4", "cosCheckSub()");</script>
                    </div>
                  </TD>
                </TR>
            </TABLE>


        </form>

        <form x-show="active==='tab3'" name="qos_dscp" method="POST" action="/goform/setDscp" class="formContain">
          <input type="hidden" name="left_menu_id" value="@left_menu_id#">
          <input type="hidden" name="ltime" value=<% write(lltime); %>>
          <input type="hidden" name="lastts" value=<% write(serverts); %>>
          <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
          <input type="hidden" name="tabName" id="tabName">
          <input type="hidden" name="param1" id="param1">
          <table id="mainTblDscp" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
            <tr>
              <td>
                <table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0">
                  <tr>
                    <td valign="top">
                      <table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
                        <tr>
                          <td>

                            <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
                              <tr>
                                <td height="30px">
                                  <font size="5" color="#404040">
                                    <div class="bot">DSCP映射</div>
                                  </font>
                                </td>
                              </tr>
                            </table>

                          </td>
                        </tr>


                        <tr>
                          <td x-data="myApp">
                            <table width="100%" height="35" border="0" cellpadding="0" cellspacing="0" class="tablebord"
                              id="table_port">
                              <thead height="30" align="center" class="td7" bgcolor="#fff">
                                <th align="center" class="td2" width="31%">
                                  <script>writemsg(<% write(lang); %>, "DSCP");</script>
                                </th>
                                <th align="center" class="td2" width="51%">
                                  <script>writemsg(<% write(lang); %>, "队列");</script>
                                </th>
                              </thead>
                              <template x-for="(row,index) in appData.data1" :key="index+1">
                                <tr align="center">
                                  <td x-text="Number(row.qosDscp)"></td>
                                  <td>
                                    <select x-model="row.qosCos" @change="changeDscp(row)">
                                      <option value="0">0</option>
                                      <option value="1">1</option>
                                      <option value="2">2</option>
                                      <option value="3">3</option>
                                      <option value="4">4</option>
                                      <option value="5">5</option>
                                      <option value="6">6</option>
                                      <option value="7">7</option>

                                    </select>
                                  </td>
                                </tr>

                              </template>

                              <!-- <script language="javascript">
                                writeLinesDSCP();
                              </script> -->
                            </table>
                          </td>
                        </tr>
                        <tr>
                          <td align="center" height="35">
                            <script>writebutton(1,<% write(lang); %>, "刷  新", "button", "button", "Refresh", "refreshpage(`tab3`)");</script>
                            &nbsp;
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>


          </table>

          <input type="hidden" name="todo" value="save">
          <INPUT type="hidden" name="this_file" value="qosdscp.asp">
          <INPUT type="hidden" name="next_file" value="qosdscp.asp">
          <input type="hidden" name="message" value="@msg_text#">
        </form>

        <script>
          // changebgcolor11();
          changebgcolor_port11();
          // changebgcolor41();
          changebgcolor_port();

  <% if (errorcode != "") { write_errorcode(errorcode); } %>
        </script>

</BODY>

</HTML>