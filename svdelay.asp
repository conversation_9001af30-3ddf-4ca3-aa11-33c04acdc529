<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"lldp管理");</script></title>
<script language="javascript" >
<% var svinfo,errorcode;getSvdelayCfg(); %>

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function AddOption(portname){

	var selectObject = document.getElementById("config_port");
	var y=document.createElement('option');
  	y.text=portname;
	y.value = portname;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  	catch(ex)
    {
   	selectObject.add(y); // IE only
  	}
}

function display(){
 	var hid=document.webForm;
 	allvalue = "<% write(svinfo); %>";
	if("enable" == allvalue.split(",")[0])
	{
		document.getElementById("c1").checked = true;
	}
	else
	{
		document.getElementById("c2").checked = true;
	}

	hid.ipgtime.value = allvalue.split(",")[1];

	if("enable" == allvalue.split(",")[2])
	{
		document.getElementById("g1").checked = true;
	}
	else
	{
		document.getElementById("g2").checked = true;
	}

	
 	//hid.sv100in.value = allvalue.split(",")[1];
 	//hid.sv100out.value  = allvalue.split(",")[2];
 	//hid.sv1000in.value  = allvalue.split(",")[3];
 	//hid.sv1000out.value  = allvalue.split(",")[4];

}

function P(portId, ingress, egress)
{
    var narr=3;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+portId);	


    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		
	        tbtr.appendChild(tbtd);
    }
	            
	
	tbtr.cells[0].innerHTML = portId;

    tbtr.cells[1].innerHTML = ingress;
    tbtr.cells[2].innerHTML = egress;

}


function refreshpage()
{
  location.href='svdelay.asp?ltime='+<% write(lltime); %>;
}

function checkvalue()
{
    var hid=document.webForm;
	
    var sv1000in = document.webForm.sv1000in.value;
    var sv1000out = document.webForm.sv1000out.value;
    var ipgtime = document.webForm.ipgtime.value;
	
  	if((parseInt(sv1000in) < -10000000) || (parseInt(sv1000in) > 10000000))
	{
   		alert(putmsg(<% write(lang); %>,"Ingress time is out of range!"));
		  return 0;
	} 
	//var num1 = parseInt(sv1000in) - 10000;
	//document.webForm.sv1000in.value = num1.toString();
		
  	if((parseInt(sv1000out) < -10000000) || (parseInt(sv1000out) > 10000000))
	{
   		alert(putmsg(<% write(lang); %>,"Egress time is out of range!"));
		  return 0;
	} 
	//var num2 = parseInt(sv1000out) - 10000;
	//document.webForm.sv1000out.value = num2.toString();
	
  	if((parseInt(ipgtime) < 1) || (parseInt(ipgtime) > 12))
	{
   		alert(putmsg(<% write(lang); %>,"ipy time is out of range!"));
		  return 0;
	} 
		
	hid.submit();

	return true;
}


</script>
</head>
<body  onload="display()"><br>

<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setSvdelayConfig">
<input type="hidden" name="flag">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<% var errorcode, lldpstatus,lldptimer; getLldpCfg(); %>
<table  id="table111"   width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td>
		

 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>电网报文设置/交换延时累加设置参数</b></font></td></tr>
 </table>
 
<!--		<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
            <tr height="25">
              <td colspan="2" align="left" class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"SV延时");</script></td>

            </tr>
        </table>
-->
        </td>
      </tr>
      
      <tr>
      
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table1" class="tablebord">




            <tr height="25">
				<td width="15%" align="left" class="crons">&nbsp;
					<script>writemsg(<% write(lang); %>,"SV bypass");</script>
				</td>
				<td width="27%" align="left" class="crons">
					<input type="radio" name="svstatus" value="enable" id="c1" >Enable
					<input type="radio" name="svstatus" value="disable" id="c2">Disable 
				</td>

				<td width="15%" align="left" class="crons">&nbsp;
					<script>writemsg(<% write(lang); %>,"GOOSE bypass");</script>
				</td>
				<td width="43%" align="left" class="crons">
					<input type="radio" name="goosestatus" value="enable" id="g1" >Enable
					<input type="radio" name="goosestatus" value="disable" id="g2">Disable 
				</td>
            </tr>
 

            <tr height="25">
              <td width="15%" align="left" class="crons">&nbsp;
                <script>writemsg(<% write(lang); %>,"ipg time");</script>
              </td>
              <td width="35%" align="left" class="crons">
					<input type="text" name="ipgtime"> (1-12) 
              </td>
              <td width="15%" align="left" class="crons">&nbsp;</td>
              <td width="35%"  align="middle" class="crons">&nbsp;               	          </td>
            </tr>
           
            <tr height="25">
              	<td align="left" class="crons">&nbsp;&nbsp;端口 <br></td>
     	  		<td align="left" class="crons"  colspan="3">
     	  			&nbsp;<select id="config_port" name="config_port"></select>
 					<script>
					<% AppendOptionForfil(); %>	
					</script>				 
			 	</td>
			</tr>
						
            <tr height="25">
              <td align="left" class="crons">&nbsp;&nbsp;入口时间补偿 <br></td>
              <td align="left" class="crons"><input type="text" name="sv1000in">(单位:ns，-10000000~10000000)</td>
              <td align="left" class="crons">&nbsp;&nbsp;出口时间补偿</td>
              <td align="left" class="crons"><input type="text" name="sv1000out">(单位:ns，-10000000~10000000)</td>
            </tr>
            <tr height="25">
              <td colspan="4" align="middle" class="crons">&nbsp;
                <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify2","checkvalue()");</script>
              </td>
            </tr>
            
		<tr>
			<td colspan="4">
				<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
					<tr align="center" height="25" class="crons">
						<th width="30%" class="td2"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></b></font></th>
						<th width="35%" class="td2"><font color="#333333"><b>&nbsp;入口时间补偿</b></font></th>
						<th width="35%" class="td2"><font color="#333333"><b>&nbsp;出口时间补偿</b></font></th>												
					</tr>

					<script>  <%  var errorcode; showsvdelaytime(); %></script>
				
				</table>
			</td>
		</tr>            
             
             
        </table></td>
      </tr>
      

   <tr>
	  	   <td colspan="2" align="center" height="35">
	  	   <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
	  		</td>
   </tr>

      
    </table></td>
  </tr> 
  
</table>
</td></tr>
<tr><td>
</td></tr></table>
<script>
changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</form>
 
</body>
</html>

