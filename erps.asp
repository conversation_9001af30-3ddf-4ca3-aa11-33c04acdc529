<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
	<% var lltime,lang; getltime_lanflag(); %>
		<meta http-equiv="Content-Type" content="text/html; charset=utf8">
		<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
		<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
		<script src="js/alpinejs.min.js" defer></script>
		<title>
			<script>writemsg(<% write(lang); %>, "端口管理");</script>
		</title>
		<link href="css/display_web.css" rel="stylesheet" type="text/css" />
		<style>
			.select1{
				width: 197px;
			}
		</style>
		<script language="JavaScript">
			var boardType = <% getSysCfg(); %>;
		</script>
		<script language="JavaScript">
			var tabActive='tab1'

			function isValidString(str) {
				var validc = "/'%`\"\\><";
				var a, c;
				for (i = 0; i < validc.length; i++) {
					c = validc.substring(i, i + 1);
					if (str.indexOf(c) > -1) {
						return true;
					}
				}
				return false;
			}
			function AddOption(port_name) {
				var selectObject = document.getElementById("rl_port");
				var selectObject2 = document.getElementById("rpl_port");
				var selectObject3 = document.getElementById("rl_port2");
				var selectObject4 = document.getElementById("rpl_port2");
				var y = document.createElement('option');
				var y2 = document.createElement('option');
				var y3 = document.createElement('option');
				var y4 = document.createElement('option');
				y.text = port_name;
				y.value = port_name;
				y2.text = port_name;
				y2.value = port_name;
				y3.text = port_name;
				y3.value = port_name;
				y4.text = port_name;
				y4.value = port_name;
				try {
					selectObject.add(y, null); // standards compliant3
					selectObject2.add(y2, null); // standards compliant3
					selectObject3.add(y3, null); // standards compliant3
					selectObject4.add(y4, null); // standards compliant3
				}
				catch (ex) {
					selectObject.add(y); // IE only
					selectObject2.add(y2); // IE only
					selectObject3.add(y3); // IE only
					selectObject4.add(y4); // IE only
				}
			}
			function checkavidhybrid(arguments) {
				var arr = new Array();
				var arry = new Array();
				arry = arguments.split("");
				arr = arguments.split(",");
				var i;
				for (i = 0; i < arry.length; i++) {
					if (arry[i] != "," && isNaN(arry[i])) {
						return false;
					}
				}

				for (i = 0; i < arr.length; i++) {
					if (isNaN(arr[i]) || (arr[i] < 1 || arr[i] > 4094)) {
						return false;
					}
				}
				return true;
			}
			function ring_en(ring, erps) {
				var tf = document.erps_setting;
				var ring_id = ring;
				var erps_id = erps;


				tf.ring_id.value = ring_id;
				tf.erps_id.value = erps_id;
				tf.tabName.value = tabActive;
				tf.action = "/goform/ringenable";
				tf.submit();


			}
			function ring_dis(ring, erps) {
				var tf = document.erps_setting;
				var ring_id = ring;
				var erps_id = erps;


				tf.ring_id.value = ring_id;
				tf.erps_id.value = erps_id;
				tf.tabName.value = tabActive;
				tf.action = "/goform/ringdisable";
				tf.submit();
			}
			function ring_del(ring, erps) {
				var tf = document.erps_setting;
				var ring_id = ring;
				var erps_id = erps;

				tf.ring_id.value = ring_id;
				tf.erps_id.value = erps_id;
				tf.tabName.value = tabActive;
				tf.action = "/goform/ringdel";
				tf.submit();
			}
			function checkingerps() {
				var tf = document.erps_setting;
				var erps_id = document.getElementById("erps_id2").value;

				if (erps_id == "") {
					alert(putmsg(<% write(lang); %>, "配置不能为空!"));
					return;
				}

				if (erps_id > 8 || erps_id < 1) {
					alert(putmsg(<% write(lang); %>, "erps id invalid (1-8)!"));
					return;
				}

				tf.action = "/goform/ringmodifyerps";
				tf.submit();

			}
			function checking() {
				// checkingerps()
				var tf = document.erps_setting;
				var erps_id = document.getElementById("erps_id2").value;
				var ring_id = document.getElementById("ring_id").value;
				var raps_vlan = document.getElementById("raps_vlan").value;
				var traffic_vlan = document.getElementById("traffic_vlan").value;

				var guartime = document.getElementById("guartime").value;;
				var wtrtime = document.getElementById("wtrtime").value;;

				if (guartime == "" || wtrtime == "" || erps_id == "" || ring_id == "" || raps_vlan == "" || traffic_vlan == "") {
					alert(putmsg(<% write(lang); %>, "配置不能为空!"));
					return;
				}

				if (erps_id > 8 || erps_id < 1) {
					alert(putmsg(<% write(lang); %>, "erps id invalid (1-8)!"));
					return;
				}
				if (ring_id > 32 || ring_id < 1) {
					alert(putmsg(<% write(lang); %>, "ring id invalid (1-32)!"));
					return;
				}
				if (raps_vlan > 4094 || raps_vlan < 1) {
					alert(putmsg(<% write(lang); %>, "raps vlan invalid (1-4094)!"));
					return;
				}

				if (!checkavidhybrid(traffic_vlan)) {
					alert(putmsg(<% write(lang); %>, "traffic vlan格式必须为: X,X,X,X . "));
					return 0;
				}


				if (guartime > 2000 || guartime < 10) {
					alert(putmsg(<% write(lang); %>, "guar time unit millisecond, step 10 millisecond,range from 10 to 2000!"));
					return;
				}
				if (wtrtime > 12 || wtrtime < 1) {
					alert(putmsg(<% write(lang); %>, "wtr time unit minute, step 1 minute,range from 1 to 12!"));
					return;
				}



				tf.action = "/goform/ringmodify";
				tf.tabName.value = "tab2";
				tf.submit();

			}
			function fastchecking() {
				var tf = document.erps_setting;
				var traffic_vlan = document.getElementById("traffic_vlan2").value;

				if (traffic_vlan == "") {
					alert(putmsg(<% write(lang); %>, "配置不能为空!"));
					return;
				}

				if (!checkavidhybrid(traffic_vlan)) {
					alert(putmsg(<% write(lang); %>, "traffic vlan格式必须为: X,X,X,X . "));
					return 0;
				}

				tf.action = "/goform/fasterps";
				tf.submit();

			}
			
			function addToERPS(obj)
			{
				var tmpPos;
				var trid="tr_"+obj.value; 
				var trobj = document.getElementById(trid);

				if(obj.checked){
					document.getElementById("erps_id2").value		=trobj.cells[1].innerHTML;
					if(trobj.cells[2].innerHTML == "none")
						document.getElementById("node_role").value = "none-interconnection";
					else
						document.getElementById("node_role").value = "interconnection";
					document.getElementById("ring_id").value		=trobj.cells[3].innerHTML;
					document.getElementById("ring_enable").value	=trobj.cells[4].innerHTML;
					document.getElementById("ring_mode").value		=trobj.cells[5].innerHTML;
					document.getElementById("node_mode").value		=trobj.cells[6].innerHTML;
					document.getElementById("raps_vlan").value		=trobj.cells[8].innerHTML;
					document.getElementById("traffic_vlan").value	=trobj.cells[9].innerHTML;
					
					tmpPos = trobj.cells[10].innerHTML.indexOf(':');
					if(tmpPos == -1)
						document.getElementById("rpl_port").value	=trobj.cells[10].innerHTML;
					else
						document.getElementById("rpl_port").value	=trobj.cells[10].innerHTML.substring(0, tmpPos);

					tmpPos = trobj.cells[11].innerHTML.indexOf(':');
					if(tmpPos == -1)
						document.getElementById("rl_port").value	=trobj.cells[11].innerHTML;
					else
						document.getElementById("rl_port").value	=trobj.cells[11].innerHTML.substring(0, tmpPos);
					
					document.getElementById("guartime").value		=trobj.cells[12].innerHTML;
					document.getElementById("wtrtime").value		=trobj.cells[13].innerHTML;
				}
				return true;
			}

			
			/*display function*/
			function P(erps, inter, ring, ringen, ringm, nodem, nodes, rapsv, trafficv, rplport, rlport, guardt, wtrt) {

				var narr = 16;
				var arr = "";
				var speed = "";
				var Desc = "";
				var tbtd;
				var i;
				var opt;
				var tbtr = document.getElementById("table_erps").insertRow(-1);

				tbtr.classname = "td7";
				tbtr.height = "30";

				tbtr.setAttribute("height", "30");
				tbtr.setAttribute("class", "td7");
				tbtr.setAttribute("className", "td7");
				tbtr.setAttribute("id", "tr_" + ring);

				/*Cycle respectively and setting attributes*/
				for (i = 0; i < narr; i++) {
					tbtd = document.createElement("td");

					tbtd.align = "center";
					tbtd.setAttribute("class", "td2");
					tbtd.setAttribute("className", "td2");
					tbtr.appendChild(tbtd);
				}

				//ring,erps,ringen,ringm,nodem,rapsv,trafficv,rplport,rlport)

				/*display*/
				let index=document.getElementById("table_erps").rows.length-2;
				tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"checkbox_index\" value=\""+index+"\" onchange=\"addToERPS(this)\"/>";
	
				tbtr.cells[1].innerHTML = erps;
				tbtr.cells[2].innerHTML = inter;
				tbtr.cells[3].innerHTML = ring;
				tbtr.cells[4].innerHTML = ringen;
				tbtr.cells[5].innerHTML = ringm;
				tbtr.cells[6].innerHTML = nodem;
				tbtr.cells[7].innerHTML = nodes;
				tbtr.cells[8].innerHTML = rapsv;
				tbtr.cells[9].innerHTML = trafficv;
				tbtr.cells[10].innerHTML = rplport;
				tbtr.cells[11].innerHTML = rlport;
				tbtr.cells[12].innerHTML = guardt;
				tbtr.cells[13].innerHTML = wtrt;
				if (ringen != "enable")
					tbtr.cells[14].innerHTML = " <input type='button' name='button2' id='button2' class='botton_under_line' value='启用' onclick='ring_en(" + ring + "," + erps + ")'>";
				else
					tbtr.cells[14].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='关闭' onclick='ring_dis(" + ring + "," + erps + ")'>";

				tbtr.cells[15].innerHTML = " <input type='button' name='button4' id='button4' class='botton_under_line' value='删除' onclick='ring_del(" + ring + "," + erps + ")'>";



			}

			function refreshpage() {
				location.href = 'erps.asp?ltime=' +<% write(lltime); %>+'&tab='+tabActive;
			}

			function changebgcolor_name(value) {
				var tab = document.getElementById(value);
				var len = tab.rows.length;

				if(value=='table_erps'){
				
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length;
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							// tab.rows[i].cells[j].bgColor = "F9F9F9";
							tab.rows[i].cells[j].className = " all_tables";
						}
						else {
							// tab.rows[i].cells[j].bgColor = "FFF";
							tab.rows[i].cells[j].className = "all_tables";
						}

					}

				}
				}else{
					for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length;
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							// tab.rows[i].cells[j].bgColor = "F9F9F9";
							tab.rows[i].cells[j].className = " all_tables";
						}
						else {
							// tab.rows[i].cells[j].bgColor = "FFF";
							tab.rows[i].cells[j].className = "all_tables all_table3-right";
						}

					}

				}
				}
				
			}
			
	
			function changebgcolor() {
				var tab = document.all.table1;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							// tab.rows[i].cells[j].bgColor = "F9F9F9";
							tab.rows[i].cells[j].className = "all_tables";
						}
						else {
							// tab.rows[i].cells[j].bgColor = "FFFFFF";
							tab.rows[i].cells[j].className = "all_tables all_table3-right";
						}

					}

				}
			}
			
			function getUrlParamTab() {
				let urlSearch = window.location.search
				const search = new URLSearchParams(urlSearch)
				
				const params = Object.fromEntries(search.entries())
				
				try {
				if (params.tab){
					tabActive=params.tab
					return  params.tab
				} 
				} catch { }
				return 'tab1'
      }
	  
		</SCRIPT>
</head>

<body onload="" x-data="{active:'tab1'}" x-init="active=getUrlParamTab()"><br>
	<% var authmode; checkCurMode(); %>
		<script>
			checktop(<% write(lang); %>);
		</script>
			  <div>
				<ul class="tabmenu">
				  <li  id="tab1" :class="active==='tab1'?'tab':'tab-disable'">
					<a herf="#" x-on:click="active='tab1',tabActive='tab1'">ERPS快速设置</a>
				  </li>
				  <li  id="tab2" :class="active==='tab2'?'tab':'tab-disable'">
					<a herf="#" x-on:click="active='tab2',tabActive='tab2'">ERPS高级设置</a>
				  </li>
				 
				</ul>
			  </div>
		<form   name="erps_setting" method="POST" action="" class="formContain" style="min-height: 560px;">
			<input type="hidden" name="left_menu_id" value="@left_menu_id#">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input type="hidden" name="erps_id" >
			<input type="hidden" name="tabName" id="tabName" >
			<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
				<tr>
					<td>
						<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0">
							<tr>
								<td valign="top">
									<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"
										class="cword09">
										<tr>
											<td>
												<table width="100%" align="center" border="0" cellspacing="0"
													cellpadding="0">
													<tr>
														<td height="30px">
															<font size="5" color="#404040">
																<div class="bot" x-text="active==='tab1'?'ERPS快速设置':'ERPS高级设置'"></div>
															</font>
														</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</td>
							</tr>


						</table>
					</td>
					</tr>
					</table>
						<input type="hidden" name="todo" value="save">
						<INPUT type="hidden" name="this_file" value="erps.asp">
						<INPUT type="hidden" name="next_file" value="erps.asp">
						<input type="hidden" name="message" value="@msg_text#">
						<div class="" x-show="active==='tab1'">
							<table width="100%" border="0" align="center" cellpadding="0"
							cellspacing="0" id="table3" class="tablebord">
							<tr height="30">
								<td  align="left" class="td7" width="50%" style="text-align: right;">&nbsp;
									<script>writemsg(<% write(lang); %>, "erps id:");</script>
								</td>
								<td  align="left" class="td7" width="50%">&nbsp;
									<input id="index_erps_id" type="text" name="index_erps_id"
										class="input_board4" value="1" />&nbsp;
									<script>writemsg(<% write(lang); %>, "(1,8)");</script>
								</td>
							</tr>
							<tr height="30">
								<td  align="left" class="td7" width="50%" style="text-align: right;">&nbsp;
									<script>writemsg(<% write(lang); %>, "环网 id:");</script>
								</td>
								<td  align="left" class="td7" width="50%">&nbsp;
									<input id="index_ring_id" type="text" name="index_ring_id"
										class="input_board4" value="1" />&nbsp;
									<script>writemsg(<% write(lang); %>, "(1,32)");</script>
								</td>
							</tr>
							<tr height="30">
								<td  align="left" width="50%" class="td7" style="text-align: right;">&nbsp;
									<script>writemsg(<% write(lang); %>, "节点模式:");</script>
								</td>
								<td  align="left" class="td7" width="50%">&nbsp;
									<select id="node_mode2" name="node_mode2" class="select1" >
										<option value="ring-node" selected>
											<script>writemsg(<% write(lang); %>, "普通节点");</script>
										</option>
										<option value="rpl-neighbor-node">
											<script>writemsg(<% write(lang); %>, "邻居节点");</script>
										</option>
										<option value="rpl-owner-node">
											<script>writemsg(<% write(lang); %>, "主节点");</script>
										</option>
									</select>
								</td>

							
							</tr>
							<tr>
								<td  align="left" class="td7" width="50%" style="text-align: right;">&nbsp;
									<script>writemsg(<% write(lang); %>, "数据 vlan:");</script>
								</td>
								<td  align="left" class="td7" width="50%">&nbsp;
									<input id="traffic_vlan2" type="text" name="traffic_vlan2"
										class="input_board4" value="1" />&nbsp;
									<script>writemsg(<% write(lang); %>, "(1,2,4094)");</script>
								</td>
							</tr>

							<tr height="30">
								<td  align="left" class="td7" width="50%" style="text-align: right;">&nbsp;
									<script>writemsg(<% write(lang); %>, "RPL端口:");</script>
								</td>
								<td  align="left" class="td7" width="50%">&nbsp;&nbsp;<select
										 id="rpl_port2"
										name="rpl_port2"
										class="select1"
										></select></td>
								

							</tr>
							<tr>
								<td  align="left" width="50%" class="td7" style="text-align: right;">&nbsp;
									<script>writemsg(<% write(lang); %>, "RL端口:");</script>
								</td>
								<td  width="50%" align="left" class="td7">&nbsp;&nbsp;<select
										 id="rl_port2"
										 class="select1"
										name="rl_port2"
										></select></td>
							</tr>

							<tr>
								<td colspan="2" align="center" class="td7" style="text-align: center;">
									<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "快速配置", "button", "button", "Refresh", "fastchecking()");</script>
								</td>
							</tr>

						</table>
						</div>
						<div class="" x-show="active==='tab2'">
							<table width="100%" border="0" align="center" cellpadding="0"
							cellspacing="0" id="table2" class="tablebord">

							<tr height="30">
								<td width="15%" align="left" class="td7">&nbsp;
									<script>writemsg(<% write(lang); %>, "erps id:");</script>
								</td>
								<td width="35%" align="left" class="td7">&nbsp;
									<input id="erps_id2" type="text" name="erps_id2"
										class="input_board4" />&nbsp;
									<script>writemsg(<% write(lang); %>, "(1-8)");</script>
								</td>
								<td width="15%" align="left" class="td7">&nbsp;
									<script>writemsg(<% write(lang); %>, "节点角色:");</script>
								</td>
								<td width="35%" align="left" class="td7">&nbsp;
									<select id="node_role" name="node_role" class="select1">
										<option value="none-interconnection" selected>
											<script>writemsg(<% write(lang); %>, "非相交环");</script>
										</option>
										<option value="interconnection">
											<script>writemsg(<% write(lang); %>, "相交环");</script>
										</option>
										<option value="del">
											<script>writemsg(<% write(lang); %>, "删除");</script>
										</option>
									</select>
								</td>
							</tr>
							<!-- <tr height="30">
								<td width="15%" align="left" class="td7">&nbsp;
									<script>writemsg(<% write(lang); %>, "erps id:");</script>
								</td>
								<td width="35%" align="left" class="td7">&nbsp;
									<input id="erps_id" type="text" name="erps_id"
										class="input_board4" />&nbsp;
									<script>writemsg(<% write(lang); %>, "(1-8)");</script>
								</td>


							</tr> -->
							<tr height="30">
								<td width="15%" align="left" class="td7">&nbsp;
									<script> writemsg(<% write(lang); %>, "环网 id:"); </script>
								</td>
								<td width="35%" align="left" class="td7">&nbsp;
									<input name="ring_id" type="text" class="input_board4"
										id="ring_id" />&nbsp;
									<script>writemsg(<% write(lang); %>, "(1-32)");</script>
								</td>

								<td width="15%" align="left" class="td7">&nbsp;
									<script>writemsg(<% write(lang); %>, "环网启用:");</script>
								</td>
								<td width="35%" align="left" class="td7">&nbsp;
									<select id="ring_enable" name="ring_enable" class="select1">
										<option value="enable" selected>
											<script>writemsg(<% write(lang); %>, "开启");</script>
										</option>
										<option value="disable">
											<script>writemsg(<% write(lang); %>, "关闭");</script>
										</option>
									</select>
								</td>
							</tr>

							<tr height="30">
								<td width="15%" align="left" class="td7">&nbsp;
									<script>writemsg(<% write(lang); %>, "环网模式:");</script>
								</td>
								<td width="35%" align="left" class="td7">&nbsp;
									<select name="ring_mode" id="ring_mode" class="select1">
										<option value="major-ring">
											<script>writemsg(<% write(lang); %>, "主环");</script>
										</option>
										<option value="sub-ring">
											<script>writemsg(<% write(lang); %>, "子环");</script>
										</option>

									</select>
								</td>

								<td width="15%" align="left" class="td7">&nbsp;
									<script>writemsg(<% write(lang); %>, "节点模式:");</script>
								</td>
								<td width="35%" align="left" class="td7">&nbsp;
									<select id="node_mode" name="node_mode" class="select1">
										<option value="ring-node" selected>
											<script>writemsg(<% write(lang); %>, "普通节点");</script>
										</option>
										<option value="rpl-neighbor-node">
											<script>writemsg(<% write(lang); %>, "邻居节点");</script>
										</option>
										<option value="rpl-owner-node">
											<script>writemsg(<% write(lang); %>, "主节点");</script>
										</option>
									</select>
								</td>
							</tr>

							<tr height="30">
								<td width="15%" align="left" class="td7">&nbsp;
									<script>writemsg(<% write(lang); %>, "协议 vlan:");</script>
								</td>
								<td width="30%" align="left" class="td7">&nbsp;
									<input id="raps_vlan" type="text" name="raps_vlan"
										class="input_board8" />&nbsp;
									<script>writemsg(<% write(lang); %>, "(2-4094)");</script>
								</td>

								<td width="15%" align="left" class="td7">&nbsp;
									<script>writemsg(<% write(lang); %>, "数据 vlan:");</script>
								</td>
								<td width="35%" align="left" class="td7">&nbsp;
									<input id="traffic_vlan" type="text" name="traffic_vlan"
										class="input_board4" />&nbsp;
									<script>writemsg(<% write(lang); %>, "(1,2,4094)");</script>
								</td>
							</tr>


							<tr height="30">
								<td width="15%" align="left" class="td7">&nbsp;
									<script>writemsg(<% write(lang); %>, "RPL端口:");</script>
								</td>
								<td width="35%" align="left" class="td7">&nbsp;&nbsp;<select
										 id="rpl_port"
										 class="select1"
										name="rpl_port"></select></td>
								<td width="15%" align="left" class="td7">&nbsp;
									<script>writemsg(<% write(lang); %>, "RL端口:");</script>
								</td>
								<td width="35%" align="left" class="td7">&nbsp;&nbsp;<select
									class="select1"
										 id="rl_port" name="rl_port">
										<script>AddOption("none");<% var errorcode; AppendOption(); %></script>
									</select></td>

							</tr>
							<tr height="30">
								<td width="15%" align="left" class="td7">&nbsp;
									<script>writemsg(<% write(lang); %>, "保护时间:");</script>
								</td>
								<td width="35%" align="left" class="td7">&nbsp;
									<input id="guartime" type="text" name="guartime"
										class="input_board4" value="500" />&nbsp;
									<script>writemsg(<% write(lang); %>, "(10,20-2000 ms)");</script>
								</td>

								<td width="15%" align="left" class="td7">&nbsp;
									<script>writemsg(<% write(lang); %>, "复位时间:");</script>
								</td>
								<td width="35%" align="left" class="td7">&nbsp;
									<input id="wtrtime" type="text" name="wtrtime"
										class="input_board4" value="5" />&nbsp;
									<script>writemsg(<% write(lang); %>, "(1,2-12 min)");</script>
								</td>
							</tr>
							<tr>
								<td colspan="4" align="center" class="td7" style="text-align: center;">
									<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "buttons_add", "button", "modify4", "checking()");</script>
								</td>
							</tr>

						</table>
						</div>
						<div  style="margin-top: 20px;overflow: auto;">
							<font size="5" color="#404040"><div class="bot" >ERPS信息</div></font>
							
							<table width="100%"  border="0" cellpadding="0"
							cellspacing="0" class="tablebord " id="table_erps">
							<thead height="30" align="center" class="td7">
								<th align="center" class="td2" >
									<script>writemsg(<% write(lang); %>, "");</script>
								</th>
								<th align="center" class="td2" >
									<script>writemsg(<% write(lang); %>, "Erps");</script>
								</th>
								<th align="center" class="td2" >
									<script>writemsg(<% write(lang); %>, "节点角色");</script>
								</th>
								<th align="center" class="td2" >
									<script>writemsg(<% write(lang); %>, "环网");</script>
								</th>
								<th align="center" class="td2">
									<script>writemsg(<% write(lang); %>, "当前状态");</script>
								</th>
								<th align="center" class="td2" >
									<script>writemsg(<% write(lang); %>, "环网模式");</script>
								</th>
								<th align="center" class="td2">
									<script>writemsg(<% write(lang); %>, "节点模式");</script>
								</th>
								<th align="center" class="td2" >
									<script>writemsg(<% write(lang); %>, "节点状态");</script>
								</th>
								<th align="center" class="td2">
									<script>writemsg(<% write(lang); %>, "协议vlan");</script>
								</th>
								<th align="center" class="td2">
									<script>writemsg(<% write(lang); %>, "数据vlan");</script>
								</th>
								<th align="center" class="td2" >
									<script>writemsg(<% write(lang); %>, "RPL端口");</script>
								</th>
								<th align="center" class="td2" >
									<script>writemsg(<% write(lang); %>, "RL端口");</script>
								</th>
								<th align="center" class="td2" >
									<script>writemsg(<% write(lang); %>, "保护时间");</script>
								</th>
								<th align="center" class="td2" >
									<script>writemsg(<% write(lang); %>, "复位时间");</script>
								</th>

								<th align="center" class="td2" >
									<script>writemsg(<% write(lang); %>, "开关");</script>
								</th>
								<th align="center" class="td2" >
									<script>writemsg(<% write(lang); %>, "删除");</script>
								</th>
							</thead>
							<tr>
								<script><% var errorcode; ErpsShow();%></script>
							</tr>
							
						</table>
						<div align="center" style="padding: 5px;">
							<script>writebutton(1,<% write(lang); %>, "刷  新", "button", "button", "Refresh", "refreshpage()");</script>
						</div>
						
						</div>
		</form>
		<script>
			// changebgcolor();
			changebgcolor_name("table2");
			changebgcolor_name("table3");
 changebgcolor_name("table_erps");
<% if (errorcode != "") { write_errorcode(errorcode); } %>
		</script>
</body>

</html>