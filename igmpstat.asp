<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<% var errorcode, igmptraffic; getIgmpTraffic(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<title>&nbsp;<script>writemsg(<% write(lang); %>,"igmp管理");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<SCRIPT LANGUAGE="javascript">

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function fucCheckNUM(NUM)
{
var i,j,strTemp;
strTemp="0123456789";
if ( NUM.length== 0)
return 0
for (i=0;i<NUM.length;i++)
{
j=strTemp.indexOf(NUM.charAt(i));
if (j==-1)
{
//说明有字符不是数字
return false;
}
}
//说明是数字
return true;
}

function changebgcolor(){
var tab = document.all.table1;
var len = tab.rows.length ;
for (var i=0; i<len; i++)
{
var lencol = tab.rows[i].cells.length
for (var j=0; j<lencol; j++)
{
if (j % 2 == 1){

tab.rows[i].cells[j].bgColor = "efefef";
tab.rows[i].cells[j].className = "all_tables" ;
}
else{
tab.rows[i].cells[j].bgColor = "efefef";
tab.rows[i].cells[j].className = "all_tables" ;
}

}

}
}
function display()
{
changebgcolor();

}

var igmpStaList=[<% write(igmptraffic); %>];



function writeLines()
{
var j = 0;
for(var i=0;i<igmpStaList.length/7;i++)
{
document.write(" <tr  class='tables_all'>");


document.write("    <td class='inputsyslog1'>"+igmpStaList[j]+"</td>");

j++
document.write("    <td  class='inputsyslog1'>"+igmpStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+igmpStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+igmpStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+igmpStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+igmpStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+igmpStaList[j]+"</td>");
j++;

document.write("  </tr>");

}
}

function changePort()
{
document.form1.submit();
}

function clearSta()
{
if(!confirm("您确定要清除吗？"))
{
return;
}
document.getElementById("igmpDeleteFlag").value = 1;
document.form1.submit();
}


</script>
</HEAD>
<BODY  onload=display() >

<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<br>
<form id="form1" name="form1" method="post" action="" onSubmit="">
<br>

 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>IGMP Snooping设置/IGMP统计</b></font></td></tr>
 </table>
 
<!--
<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
<tr>
<td width="51%"  class=Tablelist id=tabs name=tabs>IGMP统计</td>
<td width="49%"   class="tablenew" id=tabs name=tabs>&nbsp;</td>
</tr>
</table>
-->

<table width="98%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table1"  >
<tr class="table_maintable">
<td width="16%"  align="center"  class="all_tables_list">VLAN</td>
<td width="14%" align="center"   class="all_tables_list">v1 report</td>
<td width="14%"  align="center"  class="all_tables_list">v2 report</td>
<td width="14%"  align="center"  class="all_tables_list">v3 report</td>
<td width="14%"  align="center"  class="all_tables_list">v2 leave</td>
<td width="14%"  align="center"  class="all_tables_list">query</td>
<td width="14%"  align="center"  class="all_tables_list">general query</td>
</tr>
<script language="javascript">
writeLines();

</script></table>
<span class="table_main1"></span></form>
<br><br><br>
</BODY></HTML>
