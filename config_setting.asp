<HTML>

<HEAD>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<TITLE>page</TITLE>
		<META http-equiv=Content-Type content="text/html; charset=utf8">
		<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
		<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
		<link href="css/display_web.css" rel="stylesheet" type="text/css" />
		<script>
<% var Allipaddr, errorcode; getIpAddr(); %>
<% var timeshow; gettimeshow(); %>

				function tdIpCheck(textValue) {
					re1 = /(\d+)(\W)(\d+)(\W)(\d+)(\W)(\d+)/
					Check = textValue.search(re1);
					if (Check == -1) {
						return false;
					}
					else {
						ipSplit = textValue.split('.');
						if (ipSplit.length != 4) {
							return false;
						}

						for (i = 0; i < ipSplit.length; i++) {
							if (isNaN(ipSplit[i])) return false;
							if (ipSplit[i].length > 1 && ipSplit[i].charAt(0) == "0") return false;
						}

						for (i = 0; i < ipSplit.length; i++) {
							if (ipSplit[i] > 255) {
								return false;
							}
							if (ipSplit[i] < 0) {
								return false;
							}
						}
						if ((ipSplit[0] == 255) && (ipSplit[1] == 255) && (ipSplit[2] == 255) && (ipSplit[3] == 255)) {
							return false;
						}

						if ((ipSplit[0] == 0) || (ipSplit[0] == 127) || (ipSplit[3] == 0) || (ipSplit[3] == 255)) {
							return false;
						}

						if (ipSplit[0] >= 224) {
							return false;
						}
						return true;
					}
				}

			function IpCheckAndMask(ip_addr) {
				/*by FC-fcy 2012-4-23 start*/
				var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])(\u002F)(([0-9])|([1-2][0-9])|(3[0-2]))$/;
				/*by FC-fcy 2012-4-23 end*/
				if (myRE.test(ip_addr)) {
					return true;
				}
				else {
					return false;
				}
			}

			function showHelp(helpname, lang) {
				var tmp = lang + "_help.html#" + helpname;
				window.open(tmp);
			}

			function changebgcolor() {
				var tab = document.all.table1;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							tab.rows[i].cells[j].className = "all_tables all_tables2";
						}
						else {
							tab.rows[i].cells[j].className = "all_tables all_tables1";
						}

					}

				}
			}

			function changebgcolor2() {
				var tab = document.all.table2;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							tab.rows[i].cells[j].className = "all_tables all_tables2";
						}
						else {
							tab.rows[i].cells[j].className = "all_tables all_tables1";
						}

					}

				}
			}


			function changebgcolor3() {
				var tab = document.all.table3;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							tab.rows[i].cells[j].className = "all_tables all_tables2";
						}
						else {
							tab.rows[i].cells[j].className = "all_tables all_tables1";
						}

					}

				}
			}

			function changebgcolor4() {
				var tab = document.all.table4;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							tab.rows[i].cells[j].className = "all_tables all_tables2";
						}
						else {
							tab.rows[i].cells[j].className = "all_tables all_tables1";
						}

					}

				}
			}


			function changebgcolor5() {
				var tab = document.all.table5;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							tab.rows[i].cells[j].className = "all_tables all_tables2";
						}
						else {
							tab.rows[i].cells[j].className = "all_tables all_tables1";
						}

					}

				}
			}

			function changebgcolor6() {
				var tab = document.all.table6;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							tab.rows[i].cells[j].className = "all_tables all_tables2";
						}
						else {
							tab.rows[i].cells[j].className = "all_tables all_tables1";
						}

					}

				}
			}


			function changebgcolor7() {
				var tab = document.all.table7;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							tab.rows[i].cells[j].className = "all_tables all_tables2";
						}
						else {
							tab.rows[i].cells[j].className = "all_tables all_tables1";
						}

					}

				}
			}

			async function messageCheck() {
				// var fName = document.getElementById("configup_filename").value;
				// var hid = document.webForm;


				// hid.action = "/jw_get_ConfigPath";
				// hid.eventType.value = 'config';
				// hid.submit();
				// var a = document.createElement('a');
				// a.href = url + '?ltime=' +<% write(lltime); %>;
				// a.download = document.getElementById("configup_filename").value;
				// a.click();
				//return true;

				var hid = document.webForm;
				var fName = hid.configup_filename.value;
				var dplen;
				var dpvalue;
				var reauthn;
				var reauthpd;


				// if (tdIpCheck(hid.configup_ip.value) == false) {
				// 	alert("IP地址输入非法！格式：A.B.C.D");
				// 	return false;
				// }

				if (fName.length <= 4) {
					alert("文件必须以 .cfg 为扩展名！");
					return false;
				}

				if (fName.indexOf(".cfg") != fName.length - 4) {
					alert("文件必须以 .cfg 为扩展名！");
					return false;
				}

				//var name = prompt("操作认证-用户名", "");
				document.getElementById("reauthn").value = await userNamePrompt();
				// var pwd = prompt("操作认证-密码", "");
				// document.getElementById("reauthpd").value = pwd;
				document.getElementById("reauthpd").value = await testThePrompt();

				reauthn = document.getElementById("reauthn").value;
				reauthpd = document.getElementById("reauthpd").value;

				dplen = parseInt(reauthn.length / 2);
				dpvalue = reauthn;
				reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
					+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

				dplen = parseInt(reauthpd.length / 2);
				dpvalue = reauthpd;
				document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
					+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
				//console.log(reauthn, 'reauthn')
				document.getElementById("reauthn").value = reauthn
				// "NI1w98Rc%Pk!3$jD";

				document.getElementById("aaa").disabled = true;
				document.getElementById("bbb").disabled = true;
				document.getElementById("ccc").disabled = true;
				//document.getElementById("ddd").disabled = true;
				document.getElementById("eee").disabled = true;
				document.getElementById("fff").disabled = true;

				const formData = new FormData();
				formData.append('eventType', 'config');
				formData.append('reauthn', document.getElementById("reauthn").value);
				formData.append('reauthpd', document.getElementById("reauthpd").value);
				formData.append('configup_ip', hid.configup_ip.value);
				formData.append('ltime', hid.ltime.value);
				formData.append('lastts', hid.lastts.value);
				formData.append('alpha', hid.alpha.value);
				fetch('/goform/jw_get_ConfigPath', {
					method: 'POST',
					body: formData

				}).then(function (response) {

					if (response.url.indexOf('error') != -1) {

						alert('不具备执行该操作的权限')
						window.location.reload();
						return
					}
					const href = response.url;
					//获取path参数
					const path1 = href.split('?')[1];

					var a = document.createElement('a');
					a.href = window.location.origin + '/' + path1.split('&')[1].slice(5) + '?ltime=' +<% write(lltime); %>
						a.download = document.getElementById("configup_filename").value;
					a.click();
					setTimeout(function () {
						window.location.reload();
					}, 1000);
					//
				}).catch(function (e) {
					alert('下载失败,请刷新页面重试')

				});
				//hid.submit();
				return true;

			}

			async function messageCheck2() {
				var hid = document.webForm;
				var fName = hid.configdown_filename.value;
				var dplen;
				var dpvalue;
				var reauthn;
				var reauthpd;


				// if (tdIpCheck(hid.configdown_ip.value) == false) {
				// 	alert("IP地址输入非法！格式：A.B.C.D");
				// 	return false;
				// }

				if (fName.length <= 4) {
					alert("文件必须以 .cfg 为扩展名！");
					return false;
				}


				if (fName.indexOf(".cfg") != fName.length - 4) {
					alert("文件必须以 .cfg 为扩展名！");
					return false;
				}

				//var name = prompt("操作认证-用户名", "");
				document.getElementById("reauthn").value = await userNamePrompt();
				// var pwd = prompt("操作认证-密码", "");
				// document.getElementById("reauthpd").value = pwd;
				document.getElementById("reauthpd").value = await testThePrompt();


				reauthn = document.getElementById("reauthn").value;
				reauthpd = document.getElementById("reauthpd").value;

				dplen = parseInt(reauthn.length / 2);
				dpvalue = reauthn;
				reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
					+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

				dplen = parseInt(reauthpd.length / 2);
				dpvalue = reauthpd;
				document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
					+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
				document.getElementById("reauthn").value = reauthn

				//console.log(reauthn, 'reauthn', reauthpd, 'reauthpd')
				document.getElementById("aaa").disabled = true;
				document.getElementById("bbb").disabled = true;
				document.getElementById("ccc").disabled = true;
				//document.getElementById("ddd").disabled = true;
				document.getElementById("eee").disabled = true;
				document.getElementById("fff").disabled = true;
				const fileInput = document.getElementById('configdown_filename');
				const file = fileInput.files[0]; // 获取用户选择的文件
				if (file) {
					if (file.size > 1024 * 1024 * 6) {
						alert('文件过大，不支持该配置文件导入')
						window.location.reload();
						return
					}
					const pro = document.getElementsByClassName("progress")[0];
					pro.style.display = "block";

					uploadFile(file); // 调用上传文件的函数
				}

				// hid.action = "/goform/downloadConfig";
				// hid.submit();
				return true;

			}
			function uploadFile(file) {
				const formData = new FormData(); // 创建FormData对象
				formData.append('configdown_filename', file); // 将文件添加到FormData中，可以设置自定义的键名
				formData.append('configdown_ip', '127.0.0.1');
				formData.append('reauthn', document.getElementById("reauthn").value);
				formData.append('reauthpd', document.getElementById("reauthpd").value);
				formData.append('ltime', document.getElementsByName("ltime")[0].value);
				formData.append('lastts', document.getElementsByName("lastts")[0].value);
				formData.append('alpha', document.getElementsByName("alpha")[0].value);
				const progress = document.getElementById("progress");
				const span = document.getElementById("span");
				const timer = setInterval(() => {
					if (progress.value < 99) {
						progress.value++;
						span.innerHTML = progress.value + "%";
					}
					else {
						clearInterval(timer);
					}

				}, 200)
				// 发送POST请求
				fetch('/goform/downloadConfig', {
					method: 'POST',
					body: formData
				})
					.then(response => {

						if (response.url.indexOf('error') != -1) {
							//获取path参数
							const href = response.url;
							const path1 = href.split('?')[1];
							const search = new URLSearchParams(path1)
							const params = Object.fromEntries(search.entries())
							document.getElementsByClassName("progress")[0].style.display = "none";
							if (params.error == 3) {
								alert('密码校验失败')
								window.location.reload();
								return
							} else if (params.error == 4) {
								alert('更新失败')
								window.location.reload();
								return
							} else {
								alert('不具备执行该操作的权限')
								window.location.reload();
							}


							return
						}
						if (response.status == 200) {
							progress.value = 100;
							span.innerHTML = progress.value + "%";
							document.getElementsByClassName("progress")[0].style.display = "none";
							setTimeout(() => {
								alert("恢复成功！");
								window.location.reload();
							}, 200)
						}
						else {
							document.getElementsByClassName("progress")[0].style.display = "none";
							alert("恢复失败！");
							window.location.reload();
						}
					})
					.catch(error => {
						// 处理错误

						window.location.reload();
					});
			}
			async function messageCheck3() {
				var hid = document.webForm;
				var fName = hid.config61850up_filename.value;
				var dplen;
				var dpvalue;
				var reauthn;
				var reauthpd;


				// if (tdIpCheck(hid.config61850up_ip.value) == false) {
				// 	alert("IP地址输入非法！格式：A.B.C.D");
				// 	return false;
				// }

				if (fName.length <= 4) {
					alert("文件必须以 .xml 为扩展名！");
					return false;
				}


				if (fName.indexOf(".xml") != fName.length - 4) {
					alert("文件必须以 .xml 为扩展名！");
					return false;
				}

				//var name = prompt("操作认证-用户名", "");
				document.getElementById("reauthn").value = await userNamePrompt();
				// var pwd = prompt("操作认证-密码", "");
				// document.getElementById("reauthpd").value = pwd;
				document.getElementById("reauthpd").value = await testThePrompt();


				reauthn = document.getElementById("reauthn").value;
				reauthpd = document.getElementById("reauthpd").value;

				dplen = parseInt(reauthn.length / 2);
				dpvalue = reauthn;
				reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
					+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

				dplen = parseInt(reauthpd.length / 2);
				dpvalue = reauthpd;
				document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
					+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
				document.getElementById("reauthn").value = "4qNvm4qu5Fcqeqp7";


				document.getElementById("aaa").disabled = true;
				document.getElementById("bbb").disabled = true;
				document.getElementById("ccc").disabled = true;
				//document.getElementById("ddd").disabled = true;
				document.getElementById("eee").disabled = true;
				document.getElementById("fff").disabled = true;
				const formData = new FormData();
				formData.append('eventType', 'versionInfo');
				formData.append('reauthn', document.getElementById("reauthn").value);
				formData.append('reauthpd', document.getElementById("reauthpd").value);
				formData.append('configup_ip', hid.configup_ip.value);
				formData.append('ltime', hid.ltime.value);
				formData.append('lastts', hid.lastts.value);
				formData.append('alpha', hid.alpha.value);
				fetch('/goform/jw_get_ConfigPath', {
					method: 'POST',
					body: formData

				}).then(function (response) {
					//console.log(response)
					if (response.url.indexOf('error') != -1) {

						alert('不具备执行该操作的权限')
						window.location.reload();
						return
					}
					const href = response.url;

					const path1 = href.split('?')[1];

					var a = document.createElement('a');
					a.href = window.location.origin + '/' + path1.split('&')[1].slice(5) + '?ltime=' +<% write(lltime); %>
						a.download = document.getElementById("config61850up_filename").value;
					a.click();
					setTimeout(function () {
						window.location.reload();
					}, 1000);
					//
				}).catch(function (e) {
					alert('下载失败,请刷新页面重试')

				});

				// hid.action = "/goform/uploadVerxmlFile";
				// hid.submit();
				return true;

			}

			function messageCheck4() {
				var hid = document.webForm;
				var fName = hid.config61850down_filename.value;


				if (tdIpCheck(hid.config61850down_ip.value) == false) {
					alert("IP地址输入非法！格式：A.B.C.D");
					return false;
				}

				if (fName.length <= 4) {
					alert("文件必须以 .cid 为扩展名！");
					return false;
				}


				if (fName.indexOf(".cid") != fName.length - 4) {
					alert("文件必须以 .cid 为扩展名！");
					return false;
				}

				document.getElementById("aaa").disabled = true;
				document.getElementById("bbb").disabled = true;
				document.getElementById("ccc").disabled = true;
				//document.getElementById("ddd").disabled = true;
				document.getElementById("eee").disabled = true;
				document.getElementById("fff").disabled = true;


				hid.action = "/goform/download61850Config";
				hid.submit();
				return true;

			}

			async function messageCheck5() {
				var hid = document.webForm;
				var fName = hid.uploadsystemlog_filename.value;
				var dplen;
				var dpvalue;
				var reauthn;
				var reauthpd;


				// if (tdIpCheck(hid.uploadsystemlog_ip.value) == false) {
				// 	alert("IP地址输入非法！格式：A.B.C.D");
				// 	return false;
				// }

				if (fName.length <= 4) {
					alert("文件必须以 .log 为扩展名！");
					return false;
				}


				if (fName.indexOf(".log") != fName.length - 4) {
					alert("文件必须以 .log 为扩展名！");
					return false;
				}

				//var name = prompt("操作认证-用户名", "");
				document.getElementById("reauthn").value = await userNamePrompt();
				// var pwd = prompt("操作认证-密码", "");
				// document.getElementById("reauthpd").value = pwd;
				document.getElementById("reauthpd").value = await testThePrompt();


				reauthn = document.getElementById("reauthn").value;
				reauthpd = document.getElementById("reauthpd").value;

				dplen = parseInt(reauthn.length / 2);
				dpvalue = reauthn;
				reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
					+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

				dplen = parseInt(reauthpd.length / 2);
				dpvalue = reauthpd;
				document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
					+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
				document.getElementById("reauthn").value = "Ig9&N8^CpIBFhXIM";


				document.getElementById("aaa").disabled = true;
				document.getElementById("bbb").disabled = true;
				document.getElementById("ccc").disabled = true;
				//document.getElementById("ddd").disabled = true;
				document.getElementById("eee").disabled = true;
				document.getElementById("fff").disabled = true;
				const formData = new FormData();
				formData.append('eventType', 'systemLog');
				formData.append('reauthn', document.getElementById("reauthn").value);
				formData.append('reauthpd', document.getElementById("reauthpd").value);
				formData.append('configup_ip', hid.configup_ip.value);
				formData.append('ltime', hid.ltime.value);
				formData.append('lastts', hid.lastts.value);
				formData.append('alpha', hid.alpha.value);
				fetch('/goform/jw_get_ConfigPath', {
					method: 'POST',
					body: formData

				}).then(function (response) {
					if (response.url.indexOf('error') != -1) {

						alert('不具备执行该操作的权限')
						window.location.reload();
						return
					}

					try {
						const href = response.url;
						const path1 = href.split('?')[1];

						var a = document.createElement('a');
						a.href = window.location.origin + '/' + path1.split('&')[1].slice(5) + '?ltime=' +<% write(lltime); %>


							a.download = document.getElementById("uploadsystemlog_filename").value;
						a.click();
						setTimeout(function () {
							window.location.reload();
						}, 1000);
					} catch (error) {

					}

					//
				}).catch(function (e) {
					alert('下载失败,请刷新页面重试')

				});


				// hid.action = "/goform/uploadSystemlog";
				// hid.submit();
				return true;

			}

			async function messageCheck6() {
				var hid = document.webForm;
				var fName = hid.uploadalarmlog_filename.value;
				var dplen;
				var dpvalue;
				var reauthn;
				var reauthpd;


				// if (tdIpCheck(hid.uploadalarmlog_ip.value) == false) {
				// 	alert("IP地址输入非法！格式：A.B.C.D");
				// 	return false;
				// }

				if (fName.length <= 4) {
					alert("文件必须以 .log 为扩展名！");
					return false;
				}


				if (fName.indexOf(".log") != fName.length - 4) {
					alert("文件必须以 .log 为扩展名！");
					return false;
				}

				//var name = prompt("操作认证-用户名", "");
				document.getElementById("reauthn").value = await userNamePrompt();
				// var pwd = prompt("操作认证-密码", "");
				// document.getElementById("reauthpd").value = pwd;
				document.getElementById("reauthpd").value = await testThePrompt();


				reauthn = document.getElementById("reauthn").value;
				reauthpd = document.getElementById("reauthpd").value;

				dplen = parseInt(reauthn.length / 2);
				dpvalue = reauthn;
				reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
					+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

				dplen = parseInt(reauthpd.length / 2);
				dpvalue = reauthpd;
				document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
					+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
				document.getElementById("reauthn").value = "a7@Na3E1aNbREJI2";


				document.getElementById("aaa").disabled = true;
				document.getElementById("bbb").disabled = true;
				document.getElementById("ccc").disabled = true;
				//document.getElementById("ddd").disabled = true;
				document.getElementById("eee").disabled = true;
				document.getElementById("fff").disabled = true;
				const formData = new FormData();
				formData.append('eventType', 'alarmLog');
				formData.append('reauthn', document.getElementById("reauthn").value);
				formData.append('reauthpd', document.getElementById("reauthpd").value);
				formData.append('configup_ip', hid.configup_ip.value);
				formData.append('ltime', hid.ltime.value);
				formData.append('lastts', hid.lastts.value);
				formData.append('alpha', hid.alpha.value);
				fetch('/goform/jw_get_ConfigPath', {
					method: 'POST',
					body: formData

				}).then(function (response) {
					if (response.url.indexOf('error') != -1) {

						alert('不具备执行该操作的权限')
						window.location.reload();
						return
					}
					try {
						const href = response.url;
						const path1 = href.split('?')[1];

						var a = document.createElement('a');
						a.href = window.location.origin + '/' + path1.split('&')[1].slice(5) + '?ltime=' +<% write(lltime); %>


							a.download = document.getElementById("uploadalarmlog_filename").value;
						a.click();
						setTimeout(function () {
							window.location.reload();
						}, 1000);
					} catch (error) {

					}

					//
				}).catch(function (e) {
					alert('下载失败,请刷新页面重试')

				});
				// hid.action = "/goform/uploadAlarmlog";
				// hid.submit();
				return true;

			}

			function showHelpinfo() {
				showHelp('ip',<% write(lang); %>);
			}

			function display() {
				tmp = "<% write(timeshow); %>";
				document.getElementById("time_show").innerText = tmp.split(",")[0];
			}

		</script>
		<style>
			.progress {
				display: none;


			}

			.mask {
				position: absolute;
				width: 100%;
				height: 100%;
				background: rgba(0, 0, 0, 0.5);
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
			}

			.pro {

				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
			}
		</style>
</HEAD>

<BODY onload="display()">
	<br>
	<% web_get_stat(); %>
		<% var authmode; checkCurMode(); %>
			<script>
				checktop(<% write(lang); %>);
			</script>
			<form name="webForm" method="post" action="/goform/uploadConfig">
				<input type="hidden" id="reauthn" name="reauthn" value="">
				<input type="hidden" id="reauthpd" name="reauthpd" value="">
				<input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
				<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
				<input type="hidden" id="eventType" name="eventType" value="">
				<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td height="30px" class="tit">
							<font color="#0069d6">
								<div>配置文件管理</div>
							</font>
						</td>
					</tr>
				</table>

				<br>

				<div class="formContain">
					<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="20px">
								<font size="5" color="#404040">
									<div class="bot">备份交换机配置文件</div>
								</font>
							</td>
						</tr>
					</table>
					<!--
	<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
	<tr>
		<td  class=Tablelist id=tabs name=tabs>
			<nobr>&nbsp;<script>writemsg(<% write(lang); %>,"备份交换机配置文件");</script></nobr>
		</td>
	</tr>
	</table>
-->

					<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
						<TBODY>
							<TR height=22 style="display: none;">
								<TD width="42%" valign="top">&nbsp;&nbsp;TFTP Server IP:</TD>
								<td>
									<span class="crons">
										<input type="text" name="configup_ip" id="configup_ip" value="127.0.0.1">
									</span>
								</td>
							</TR>
							<TR height=22>
								<TD valign="top" width="42%">&nbsp;&nbsp;
									<script>writemsg(<% write(lang); %>, "文件名:");</script>
								</TD>
								<td>
									<input type="text" name="configup_filename" id="configup_filename"
										value="">例如:xxx.cfg
								</td>
							</TR>
							<TR height=22>
								<TD colspan="2" valign="top">
									<div align="center">
										<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "备份", "buttons_apply", "button", "aaa", "messageCheck()");</script>
									</div>
								</TD>
							</TR>
					</TABLE>
				</div>
				<br />

				<br />
				<div class="progress">
					<div class="mask"></div>
					<div class="pro"><label for="progress" style="color: aliceblue;">上传进度:<span id="span">70%</span>
						</label>
						<progress id="progress" value="0" max="100"> </progress>
					</div>

				</div>
				<div class="formContain">
					<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="20px">
								<font size="5" color="#404040">
									<div class="bot">恢复交换机配置备份</div>
								</font>
							</td>
						</tr>
					</table>
					<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table2" class="tablebord">
						<TBODY>
							<TR height=22 style="display: none;">
								<TD width="42%" valign="top">&nbsp;&nbsp;TFTP Server IP:</TD>
								<td>
									<span class="crons">
										<input type="text" name="configdown_ip" id="configdown_ip" value="127.0.0.1">
									</span>
								</td>
							</TR>
							<TR height=22>
								<TD valign="top" width="42%">&nbsp;&nbsp;
									<script>writemsg(<% write(lang); %>, "文件名:");</script>
								</TD>
								<td>
									<input type="file" name="configdown_filename" id="configdown_filename"
										value="">例如:xxx.cfg
								</td>
							</TR>
							<TR height=22>
								<TD colspan="2" valign="top">
									<div align="center">
										<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "恢复", "buttons_apply", "button", "bbb", "messageCheck2()");</script>
									</div>
								</TD>
							</TR>
					</TABLE>
				</div>
				<br />
				<br />
				<div class="formContain">
					<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="20px">
								<font size="5" color="#404040">
									<div class="bot">导出版本信息文件</div>
								</font>
							</td>
						</tr>
					</table>

					<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table3" class="tablebord">
						<TBODY>
							<TR height=22 style="display: none;">
								<TD width="42%" valign="top">&nbsp;&nbsp;TFTP Server IP:</TD>
								<td>
									<span class="crons">
										<input type="text" name="config61850up_ip" id="config61850up_ip" value="">
									</span>
								</td>
							</TR>
							<TR height=22>
								<TD valign="top" width="42%">&nbsp;&nbsp;
									<script>writemsg(<% write(lang); %>, "文件名:");</script>
								</TD>
								<td>
									<input type="text" name="config61850up_filename" id="config61850up_filename"
										value="">例如:xxx.xml
								</td>
							</TR>
							<TR height=22>
								<TD colspan="2" valign="top">
									<div align="center">
										<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "导出", "button", "button", "ccc", "messageCheck3()");</script>
									</div>
								</TD>
							</TR>
					</TABLE>
				</div>
				<!--
	<br />
	<br />
<div class="formContain">	
 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0"  style='display:none'>
	<tr><td height="20px" ><font size="5" color="#404040"><div class="bot">恢复61850配置文件</div></font></td></tr>
 </table>

	<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table4" class="tablebord" style='display:none'>
	<TBODY>
	<TR height=22>
		<TD width="42%" valign="top">&nbsp;&nbsp;TFTP Server IP:</TD>
		<td >
			<span class="crons">
			<input type="text" name="config61850down_ip"  id="config61850down_ip"  value="">
			</span>
		</td>
	</TR>
	<TR height=22>
		<TD valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"文件名:");</script></TD>
		<td >
			<input type="text" name="config61850down_filename"  id="config61850down_filename"  value="">
		</td>
	</TR>
	<TR height=22>
		<TD colspan="2" valign="top">
			<div align="center">
	  		<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"恢复","buttons_apply","button","ddd","messageCheck4()");</script>
			</div>
		</TD>
	</TR>
	</TABLE>
</div>
-->
				<br />
				<br />
				<div class="formContain">
					<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="20px">
								<font size="5" color="#404040">
									<div class="bot">导出系统日志文件systemlog.log</div>
								</font>
							</td>
						</tr>
					</table>

					<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table5" class="tablebord">
						<TBODY>
							<TR height=22 style="display: none;">
								<TD width="42%" valign="top">&nbsp;&nbsp;TFTP Server IP:</TD>
								<td>
									<span class="crons">
										<input type="text" name="uploadsystemlog_ip" id="uploadsystemlog_ip"
											value="********">
									</span>
								</td>
							</TR>
							<TR height=22>
								<TD valign="top" width="42%">&nbsp;&nbsp;
									<script>writemsg(<% write(lang); %>, "文件名:");</script>
								</TD>
								<td>
									<input type="text" name="uploadsystemlog_filename" id="uploadsystemlog_filename"
										value="">例如:xxx.log
								</td>
							</TR>
							<TR height=22>
								<TD colspan="2" valign="top">
									<div align="center">
										<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "导出", "button", "button", "eee", "messageCheck5()");</script>
									</div>
								</TD>
							</TR>
					</TABLE>
				</div>
				<br />
				<br />
				<div class="formContain">
					<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="20px">
								<font size="5" color="#404040">
									<div class="bot">导出告警日志文件alarmlog.log</div>
								</font>
							</td>
						</tr>
					</table>
					<!--
	<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
	<tr>
    	<td  class=Tablelist id=tabs name=tabs>
    	<nobr>&nbsp;<script>writemsg(<% write(lang); %>,"上传告警日志文件alarmlog.log");</script></nobr>
    	</td>
	</tr>
	</table>
	-->

					<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table6" class="tablebord">
						<TBODY>
							<TR height=22 style="display: none;">
								<TD width="42%" valign="top">&nbsp;&nbsp;TFTP Server IP:</TD>
								<td>
									<span class="crons">
										<input type="text" name="uploadalarmlog_ip" id="uploadalarmlog_ip"
											value="127.0.0.1">
									</span>
								</td>
							</TR>
							<TR height=22>
								<TD valign="top" width="42%">&nbsp;&nbsp;
									<script>writemsg(<% write(lang); %>, "文件名:");</script>
								</TD>
								<td>
									<input type="text" name="uploadalarmlog_filename" id="uploadalarmlog_filename"
										value="">例如:xxx.log
								</td>
							</TR>
							<TR height=22>
								<TD colspan="2" valign="top">
									<div align="center">
										<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "导出", "button", "button", "fff", "messageCheck6()");</script>
									</div>
								</TD>
							</TR>
					</TABLE>


					<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table7" class="tablebord"
						style='display:none'>
						<TBODY>
							<TR height=22>
								<TD valign="top">
									<div align="center">
										&nbsp;&nbsp;<label id="time_show"></label>
									</div>
								</TD>
							</TR>
					</TABLE>
				</div>
				<script>
					changebgcolor();
					changebgcolor2();
					changebgcolor3();
					//changebgcolor4();
					changebgcolor5();
					changebgcolor6();
					changebgcolor7();

<% if (errorcode == "3") { write("alert(putmsg("); write(lang); write(",'密码校验失败!'));"); } %>
<% if (errorcode != "") { if (errorcode != "3") { write_errorcode(errorcode); } } %>
				</script>

			</form>
</BODY>

</HTML>