<HTML>
<HEAD>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<%  var authmode; checkCurMode(); %>

<TITLE>page</TITLE>
<META http-equiv=Content-Type content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script>
<% var outbandip, Allipaddr,errorcode;getIllegalIpAddr(); %>

function tdIpCheck(textValue)
{
    re1=/(\d+)(\W)(\d+)(\W)(\d+)(\W)(\d+)/
    Check=textValue.search(re1);
    if(Check==-1)
    {
        return false;
    }
    else
    {
        ipSplit=textValue.split('.');
        if(ipSplit.length!=4)
        {
            return false;
        }

        for(i=0; i<ipSplit.length; i++)
        {
            if(isNaN(ipSplit[i]))  return false;
			if(ipSplit[i].length > 1 && ipSplit[i].charAt(0) == "0") return false;
        }

        for(i=0; i<ipSplit.length; i++)
        {
            if(ipSplit[i] > 255)
            {
                return false;
            }
            if(ipSplit[i] < 0)
            {
                return false;
            }
        }
        if((ipSplit[0]==255) && (ipSplit[1]==255) && (ipSplit[2]==255) && (ipSplit[3]==255))
        {
            return false;
        }

        if((ipSplit[0]==0) || (ipSplit[0]==127) || (ipSplit[3]==0) || (ipSplit[3]==255))
        {
            return false;
        }

        if(ipSplit[0] >= 224)
        {
            return false;
        }
        return true;
    }
}

function IpCheckAndMask(ip_addr)
{
/*by FC-fcy 2012-4-23 start*/
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])(\u002F)(([0-9])|([1-2][0-9])|(3[0-2]))$/;
/*by FC-fcy 2012-4-23 end*/
	if(myRE.test(ip_addr))
	{
        return true;
	}
	else
	{
		return false;
	}
}

function showHelp(helpname,lang)
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function P(index)
{
    var narr=4;
    var tbtd;
    var i;
    var tbtr = document.getElementById("table1").insertRow(-1);
	var outputstr ="";


    tbtr.classname = "td7";
	tbtr.height = "30";
    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "td7");
    tbtr.setAttribute("className", "td7");
	//tbtr.setAttribute("id", "tr_"+portId);


	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");

        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");
		tbtd.setAttribute("className", "td2");
		tbtr.appendChild(tbtd);
    }


	/*display*/
	tbtr.cells[0].innerHTML = putmsg(<% write(lang); %>, "IP"+index);
	tbtr.cells[1].innerHTML = "<input type=\"text\" name=\"setip"+index+"\"  id=\"setip"+index+"\"  value=\"\">";
    tbtr.cells[2].innerHTML = "<select name=\"setipstate"+index+"\" id=\"setipstate"+index+"\"><option value=\"1\" >allow</option><option value=\"2\" >refuse</option></select>";

	if((<% write(authmode); %> == 1)||(<% write(authmode); %> == 5))
		tbtr.cells[3].innerHTML = "<input class=\"buttons_apply\" id=\"aaa"+index+"\" name=\"aaa"+index+"\" type=\"button\" value=\""+putmsg(<% write(lang); %>, "应   用")+"\" onClick=\"return messageCheck("+index+");\">";
	else
		tbtr.cells[3].innerHTML = "&nbsp;";
}


function P_mac(index)
{
    var narr=4;
    var tbtd;
    var i;
    var tbtr = document.getElementById("table2").insertRow(-1);
	var outputstr ="";


    tbtr.classname = "td7";
	tbtr.height = "30";
    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "td7");
    tbtr.setAttribute("className", "td7");
	//tbtr.setAttribute("id", "tr_"+portId);


	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");

        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");
		tbtd.setAttribute("className", "td2");
		tbtr.appendChild(tbtd);
    }


	/*display*/
	tbtr.cells[0].innerHTML = putmsg(<% write(lang); %>, "MAC"+index);
	tbtr.cells[1].innerHTML = "<input type=\"text\" name=\"setmac"+index+"\"  id=\"setmac"+index+"\"  value=\"\">";
    tbtr.cells[2].innerHTML = "<select name=\"setmacstate"+index+"\" id=\"setmacstate"+index+"\"><option value=\"1\" >allow</option><option value=\"2\" >refuse</option></select>";

	if((<% write(authmode); %> == 1)||(<% write(authmode); %> == 5))
		tbtr.cells[3].innerHTML = "<input class=\"buttons_apply\" id=\"bbb"+index+"\" name=\"bbb"+index+"\" type=\"button\" value=\""+putmsg(<% write(lang); %>, "应   用")+"\" onClick=\"return messageCheck2("+index+");\">";
	else
		tbtr.cells[3].innerHTML = "&nbsp;";
}

var Allipaddr=[] ;
function display(){
 	var hid=document.webForm;
 	var allvalue = "<% write(Allipaddr); %>";
	var outvalue = "<% write(outbandip); %>";
	var i, j;
	var name;

	//hid.setip1.value = allvalue.split(",")[0];
	//hid.setipstate1.value  = allvalue.split(",")[1];
	for (i = 0; i < 16; i++)
	{
		j = i + 1;

		name = "setip" + j;
 		document.getElementById(name).value = allvalue.split(",")[i*2];

		name = "setipstate" + j;
 		document.getElementById(name).value = allvalue.split(",")[i*2+1];
		 Allipaddr.push([allvalue.split(",")[i*2],allvalue.split(",")[i*2+1]])
	}

 	//hid.ipaddr.value = outvalue.split(",")[0];
	//hid.globalpri2.value  = outvalue.split(",")[1];
	for (i = 0; i < 16; i++)
	{
		j = i + 1;

		name = "setmac" + j;
 		document.getElementById(name).value = outvalue.split(",")[i*2];

		name = "setmacstate" + j;
 		document.getElementById(name).value = outvalue.split(",")[i*2+1];
	}

}


function messageCheck(index)
{
 	var hid=document.webForm;
	var dplen;
	var dpvalue;
	var reauthn;
	var reauthpd;


	 hid.setipindex.value = index;

	var name = prompt("操作认证-用户名", "");
	document.getElementById("reauthn").value = name;
	var pwd = prompt("操作认证-密码", "");
	document.getElementById("reauthpd").value = pwd;



	reauthn = document.getElementById("reauthn").value;
	reauthpd = document.getElementById("reauthpd").value;

	dplen = parseInt(reauthn.length/2);
	dpvalue = reauthn;
	reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(reauthpd.length/2);
	dpvalue = reauthpd;
	document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
	document.getElementById("reauthn").value = "oZBB&6DcYQeb5$Pu";


	hid.submit();
	return true;
}

function messageCheck2(index)
{
 	var hid=document.webForm;
	var dplen;
	var dpvalue;
	var reauthn;
	var reauthpd;


	hid.setmacindex.value = index;

	var name = prompt("操作认证-用户名", "");
	document.getElementById("reauthn").value = name;
	var pwd = prompt("操作认证-密码", "");
	document.getElementById("reauthpd").value = pwd;


	reauthn = document.getElementById("reauthn").value;
	reauthpd = document.getElementById("reauthpd").value;

	dplen = parseInt(reauthn.length/2);
	dpvalue = reauthn;
	reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(reauthpd.length/2);
	dpvalue = reauthpd;
	document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
	document.getElementById("reauthn").value = "$Gmh4^&d2fTxA9n0";


	hid.action="/goform/setIllegalMacAddr";
	hid.submit();
	return true;
}
function submitIP(){
	let ipArr=[]
	for (i = 0; i < 16; i++)
	{
		j = i + 1;

		name = "setip" + j;
 		// document.getElementById(name).value 

		 name1 = "setipstate" + j;
 		// document.getElementById(name1).value;
		ipArr.push([document.getElementById(name).value,document.getElementById(name1).value])
	}
	let flagIdx=[]//有改动的index
	Allipaddr.forEach((item,index)=>{
		if(item[0]!=ipArr[index][0]||item[1]!=ipArr[index][1]){
			flagIdx.push(index+1)
		}
	})
	if(flagIdx.length==0){
		alert('没有改动')
		return
	}
	var hid=document.webForm;
	var dplen;
	var dpvalue;
	var reauthn;
	var reauthpd;


	

	var name = prompt("操作认证-用户名", "");
	document.getElementById("reauthn").value = name;
	var pwd = prompt("操作认证-密码", "");
	document.getElementById("reauthpd").value = pwd;



	reauthn = document.getElementById("reauthn").value;
	reauthpd = document.getElementById("reauthpd").value;

	dplen = parseInt(reauthn.length/2);
	dpvalue = reauthn;
	reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(reauthpd.length/2);
	dpvalue = reauthpd;
	document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
	document.getElementById("reauthn").value = "oZBB&6DcYQeb5$Pu";
	flagIdx.forEach(val=>{
		hid.setipindex.value = val
		hid.submit();
	})
	;
}

</script>
</HEAD>
<BODY  onload=display() >
<br>
<% web_get_stat(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setIllegalIpAddr">
<div id="view_help"  style="display:none">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<input type="hidden" id="reauthn" name="reauthn" value="">
<input type="hidden" id="reauthpd" name="reauthpd" value="">

<input type="hidden" name="setipindex">
<input type="hidden" name="setipaddress">
<input type="hidden" name="setipstate">

<input type="hidden" name="setmacindex">
<input type="hidden" name="setmacaddress">
<input type="hidden" name="setmacstate">


<TABLE width="98%" align="center" cellpadding=0 cellspacing=0   class="tablebord" bgcolor="efefef">


</TABLE>
</div>



 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="30px" class="tit"><font color="#0069d6"><div>非法访问控制</div></font></td></tr>
 </table>


<br>
<div class="formContain">
 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
<tr><td height="20px" ><font size="5" color="#404040"><div class="bot">非法IP访问控制</div></font><span style="font-size: 14px;"> &nbsp;(格式:A.B.C.D,  0.0.0.0表示空)</span></td></tr>
 </table>

<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord1">
  <TBODY>

    <TR height=22 bgcolor="#f9f9f9">
      <TD width="20%" align="center">
      	<script>writemsg(<% write(lang); %>,"IP1");</script>
      </TD>
      <td width="40%" align="center">
      	<input type="text" name="setip1"  id="setip1"  value="">
      </td>
      <td width="20%" align="center">
		<select name="setipstate1" id="setipstate1">
			<option value="1" >allow</option>
			<option value="2" >refuse</option>
			</select>
      </td>
      <TD align="center">

        <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"应  用","buttons_apply","button","aaa","messageCheck(1)");</script>
      </TD>
    </TR>

	<script>
		P(2);  P(3);  P(4);  P(5);
		P(6);  P(7);  P(8);  P(9);
		P(10); P(11); P(12); P(13);
		P(14); P(15); P(16);
	</script>
	
</TABLE>


</br>

 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
<tr><td height="20px" ><font size="5" color="#404040"><div class="bot">非法MAC访问控制</div></font> <span style="font-size: 14px;">&nbsp;(格式:HHHH.HHHH.HHHH, 0000.0000.0000表示空)</span></td></tr>
 </table>

<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table2" class="tablebord1">
  <TBODY>

    <TR height=22  bgcolor="#f9f9f9">
		<TD width="20%" align="center">MAC1</TD>
		<td  width="40%" align="center">
        	<input type="text" name="setmac1" id="setmac1"  value="">
		</td>

		<td  width="20%" align="center">
			<select name="setmacstate1" id="setmacstate1">
			<option value="1" >allow</option>
			<option value="2" >refuse</option>
			</select>
		</td>

		<TD align="center">
        	<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"应  用","buttons_apply","button","bbb","messageCheck2(1)");</script>
        </TD>
    </TR>

    <script>
		P_mac(2);  P_mac(3);  P_mac(4);  P_mac(5);
		P_mac(6);  P_mac(7);  P_mac(8);  P_mac(9);
		P_mac(10); P_mac(11); P_mac(12); P_mac(13);
		P_mac(14); P_mac(15); P_mac(16);
	</script>
</TABLE>
</div>
<script>
changebgcolor();

changebgcolor2();

<% if (errorcode=="3") { write("alert(putmsg(");write(lang);write(",'不具备执行该操作的权限!'));");  } %>
<% if (errorcode!="") { if (errorcode!="3") { write_errorcode(errorcode); } } %>

</script>

</form>
</BODY></HTML>

