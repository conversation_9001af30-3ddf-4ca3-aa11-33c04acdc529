<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<title>端口管理</title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script>

function messageCheck()
{
    var hid=document.webForm;
    if((hid.oldusername.value.length==0)||(hid.oldpassword.value.length==0)||(hid.newpassword1.value.length==0)||(hid.newpassword2.value.length==0))
	{
		alert("用户名和密码不能为空,请重新输入!");
		return false;
	}
	if((hid.newpassword1.value!=hid.newpassword2.value))
	{
		alert("两次输入的用户名或密码不一致,请重新输入!");
		return false;
	}
	hid.submit();
	return true;
}
function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm?name=user";
	tf.submit();
}
function init()
{
/*这里的跨域访问对于火狐浏览器会无法访问*/
	location.href="user.asp?ltime="+top.sf.ltime.value;
	
	return ;
}

</script>
</head>
<body  onload="init();">
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setUsername">
<% var errorcode,oldusername;getUsername(); %>
<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0"><tr><td>
	<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" ><tr><td valign="top" >
		<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
			<tr>
				<td>
					<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
		            <tr>
		              <td colspan="2" align="left" class="cword09">系统管理 <b><font color="#FF7F00">&gt;&gt;</font></b> 用户管理</td>
		            </tr>
        			</table>
        	   </td>
      		</tr>
		      <tr>
		        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
		            <tr height="25">
		              <td colspan="2" align="left">&nbsp;用户参数配置</th>
		            </tr>
		            <tr height="25">
		              <td width="20%" align="left" class="crons">&nbsp;原用户名</td>
		              <td align="left" class="crons">&nbsp;
		                  <input type="text" name="oldusername" disabled class="input_board3" value=<% write(oldusername); %>  >
		                 </td>
		            </tr>
		            <tr height="25">
		              <td width="20%" align="left" class="crons">&nbsp;原密码</td>
		              <td align="left" class="crons">&nbsp;
		                  <input type="password" name="oldpassword" class="input_board3" >
		                 </td>
		            </tr>
		            <tr height="25">
		              <td width="20%" align="left" class="crons">&nbsp;新密码</td>
		              <td align="left" class="crons">&nbsp;
		                  <input type="password" name="newpassword1" class="input_board3" >
		                 </td>
		            </tr>
		            <tr height="25">
		              <td width="20%" align="left" class="crons">&nbsp;确认新密码</td>
		              <td align="left" class="crons">&nbsp;
		                  <input type="password" name="newpassword2" class="input_board3">
		                 </td>
		            </tr>
			     </tr>
			     <tr>
				    <td colspan="2" align="center" height="35">
				    	<input align="middle" name="modify"  type="button" class="button" value="修  改" onClick="return messageCheck();">
				    	&nbsp;
				      <input name="Submit" type="button" class="button" value="保  存" onclick="checkData()">
				      &nbsp;
				      <input name="Help" type="button" class="button" id="Help" value="帮  助" onclick="showHelp('usernothing')"  /></td>
					</tr>
		        </table></td>
		      </tr>
    </table></td>
  </tr>
  
</table>
</td></tr>
<tr><td>
</td></tr></table>
</body>
</form>
</html>
