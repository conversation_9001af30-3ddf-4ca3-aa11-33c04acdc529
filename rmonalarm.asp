
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>

<title>&nbsp;<script>writemsg(<% write(lang); %>,"GMRP");</script></title>

<script language="JavaScript">

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

 
var portStaList=[<%rmonalarm_viewSetShow();%>];

function delmac(i)
{

	var hid = document.formaa;
	
	hid.rmonalarmviewName.value = portStaList[9*i];


	hid.action="/goform/rmonalarm_viewSet";
	hid.submit();
	return 0;

}

function writeLines()
{
var j = 0;
for(var i=0;i<portStaList.length/9;i++)
{
document.write(" <tr  class='tables_all'>");



document.write("    <td class='inputsyslog1'>"+portStaList[j]+"</td>");

j++
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>&nbsp;"+portStaList[j]+"&nbsp;</td>");
j++;


document.write("    <td class='inputsyslog1'>"+portStaList[j]+"</td>");

j++
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;


			document.write("    <td  class='inputsyslog1'>");

			document.write("      <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick='delmac("+i+")'  />");

		document.write("      </td>");




document.write("  </tr>");

}
}
 
 
 
/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true;  
			 addToPortRange(i);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRange(i);
			}
             
        }
    } 
    
}


function checkup()
{
	//var tf = document.vlan_port;
	//tf.action = "/goform/selportsecurity";
	//tf.submit();
}


function checking2()
{
 	//var port_range = document.getElementById("port_range").value;
 
  	//var checkbox_index = document.getElementsByName("checkbox_index");
	//var table_port = document.getElementById("table_port");
	var tf=document.vlan_port;
	//var i,j;


	 
			tf.submit();
 

	//if(port_txrate.length==0)port_txrate="-";
	////if(port_txburst.length==0)port_txburst="-";
	//if(port_rxrate.length==0)port_rxrate="-";	    
	//if(port_rxburst.length==0)port_rxburst="-";	
	 

}


function AddOption(portname){

	var selectObject = document.getElementById("rmonalarmport");
	var y=document.createElement('option');
  	y.text=portname;
	y.value = portname;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  	catch(ex)
    {
   	selectObject.add(y); // IE only
  	}
}


function checkSub()
{
/*
	if(!tdOIDCheck(document.getElementById("rmonalarmsubTree").value))
	{
		alert("OID 错误");
		return;
	}
	if(document.getElementById("rmonalarmsubTree").value.length > 128 ||document.getElementById("rmonalarmsubTree").value.length == 0)
	{
		alert("OID 不能为空，长度也不能超过128");
		return;
	}
	if(!checkname(document.getElementById("rmonalarmviewName"),"视图名为空","视图名错误","视图名错误",1))
	//if(!tdCheckASCIICode(document.getElementById("rmonalarmviewName").value))
	{
		//alert("View Name 错误");
		return;
	}
	
	if(document.getElementById("rmonalarmviewName").value.length > 32 || document.getElementById("rmonalarmviewName").value.length == 0)
	{
		alert("视图名不能为空，长度也不能超过32");
		return;
	}
*/
	document.vlan_port.submit();
}

function refreshpage()
{

  location.href='rmonalarm.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('vlan_port',<% write(lang); %>);
}

</script>
</head>

<body  ><br>

<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>

<form  name="vlan_port" method="post" action="/goform/rmonalarm_viewSet">

<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
<tr>
    <td  class=Tablelist id=tabs name=tabs>RMON告警</td>
    <td   class="tablenew" id=tabs name=tabs><div align="right">
      <input type="hidden" name="rmonalarmDelViewID" id="rmonalarmDelViewID"/>
      <input type="hidden" name="rmonalarmDelViewOid" id="rmonalarmDelViewOid">
      <input name="b_submit2" class="buttons_apply" type="button" id="b_submit" value="提 交"  onclick="checking2()" >
        <input name="b_reset2" class="buttons_apply" type="button" onClick="javascript:window.location.reload() " id="b_reset2" value="取消">
    </div></td>
    
  </tr>
</table>
<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
  <TBODY>
    <TR height=22>
      <TD width="18%" valign="top"><div align="left">&nbsp;&nbsp;<span class="td25">告警</span></div></TD>
      <TD width="32%" ><span class="crons">
      <input type="text" name="rmonalarmviewName" id="rmonalarmviewName" size="20" maxlength="32">
&lt;1-16&gt;</span></TD>
      <TD width="18%" >&nbsp;&nbsp;端口</TD>
      <TD width="32%" ><span class="crons"><select id="rmonalarmport" name="rmonalarmport"></select>
	 	<script>
						<% AppendOptionForfil(); %>	
					</script>				 
					
      </span></TD>
    </TR>
    <TR height=22>
      <TD valign="top">&nbsp;&nbsp;<span class="td25">detection interval</span></TD>
      <TD ><span class="crons">
        <input type="text" name="rmonalarminterval" id="rmonalarminterval" size="20" maxlength="128">
      </span></TD>
      <TD >&nbsp;<span class="td25">&nbsp;&nbsp;oid of packet type</span></TD>
      <TD ><span class="crons">
        <input type="text" name="rmonalarmoid" id="rmonalarmoid" size="20" maxlength="128">
      </span></TD>
    </TR>
    <TR height=22>
      <TD valign="top">&nbsp; value</TD>
      <TD ><span class="crons">
        <select name="rmoneventvalue" id="rmoneventvalue" size="1">
          <option value="delta"  >DELTA</option>
          <option value="absolute"  >ABSOLUTE</option>
        </select>
      </span></TD>
      <TD >&nbsp;</TD>
      <TD >&nbsp;</TD>
    </TR>
    <TR height=22>
      <TD >&nbsp;&nbsp;rising-threshold</TD>
      <TD ><span class="crons">
        <input type="text" name="rmonalarmrisingthreshold" id="rmonalarmrisingthreshold" size="20" maxlength="128">
      </span></TD>
      <TD valign="top">&nbsp;&nbsp;event index</TD>
      <TD ><span class="crons">
        <input type="text" name="rmonalarmeventindex1" id="rmonalarmeventindex1" size="20" maxlength="128">
      </span></TD>
    </TR>
    <TR height=22>
      <TD >&nbsp;&nbsp;falling-threshold</TD>
      <TD ><span class="crons">
        <input type="text" name="rmonalarmfallingthreshold" id="rmonalarmfallingthreshold" size="20" maxlength="128">
      </span></TD>
      <TD valign="top">&nbsp;&nbsp;event index</TD>
      <TD ><span class="crons">
        <input type="text" name="rmonalarmeventindex2" id="rmonalarmeventindex2" size="20" maxlength="128">
      </span></TD>
    </TR>
</TABLE>



<br>
<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
  <tr>
    <td width="51%"  class=Tablelist id=tabs name=tabs>RMON告警表</td>
    <td width="49%"   class="tablenew" id=tabs name=tabs><div align="right"></div></td>
  </tr>
</table>
<table width="98%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table_port_vlan"  >
    <TR align="center" height=22>
      <TD width="9%"   nowrap class="all_tables_list">告警</TD>
      <TD width="9%"   nowrap class="all_tables_list">端口</TD>
      <TD width="9%"   nowrap class="all_tables_list"><span class="td25">detection interval</span></TD>
      <TD width="9%"   nowrap class="all_tables_list"><span class="td25">oid of packet type</span></TD>
      <TD width="9%"   nowrap class="all_tables_list">value</TD>
      <TD width="9%"   nowrap class="all_tables_list">rising-threshold</TD>
      <TD width="9%"   nowrap class="all_tables_list">event index</TD>
      <TD width="9%"   nowrap class="all_tables_list">&falling-threshold</TD>
      <TD width="9%"   nowrap class="all_tables_list">event index</TD>
      <TD width="19%"   nowrap class="all_tables_list"><span class="partition">删除</span></TD>
    </TR>
  <script language="javascript">
  writeLines();
  </script>
  </table>
</form>
<form name="formaa" method="POST" action="">
<input name="rmonalarmviewName" type="hidden" class="input_x"  >
<input name="del_flag" type="hidden" class="input_x"  value="1">
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

</form> 
<script>
changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</body>
</html>
