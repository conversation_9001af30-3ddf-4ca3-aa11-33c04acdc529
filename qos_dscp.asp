<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>

<title><script>writemsg(<% write(lang); %>,"DSCP映射");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
 
<script language="JavaScript">
 
 
var dscpStaList=[<%QosDscpShow();%>];

function writeLines()
{
var j = 0;
for(var i=0;i<(dscpStaList.length-1)/2;i++)
{
document.write(" <tr  class='tables_all'>");


 
document.write("    <td class='inputsyslog1'><input type='checkbox' name='checkbox_index' onchange=addToPortRange('"+i+"') ></td>");

document.write("    <td class='inputsyslog1'>"+dscpStaList[j]+"</td>");

j++
document.write("    <td  class='inputsyslog1'>"+dscpStaList[j]+"</td>");
j++;
 

document.write("  </tr>");

}
}
 
/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true;  
			 addToPortRange(i);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRange(i);
			}
             
        }
    } 
    
}

/*MTU judgment*/
function checking2()
{
	var tf=document.port_setting;
	tf.submit();
}

/*display function*/
function P(portId, admin,links,nego,cspeed,flowS,flowR,setmtu,portDesc,txlinerate,txburst,rxlinerate,rxburst)
{

    var narr=7;
	var arr="";
	var speed="";
	var Desc="";
    var tbtd;
    var i;
    var opt;
    var tbtr = document.getElementById("table_port").insertRow(-1);

	
    tbtr.classname = "td7";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "td7");
    tbtr.setAttribute("className", "td7");
	tbtr.setAttribute("id", "tr_"+portId);

	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
	/*	
		if(i==4 || i==11 )
		{
			tbtd.setAttribute("style","display:none");
		}
		*/
        tbtr.appendChild(tbtd);
    }


	arr=cspeed.split(".");
	if(arr[1]=="full")
	{
		if(arr[0]!=0)
			speed=arr[0]+putmsg(<% write(lang); %>,"/全双工");
		else
			speed=putmsg(<% write(lang); %>,"自动协商");
	}
	else
	{
		if(arr[0]!=0)
			speed=arr[0]+putmsg(<% write(lang); %>,"/半双工");
		else
			speed=putmsg(<% write(lang); %>,"自动协商");
	}
		
	/*display*/
	tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"checkbox_index\" value=\""+portId+"\" onclick=\"addToPortRange(this)\"/>";
	tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = putmsg(<% write(lang); %>,(admin=="up")?"开启":"关闭");
    tbtr.cells[3].innerHTML = links;
    //tbtr.cells[4].innerHTML = putmsg(<% write(lang); %>,speed);
	tbtr.cells[4].innerHTML = putmsg(<% write(lang); %>,(flowS=="up")?"开启":"关闭");
	tbtr.cells[5].innerHTML = putmsg(<% write(lang); %>,(flowR=="up")?"开启":"关闭");
	//tbtr.cells[7].innerHTML = txlinerate;
	//tbtr.cells[8].innerHTML = txburst;
	//tbtr.cells[9].innerHTML = rxlinerate;
	//tbtr.cells[10].innerHTML = rxburst;
	//tbtr.cells[7].innerHTML = setmtu;
	if(portDesc.length>10){
		Desc=portDesc.substring(0,portDesc.length-(portDesc.length-10));
		tbtr.cells[6].innerHTML = "<font title=\""+portDesc+"\">"+Desc+"......</font>";
	}
	else
		tbtr.cells[6].innerHTML = "<font title=\""+portDesc+"\">"+portDesc+"</font>";

}

/*
	Show all check true port, and will last a port data displayed
*/
function addToPortRange(index){
	//alert(index);

	
	var target = document.getElementById("dscp_range");

	var qos_cos=document.getElementById("qos_cos");

     var objs = document.getElementsByName("checkbox_index"); 

	if(objs[index].checked){

		target.value = target.value  + dscpStaList[2*index] + " ";
		
 
			qos_cos.value= dscpStaList[2*index+1];

	}else{

		target.value = target.value.replace(dscpStaList[2*index]+" ","");
	}

}

function checkData()
{
	var tf=document.port_setting;
	tf.action = "/goform/saveComm?name=port";
	tf.submit();
}

function refreshpage()
{
  location.href='qos_dscp.asp?ltime='+<% write(lltime); %>;
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor_port(){
 var tab = document.all.table_port;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function showHelpinfo()
{
   showHelp('port',<% write(lang); %>);
}

</SCRIPT>
</head>

<body  onload=""><br>

<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="port_setting" method="POST" action="/goform/setDscp">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" ><table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td>

 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>QOS设置/DSCP映射</b></font></td></tr>
 </table>
 
        </td>
      </tr>
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">
            <tr height="30">
              <td align="left" class="td7">&nbsp;<script>writemsg(<% write(lang); %>,"DSCP选择");</script></td>
              <td width="81%" align="left" class="td7">&nbsp;
                <input name="dscp_range" type="text" class="input_x" id="dscp_range" readonly="true"/></td>
            </tr>
   <tr height="30"  >
              <td width="20%" align="left" class="td7"  >&nbsp;<script>writemsg(<% write(lang); %>,"映射COS值");</script></td>
              <td align="left" class="td7">&nbsp;			  <span class="crons">
                <select name="qos_cos" id="qos_cos" >
                  <option value="0" >0</option>
                  <option value="1" >1</option>
                  <option value="2" >2</option>
                  <option value="3" >3</option>
                  <option value="4" >4</option>
                  <option value="5" >5</option>
                  <option value="6" >6</option>
                  <option value="7" >7</option>
               </select>
                </span></td>
            </tr>
  
			<tr>
			 	<td colspan="2" align="center" class="td7">
			 	<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify","checking2()");</script>				</td>
			 </tr>
			
        </table></td>
      </tr>
      <tr>
         <td height="8"></td>
      </tr>
      <tr>
        <td><table width="100%" height="35"  border="0" cellpadding="0" cellspacing="0"  class="tablebord" id="table_port">
            <tr height="30" align="center" class="td7">
              <th width="18%" height="13" align="center" class="td2"><input type="checkbox" name="check_all" value="all" onClick="selectToAll()" /></th>
              <th align="center" class="td2" width="31%"><script>writemsg(<% write(lang); %>,"DSCP");</script></th>
              <th align="center" class="td2" width="51%"><script>writemsg(<% write(lang); %>,"COS");</script></th>
              </tr>
			
	
           <script language="javascript">
writeLines();

</script>
        </table></td>
      </tr>
      <tr>
        <td align="center" height="35">
          <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
          &nbsp;
         <!-- <input name="Submit" type="button" class="button" value="保 存" onclick="checkData()">
          &nbsp;
          <script>writebutton(1,<% write(lang); %>,"帮  助","button","button","Help","showHelpinfo()");</script>
-->          
      </tr>
    </table></td>
  </tr> 
</table>
</td></tr>


</table>

<input type="hidden" name="todo" value="save">
<INPUT type="hidden" name="this_file" value="qosdscp.asp">
<INPUT type="hidden" name="next_file" value="qosdscp.asp">
<input type="hidden" name="message" value="@msg_text#">
</form> 
<script>
changebgcolor();
changebgcolor_port();
<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>

</html>

