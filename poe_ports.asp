<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<title>端口管理</title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script>
var first_run;
function show_poe_port_state(line1,line2,line3,line4,line5,line6,line7,line8,line9,line10,line11,line12,line13)
{
	document.write("<tr height='30'><td align='left' class='crons' width='40%'>&nbsp;Port</td><td align='left' class='crons' width='60%'>&nbsp;"+line13+"</td></tr>");
	document.write("<tr height='30'><td align='left' class='crons' width='40%'>&nbsp;Port power enabled</td><td align='left' class='crons' width='60%'>&nbsp;"+line1+"</td></tr>");
	document.write("<tr height='30'><td align='left' class='crons' width='40%'>&nbsp;Port power on/off</td><td align='left' class='crons' width='60%'>&nbsp;"+line2+"</td></tr>");
	/*
	document.write("<tr height='30'><td align='left' class='crons' width='40%'>&nbsp;Port power status</td><td align='left' class='crons' width='60%'>&nbsp;"+line3+"</td></tr>");
	document.write("<tr height='30'><td align='left' class='crons' width='40%'>&nbsp;Port power mode</td><td align='left' class='crons' width='60%'>&nbsp;"+line4+"</td></tr>");
	*/
	document.write("<tr height='30'><td align='left' class='crons' width='40%'>&nbsp;Port PD class</td><td align='left' class='crons' width='60%'>&nbsp;"+line5+"</td></tr>");
	document.write("<tr height='30'><td align='left' class='crons' width='40%'>&nbsp;port power priority</td><td align='left' class='crons' width='60%'>&nbsp;"+line6+"</td></tr>");
	document.write("<tr height='30'><td align='left' class='crons' width='40%'>&nbsp;Port max power</td><td align='left' class='crons' width='60%'>&nbsp;"+line7+"</td></tr>");
	document.write("<tr height='30'><td align='left' class='crons' width='40%'>&nbsp;Port current power</td><td align='left' class='crons' width='60%'>&nbsp;"+line8+"</td></tr>");
	/*
	document.write("<tr height='30'><td align='left' class='crons' width='40%'>&nbsp;Port peak power</td><td align='left' class='crons' width='60%'>&nbsp;"+line9+"</td></tr>");
	document.write("<tr height='30'><td align='left' class='crons' width='40%'>&nbsp;Port average power</td><td align='left' class='crons' width='60%'>&nbsp;"+line10+"</td></tr>");
	*/
	document.write("<tr height='30'><td align='left' class='crons' width='40%'>&nbsp;Port current</td><td align='left' class='crons' width='60%'>&nbsp;"+line11+"</td></tr>");
	document.write("<tr height='30'><td align='left' class='crons' width='40%'>&nbsp;Port voltage</td><td align='left' class='crons' width='60%'>&nbsp;"+line12+"</td></tr>");


	document.getElementById("portNum").value = line13;
	first_run=line13;

}
function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function AddOption(port_name){
	var selectObject = document.getElementById("portNum");
	var y=document.createElement('option');
  	y.text=port_name;
	y.value=port_name;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  catch(ex)
    {
    selectObject.add(y); // IE only
    }
}
function messageCheck(option)
{
	var hid=document.webForm;
	switch(option)
	{
		case 1:
			hid.index.value=1;
			break;
		case 2:
		  hid.index.value=2;
		  if(hid.port_max_power.value.length!=0)
		  {
			  if((hid.port_max_power.value<1000)||(hid.port_max_power.value>30000))
				{
					alert("最大功率必须大于1000mW小于30000mW");
					//location.href="poe_ports.asp";
					document.webForm.submit();
					return ;
				}
			}
			break;
		case 3:
		  hid.index.value=3;
			break;
		case 4:
		  hid.index.value=4;
			break;
		default:
			return;
	}
	hid.submit();
	return ;
}

function showPoePortcfg()
{
	//window.location.href="poe_ports.asp?portNo="+portNo;
	//window.location.href="poe_ports.asp?portNo="+portNo+"&ltime="+<% write(lltime); %>;
	window.location.href="poe_ports.asp?portNo="+document.getElementById("portNum").value+"&ltime="+<% write(lltime); %>;
}
function refreshpage()
{
	window.location.href="poe_ports.asp?portNo="+document.getElementById("portNum").value+"&ltime="+<% write(lltime); %>;
}

function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm?name=poe_ports";
	tf.submit();
}
</script>
</head>
<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop();
</script>
<form  name="webForm" method="post" action="/goform/set_poe_port_cfg">

<% var errorcode,switch,mode,priority,max_power; get_poe_port_cfg(); %>
<input name="index" id="index" type="hidden">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0"><tr><td>
	<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" ><tr><td valign="top" >
		<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
	      <tr>
	        <td>
	         <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>POE设置/POE端口</b></font></td></tr>
 </table>
	        </td>
	      </tr>
	      <tr>
	        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="tablebord" id="table1">
	            <tr height="25">
	              <td colspan="3" align="left">&nbsp;端口参数管理</th>
	            </tr>
	            <tr height="25">
	            
	              <td width="20%" align="left" class="crons" colspan="3">&nbsp;端口:

                 <select name="portNum" id="portNum" onchange="showPoePortcfg();" </select>			
                 <script>
				 	<% AppendOptionPoeport(); %>
				</script>
									

                 </td> 

                 
	            </tr>
	            <tr height="25">
	              <td width="20%" align="left" class="crons">&nbsp;端口使能</td>
	              <td width="50%" align="left" class="crons">&nbsp;
	                  <input type="radio" name="port_enable" value="enable" <% if (switch=="enable") write("checked"); %>>Enable
		          			<input type="radio" name="port_enable" value="disable" <% if (switch!="enable") write("checked"); %> >Disable
	               </td>
                <td width="50%" align="left" class="crons">&nbsp;
	                  <input type="button" class="button" value="修改" onclick="messageCheck(1)">
	               </td>
	            </tr>
	            <tr height="25">
	              <td width="20%" align="left" class="crons">&nbsp;最大功率</td>
	              
	              <td width="50%" align="left" class="crons">&nbsp;
            			<!--	<input type="text" name="port_max_power" value=<% write(max_power); %>>(大于1000mW小于32000mW)-->
            		<select name="port_max_power" id="port_max_power">
					<option value="15400" <% if (max_power=="15400") write("selected"); %>>15.4W
					<option value="30000" <% if (max_power=="30000") write("selected"); %>>30.0W
					</select> 
	               </td>
	               
 	                 
                <td width="50%" align="left" class="crons">&nbsp;
	                  <input type="button" class="button" value="修改" onclick="messageCheck(2)">
	               </td>
	            </tr>
	      <!--      <tr height="25">
	              <td width="20%" align="left" class="crons">&nbsp;端口模式</td>
	              <td width="50%" align="left" class="crons">&nbsp;
	              		<input type="radio" name="port_mode" value="signal" <% if (mode=="signal") write("checked"); %>>signal
		          			<input type="radio" name="port_mode" value="spare" <% if (switch=="spare") write("checked"); %>>spare
	               </td>
                <td width="50%" align="left" class="crons">&nbsp;
	                  <input type="button" class="button" value="修改" onclick="messageCheck(3)">
	               </td>
	            </tr>-->
	            <tr height="25" style="display:none">
	              <td width="20%" align="left" class="crons">&nbsp;端口优先级</td>
	              <td width="50%" align="left" class="crons">&nbsp;
	                <select name="port_priority" id="port_priority">
									<option value="critical" <% if (priority=="critical") write("selected"); %>>critical
									<option value="high" <% if (priority=="high") write("selected"); %>>high
									<option value="low" <% if (priority=="low") write("selected"); %>>low
									</select>
	               </td>
                <td width="50%" align="left" class="crons">&nbsp;
	                  <input type="button" class="button" value="修改" onclick="messageCheck(4)">
	               </td>
	            </tr>
	        </table></td>
	      </tr>
	      <tr>
	        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="tablebord" id="table2">
	            <tr height="25">
	              <th align="left" colspan="2">&nbsp;POE端口参数</th>
	            </tr>
	            <script>
	              <% get_poe_port_state(); %>
							</script>
	        </table></td>
	      </tr>
	     <tr>
				    <td colspan="2" align="center" height="35">
				    	<input align="middle" name="modify"  type="button" class="button" value="刷 新" onClick="refreshpage();">
				      </td>
					</tr>
    </table></td>
  </tr> 
</table>
</td></tr></table>
<script>
changebgcolor();
changebgcolor2();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</form>
</html>
