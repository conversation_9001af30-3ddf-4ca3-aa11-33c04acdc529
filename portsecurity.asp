<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<%  var authmode; checkCurMode(); %>

<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"GMRP");</script></title>

<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>


<script language="JavaScript">

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}


 
var portStaList=[<%portsecurityMacShow();%>];

function delmac(i)
{

	var hid = document.formaa;
	
	hid.port_range.value = portStaList[3*i];
	hid.mac_range.value = portStaList[3*i+1];
	hid.vlan_range.value = portStaList[3*i+2];


	hid.action="/goform/PortSecurityMac";
	hid.submit();
	return 0;

}

function writeLines()
{
var j = 0;
for(var i=0;i<portStaList.length/3;i++)
{
document.write(" <tr  class='tables_all'>");



document.write("    <td class='inputsyslog1'>"+portStaList[j]+"</td>");

j++
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;
			document.write("    <td  class='inputsyslog1'>");

		if (<% write(authmode); %> == 1)
			document.write("      <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick='delmac("+i+")'  />");

		document.write("      </td>");




document.write("  </tr>");

}
}
 
 
 
/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true;  
			 addToPortRange(i);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRange(i);
			}
             
        }
    } 
    
}


function checkup()
{
	//var tf = document.vlan_port;
	//tf.action = "/goform/selportsecurity";
	//tf.submit();
}


function checking2()
{
 	var port_range = document.getElementById("port_range").value;
 
  	var checkbox_index = document.getElementsByName("checkbox_index");
	var table_port = document.getElementById("table_port");
	var tf=document.vlan_port;
	var i,j;


	 
			tf.submit();
 

	//if(port_txrate.length==0)port_txrate="-";
	////if(port_txburst.length==0)port_txburst="-";
	//if(port_rxrate.length==0)port_rxrate="-";	    
	//if(port_rxburst.length==0)port_rxburst="-";	
	 

}


function AddOption(portname){

	var selectObject = document.getElementById("port_range");
	var y=document.createElement('option');
  	y.text=portname;
	y.value = portname;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  	catch(ex)
    {
   	selectObject.add(y); // IE only
  	}
}



function addToPortRange(index)
{
	//alert(index);

	
	var target = document.getElementById("port_range");
	var mac_value = document.getElementById("mac_range");
	var vlan_speed = document.getElementById("vlan_range");
	
//	var port_flow_t = document.getElementById("port_flow_t");
//	var port_flow_r = document.getElementById("port_flow_r");
//	var port_mtu = document.getElementById("port_mtu");
//	var port_description=document.getElementById("port_description");

     var objs = document.getElementsByName("checkbox_index"); 

	if(objs[index].checked){

		target.value = portStaList[3*index];
		mac_value.value = portStaList[3*index+1];
		vlan_speed.value = portStaList[3*index+2];
 
	}else{

		target.value = "";
		mac_value.value ="";
		vlan_speed.value ="";
 	}

}

function P(portId,enable)
{
    var narr=2;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+portId);	


    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		
	        tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
    tbtr.cells[0].abbr = portId;
    
   
	

		//tbtr.cells[0].innerHTML = "<input type=\"checkbox\" id=\"input_"+portId+"\" name=\"checkbox_index\" value=\""+portId+"\" onclick=\"addToPortRange(this)\"/>";
	
		tbtr.cells[0].innerHTML = portId;
    tbtr.cells[1].innerHTML = enable;


}


function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function changebgcolor_port(){
 var tab = document.all.table_port;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function refreshpage()
{
  location.href='portsecurity.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('vlan_port',<% write(lang); %>);
}

</script>
</head>

<body  ><br>

<script>
	checktop(<% write(lang); %>);
</script>
<form name="vlan_port" method="POST" action="/goform/PortSecurityMac">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="vlan_port_id" id="vlan_port_id" >
<input type="hidden" name="flag" id="flag" >
<input type="hidden" name="type_value" id = "type_value"  value="@trunk_config#">
<input type="hidden" name="filter_value" id = "filter_value"  value="@trunk_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="pvid_config"  value="@pvid_config#">
<input type="hidden" name="egress_tagged" id="egress_tagged" value="@egress_tagged#">
<input name="del_flag" type="hidden" class="input_x"  value="0">

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">

<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
     	 

 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>端口MAC地址绑定</b></font></td></tr>
 </table>
 
<!--    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
	     <tr height="25">
	       <td align="left" colspan="4"  class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"MAC地址绑定");</script></td>
	     </tr>
        </table>
-->
        
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%" id="table1" class="tablebord">

		<tr height="30">
              <td width="20%" align="left" class="td7">&nbsp;<script>writemsg(<% write(lang); %>,"端口选择");</script></td>
              <td colspan="3" align="left" class="td7">&nbsp;
			  <select id="port_range" name="port_range"></select>
				 <script><% AppendOption(); %> </script>			  
			  </td>
 	    </tr>
 	          
	     <tr height="25">
	     	  <td width="20%" class="crons"><script>writemsg(<% write(lang); %>,"MAC地址");</script> (HHHH.HHHH.HHHH, 为16进制格式) </td>
	     	  <td width="30%" class="crons">&nbsp;<input name="mac_range" type="text" class="input_x" id="mac_range"  ></td>
	          <td width="20%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN");</script></td>
	          <td width="30%" class="crons">&nbsp;<input name="vlan_range" type="text" class="input_x" id="vlan_range"  ></td>
	     </tr>		 
	    <tr height="25">

	     	  <td colspan="4" align="middle" class="crons">
	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"创  建","button","button","modify","checking2()");</script>
&nbsp;&nbsp;&nbsp;</td>
	     </tr>	  
			 
   <tr>
	    	<td colspan="4">
	    		<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
	    		 	<tr align="center" height="25" class="crons">
    		 		<th class="td2" width="19%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></b></font></th>
	    		 		<th class="td2" width="34%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"MAC地址");</script></b></font></th>						
	    		 	    <th class="td2" width="35%">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN");</script></th>
	    		 	    <th class="td2" width="35%">&nbsp;<script>writemsg(<% write(lang); %>,"删除");</script></th>
    		 	</tr>

           <script language="javascript">
writeLines();

</script>
	    		</table>	    	</td>
   </tr>
     </table>
   </td></tr>
   <tr>
	  	   <td colspan="2" align="center" height="35">
	  	   <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
<!--
	  	   <script>writebutton(1,<% write(lang); %>,"帮  助","button","button","Help","showHelpinfo()");</script>
-->	  	   
	  		</td>
   </tr>
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>

</form>  
<form name="formaa" method="POST" action="">
<input name="port_range" type="hidden" class="input_x"  >
<input name="mac_range" type="hidden" class="input_x"   >
<input name="vlan_range" type="hidden" class="input_x"  >
<input name="del_flag" type="hidden" class="input_x"  value="1">
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

</form> 
<script>
changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</body>
</html>

