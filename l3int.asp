<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_3.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"端口VLAN设置");</script></title>

<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script language="JavaScript" type="text/JavaScript">

function dofirst(){
    location.href="l3int.asp?page=1&ltime="+<% write(lltime); %>;
}
function getPage(page)
{
   location.href="l3int.asp?page="+page+"&ltime="+<% write(lltime); %>;
}
function addToPortRange(obj){
	var tf = document.l3intf_setting;
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var vid = document.getElementById("vid");
	var ip = document.getElementById("ip");
	var age = document.getElementById("age");
	var pattern = document.getElementById("pattern");
	var vlan_id = document.getElementById("vlan_id");
	var sedIp = document.getElementById("sedIp");
	var vlanIdName = document.getElementById("vlanIdName");
	var l3_port = document.getElementById("l3_port");
	var p = obj.value;
	var i;

	if(obj.checked){
		sedIp.value = trobj.cells[4].innerHTML;
		vid.value = trobj.cells[1].innerHTML;
		l3_port.value = trobj.cells[2].innerHTML;
		ip.value = trobj.cells[3].innerHTML;
		age.value = trobj.cells[5].innerHTML;
		vlan_id.value = trobj.cells[2].innerHTML;
		if(pattern.options[0].text==trobj.cells[6].innerHTML)
			{
				pattern.options[0].selected=true;
				pattern.options[1].selected=false;
			}
		else
			{
				pattern.options[1].selected=true;
				pattern.options[0].selected=false;	
			}
/*		for(i=0;i<vlan_id.options.length;i++){
			if(vlan_id.options[i].text==trobj.cells[2].innerHTML)
			{
				vlan_id.options[i].selected=true;
			}
			else
			{
				vlan_id.options[i].selected=false;
			}
		}
		
		var xmlhttp;
		if (window.XMLHttpRequest){
 		 xmlhttp=new XMLHttpRequest();
 		 }
		else{
 		 xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
		  }
		xmlhttp.onreadystatechange=function()
  		{
  			if (xmlhttp.readyState==4 && xmlhttp.status==200)
  		 	 {
 		   		var text = xmlhttp.responseText;
		   		var funcs = text.split(";");
				var tablel3 = document.getElementById("table_l3intf1");
				var l = tablel3.rows.length;
				for(i=1;i<l;i++){
					tablel3.deleteRow(l-i);
				}
		   		for(i=0;i<funcs.length;i++){
					eval(funcs[i]);
		   		}
		    }
 		 }
		var url = "/goform/showL3if2ip1?vlanIdName="+trobj.cells[2].innerHTML;
		xmlhttp.open("GET",url,true);
		xmlhttp.send();
*/
	}

}

function P(portId,port,ipaddress,ipaddresssec,instance,interface)
{
    var narr=7;
    var tbtd;
    var i;
    var tbtr = document.getElementById("table_l3intf").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+portId);	

    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
    tbtr.cells[1].abbr = portId;
	
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+portId+"\" onclick=\"addToPortRange(this)\"/>";
	tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = port;
    tbtr.cells[3].innerHTML = ((ipaddress == "unassigned") ? "-":ipaddress) ;
	tbtr.cells[4].innerHTML = ((ipaddresssec == "unassigned") ? "-":ipaddresssec) ;
    tbtr.cells[5].innerHTML = instance;
    tbtr.cells[6].innerHTML = interface;		
}

function addToPortRanges(obj){
	var tdobj = obj.parentNode;
	var trobj = tdobj.parentNode;
	var sedIp = document.getElementById("sedIp");
	var vlan_id = document.getElementById("vlan_id");
	var p = obj.value;
	var i;

	if(obj.checked){
		sedIp.value = trobj.cells[4].innerHTML;
		vlan_id.value = trobj.cells[2].innerHTML;
	}
}


function SecIp(portId,SedId)
{
	var narr=3;
    var tbtd;
    var i;
    var tbtr = document.getElementById("table_l3intf1").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "t_"+portId);	

    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+portId+"\" onclick=\"addToPortRanges(this)\"/>";
	tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = SedId;	
}

function VlanName(vlanid){

	var selectObject = document.getElementById("vlan_id");
	var y=document.createElement("option");
  	y.text=vlanid;
	y.value = vlanid;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  	catch(ex)
    {
   	selectObject.add(y); // IE only
  	}
}

function AddIp(ip_sel)
{
	var tf = document.l3intf_setting;
	var vid = document.getElementById("vid").value;
	var ip = document.getElementById("ip").value;
	var age = document.getElementById("age").value;
	var sedIp = document.getElementById("sedIp").value;
	var l3int_flag = document.getElementById("l3int_flag");
	
	switch(ip_sel)
	{
		case 1:
			if(!DataScope(vid,4094,1))
			{
				alert(putmsg(<% write(lang); %>,"VLAN ID 范围在1-4094之间，且必须为数字!"));
				break;
			}
			if(!IpCheckAndMask(ip) && ip!="")
			{
				alert(putmsg(<% write(lang); %>,"IP地址输入非法！格式为 A.B.C.D/M"));
				break;
			}
			if(!DataScope(age,1000,100))
			{
				alert(putmsg(<% write(lang); %>,"AGE范围在100-1000之间，且必须为数字!"));
				break;
			}
			if(ip=="")
			{
				l3int_flag.value="1";
			}
			tf.action = "/goform/l3intfConfig";
			tf.submit();
			
			break;
		case 2:
			if(!IpCheckAndMask(sedIp))
			{
				alert(putmsg(<% write(lang); %>,"IP地址输入非法！格式为 A.B.C.D/M"));
				break;
			}
			tf.action = "/goform/Addl3if2ip";
			tf.submit();
			break;
	}
}

function updl3intf(selip)
{
	var tf = document.l3intf_setting;
	var vid = document.getElementById("vid").value;
	var ip = document.getElementById("ip").value;
	var age = document.getElementById("age").value;
	var pattern = document.getElementById("pattern");
	var l3_port = document.getElementById("l3_port").value; 
		
	var sedIp = document.getElementById("sedIp").value;
	var vlan_id = document.getElementById("vlan_id");

	var i,j=0;

	var table_l3intf = document.getElementById("table_l3intf");
	var table_l3intf1 = document.getElementById("table_l3intf1");
	
	switch(selip){
		case 1:
		  if(!DataScope(age,1000,100))
			{
				alert(putmsg(<% write(lang); %>,"AGE范围在100-1000之间，且必须为数字!"));
				return;
			}

			if(!IpCheckAndMask(ip) && ip != "-")
			{
				alert(putmsg(<% write(lang); %>,"IP地址输入非法！格式为 A.B.C.D/M"));
				return;
			}
		  
			for(i=1;i<table_l3intf.rows.length;i++)
			{
				if(table_l3intf.rows[i].cells[1].innerHTML==vid)
				{
					j++;
					tf.action = "/goform/Changel3intf";
					tf.submit();
				}
			}
			if(j==0)
			{
				alert(putmsg(<% write(lang); %>,"你要修改的vlan id 不存在!"));
			}
			break;
		case 2:
			var val;
			if(pattern.options[0].selected==true)
			{
				val = pattern.options[0].text;
			}
			else
			{
				val = pattern.options[1].text;
			}
			for(i=1;i<table_l3intf.rows.length;i++)
			{
				if(table_l3intf.rows[i].cells[1].innerHTML==vid && table_l3intf.rows[i].cells[2].innerHTML== l3_port && table_l3intf.rows[i].cells[3].innerHTML== ip && table_l3intf.rows[i].cells[5].innerHTML == age &&  table_l3intf.rows[i].cells[6].innerHTML==val)
				{
					j++;
					tf.action = "/goform/Del3intf";
					tf.submit();
				}
			}
			if(j==0)
			{
				alert(putmsg(<% write(lang); %>,"你要删除的对象数据不正确!"));
			}
			break;
		case 3:
			for(i=1;i<table_l3intf.rows.length;i++)
			{
				if(table_l3intf.rows[i].cells[4].innerHTML==sedIp)
				{
				    j++
					if(table_l3intf.rows[i].cells[4].innerHTML=="-")
    			    {
    			      alert(putmsg(<% write(lang); %>,"接口不存在从ip地址!"));
    				  return;
    			    }
					tf.action = "/goform/Del3if2ip";
					tf.submit();
				}
			}
			if(j==0)
			{
				alert(putmsg(<% write(lang); %>,"你要删除的从IP地址不存在!"));
			}
				
			break;
	}
}

function checkData()
{
	var tf=document.l3intf_setting;
	tf.action = "/goform/saveComm?name=l3int";
	tf.submit();
}

function refreshpage()
{
  location.href='l3int.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('l3int',<% write(lang); %>);
}

</script>
</head>

<body  >
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="l3intf_setting" method="POST" action="/goform/l3intfConfig">
<INPUT type="hidden" name="l3_port" id= "l3_port" value="0">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="page" value="1">
<input type="hidden" name="pvid_config"  value="@pvid_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<input type="hidden" name="l3int_flag" id="l3int_flag"  value="@pvid_config#">

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">

<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="90%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
         <tr>
           <td colspan="2" align="left" class="cword09">&nbsp;<script>writemsg(<% write(lang); %>,"三层接口管理");</script> <b><font color="#FF7F00">&gt;&gt;</font></b> &nbsp;<script>writemsg(<% write(lang); %>,"三层接口设置");</script></td>
         </tr>
        </table>
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%">
	     <tr height="25">
	       <th align="left" colspan="2">&nbsp;<script>writemsg(<% write(lang); %>,"三层接口设置");</script></td>
	     </tr>
		 <tr height="25">
              <td align="left" class="crons">&nbsp;VLAN ID</td>
              <td width="81%" align="left" class="crons">&nbsp;
                <input name="vid" type="text"  id="vid"/>&nbsp;(1-4094)</td>
         </tr>
	     <tr height="25">
	     	  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"IP 地址");</script></td>
	     	  <td class="crons">&nbsp;
	   	        <input name="ip" type="text"  id="ip"/>&nbsp;<script>writemsg(<% write(lang); %>,"(格式为 A.B.C.D/M)");</script>
	     	  </td>
	     </tr>		  

	     <tr height="25">
	     	  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"ARP AGE时间");</script></td>
	     	  <td class="crons">&nbsp;
	   	        <input name="age" type="text"  id="age" value="1000"/>(100-1000)
	     	  </td>
	     </tr>			 
		 
	     <tr height="25">
	     	  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"管理状态");</script></td>
	     	  <td class="crons">&nbsp;
	   	       	 <select name="pattern" id="pattern" class="select1">
	   	             <option value="up">Up</option>
	   	             <option value="down">Down</option>
	   	             </select>
	     	  </td>
	     </tr>	 		 		 		 
		 
	     <tr height="25">

	     	  <td colspan="2" align="middle" class="crons">
	     	  	     <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","add","AddIp(1)");</script>
	     	  	     <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify","updl3intf(1)");</script>
	     	  	     <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","delete","updl3intf(2)");</script>
	     	  </td>
	     </tr>
	   
<tr><td colspan="2" height="8"></td></tr>
		<tr height="25">
	     	  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"接口");</script></td>
	     	  <td class="crons">&nbsp;
				<input name="vlan_id" type="text"  id="vlan_id" readonly="true"/>
			  </td>
	     </tr>
		 <tr height="25">
	     	  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"从IP地址");</script></td>
	     	  <td class="crons">&nbsp;
	   	        <input name="sedIp" type="text"  id="sedIp"/>&nbsp;<script>writemsg(<% write(lang); %>,"(格式为 A.B.C.D/M)");</script>
	     	  </td>
	     </tr>
		 <tr height="25">
	     	  <td colspan="2" align="middle" class="crons">
	     	  	     <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","add","AddIp(2)");</script>
	     	  	     <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","delete","updl3intf(3)");</script>
	     	  </td>
	     </tr>	 	
<tr><td colspan="2" height="8"></td></tr>
   <tr>
	    	<td colspan="2">
	    		<table id="table_l3intf" border="0" cellspacing="0" cellpadding="0" width="100%" class="table2">
	    		 	<tr align="center" height="25" class="crons">
	    		 		<th class="td2" width="10%"></th>
	    		 		<th class="td2" width="10%"><font color="#333333"><b>VLAN ID</b></font></th>
	    		 		<th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"接口");</script></b></font></th>						
	    		 		<th class="td2" width="20%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"IP地址");</script></b></font></th>
	    		 		<th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"从IP地址");</script></b></font></th>
						<th class="td2" width="15%"><font color="#333333"><b>Age</b></font></th>
	    		 		<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"管理状态");</script></b></font></th>																		
	    		 	</tr>
					<script> 
						<% showL3intf("l3int");%>
					</script>
	    		</table>
	    	</td>
   </tr>
<tr class="crons"><td colspan="2" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"(说明:此处只显示第一个从IP地址)");</script></td></tr>
	<tr>
	  	   <td colspan="2" align="center" height="35">
	  	   <script>writebutton(1,<% write(lang); %>,"首  页","button","button","firstpage","getPage(1)");</script>
				<%showL3intf("pagebutton");%>
            	  		    &nbsp;
	  		    <%showL3intf("pagenum");%>
			    <%showL3intf("allpage");%>
	  		</td>
   </tr>	
	<!--	<tr>
	    	<td colspan="2">
	    		<table id="table_l3intf1" border="0" cellspacing="0" cellpadding="0" width="100%" class="table2">
	    		 	<tr align="center" height="25" class="crons">
	    		 		<th class="td2" width="10%"></th>
	    		 		<th class="td2" width="20%"><font color="#333333"><b>接口</b></font></th>
	    		 		<th class="td2" width="70%"><font color="#333333"><b>从IP地址</b></font></th>																							
	    		 	</tr>
	    		</table>
	    	</td>
   </tr>-->
		
		
     </table>
   </td></tr>
 <!--  <tr>
	  	   <td colspan="2" align="center" height="35">
	  		    <input name="Refresh" type="button" value="刷 新"  class="button" onClick="location.href='l3int.asp'"/>&nbsp;
	  		    <input name="Submit" type="submit" class="button" value="保 存" onclick="return checkData();">&nbsp;
	  		    <input name="Help" type="button" class="button" id="Help" value="帮 助" onclick="showHelp('l3int')" />
	  		</td>
   </tr>
  -->
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>
</form>
<script>
changebgcolor();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</html>
