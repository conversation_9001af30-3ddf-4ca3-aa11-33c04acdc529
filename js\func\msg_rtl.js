//  shared  messages - used in more than 1 file

var _msgTable_en={
"ZZZ":"ZZZ"
};
var _msgTable_ch={
"ZZZ":"ZZZ"
};
function putmsg(lang,key)
{
try {
eval("var m=_msgTable_"+lang+"[key];");
if (!m)
{
return key;
}
return m;
}
catch (e) {
return key;
}
}
function writemsg(lang,key)
{
document.write(putmsg(lang,key));
}
function writebutton(priv,lang,key,bclass,btype,bname,bfunc)
{
var outputstr ="";
if((priv == 1)||(priv == 5))
outputstr="<input class="+bclass+" id='"+bname+"' name='"+bname+"' type='"+btype+"' value='"+putmsg(lang,key)+"' onClick='return "+bfunc+";'>";
else
outputstr="&nbsp;";
document.write(outputstr);
}
function writebutton2(priv,lang,key,bclass,btype,bname,benable,bfunc)
{
var outputstr ="";
if((priv == 1)||(priv == 5))
outputstr="<input class="+bclass+" id='"+bname+"' "+benable+" name='"+bname+"' type='"+btype+"' value='"+putmsg(lang,key)+"' onClick='return "+bfunc+";'>";
else
outputstr="&nbsp;";
document.write(outputstr);
}
	function passwordPrompt() {
					var maskDiv = document.createElement('div');
					maskDiv.className = 'custom-prompt-mask';
					maskDiv.id = 'customPrompt';

					var boxDiv = document.createElement('div');
					boxDiv.className = 'custom-prompt-box';

					var label = document.createElement('label');
					label.setAttribute('for', 'password');
					label.className = 'label1';
					label.textContent = '操作认证-密码';

					var input = document.createElement('input');
					input.type = 'password';
					input.id = 'password';

					var buttonDiv = document.createElement('div');
					buttonDiv.className = 'button';

					var okDiv = document.createElement('div');
					okDiv.textContent = '确定';
					okDiv.className = 'ok';

					var cancelDiv = document.createElement('div');
					cancelDiv.textContent = '取消';
					cancelDiv.className = 'cancel';

					// 组装各个节点
					buttonDiv.appendChild(okDiv);
					buttonDiv.appendChild(cancelDiv);

					boxDiv.appendChild(label);
					boxDiv.appendChild(input);
					boxDiv.appendChild(buttonDiv);

					maskDiv.appendChild(boxDiv);
					// 将 maskDiv 节点插入到文档中
					let dom = top.mainDom
					dom.appendChild(maskDiv);

					input.focus();
					/*now comes the magic: create and return a promise*/
					return new Promise(function (resolve, reject) {
                  input.addEventListener("keydown", function(event) {
                  // 
                  if (event.key === "Enter" || event.keyCode === 13) {
                      // 
                      		resolve(input.value); //resolve the password
							dom.removeChild(maskDiv); //remove the password-prompt
                     
                  }
              });
						okDiv.addEventListener('click', function () { //if user clicks ok
							resolve(input.value); //resolve the password
							dom.removeChild(maskDiv); //remove the password-prompt
						});
						cancelDiv.addEventListener('click', function () { //if user clicks cancel
							reject("User cancelled the prompt"); //reject the promise
							dom.removeChild(maskDiv); //remove the password-prompt
						});


					});
				}
        	function namePrompt() {
					var maskDiv = document.createElement('div');
					maskDiv.className = 'custom-prompt-mask';
					maskDiv.id = 'customPrompt';

					var boxDiv = document.createElement('div');
					boxDiv.className = 'custom-prompt-box';

					var label = document.createElement('label');
					label.setAttribute('for', 'password');
					label.className = 'label1';
					label.textContent = '操作认证-用户名';

					var input = document.createElement('input');
					input.type = 'text';
					input.id = 'userName';

					var buttonDiv = document.createElement('div');
					buttonDiv.className = 'button';

					var okDiv = document.createElement('div');
					okDiv.textContent = '确定';
					okDiv.className = 'ok';

					var cancelDiv = document.createElement('div');
					cancelDiv.textContent = '取消';
					cancelDiv.className = 'cancel';

					// 组装各个节点
					buttonDiv.appendChild(okDiv);
					buttonDiv.appendChild(cancelDiv);

					boxDiv.appendChild(label);
					boxDiv.appendChild(input);
					boxDiv.appendChild(buttonDiv);

					maskDiv.appendChild(boxDiv);
					// 将 maskDiv 节点插入到文档中
					let dom = top.mainDom
					dom.appendChild(maskDiv);

					input.focus();
					/*now comes the magic: create and return a promise*/
					return new Promise(function (resolve, reject) {
            input.addEventListener("keydown", function(event) {
                  // 
                  if (event.key === "Enter" || event.keyCode === 13) {
                      // 
                      		resolve(input.value); //resolve the password
							dom.removeChild(maskDiv); //remove the password-prompt
                     
                  }
              });
						okDiv.addEventListener('click', function () { //if user clicks ok
							resolve(input.value); //resolve the password
							dom.removeChild(maskDiv); //remove the password-prompt
						});
						cancelDiv.addEventListener('click', function () { //if user clicks cancel
							reject("User cancelled the prompt"); //reject the promise
							dom.removeChild(maskDiv); //remove the password-prompt
						});


					});
				}
				async function testThePrompt() {
					let res = await passwordPrompt();
					return res
				}
        	async function userNamePrompt() {
					let res = await namePrompt();
					return res
				}

