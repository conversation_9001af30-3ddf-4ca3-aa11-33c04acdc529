<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
  <% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
    <meta http-equiv="Content-Type" content="text/html; charset=utf8" />
    <title>
      <script>writemsg(<% write(lang); %>, "端口管理");</script>
    </title>
    <script language="JavaScript">
      var boardType = <% getSysCfg(); %>;
    </script>
    <script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
    <script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
    <script language="javascript" type="text/javascript" defer src="js/alpinejs.min.js"></script>
    <link href="css/display_web.css" rel="stylesheet" type="text/css" />
    <script language="JavaScript">
      var portshow = 1;

      function isValidString(str) {
        var validc = "/'%`\"\\><";
        var a, c;
        for (i = 0; i < validc.length; i++) {
          c = validc.substring(i, i + 1);
          if (str.indexOf(c) > -1) {
            return true;
          }
        }
        return false;
      }


      /*select ALL*/
      function selectToAll() {
        var cf = document.forms[0];
        var objs = document.getElementsByName("checkbox_index");
        var i;
        if (cf.check_all.checked == true) {
          for (i = 0; i < objs.length; i++) {
            if (objs[i].disabled == false && objs[i].checked == false) {
              objs[i].checked = true;
              addToPortRange(objs[i]);
            }
          }
        }
        else {
          for (i = 0; i < objs.length; i++) {
            if (objs[i].checked == true) {
              objs[i].checked = false;
              addToPortRange(objs[i]);
            }

          }
        }

      }

      /*MTU judgment*/
      function checking2() {
        var port_description = document.getElementById("port_description");
        var port_range = document.getElementById("port_range").value;
        var tf = document.port_setting;

        var i, j;

        if (((port_range.indexOf("ge") >= 0) && (port_range.indexOf("xe") >= 0)) ||
          ((port_range.indexOf("ge") >= 0) && (port_range.indexOf("sa") >= 0)) ||
          ((port_range.indexOf("ge") >= 0) && (port_range.indexOf("po") >= 0)) ||
          ((port_range.indexOf("xe") >= 0) && (port_range.indexOf("sa") >= 0)) ||
          ((port_range.indexOf("xe") >= 0) && (port_range.indexOf("po") >= 0)) ||
          ((port_range.indexOf("po") >= 0) && (port_range.indexOf("sa") >= 0))) {
          alert(putmsg(<% write(lang); %>, "不同类型的端口不能同时配置!"));
          return;
        }


        if (isValidString(port_description.value)) {
          alert(putmsg(<% write(lang); %>, "输入中发现非法字符!"));
        }
        else if (port_description.value.length > 256) {
          alert(putmsg(<% write(lang); %>, "描述字符不能超过256个字符!"));
        }
        else {
          tf.submit();

        }



      }

      /*display function*/

      function P(portId, admin, links, nego, cspeed, flowS, flowR, setmtu, portDesc, txlinerate, txburst, rxlinerate, rxburst, discardV, forcelink, fiber) {
        var narr = 9;
        var arr = "";
        var speed = "";
        var Desc = "";
        var tbtd;
        var i;
        var opt;
        var tbtr = document.getElementById("table_port").insertRow(-1);
        var portNum = document.getElementById("portNum");

        tbtr.classname = "td7";
        tbtr.height = "30";

        tbtr.setAttribute("height", "30");
        tbtr.setAttribute("class", "td7");
        tbtr.setAttribute("className", "td7");
        tbtr.setAttribute("id", "tr_" + portId);

        /*Cycle respectively and setting attributes*/
        for (i = 0; i < narr; i++) {
          tbtd = document.createElement("td");

          tbtd.align = "center";
          tbtd.setAttribute("class", "td2");
          tbtd.setAttribute("className", "td2");

          tbtr.appendChild(tbtd);
        }


        arr = cspeed.split(".");
        if (arr[1] == "full") {
          if (arr[0] != 0)
            speed = arr[0] + putmsg(<% write(lang); %>, "/全双工");
      		else
          speed = putmsg(<% write(lang); %>, "自动协商");
        }
        else {
          if (arr[0] != 0)
            speed = arr[0] + putmsg(<% write(lang); %>, "/半双工");
      		else
          speed = putmsg(<% write(lang); %>, "自动协商");
        }

        /*display*/
        tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"checkbox_index\" value=\"" + portId + "\" onchange=\"addToPortRange(this)\"/>";
        tbtr.cells[1].innerHTML = portId;
        tbtr.cells[2].innerHTML = putmsg(<% write(lang); %>, (admin == "up") ? "开启" : "关闭");
        tbtr.cells[3].innerHTML = links;
        tbtr.cells[4].innerHTML = (fiber == "1") ? "光口" : ((fiber == "0") ? "电口" : "  ");
        tbtr.cells[5].innerHTML = putmsg(<% write(lang); %>, speed);
        tbtr.cells[6].innerHTML = putmsg(<% write(lang); %>, (flowS == "up") ? "开启" : "关闭");
        //tbtr.cells[7].innerHTML = putmsg(<% write(lang); %>,(flowR=="up")?"开启":"关闭");
        //tbtr.cells[7].innerHTML = txlinerate;
        //tbtr.cells[8].innerHTML = txburst;
        //tbtr.cells[9].innerHTML = rxlinerate;
        //tbtr.cells[10].innerHTML = rxburst;

        portshow++;

        if (0 == discardV)
          tbtr.cells[7].innerHTML = "none";
        else if (1 == discardV)
          tbtr.cells[7].innerHTML = "all";
        else if (2 == discardV)
          tbtr.cells[7].innerHTML = "untag";
        else if (3 == discardV)
          tbtr.cells[7].innerHTML = "tag";


        if (portDesc.length > 8) {
          Desc = portDesc.substring(0, portDesc.length - (portDesc.length - 10));
          tbtr.cells[8].innerHTML = "<font title=\"" + portDesc + "\">" + Desc + "......</font>";
        }
        else
          tbtr.cells[8].innerHTML = "<font title=\"" + portDesc + "\">" + portDesc + "</font>";

        //tbtr.cells[10].innerHTML = putmsg(<% write(lang); %>,(forcelink=="1")?"开启":"关闭");
      }

      /*
        Show all check true port, and will last a port data displayed
      */
      function addToPortRange(obj) {
        var trid = "tr_" + obj.value;
        var nodeArray;
        var trobj = document.getElementById(trid);
        var target = document.getElementById("port_range");
        var port_enable = document.getElementById("port_enable");
        var port_speed = document.getElementById("port_speed");
        var port_flow_t = document.getElementById("port_flow_t");
        var port_mtu = document.getElementById("port_mtu");
        var port_description = document.getElementById("port_description");
        var select_discard = document.getElementById("select_discard");


        var p = obj.value;
        var i;

        if (obj.checked) {

          target.value = target.value + p + " ";
          //port_mtu.value=trobj.cells[11].innerHTML;
          if (port_enable.options[0].text == trobj.cells[2].innerHTML) {
            port_enable.options[0].selected = true;
            port_enable.options[1].selected = false;
          }
          else {
            port_enable.options[0].selected = false;
            port_enable.options[1].selected = true;
          }

          for (i = 0; i < port_speed.options.length; i++) {
            if (port_speed.options[i].text == trobj.cells[5].innerHTML) {
              port_speed.options[i].selected = true;
              break;
            }
          }
          if ((trobj.cells[6].innerHTML == putmsg(<% write(lang); %>, "开启")?putmsg(<% write(lang); %>, "启用"): putmsg(<% write(lang); %>, "禁用"))== port_flow_t.options[0].text)
          {
            port_flow_t.options[0].selected = true;
            port_flow_t.options[1].selected = false;
          }
      		else
          {
            port_flow_t.options[0].selected = false;
            port_flow_t.options[1].selected = true;
          }



          for (i = 0; i < document.port_setting.select_discard.options.length; i++) {
            if (document.port_setting.select_discard.options[i].text == trobj.cells[7].innerHTML) {
              document.port_setting.select_discard.options[i].selected = true;
              break;
            }
          }


          nodeArray = trobj.cells[8].childNodes;
          port_description.value = (nodeArray[0].title == "-" ? "" : nodeArray[0].title);

        } else {

          target.value = target.value.replace(p + " ", "");
        }

      }

      function refreshpage() {
        location.href = 'port.asp?ltime=' +<% write(lltime); %>;
      }
      function filter(val) {

        let arr = [{
          value: 0,
          text: '自动协商'
        },
        {
          value: '10g.full',
          text: '10g/全双工'
        },
        {
          value: '1g.full',
          text: '1g/全双工'
        },
        {
          value: '100m.full',
          text: '100m/全双工'
        },
        {
          value: '100m.half',
          text: '100m/半双工'
        },
        {
          value: '10m.full',
          text: '10m/全双工'
        },
        {
          value: '10m.half',
          text: '10m/半双工'
        },{
          value: 'auto',
          text: '自动协商'
        
        },{
          value:'down',
          text:'关闭'
        }]
        return arr.filter(item => item.value == val)[0].text
      }
    </script>
</head>

<body onload="">
  <br />
  <% var authmode; checkCurMode(); %>
    <script>
      checktop(<% write(lang); %>);

      retValue = <% var responseJsonStr; jw_get_portConfig(); %>

        responseStr = <% write(responseJsonStr); %>;
      const appData = {
        headers: ['端口', '端口启用', '当前状态', '类型', '端口速率', '流控', '报文丢弃', '端口描述'],
        PortConfig: [
          {
            portName: '',
            portState: '',
            portLinks: null,
            portFiber: null,
            portSpeed: '',
            portFlowS: '',
            portDiscardV: null,
            portDesc: ''
          },
          // 其他端口的配置
        ],

        selectedValues: {}
      };
      appData.PortConfig = [...responseStr.PortConfig]
      
      // 定义行为
      const appMethods = {
        // 这里可以写一些方法或事件处理函数
      };
      function portChange(row, index) {
        let obj = {
          pageName: 'port.asp',
        }
        obj.PortConfig = [
          {
            portName: row.portName,
            portState: row.portState,
            portSpeed: row.portSpeed == '1g.full' ? '1000-full' : row.portSpeed == '100m.full' ? '100-full' : row.portSpeed == '100m.half' ? '100-half' : row.portSpeed == '10m.full' ? '10-full' : row.portSpeed == '10m.half' ? '10-half' : 'auto',
            portFlowS: row.portFlowS,
            portDiscardV: row.portDiscardV == 0 ? "none" : row.portDiscardV == 2 ? "untag" : row.portDiscardV == 3 ? "tag" : "",
            portDesc: row.portDesc
          }
        ]
        var tf = document.port_setting;
        tf.param1.value = JSON.stringify(obj)
        tf.action = "/goform/jw_set_portConfig";
        tf.submit();
      }

    </script>
    <form name="port_setting" method="POST" action="/goform/PortChange">
      <input type="hidden" name="param1" id="param1" />
      <input type="hidden" name="ltime" value=<% write(lltime); %>>
      <input type="hidden" name="lastts" id="lastts" value=<% write(serverts); %>>
      <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
    </form>
    <form>
      <div class="formContain">
        <table width="96%" align="center" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td height="30px">
              <font size="5" color="#404040">
                <div class="bot">端口模式配置</div>
              </font>
            </td>
          </tr>
        </table>
        <div x-data="appData">
          <table class="tab1 tablebord" width="96%" border="0" align="center" cellpadding="0" cellspacing="0">
            <thead>
              <tr>
                <template x-for="(header, index) in headers" :key="index + 1">
                  <th class="all_tables" x-text="header"></th>
                </template>
              </tr>
            </thead>
            <tbody>
              <template x-for="(row, rowIndex) in appData.PortConfig" :key="rowIndex">
                <tr style="text-align: center">
                  <td class="all_tables" x-text="row.portName"></td>
                  <td class="all_tables">
                    <select x-model="row.portState" @change="portChange(row,rowIndex)">
                      <option value="up">开启</option>
                      <option value="down">关闭</option>
                    </select>
                  </td>
                  <td class="all_tables" x-text="filter(row.portLinks)"></td>
                  <td class="all_tables" x-text="row.portFiber==1?'光口':row.portFiber==0?'电口':''"></td>
                  <td class="all_tables">
                    <select x-model="row.portSpeed" @change="portChange(row,rowIndex)">
                      <option value="0">自动协商</option>
                      <option value="1g.full">1g/全双工</option>
                      <option value="100m.full">100m/全双工</option>
                      <option value="100m.half">100m/半双工</option>
                      <option value="10m.full">10m/全双工</option>
                      <option value="10m.half">10m/半双工</option>
                    </select>
                  </td>
                  <td class="all_tables" @change="portChange(row,rowIndex)">
                    <select x-model="row.portFlowS">
                      <option value="up">开启</option>
                      <option value="down">关闭</option>
                    </select>
                  </td>
                  <td class="all_tables" @change="portChange(row,rowIndex)">
                    <select x-model="row.portDiscardV">
                      <option value="0">none</option>
                      <option value="2">untag</option>
                      <option value="3">tag</option>
                    </select>
                  </td>
                  <td class="all_tables">
                    <input type="text" x-model="row.portDesc" @change="portChange(row,rowIndex)" />
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
      <br /><br />
      <div class="formContain">
        <table width="96%" align="center" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td height="30px">
              <font size="5" color="#404040">
                <div class="bot">端口状态显示信息</div>
              </font>
            </td>
          </tr>
        </table>
        <div x-data="{}">
          <table class="tablebord tab2" width="96%" border="0" align="center" cellpadding="0" cellspacing="0">
            <thead>
              <tr>
                <template x-for="(header, index) in appData.headers" :key="index + 1">
                  <th class="all_tables" x-text="header"></th>
                </template>
              </tr>
            </thead>
            <tbody>
              <template x-for="(row, rowIndex) in appData.PortConfig" :key="rowIndex">
                <tr style="text-align: center">
                  <td class="all_tables" x-text="row.portName"></td>
                  <td class="all_tables" x-text="row.portState=='up'?'开启':'关闭'"></td>
                  <td class="all_tables" x-text="filter(row.portLinks) "></td>
                  <td class="all_tables" x-text="row.portFiber==1?'光口':row.portFiber==0?'电口':''"></td>
                  <td class="all_tables"
                    x-text="row.portSpeed=='1g.full'?'1g/全双工':row.portSpeed=='100m.full'?'100m/全双工':row.portSpeed=='100m.half'?'100m/半双工':row.portSpeed=='10m.full'?'10m/全双工':row.portSpeed=='10m.half'?'10m/半双工':'自动协商'">
                  </td>
                  <td class="all_tables" x-text="row.portFlowS=='up'?'开启':'关闭'"></td>
                  <td class="all_tables"
                    x-text="row.portDiscardV=='0'?'none':row.portDiscardV=='2'?'untag':row.portDiscardV=='3'?'tag':''">
                  </td>
                  <td class="all_tables" x-text="row.portDesc"></td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </form>
    <script>


      <% if (errorcode != "") { write_errorcode(errorcode); } %>
    </script>
</body>

</html>