<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script src="js/alpinejs.min.js" defer></script>
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"端口管理");</script></title>
<script>

function isINT(str){
var re = /^[0-9]*[0-9][0-9]*$/ ;

var result = re.test(str);   //测试 返回true或false

return result;

}

function changebgcolor(){
 var tab = document.all.table1;

  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			 tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			 tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function changebgcolor3(){
 var tab = document.all.table3;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function changebgcolor4(){
 var tab = document.all.table4;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function MSTPBRIINSTVLAN(v1, v2, v3)
{
	document.write("<tr height='30'><td align='left' class='crons'>"+v1+"</td><td align='left' class='crons'>"+v2+"</td><td align='left' class='crons'>"+v3+"</td></tr>");
	return ;
}
function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm?name=mstpcfg";
	tf.submit();
}
function checkMessage()
{
	var hid=document.webForm;
	if(!isINT(hid.prioritycom.value))
	{
	    alert(putmsg(<% write(lang); %>,"优先级必须是整数!"));
		return false;
	}
	if(((hid.prioritycom.value%4096)!=0)||(hid.prioritycom.value<0||hid.prioritycom.value>61440))
	{
		alert(putmsg(<% write(lang); %>,"优先级必须是4096的倍数,范围必须在0-61440之间!"));
		return false;
	}
	if(!isINT(hid.forwardtime.value))
	{
	    alert(putmsg(<% write(lang); %>,"转发时延必须是整数!"));
		return false;
	}
	if((2*(hid.forwardtime.value-1)<(hid.maxage.value))||(hid.forwardtime.value<4)||(hid.forwardtime.value>30)||(hid.maxage.value<6)||(hid.maxage.value>40))
	{
		alert(putmsg(<% write(lang); %>,"必须满足关系:2*(forwardtime-1)>=max-age,且4<=forwardtime<=30,6<=maxage<=40!"));
		return false;
	}
	//if(!isINT(hid.maxhops.value))
	//{
	   // alert(putmsg(<% write(lang); %>,"最大跳数必须是整数!"));
		//return false;
	//}
	//if((1>hid.maxhops.value)||(hid.maxhops.value>40))
	//{
		//alert(putmsg(<% write(lang); %>,"最大跳数的范围必须在1~40之间!"));
		//return false;
	//}
	if(!isINT(hid.hellotime.value))
	{
	    alert(putmsg(<% write(lang); %>,"HELLO时延必须是整数!"));
		return false;
	}
	if((1>hid.hellotime.value)||(hid.hellotime.value>10))
	{
		alert(putmsg(<% write(lang); %>,"HELLO时延的范围必须在1~10之间!"));
		return false;
	}
	if(hid.region.value.length>20)
	{
		alert(putmsg(<% write(lang); %>,"域名的长度不能超过20个字符!"));
		return false;
	}
	if(!isINT(hid.revision.value))
	{
	    alert(putmsg(<% write(lang); %>,"修订版本必须是整数!"));
		return false;
	}
	if((hid.revision.value>255)||(hid.revision.value<0))
	{
		alert(putmsg(<% write(lang); %>,"修订版本的范围必须在0~255之间!"));
		return false;
	}
	if(!isINT(hid.interval.value))
	{
	    alert(putmsg(<% write(lang); %>,"周期必须是整数!"));
		return false;
	}
	if(hid.interval.value!=1)
	{
  	if((hid.interval.value<10)||(hid.interval.value>1000000))
  	{
  		alert(putmsg(<% write(lang); %>,"周期的范围必须在10~1000000之间或者默认值1!"));
  		return false;
  	}	
	}
	
	hid.submit();
	return true;
	
}
function messageCheck1(val)
{
	var tf=document.webForm;
	switch(val)
	{
		case 1:
		if(tf.newId.value.length==0)
			{
				alert(putmsg(<% write(lang); %>,"请填写实例ID!"));
				return ;
			}
	  if(!isINT(tf.newId.value))
    	{
    	    alert(putmsg(<% write(lang); %>,"实例ID必须是整数!"));
    		return false;
    	}
	  if(tf.newId.value<1||tf.newId.value>15)
  		{
  			alert(putmsg(<% write(lang); %>,"实例ID必须在1到15之间!"));
  			return ;
  		}
			tf.idx.value=1;
		  break;
		case 2:
		    if(!isINT(tf.addvlan.value))
         	{
         	    alert(putmsg(<% write(lang); %>,"VLAN必须是整数!"));
         		return false;
         	}

			if(tf.addvlan.value<1||tf.addvlan.value>4094)
			{
				alert(putmsg(<% write(lang); %>,"vlan必须在1到4094之间!"));
				return ;
			}
		  tf.idx.value=2;
		  break;
		case 3:
		  if(!isINT(tf.delvlan.value))
         	{
         	    alert(putmsg(<% write(lang); %>,"VLAN必须是整数!"));
         		return false;
         	}
		  if(tf.delvlan.value<1||tf.delvlan.value>4094)
			{
				alert(putmsg(<% write(lang); %>,"vlan必须在1到4094之间!"));
				return ;
			}
		  tf.idx.value=3;
		  break;
		case 4:
		  tf.idx.value=4;
		  break;
		default:
		  return ;
	}
	tf.action = "/goform/modifyInstance";
	tf.submit();
	return ;
}
function messageCheck2()
{
	var tf=document.webForm;

	// var radio_ms = document.getElementsByName("mstpswitch");
	// var radio_l2p = document.getElementsByName("l2pro");

	// for(var i=0; i<radio_ms.length; i++)
	// {
	// 	if (radio_ms[i].checked)
	// 	{
	// 		var ms = radio_ms[i].value;
	// 	}
	// }
	// for(var i=0; i<radio_l2p.length; i++)
	// {
	// 	if (radio_l2p[i].checked)
	// 	{
	// 		var l2p = radio_l2p[i].value;
	// 	}
	// }
	/*
	if ( ms == "enable" && l2p != "pass")
	{
		alert("使用STP时请将L2 Protocol设为Enable状态");
		return;	
	}
	*/
	tf.action = "/goform/mstpSwitch";
	tf.submit();
}

function refreshpage()
{
  location.href='mstpcfg.asp?ltime='+<% write(lltime); %>;
}
function refreshpage1()
{
  location.href='mstpcfg.asp?ltime='+<% write(lltime); %>+'&tab=tab3';
}
function showHelpinfo()
{
   showHelp('mstpcfg',<% write(lang); %>);
}

<%  var authmode; checkCurMode(); %>
function display()
{
	if(document.getElementsByName("isauthA"))
	{
		if(<% write(authmode); %> != 1)
		{
			allitem = document.getElementsByName("isauthA");
			for(i=0;i<allitem.length;i++)
				allitem[i].style.display = "none";		
		}
			
	}

}







function changebgcolor11(){
 var tab = document.all.table11;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}


function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}



function checkMessagePortCfg()
{	
	var hid=document.webFormPortCfg;
	var target = document.getElementById("portNumm");


	if(!isINT(hid.pathcost.value))
	{
	    alert(putmsg(<% write(lang); %>,"路径开销必须是整数!"));
		return false;
	}
	if(hid.pathcost.value<1||hid.pathcost.value>200000000)
	{
		alert(putmsg(<% write(lang); %>,"路径开销应该在范围(1-200000000)之间!"));
		return false;
	}
	if(!isINT(hid.priority.value))
	{
	    alert(putmsg(<% write(lang); %>,"优先级必须是整数!"));
		return false;
	}
	if((hid.priority.value<0||hid.priority.value>240)||((hid.priority.value%16)!=0))
	{
		alert(putmsg(<% write(lang); %>,"优先级应该在范围(0-240)之间并且是16的倍数!"));
		return false;
	}

	target.value = target.value.replace(/\s/g, "|");
	hid.tabName.value ='tab1';
	hid.submit();
	return true;
}
function showportcfg(portNo)
{
	window.location.href="mstpcfg.asp?portNo="+portNo;
}
function showportcfg2(v)
{
	var portNum=document.webFormPortCfg.portNum.value;
	window.location.href="mstpcfg.asp?portNo="+portNum+"&fastoredgeIdx="+v;
}
function addToPortRange(obj)
{
	var target = document.getElementById("portNumm");
	var p = obj.value;
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);


	if(obj.checked)
	{
		//target.value = p ;
		target.value = target.value  + p + " ";

		document.webFormPortCfg.pathcost.value=trobj.cells[2].innerHTML;
		document.webFormPortCfg.priority.value=trobj.cells[3].innerHTML;
		document.webFormPortCfg.linktype.value=trobj.cells[5].innerHTML;

		//document.webFormPortCfg.fastoredge.value=trobj.cells[4].innerHTML;
		document.webFormPortCfg.fastoredge.value="portfast";
		
		if(trobj.cells[4].innerHTML == "enable")
			document.getElementById("k1").checked = true;
		else 
			document.getElementById("k2").checked = true;
		
		if(trobj.cells[6].innerHTML == "RSTP")
		document.webFormPortCfg.forceversion.value=2;
		else if(trobj.cells[6].innerHTML == "STP")
		document.webFormPortCfg.forceversion.value=0;

		if(trobj.cells[7].innerHTML == "enable")
			document.getElementById("pf1").checked = true;
		else 
			document.getElementById("pf2").checked = true;

		if(trobj.cells[8].innerHTML == "enable")
			document.getElementById("en1").checked = true;
		else 
			document.getElementById("en2").checked = true;
	}
	else{

		target.value = target.value.replace(p+" ", "");
	}
	
	return true;
}

/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;


	if (cf.check_all.checked == true)
    {
		for (i = 0; i < objs.length; i++) 
        {    
        	if (objs[i].disabled==false && objs[i].checked==false)
		    {
            	objs[i].checked = true;  
				addToPortRange(objs[i]);
			}
        }
    }
    else
    {
        for (i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true)
			{
				objs[i].checked = false;  
			 	addToPortRange(objs[i]);
			}             
        }
    }     
}

function PortAttr(aa,bb,cc,dd,ee,ff,en, pf)
{
    var narr=9;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+aa);	


    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		
	        tbtr.appendChild(tbtd);
    }
	            
	//tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+aa+"\" onclick=\"addToPortRange(this)\"/>";
	tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"checkbox_index\" value=\""+aa+"\" onchange=\"addToPortRange(this)\"/>";

		tbtr.cells[1].innerHTML = aa;
    tbtr.cells[2].innerHTML = bb;
    tbtr.cells[3].innerHTML = cc;
   // tbtr.cells[4].innerHTML = dd;
	
		if("disable"==dd)
   	 tbtr.cells[4].innerHTML = "disable";
	 else
   	 tbtr.cells[4].innerHTML = "enable";



    tbtr.cells[5].innerHTML = ee;
	if(2==ff)
   	 tbtr.cells[6].innerHTML = "RSTP";
	 else
   	 tbtr.cells[6].innerHTML = "STP";
	 
	if(2==pf)
   	 tbtr.cells[7].innerHTML = "enable";
	 else
   	 tbtr.cells[7].innerHTML = "disable";

	if("disable"==en)
   	 tbtr.cells[8].innerHTML = "disable";
	 else
   	 tbtr.cells[8].innerHTML = "enable";

}



function changebgcolor21(){
 var tab = document.all.table21;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
		if(i==0||i==1){
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}else{
			if(i%2==0){
				tab.rows[i].style.backgroundColor = "#fff" ;
				tab.rows[i].style.textAlign = "center" ;
			}
     if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables " ;
		}
		}
   

     }

  }
}

function changebgcolor23(){
 var tab = document.all.table23;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
		if(i==0){
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}else{
		 if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}
		}


     }

  }
}

function MSTPCISTBRIBASEINFO(v1, v2, v3, v4, v5, v6, v7, v8, v9)
{
	document.write("<tr height='30'><td align=left' class='crons'>"+v2+"</td><td align=left' class='crons'>"+v3+"</td><td align=left' class='crons'>"+v4+"</td><td align=left' class='crons'>"+v5+"</td><td align=left' class='crons'>"+v6+"</td><td align=left' class='crons'>"+v8+"</td><td align=left' class='crons'>"+v9+"</td></tr>");
	return ;
}



function P(m1,m2,m3,m4,m5,m6,m7)
{
    var narr=6;
    var tbtd;
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table23").insertRow(-1);
	
    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");
	//tbtr.setAttribute("id", "tr_"+portId);
	
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	
	
    tbtr.cells[0].innerHTML = m1;
    tbtr.cells[1].innerHTML = m2;
    tbtr.cells[2].innerHTML = m3;
    tbtr.cells[3].innerHTML = m4;
    tbtr.cells[4].innerHTML = m5;
    tbtr.cells[5].innerHTML = m6;
}



function RefreshInfor()
{
 location.href="mstpcfg.asp?portNo="+document.webFormInfo.portforinstance.value;
}



var stpPortInfoPortList = [];

function writeLines()
{
var j=0;
for(var i=0;i<stpPortInfoPortList.length/7;i++)
{
	document.write("      <tr class='tables_all'>");
	
		document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	
	j++;
	document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	j++;
	document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	j++;
	document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	j++;
	document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	j++;
	document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	j++;
	document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	j++;
	document.write("      </tr>");
	
	
}
	
}
function getUrlParamTab() {
        let urlSearch = window.location.search
        const search = new URLSearchParams(urlSearch)
		
        const params = Object.fromEntries(search.entries())
		
        try {
          if (params.tab) return params.tab
        } catch { }
        return 'tab2'
      }
</script>
</head>
<body  onload="display()" x-data="{active:'tab2'}" x-init="active=getUrlParamTab()"><br>
<% web_get_stat(); %>
<script>
checktop(<% write(lang); %>);
</script>
<div >
    <ul class="tabmenu">
		<li  id="tab2" :class="active==='tab2'?'tab':'tab-disable'">
			<a herf="#" x-on:click="active='tab2'">RSTP 设置</a>
		  </li>
		<li  id="tab1" :class="active==='tab1'?'tab':'tab-disable'">
			<a herf="#" x-on:click="active='tab1'">RSTP 端口</a>
		  </li>
    
      <li  id="tab3" :class="active==='tab3'?'tab':'tab-disable'">
        <a herf="#" x-on:click="active='tab3'">RSTP 信息</a>
      </li>
	 
    </ul>
  </div>
  <form  x-show="active==='tab1'" name="webFormPortCfg" method="post" action="/goform/setMstpPortcfg" class="formContain">


	<input type="hidden" name="portIdx" value= >
	
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
	<input type="hidden" name="lastts" value=<% write(serverts); %>>
	<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
	<input type="hidden" name="tabName" id="tabName" >
	<table id="mainTbl111" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
	<tr><td>
	<table width="100%"  border="0" align="left" cellpadding="0" cellspacing="0" >
	  <tr>
		<td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
		  <tr>
			<td>
	
	 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
		<tr>
			<td  height="30px" ><font size="5" color="#404040"><div class="bot">RSTP 端口</div></font></td>
			</tr>
	 </table>
	
			</td>
		  </tr>
		  <tr>
			<td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table11" class="tablebord">
		
		  <tr  height="25">
			<td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"端口列表");</script>:          </td>
			<td align="left" class="crons" >&nbsp;
						  <input type="text" name="portNumm" id="portNumm" readonly="true" style="width: 140px;"></td>
			<td align="left" class="crons">&nbsp;</td>
			<td align="left" class="crons">&nbsp;</td>
		  </tr>
		  
			<tr height="25">
				  <td  width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"路径开销");</script>&nbsp;:</td>
				  <td   width="30%" align="left" class="crons">&nbsp;
						  <input type="text" size=15 name="pathcost" value="200000">(1-200000000)               </td> 
				  <td  width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"优先级");</script>&nbsp;:</td>
				  <td   width="30%" align="left" class="crons">&nbsp;
						  <input type="text" name="priority" size=15 value="128">(0-240)               </td>
				</tr>
				<tr height="25"  style='display:none'>
				 <td  width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"快速转发特性");</script>&nbsp;:</td>
				<td  align="left" class="crons" colspan="3">&nbsp;
				<select name="fastoredge" >
								<option value="portfast" selected>Portfast
								<option value="edgeport" >Edgeport
						  </select>
				  &nbsp;
				  <!--
						  <input type="radio" name="fastoredgesw" value="enable" >Enable
						  <input type="radio" name="fastoredgesw" value="disable" checked>Disable               
						  --></td> 
				</tr>
				<tr height="25"  style='display:none'>
				  <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"BPDU过滤");</script>&nbsp;:</td>
				  <td   width="30%" align="left" class="crons">&nbsp;
						  <select name="bpdufilter">
										<option value="default" >Default
										<option value="disable" >Disable
										<option value="enable"  >Enable
						  </select>               </td> 
				  <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"BPDU保护");</script>&nbsp;:</td>
				  <td   width="30%" align="left" class="crons">&nbsp;
						  <select name="bpduguard">
										<option value="default" >Default
										<option value="disable" >Disable
										<option value="enable"  >Enable
						  </select>               </td>
				</tr>
				<tr height="25">
					<td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"协议版本");</script>&nbsp;:</td>
					<td   width="30%" align="left" class="crons">&nbsp;
							<select name="forceversion"  style="width:157px">
									  <option value=0 >STP
									  <option value=2 selected >RSTP
							</select>               
					  </td> 

				  <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"链路类型");</script>&nbsp;:</td>
				  <td   width="30%" align="left" class="crons">&nbsp;
						  <select name="linktype" style="width:157px">
							  <option value="shared" >Shared
										<option value="point-to-point" selected>Point To Point
						  </select>               </td> 
				</tr>
				<tr height="25">
					<td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"自动边界");</script>&nbsp;:</td>
					<td   width="30%" align="left" class="crons">&nbsp;
							<input type="radio" name="autoedge" value="enable" id="k1" >开启
							<input type="radio" name="autoedge" value="disable"  id="k2"  checked>关闭               </td> 
				  <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"portfast");</script>&nbsp;:</td>
				  <td   width="30%" align="left" class="crons">&nbsp;
						  <input type="radio" name="fastoredgesw" value="enable" id="pf1" >开启
						  <input type="radio" name="fastoredgesw" value="disable" id="pf2" checked>关闭               </td> 
					</td> 
	
					<tr height="25">
	
								  <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"state");</script>&nbsp;:</td>
				  <td   width="30%" align="left" class="crons">&nbsp;
						  <input type="radio" name="portstate" value="enable" id="en1" >开启
						  <input type="radio" name="portstate" value="disable" id="en2" checked>关闭               </td> 
					</td> 
			<td align="left" class="crons">&nbsp;</td>
			<td align="left" class="crons">&nbsp;</td>
					</tr>
	<!--          	  
				  <td  align="left" class="crons">&nbsp;&nbsp;</td>
				   <td   width="30%" align="left" class="crons">
				   &nbsp;<div  style='display:none'>
						  <input type="radio" name="rootguard" value="enable" >Enable
						  <input type="radio" name="rootguard" value="disable" checked>Disable </div>               
				  </td>
	-->          	
				</tr>
			</table></td>
		  </tr>
		<tr >
		<td align="center" height="35" style="padding: 5px;">			 	 
		<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"应  用","buttons_apply","button","modify","checkMessagePortCfg()");</script>
		<!-- <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script> -->
	<!--
		  &nbsp;
		<script>writebutton(1,<% write(lang); %>,"帮  助","button","button","Help","showHelpinfo()");</script>
	-->
		</td>
		</tr>
	
	
<!-- 	
				  <tr>
				<td colspan="4">
					<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
						 <tr align="center" height="25" class="crons">
						
							 <th class="td2" width="10%"><input type="checkbox" name="check_all" value="all" onClick="selectToAll()" /></th>
							 <th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></b></font></th>
							 <th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"路径开销");</script></b></font></th>
							 <th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"优先级");</script></b></font></th>						
							 <th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"自动边界");</script></b></font></th>						
							 <th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"链路类型");</script></b></font></th>						
							 <th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"协议版本");</script></b></font></th>						
							 <th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"portfast");</script></b></font></th>	
							 <th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"state");</script></b></font></th>
						 </tr>
	
	
			  <script>
					  <%  var errorcode; showStpSetPort();%>
			  </script>
							  </table>	    	
							</td>
	   </tr> -->
			</table></td>
		  </tr>
				  </table>
				</td></tr>
				
				
		  <tr>
			<td height="8"></td>
		  </tr>
	
		</table></td>
	  </tr> 
	</table>
	</td></tr>
	<tr><td>
	</td></tr></table>
	<div class="">
		<div class=""><font size="5" color="#404040"><div class="bot">信息列表</div></font></div>
		
		<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
			<tr align="center" height="25" class="crons">
		   
				<th class="td2" width="10%"><input type="checkbox" name="check_all" value="all" onClick="selectToAll()" /></th>
				<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></b></font></th>
				<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"路径开销");</script></b></font></th>
				<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"优先级");</script></b></font></th>						
				<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"自动边界");</script></b></font></th>						
				<th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"链路类型");</script></b></font></th>						
				<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"协议版本");</script></b></font></th>						
				<th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"portfast");</script></b></font></th>	
				<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"state");</script></b></font></th>
			</tr>


 <script>
		 <%  var errorcode; showStpSetPort();%>
 </script>
				 </table>
	</div>
</form>
<form  x-show="active==='tab2'"   name="webForm" method="post" action="/goform/setMstpMstCfg" class="formContain">
<input type="hidden" name="idx" id="idx" >
<input type="hidden" name="ltime" value=<% write(lltime); %> >
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<% var errorcode, l2pro, mstpswitch,ciscointer,forwardtime,hellotime,maxage,maxhops,region,revision,errtimeout,interval,bpdufilter,bpduguard,prioritycom; getMstpMstCfg(); %>
<div class="" style="min-height: 600px;">
<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0" bgcolor="#FFFFFF">
	<tr>
		<td width="100%">
		<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr>
		<td  height="30px"  colspan="2"><font size="5" color="#404040"><div class="bot">RSTP 设置</div></font></td>
	</tr>
	<tr height="25" >
		<td align="left"  width="25%" class="tableTd"> &nbsp;<script>writemsg(<% write(lang); %>,"STP开关");</script>:</td>
		<td  align="left" class="crons" width="79%" >
			<select @change="messageCheck2" name="mstpswitch" style="width: 200px;">
				<option value="enable" <% if (mstpswitch=="enable") write("selected"); %>>开启</option>
				<option value="disable" <% if (mstpswitch!="enable") write("selected"); %>>关闭</option>
			</select>
		</td>
	</tr>
        </table>
		</td>
	</tr>
	<tr height="10"></tr>
<tr>
	<td>
		<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table1" class="tablebord">

	<!-- <tr height="25" >
		<td colspan="4" align="left" class="crons" >
		  <div align="center">
			<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"应  用","buttons_apply","button","","messageCheck2()");</script>
		  </div>
		</td>
	</tr> -->
	<tr style='display:none'>     
		<td style="border: none;">
			<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
				<tr>
					<td>
						<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09"  >
			<tr height="25">
				<td width="100%">
					<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table2" class="tablebord">
					<tr height="25"><td colspan="4" align="left" width="100%">&nbsp;<script>writemsg(<% write(lang); %>,"实例配置");</script></td></tr>
					<tr><td align="left" width="20%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"创建实例");</script>:</td>
					<td align="left" colspan="2" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"实例ID");</script>:<input type="text" name="newId"></td>
					<td align="left" width="15%" class="crons">
					<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"创  建","button","button","","messageCheck1(1)");</script>
					</td></tr>
					<% show_mstp_instance1(); %>
					</table>
				</td>                     
			</tr>
		</table>
					</td>
				</tr>
			</table>
			</td>
	  </tr> 
	  <tr height="25">
		<td colspan="4" align="left"><span class="all_tables3">&nbsp;<script>writemsg(<% write(lang); %>,"STP配置");</script></span> </td>
	  </tr>
	  <tr height="25">
		<td  align="left" class="crons"><span class="all_tables3">&nbsp;<script>writemsg(<% write(lang); %>,"优先级");</script>&nbsp;:</span></td>
		<td   width="20%" align="left" class="all_tables">
				<input type="text" name="prioritycom" value=<% write(prioritycom); %>>
		<td  align="left" class="crons" >&nbsp; &nbsp;</td>
		<td   width="20%" align="left" class="all_tables">&nbsp;<div  style='display:none'>
				<input type="radio" name="ciscointer" value="enable" <% if (ciscointer=="enable") write("checked"); %>>Enable
				<input type="radio" name="ciscointer" value="disable" <% if (ciscointer!="enable") write("checked"); %>>Disable </div>
		 </td> 
		 </td>
	  </tr>
	  <tr height="25">
		 <td   width="20%" align="left" class="crons"><span class="all_tables3">&nbsp;<script>writemsg(<% write(lang); %>,"转发时延");</script>:</span></td>
		 <td  width="20%" align="left" class="crons">
		  <input type="text" name="forwardtime" value=<% write(forwardtime); %>>
	   </td>
		<td  width="20%" align="left" class="all_tables">
			<span class="all_tables3">&nbsp;<script>writemsg(<% write(lang); %>,"Hello时延");</script>:</span>
			</td>
		<td  width="20%" align="left" class="all_tables">
		  <input type="text" name="hellotime" value=<% write(hellotime); %>>
		 </td> 
	  </tr>
	  <tr height="25" >
		<td  align="left" class="crons"><span class="all_tables3">&nbsp;<script>writemsg(<% write(lang); %>,"最大老化时间");</script>:</span> </td>
		<td  align="left" class="all_tables">
			<input type="text" name="maxage" value=<% write(maxage); %>>
		   </td>
		<td  align="left" class="crons"><span class="all_tables3">&nbsp;</span></td>
		<td  align="left"  class="all_tables"></td>
		<!--<td  align="left" class="crons"><span class="all_tables3">&nbsp;<script>writemsg(<% write(lang); %>,"最大跳数");</script>:</span></td>
		<td  align="left"  class="all_tables">
		  <input type="text" name="maxhops" value=<% write(maxhops); %>>
		 </td> --> 
	  </tr>
	  <tr height="25">
		  <td colspan="4" align="center"> 
		   <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"应  用","buttons_apply","button","modify","checkMessage()");</script>
		 </td>
	   </tr>
	   <tbody  style='display:none'>	
		<tr height="25" >
		  <td  class="crons" style="border: none;">&nbsp;<script>writemsg(<% write(lang); %>,"域名");</script>:</td>
		  <td  align="left" class="crons">
			  <input type="text" name="region" value=<% write(region); %>>
			 </td> 
		  <td   align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"修订版本");</script>:</td>
			<td  align="left" class="crons">
			  <input type="text" name="revision" value=<% write(revision); %>>
			 </td> 
		</tr>
		<tr height="25">
		  <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"禁用时延");</script>:</td>
			 <td align="left" class="crons">&nbsp;
				<input type="radio" name="errtimeout" value="enable" <% if (errtimeout=="enable") write("checked"); %>>Enable
				  <input type="radio" name="errtimeout" value="disable" <% if (errtimeout!="enable") write("checked"); %>>Disable	
			 </td> 
			 <td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"周期");</script>:</td>
			 <td  align="left" class="crons">
				<input type="text" name="interval" value=<% write(interval); %>>
			 </td>
		</tr>
		<tr height="25">
		  <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"BPDU过滤");</script>:</td>
			 <td align="left" class="crons">&nbsp;
				<input type="radio" name="bpdufilter" value="enable" <% if (bpdufilter=="enable") write("checked"); %>>Enable
				  <input type="radio" name="bpdufilter" value="disable" <% if (bpdufilter!="enable") write("checked"); %>>Disable	
			 </td> 
			 <td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"BPDU保护");</script>:</td>
			 <td align="left" class="crons">
				<input type="radio" name="bpduguard" value="enable" <% if (bpduguard=="enable") write("checked"); %>>Enable
				  <input type="radio" name="bpduguard" value="disable" <% if (bpduguard!="enable") write("checked"); %>>Disable	
			 </td>
		</tr>
		</tbody>
		<tr style='display:none'>     
			<td><table width="100%"  align="center" cellpadding="0" cellspacing="0"  style='display:none' >
				<tr height="25">
					<td width="100%" >
						<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" id="table4" class="tablebord">
						<tr height="25"><td colspan="3" align="left" width="100%">&nbsp;<script>writemsg(<% write(lang); %>,"实例vlan关系");</script></td></tr>
						<tr height="25"><td width="10%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥名");</script></td><td width="15%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"实例ID");</script></td><td width="70%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN信息");</script></td></tr>			
						<script>
						<% showMstpBriInstVlanTable(); %>
						</script>
						</table>
					</td>
				</tr>
			</table></td>
		  </tr> 
		  <tr style='display:none'>
	        <td align="center" height="35" ><script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
		        &nbsp;
		        &nbsp;
		        </td>
	      </tr>
		</table>
	</td>
</tr>
	

	<!-- ----------------------- -->
	<!-- <tr>
		<td>
	<table width="100%"  border="0" align="left" cellpadding="0" cellspacing="0" >
		<tr>
		<td valign="top" >
		<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09" >
			
      		<tr>
        		<td>

 
		<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td  height="30px" ><font size="5" color="#404040"><div class="bot">RSTP 设置</div></font></td></tr>
        </table>

        		</td>
      		</tr>
      		<tr>
			    <td valign="top" >
								<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table1" style="border-left: 1px solid #eee;border-right: 1px solid #eee;" >
		
								<tr height="25" class="all_tables" bgcolor="#ddd">
									<td align="left" width="22%" class="crons" > <span class="all_tables3">&nbsp;<script>writemsg(<% write(lang); %>,"STP开关");</script>:</span></td>
									<td width="78%" align="left" class="crons">
									<input type="radio" id="ms1" name="mstpswitch" value="enable" <% if (mstpswitch=="enable") write("checked"); %>>Enable
				          			<input type="radio" id="ms2" name="mstpswitch" value="disable" <% if (mstpswitch!="enable") write("checked"); %>>Disable
									</td>
								</tr>
																  
								<tr height="25" >
								  <td colspan="3" align="left" class="crons" >
								    <div align="center">
								      <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"应  用","buttons_apply","button","","messageCheck2()");</script>
                                    </div>
								  </td>
								  </tr>
								</table>
					
				
			    </td>
			</tr>
	
      		<tr>
		    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">      
		      <tr>     
		        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09"  style='display:none'>
		            <tr height="25">
						<td width="100%">
							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table2" class="tablebord">
							<tr height="25"><td colspan="4" align="left" width="100%">&nbsp;<script>writemsg(<% write(lang); %>,"实例配置");</script></td></tr>
							<tr><td align="left" width="20%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"创建实例");</script>:</td>
							<td align="left" colspan="2" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"实例ID");</script>:<input type="text" name="newId"></td>
							<td align="left" width="15%" class="crons">
							<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"创  建","button","button","","messageCheck1(1)");</script>
							</td></tr>
							<% show_mstp_instance1(); %>
							</table>
						</td>                     
		            </tr>
		        </table></td>
		      </tr>      
		    </td>
		  </tr> 
	      	<tr>
		        <td>
					<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table3" class="tablebord">
		            <tr height="25">
		              <td colspan="4" align="left"><span class="all_tables3">&nbsp;<script>writemsg(<% write(lang); %>,"STP配置");</script></span> </td>
		            </tr>
		            <tr height="25">
		              <td  align="left" class="crons"><span class="all_tables3">&nbsp;<script>writemsg(<% write(lang); %>,"优先级");</script>&nbsp;:</span></td>
		              <td   width="20%" align="left" class="all_tables">
		          			<input type="text" name="prioritycom" value=<% write(prioritycom); %>>
		              <td  align="left" class="crons" >&nbsp; &nbsp;</td>
		              <td   width="20%" align="left" class="all_tables">&nbsp;<div  style='display:none'>
		          			<input type="radio" name="ciscointer" value="enable" <% if (ciscointer=="enable") write("checked"); %>>Enable
		          			<input type="radio" name="ciscointer" value="disable" <% if (ciscointer!="enable") write("checked"); %>>Disable </div>
		               </td> 
		               </td>
		            </tr>
		            <tr height="25">
		         	  <td   width="20%" align="left" class="crons"><span class="all_tables3">&nbsp;<script>writemsg(<% write(lang); %>,"转发时延");</script>:</span></td>
		         	  <td  width="30%" align="left" class="crons">
						<input type="text" name="forwardtime" value=<% write(forwardtime); %>>
		             </td>
		              <td  width="20%" align="left" class="all_tables">&nbsp;<script>writemsg(<% write(lang); %>,"Hello时延");</script>:</td>
		              <td  width="30%" align="left" class="all_tables">
						<input type="text" name="hellotime" value=<% write(hellotime); %>>
		               </td> 
		            </tr>
		            <tr height="25" >
		              <td  align="left" class="crons"><span class="all_tables3">&nbsp;<script>writemsg(<% write(lang); %>,"最大老化时间");</script>:</span> </td>
		              <td  align="left" class="all_tables">
		                  <input type="text" name="maxage" value=<% write(maxage); %>>
		                 </td>
		              <td  align="left" class="crons"><span class="all_tables3">&nbsp;<script>writemsg(<% write(lang); %>,"最大跳数");</script>:</span></td>
		              <td  align="left"  class="all_tables">
						<input type="text" name="maxhops" value=<% write(maxhops); %>>
		               </td> 
		            </tr>
					<tr height="25">
						<td colspan="4" align="center"> 
						 <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"应  用","buttons_apply","button","modify","checkMessage()");</script>
					   </td>
					 </tr>
				<tbody  style='display:none'>	
		            <tr height="25">
		              <td  class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"域名");</script>:</td>
		              <td  align="left" class="crons">
		                  <input type="text" name="region" value=<% write(region); %>>
		                 </td> 
		              <td   align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"修订版本");</script>:</td>
						<td  align="left" class="crons">
		                  <input type="text" name="revision" value=<% write(revision); %>>
		                 </td> 
		            </tr>
		            <tr height="25">
		              <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"禁用时延");</script>:</td>
		                 <td align="left" class="crons">&nbsp;
							<input type="radio" name="errtimeout" value="enable" <% if (errtimeout=="enable") write("checked"); %>>Enable
		          			<input type="radio" name="errtimeout" value="disable" <% if (errtimeout!="enable") write("checked"); %>>Disable	
		                 </td> 
		                 <td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"周期");</script>:</td>
		                 <td  align="left" class="crons">
							<input type="text" name="interval" value=<% write(interval); %>>
		                 </td>
		            </tr>
					<tr height="25">
		              <td  align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"BPDU过滤");</script>:</td>
		                 <td align="left" class="crons">&nbsp;
							<input type="radio" name="bpdufilter" value="enable" <% if (bpdufilter=="enable") write("checked"); %>>Enable
		          			<input type="radio" name="bpdufilter" value="disable" <% if (bpdufilter!="enable") write("checked"); %>>Disable	
		                 </td> 
		                 <td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"BPDU保护");</script>:</td>
		                 <td align="left" class="crons">
							<input type="radio" name="bpduguard" value="enable" <% if (bpduguard=="enable") write("checked"); %>>Enable
		          			<input type="radio" name="bpduguard" value="disable" <% if (bpduguard!="enable") write("checked"); %>>Disable	
		                 </td>
		            </tr>
					</tbody>
		        </table></td>
		      </tr>
			 
			   <tr>
			    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">      
			      <tr>     
			        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  style='display:none' >
			            <tr height="25">
							<td width="100%">
								<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" id="table4" class="tablebord">
								<tr height="25"><td colspan="3" align="left" width="100%">&nbsp;<script>writemsg(<% write(lang); %>,"实例vlan关系");</script></td></tr>
								<tr height="25"><td width="10%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥名");</script></td><td width="15%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"实例ID");</script></td><td width="70%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN信息");</script></td></tr>			
								<script>
								<% showMstpBriInstVlanTable(); %>
								</script>
								</table>
							</td>
			            </tr>
			        </table></td>
			      </tr>      
			    </td>
			  </tr>
        </table>
       	</td>
	    </tr>
	     <tr style='display:none'>
	        <td align="center" height="35"><script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
		        &nbsp;
		        &nbsp;
		        </td>
	      </tr>
    </table>
</td>
</tr> -->
</table>
</div>
</form>

<form x-show="active==='tab3'" name="webFormInfo" method="post" action="/goform/setMstpPortCfg" class="formContain">
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
	<input type="hidden" name="lastts" value=<% write(serverts); %>>
	<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
	
	<table  width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
		<tr>
			<td  height="30px" ><font size="5" color="#404040"><div class="bot">RSTP 信息</div></font></td>
			
		</tr>
		<tr height="25"><td  align="left" style="text-align: left;" class="tableTd">
			<span ><script>writemsg(<% write(lang); %>,"桥基本信息");</script></span>
		</td></tr>
		
			<tr>     
				<td>
					<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" >
					<tr height="25" bgcolor="#FFFFFF">
						<td width="100%">
							<table width="100%"  id="table21" class="tablebord" border="0" align="center" cellpadding="0" cellspacing="0" >
							<thead>
								<tr>
									<th align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥状态");</script></th>
								<th align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"协议状态");</script></th><th align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥优先级");</script></th><th align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥ID");</script></th><th align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"根桥ID");</script></th><th align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"根端口");</script></th><th align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"根开销");</script></th>
								</tr>
							</thead>
							<tr>
								<script>
							<% showMstpCistBriTable(); %>
							</script>
							</tr>
							
							</table>
						</td>                     
					</tr>
					 <tr>
				
				<td  height="35" ><span class="all_tables3 all_tables" style="border: none;">STP 端口状态</span></td></tr> 
				<tr>
				<td>
			<table width="100%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table23"  >
				<TR align="center" height=22>
					<td class="all_tables_list" width="14%"  align="center">端口</td>
					<td width="14%"  class="all_tables_list">端口角色</td>
					<td width="14%"  class="all_tables_list">端口状态</td>
					<td width="14%"  class="all_tables_list">路径开销</td>
					<td width="14%"  class="all_tables_list">优先级</td>
					<td width="16%"  class="all_tables_list">类型</td>
				</TR>
				 <script>
					<% StpPortShow(); %>
				</script>
			  </table>		
						
						
						
				</td>
			</tr>
			<tr>
				
					<td align="center" height="40" style="padding: 5px;">
						<script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage1()");</script>
					</td>
			</tr>

				</table></td>
			  </tr> 
			 
			 
			
		<!-- ------------------------------------- -->
		<!-- <tr>
			<td>
			
		<table width="98%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" >
			<tr>
				<td valign="top" >
			<table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
			  <tr>
				<td>
				
	 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
		<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>RSTP 信息</b></font></td></tr>
	 </table>		
				</td>
			  </tr>  

				  <tr>

				<td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0">      
				  <tr>     
					<td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
						<tr height="25">
							<td width="100%">
								<table width="100%"  id="table21" class="tablebord" border="0" align="center" cellpadding="0" cellspacing="0" >
								<tr height="25"><td colspan="9" align="left" width="100%">&nbsp;<script>writemsg(<% write(lang); %>,"桥基本信息");</script></td></tr>
								<tr><td align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥状态");</script></td><td align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"协议状态");</script></td><td align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥优先级");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥ID");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"根桥ID");</script></td><td align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"根端口");</script></td><td align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"根开销");</script></td></tr>
								<script>
								<% showMstpCistBriTable(); %>
								</script>
								</table>
							</td>                     
						</tr>
					</table></td>
				  </tr>      
				</td>
			  </tr> 
			 
		<tr>
				<td valign="top" >
				
		
	
				
	 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
		<tr><td bgcolor="#ABD8EF" height="20px" ><font size="3" color="#FFFFFF"><b>STP 端口状态</b></font></td></tr>
	 </table>
				</td>
		</tr>
	 
	
	<tr>
		<td>
	<table width="100%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table23"  >
		<TR align="center" height=22>
			<td class="all_tables_list" width="14%"  align="center">端口</td>
			<td width="14%"  class="all_tables_list">端口角色</td>
			<td width="14%"  class="all_tables_list">端口状态</td>
			<td width="14%"  class="all_tables_list">路径开销</td>
			<td width="14%"  class="all_tables_list">优先级</td>
			<td width="16%"  class="all_tables_list">类型</td>
		</TR>
		 <script>
			<% StpPortShow(); %> 
		</script>
	  </table>		
				
				
				
		</td>
	</tr>
			  <tr>
				<td align="center" height="35"><script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
					&nbsp;
					&nbsp;
					</td>
			  </tr>
	</table>
		 </td>
		</tr> -->
	</table>
</form>





<script>
changebgcolor();
changebgcolor2();
// changebgcolor3();
changebgcolor4();
changebgcolor11();
changebgcolor_name("table_port_vlan");

changebgcolor21();
changebgcolor23();
<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</body>
</html>
