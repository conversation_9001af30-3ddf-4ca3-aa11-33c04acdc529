<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<title>&nbsp;<script>writemsg(<% write(lang); %>,"语言");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script language="JavaScript">

function messageCheck()
{
	var hid = document.webForm;
	hid.submit();
	return true;
}
</SCRIPT>
</head>
<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="get" action="/goform/set_language">
<input type="hidden" name="lltime" value=<% write(lltime); %>>
<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
            <tr>
              <td colspan="2" align="left" class="cword09">&nbsp;<script>writemsg(<% write(lang); %>,"系统管理");</script><b><font color="#FF7F00">&gt;&gt;</font></b>&nbsp;<script>writemsg(<% write(lang); %>,"语言");</script> </td>
            </tr>
        </table></td>
      </tr>
      
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
            <tr height="25">
              <td colspan="3" align="left">&nbsp;<script>writemsg(<% write(lang); %>,"语言");</script></td>
            </tr>
            <tr height="25">
              <td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"语言");</script>:</td>
              <td align="left" class="crons" >
              <select name="language" onchange="messageCheck()">
								<option value="ch" <% if (lang=='"ch"') write("selected"); %> >&nbsp;<script>writemsg(<% write(lang); %>,"中文");</script>
								<option value="en" <% if (lang=='"en"') write("selected"); %> >&nbsp;<script>writemsg(<% write(lang); %>,"英文");</script>
          		</select>
              </td>
            </tr>
        </table></td>
      </tr>
      <tr>
        <td height="8"></td>
      </tr>
    </table></td>
  </tr> 
</table>
</td></tr>
<tr><td>
</td></tr></table>
</body>
</form>
</html>


