<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<% var authmode; checkCurMode(); %>

			<meta http-equiv="Content-Type" content="text/html; charset=utf8">
			<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
			<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
			<script language="javascript" type="text/javascript" src="js/alpinejs.min.js" defer></script>
			<link href="css/display_web.css" rel="stylesheet" type="text/css" />
			<title>
				<script>writemsg(<% write(lang); %>, "端口管理");</script>
			</title>

			<script language="JavaScript">

				function showHelp(helpname, lang) {
					var tmp = lang + "_help.html#" + helpname;
					window.open(tmp);
				}


				var portStaList = [<% PortLimitShow();%>];



				function writeLines() {
					var j = 0;
					for (var i = 0; i < (portStaList.length - 1) / 5; i++) {
						document.write(" <tr class='tables_all'>");

						document.write("    <td class='inputsyslog1'><input type='checkbox' name='checkbox_index' onchange=addToPortRange('" + i + "') ></td>");

						document.write("    <td class='inputsyslog1'>" + portStaList[j] + "</td>");

						j++
						document.write("    <td class='inputsyslog1'>" + portStaList[j] / 64 * 64 + "</td>");

						j++
						//document.write("    <td class='inputsyslog1'>"+portStaList[j]+"</td>");

						j++
						document.write("    <td  class='inputsyslog1'>" + portStaList[j] / 64 * 64 + "</td>");
						j++;
						//document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
						j++;



						document.write("  </tr>");

					}
				}

				/*select ALL*/
				function selectToAll() {
					var cf = document.forms[0];
					var objs = document.getElementsByName("checkbox_index");
					var i;


					if (cf.check_all.checked == true) {
						for (i = 0; i < objs.length; i++) {
							if (objs[i].disabled == false && objs[i].checked == false) {
								objs[i].checked = true;
								addToPortRange(i);
							}
						}
					}
					else {
						for (i = 0; i < objs.length; i++) {
							if (objs[i].checked == true) {
								objs[i].checked = false;
								addToPortRange(i);
							}
						}
					}

				}

				/*MTU judgment*/
				function checking2() {
					var port_range = document.getElementById("port_range").value;
					var port_txrate = document.getElementById("tx_linerate").value;
					var port_rxrate = document.getElementById("rx_linerate").value;
					var port_txburst = document.getElementById("tx_burst").value;
					var port_rxburst = document.getElementById("rx_burst").value;
					var checkbox_index = document.getElementsByName("checkbox_index");
					var table_port = document.getElementById("table_port");
					var tf = document.port_setting;
					var i, j;
					var rate_temp;


					if ((port_txrate != 0) && (port_txrate < 64 || port_txrate > 1000000)) {
						alert(putmsg(<% write(lang); %>, "Invalid egress rate value!"));
						return;
					}
					rate_temp = port_txrate % 64;
					if (rate_temp) {
						alert(putmsg(<% write(lang); %>, "egress rate value must be a multiple of 64!"));
						return;
					}

					if ((port_rxrate != 0) && (port_rxrate < 64 || port_rxrate > 1000000)) {
						alert(putmsg(<% write(lang); %>, "invalid ingress rate value!"));
						return;
					}
					rate_temp = port_rxrate % 64;
					if (rate_temp) {
						alert(putmsg(<% write(lang); %>, "ingress rate value must be a multiple of 64!"));
						return;
					}

					/*
						if ((port_txrate != 0) && (port_txburst < 64 || port_txburst > 1000000))
						{
							alert(putmsg(<% write(lang); %>,"invalid egress burst value!"));
							return;
						}
						if (port_txburst%64)
						{
							alert(putmsg(<% write(lang); %>,"egress burst value must be a multiple of 64!"));
							return;
						}
						if ((port_rxrate != 0) && (port_rxburst < 64 || port_rxburst > 1000000))
						{
							alert(putmsg(<% write(lang); %>,"invalid ingress burst value!"));
							return;
						}
						if (port_rxburst%64)
						{
							alert(putmsg(<% write(lang); %>,"ingress burst value must be a multiple of 64!"));
							return;
						}
					*/
					document.getElementById("tx_linerate").value = (port_txrate / 64) * 64;
					document.getElementById("rx_linerate").value = (port_rxrate / 64) * 64;

					tf.submit();
				}


				/*
					Show all check true port, and will last a port data displayed
				*/
				function addToPortRange(index) {
					//alert(index);
					var target = document.getElementById("port_range");
					var rx_linerate = document.getElementById("rx_linerate");
					var rx_burst = document.getElementById("rx_burst");
					var tx_linerate = document.getElementById("tx_linerate");
					var tx_burst = document.getElementById("tx_burst");
					var objs = document.getElementsByName("checkbox_index");


					if (objs[index].checked) {
						target.value = target.value + portStaList[5 * index] + " ";


						tx_linerate.value = portStaList[5 * index + 1] / 64 * 64;
						tx_burst.value = portStaList[5 * index + 2];
						rx_linerate.value = portStaList[5 * index + 3] / 64 * 64;
						rx_burst.value = portStaList[5 * index + 4];
					}
					else {
						target.value = target.value.replace(portStaList[5 * index] + " ", "");
					}

				}


				function refreshpage() {
					location.href = 'port_linerate.asp?ltime=' +<% write(lltime); %>;
				}

			</SCRIPT>

</head>

<body onload=""><br>
	<% web_get_stat(); %>
		<script>
			checktop(<% write(lang); %>);
			retValue = <% var responseJsonStr; jw_get_portSpeedConfig(); %>
				responseStr = <% write(responseJsonStr); %>;
			var headers = ["端口", "出口限速", "入口限速"]

			var tabDatas = {
				portName: '',
				egresskbits: 0,
				ingresskbits: 0,
			}
			var selectOpt =JSON.parse(localStorage.getItem('portList')).map(item => item.portName)

			function optSelect() {
				for (let i = 0; i < responseStr.PortSpeedLimit.length; i++) {
					if (tabDatas.portName == responseStr.PortSpeedLimit[i].portName) {
						window.form1.egresskbits.value = responseStr.PortSpeedLimit[i].egresskbits
						window.form1.ingresskbits.value = responseStr.PortSpeedLimit[i].ingresskbits
					}
				}
			}
			function closestPowerOfTwo(num) {
				if(num == 0) return 0
				if (num < 64) {
					return 64;
				} else if (num > 1000000) {
					return 1000000;
				} else if ((Math.log2(num) % 1) === 0) {
					return num;
				} else {
					const lowerPower = Math.pow(2, Math.floor(Math.log2(num)));
					const higherPower = Math.pow(2, Math.floor(Math.log2(num)) + 1);
					const closest = Math.abs(num - lowerPower) < Math.abs(num - higherPower) ? lowerPower : higherPower;
					return closest > 1000000 ? 1000000 : closest;
				}
			}

			function change1() {

				tabDatas.egresskbits = closestPowerOfTwo(Number(window.form1.egresskbits.value))
				window.form1.egresskbits.value = tabDatas.egresskbits
				tabDatas.ingresskbits = closestPowerOfTwo(Number(window.form1.ingresskbits.value))
				window.form1.ingresskbits.value = tabDatas.ingresskbits
			}
			function apply() {
				if (tabDatas.portName == '') {
					alert('请选择端口')
					return
				}


				var obj = {
					pageName: "port_linerate.asp",
					PortSpeedLimit: [
						{
							portName: tabDatas.portName,
							egresskbits: closestPowerOfTwo(Number(window.form1.egresskbits.value)),
							ingresskbits: closestPowerOfTwo(Number(window.form1.ingresskbits.value))
						}
					]
				}
				var tf = document.port_setting;
				tf.param1.value = JSON.stringify(obj)
				tf.action = "/goform/jw_set_portSpeedConfig";
				tf.submit();
			}
		</script>
		<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
			<td height="30px">
				<font size="5" color="#0069d6">
					<div style="padding-bottom: 10px; border-bottom: 1px solid #eee;margin-bottom: 32px;">端口限速配置列表</div>
				</font>
			</td>
		</table>
		<div class="formContain">
			<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td height="30px">
						<font size="5" color="#0069d6">
							<div class="bot">端口限速显示</div>
						</font>
					</td>
				</tr>
			</table>
			<div x-data="{}">
				<table width="96%" class="tablebord" align="center" border="0" cellspacing="0" cellpadding="0">
					<thead>
						<tr>
							<template x-for="(item,index) in headers" :key="index">
								<th class="all_tables" x-text="item"></th>
							</template>
						</tr>
					</thead>
					<tbody>
						<template x-for="(row,rowIndex) in responseStr.PortSpeedLimit">
							<tr>
								<td style="font-weight: normal;text-align: center;" class="all_tables"
									x-text="row.portName"></td>
								<td style="font-weight: normal;text-align: center;" class="all_tables"
									x-text="row.egresskbits"></td>
								<td style="font-weight: normal;text-align: center;" class="all_tables"
									x-text="row.ingresskbits"></td>
							</tr>
						</template>
					</tbody>
				</table>
			</div>
		</div>
		<br /><br />
		<form name="port_setting" method="POST" action="/goform/PortLimitChange">
			<input type="hidden" name="param1" id="param1">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
		</form>
		<form name="form1" class="formContain">
			<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td height="30px">
						<font size="5" color="#0069d6">
							<div class="bot">端口限速配置</div>
						</font>
					</td>
				</tr>
			</table>
			<div x-data="tabDatas">
				<table width="96%" class="tablebord" align="center" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td width="42%" class="all_tables all_tables1">端口选择:</td>
						<td class="all_tables all_tables2" style="text-align: left;">
							<select x-model="portName" @change="optSelect">
								<option value="" disabled></option>
								<template x-for="(opt,optIndex) in selectOpt" :key="optIndex">
									<option x-text="opt"></option>
								</template>
							</select>
						</td>
					</tr>
					<tr>
						<td class="all_tables all_tables1">出口限速:</td>
						<td class="all_tables all_tables2" style="text-align: left; font-weight: normal;">
							<input name="egresskbits" type="number" x-model="egresskbits" @change="change1" />
							(单位为:kbps, 64-1000000且取2的n次方, 0:不限速)
						</td>
					</tr>
					<tr>
						<td class="all_tables all_tables1">入口限速:</td>
						<td class="all_tables all_tables2" style="text-align: left; font-weight: normal;">
							<input name="ingresskbits" type="number" x-model="ingresskbits" @change="change1" />
							(单位为:kbps, 64-1000000且取2的n次方, 0:不限速)
						</td>
					</tr>
					<tr>
						<td class="all_tables"></td>
						<td class="all_tables" style="text-align: left;">
							<input type="button" class="inpBtn" value="应用" @click="apply" />
						</td>
					</tr>
				</table>
			</div>
		</form>

		<script>

<% if (errorcode != "") { write_errorcode(errorcode); } %>
		</script>
</body>

</html>