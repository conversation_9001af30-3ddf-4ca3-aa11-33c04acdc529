<html ><head>
<title>Login</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<SCRIPT language=javascript>

if (top.location != document.location) top.location.href = document.location.href;
 </SCRIPT>

<style type="text/css">
<!--
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
}
.inputcss{width:150px;height:25px;}
.strone-field {width:75px;height:30px;color: #fff;background-color:#be6301;border:0px;cursor:pointer;}
.login_page_top{ height:5; background-color:#66B3FF}
.login_page_top_xia{ height:14; background-color:#dedcdd}
td.login_page_middle{ vertical-align:bottom;background-color:#66B3FF; height:80}
td.login_page_middle_left{ vertical-align:bottom;background-color:#66B3FF; height:80; text-align:left;font-size: 20px;font-weight: bold}
td.login_page_middle_right{ vertical-align:center;background-color:#66B3FF; height:80; text-align:left;font-size : 11px;color:ffffff;font-weight: bold;}
.larges_title {font-size: 24px;font-weight: bold;	color: #ffffff}
.mainHeader {
	MARGIN-TOP: 0px; PADDING-LEFT: 3px; FONT-WEIGHT: bold; FONT-SIZE: 16px; 
	VERTICAL-ALIGN: bottom; COLOR: #be6301; BORDER-TOP-STYLE: none; 
	BORDER-BOTTOM: #4891C6 1px solid; FONT-FAMILY: Geneva, Arial, Helvetica, sans-serif; 
	BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; HEIGHT: 22px; TEXT-ALIGN: left
}
.bottom {
	FONT-WEIGHT: normal; FONT-SIZE: 11px; COLOR: #ffffff; FONT-FAMILY: Arial, Helvetica, sans-serif; HEIGHT: 20px; BACKGROUND-COLOR: #336799
}
.login_name{ FONT-FAMILY: Geneva, Arial, Helvetica, sans-serif;font-size: 14px;font-weight: bold; }
.crons {vertical-align:middle }

-->
</style>


<script language="javascript">
if (window.focus) self.focus();



function login()
{
	var dplen;
	var dpvalue;
	var dpvalue_v1;
  if(document.getElementById("validateValue").value!= document.tF0.yzm_txt.value )
  {
  	alert("验证码错误");
	validteCode();
	document.tF0.yzm_txt.select();
	return false;
  }

  
	if(""==document.tF0.v1.value)
	{
		alert("请输入用户名!");
		return false;
	}
	if(""==document.tF0.v2.value)
	{
		alert("请输入用户密码!");
		return false;
	}
	if(document.tF0.v2.value.indexOf(" ")>-1)
	{
		alert("密码中含有空格,请重新输入!");
		return false;
	}

	//document.tF0.v3.value = ":3F2e6855" + document.tF0.v1.value + ":E26bE809" + document.tF0.v2.value + ":B4ea23F1";

	dplen = parseInt(document.tF0.v1.value.length/2);
	dpvalue = document.tF0.v1.value;
	dpvalue_v1 = dplen + ":3F2e6855" + dpvalue.substring(0, dplen) 
							+ document.tF0.v1.value.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(document.tF0.v2.value.length/2);
	dpvalue = document.tF0.v2.value;
	document.tF0.v3.value = dpvalue_v1 + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen) 
							+ document.tF0.v2.value.length + ":E95ca4DA" + dpvalue.substring(dplen);

	document.tF0.v1.disabled = true;
	document.tF0.v2.disabled = true;
	document.tF0.v1.value = "soPkcS%RZ$810uUn";
	document.tF0.v2.value = "bORvaD#2IhU1HG6m";

	document.tF0.yzm_txt.disabled = true;
	
	return true;
}

function messageCheck(language_type)
{
  switch(language_type)
  {
	case 1:
	  location.href="login_ch.asp";
	  break;
	case 2:
	  location.href="login_en.asp";
	  break;
	default:
      break;
  }
  return true;
}



function validteCode()
{
	var validateCode="";
	var codes = new Array(4);       // 用于存储随机验证码
	var colors = new Array("Red","Green","Gray","Blue","Maroon","Aqua","Fuchsia","Lime","Olive","Silver");
	for(var i=0;i < codes.length;i++)
	{
		//获取随机验证码
		codes[i] = Math.floor(Math.random()*10);
	}
	//var spans = document.getElementById("divCode").all;
	document.getElementById("divCode1").value = codes[0];
	document.getElementById("divCode2").value = codes[1];
	document.getElementById("divCode3").value = codes[2];
	document.getElementById("divCode4").value = codes[3];
	

	for(var i=0;i<4;i++)
	{
		validateCode+=codes[i];
	}
	
	//alert(validateCode);
	
/*
	for(var i=0;i<spans.length;i++)
	{
		spans[i].innerHTML=codes[i];
		spans[i].style.color = colors[Math.floor(Math.random()*10)];    // 随机设置验证码颜色
		validateCode+=codes[i];
	}
*/	
	//将验证码的值保存到一个隐藏控件
	document.getElementById("validateValue").value=validateCode;
	
document.forms[0].v1.focus(); 	
	
	
	}


</script>
</head>
<body  onload="validteCode();">


<table width="100%" border="0" cellspacing="0" cellpadding="0">
		<tr><td>&nbsp;</td></tr>
		<tr><td>&nbsp;</td></tr>
</table>

<table width="100%" border="0" cellspacing="0" cellpadding="0">
<tr>
	<td>&nbsp</td>
</tr>
<tr>
	<td>&nbsp</td>
	<td></td>
</tr>
<tr>
	<td>&nbsp</td>
</tr>
</table>


<table width="100%" border="0" cellspacing="0" cellpadding="0">
<tr align="center">  
	<td height="470" colspan="3" align="center">

 
<form name="tF0" method="post" action="/goform/setLoginCfg">
<% var errorcode,serverts, alpha;getLoginCfg(); %>
<% web_get_stat(); %>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="v3" id="v3" value=<% write(alpha); %>>


<h1 style="color:3cc4c4">Welcome to Use Industrial Switch</h1>

<input type="hidden" name="lang" value="ch">

	<table width="500"  border="0" cellpadding="0" cellspacing="0">
	<tr>
		<td height="12" colspan="3" bgcolor="#ABD8EF"></td>
	</tr>
	</table>
	
	<table width="500" height="198" border="0" align="center" cellpadding="0" cellspacing="0" bgcolor="#f2f2f2" >
	<tbody>
 	<tr>
		<td height="32" colspan="3" align="left" bgcolor="#fafafa" class="mainHeader" ><h3>&nbsp;欢迎登录</h3></td>
	</tr>

	<tr>
		<td height="6" colspan="3" bgcolor="#fafafa" >&nbsp;</td>
	</tr>
	
	<tr height="30">
		<td  width="40%" align="right" bgcolor="#fafafa"  class="login_name">用户名：</td>
		<td  width="60%" colspan="2"  align="left" bgcolor="#fafafa" ><input  name="v1" type="text"size="20" placeholder="用户名" maxlength="16" class="inputcss">  </td>
 	</tr>
 	
	<tr height="30">
		<td height="22" align="right" bgcolor="#fafafa" class="login_name">密&nbsp;&nbsp;&nbsp;&nbsp;码：</td>
		<td colspan="2" align="left" bgcolor="#fafafa" ><input name="v2" type="password"size="20" placeholder="密码" maxlength="16" class="inputcss"> </td>
	</tr>
 
	<tr align="center" height="22">
		<td height="22" bgcolor="#fafafa">
			<div align="right" class="login_name">验证码：</div>
		</td>
		<td width="30%" height="22" bgcolor="#fafafa">
			<input name="yzm_txt" type="text" class="inputcss" id="yzm_txt"  AUTOCOMPLETE="off" />
		</td>
		<th width="30%" height="22" bgcolor="#fafafa">
			<DIV id="divCode"  onclick="JavaScript:validteCode()">
			<input name="divCode1" type="text" id="divCode1" size="1" maxlength="1" style="height:25px;width:16px;font-size:22px;border:0;color:#000000;font-weight:bold;background-color:#fafafa; " disabled/>
			<input name="divCode2" type="text" id="divCode2" size="1" maxlength="1" style="height:25px;width:16px;font-size:22px;border:0;color:#000000;font-weight:bold;background-color:#fafafa;" disabled/>
			<input name="divCode3" type="text" id="divCode3" size="1" maxlength="1" style="height:25px;width:16px;font-size:22px;border:0;color:#000000;font-weight:bold;background-color:#fafafa;" disabled/>
			<input name="divCode4" type="text" id="divCode4" size="1" maxlength="1" style="height:25px;width:16px;font-size:22px;border:0;color:#000000;font-weight:bold;background-color:#fafafa;" disabled/>
        	</DIV>
		</th>
	</tr>
	
	<tr align="center" height="25">
		<td bgcolor="#fafafa"><INPUT name="hidden" type="hidden" id="validateValue"> &nbsp;</td>
		<td colspan="2" align="left" bgcolor="#fafafa"><input name="Submit" type="submit" class="strone-field"  value="登录"/ onClick="return login();">
			<input type="reset" name="button2" id="button2"  class="strone-field" value="重置"  onClick="validteCode();"  style="display:none"  />
		</td>
	</tr>
	</tbody>
	</table>
			
	<table width="500"  border="0" cellpadding="0" cellspacing="0">
	  <tr>
	    <td height="12" colspan="3" bgcolor="#ABD8EF"></td>
	  </tr>
	</table>
</form>


	</td>
</tr>
</table>

<script>
<% if (errorcode=="1") { write_errorcode("Bad username or password, please login again!"); } %>
<% if (errorcode=="2") { write_errorcode("Failed to change password!');"); } %>
<% if (errorcode=="3") { write_errorcode("The number of users has reached the maximum!"); } %>
<% if (errorcode=="4") { write_errorcode("This username has been locked for 5 minutes, please try again later!"); } %>
<% if (errorcode=="5") { write_errorcode("rejected check error"); } %>
</script>
</body>
</html>




