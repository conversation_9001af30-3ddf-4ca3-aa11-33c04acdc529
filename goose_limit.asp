<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<%  var authmode; checkCurMode(); %>

<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<title>&nbsp;<script>writemsg(<% write(lang); %>,"GOOSE");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript">

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function display(){
var wValue = document.getElementById("qos_cfg").value.split(',');

	if(wValue[0] == "disable")
	{
	
		document.getElementById("modify_bu").disabled = true;
		alert("QoS未开启!");
	}
}


function del_goose(portId,addid)
{
	var hid = document.macdel;
	
	hid.port_value.value=portId;
	hid.appid_value.value=addid;

	hid.action="/goform/delGooseLimit"
	hid.submit();
	return 0;
}


function selectToAll() 
{  
		var cf = document.forms[0];
		var objs = document.getElementsByName("checkbox_index"); 
		var i;
		if(cf.check_all.checked == true)
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
		       if(objs[i].disabled==false && objs[i].checked==false){
		         objs[i].checked = true;  
			 		addToPortRange(objs[i]);
			 }
		    }
		}
		else
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
					if(objs[i].checked==true){
					objs[i].checked = false;  
					 addToPortRange(objs[i]);
			}
		         
		    }
		} 
    
}

function checkup_bak()
{
	var tf = document.vlan_port;
	var bbb = document.vlan_port.appid.value;
	var aaa = "010C.CD01." + bbb;
	var myRE =/^[0-9a-fA-F]{1,4}\.[a-fA-F0-9]{1,4}\.[a-fA-F0-9]{1,4}$/;

	//0x1FF:511, 0x3FFF:16383
 	//if (!myRE.test(aaa) || (parseInt(bbb, 16) < 0 )  || (parseInt(bbb, 16) > 16383 ))
	if ( !myRE.test(aaa) )
	{
		alert(putmsg(<% write(lang); %>,"输入appid无效"));
	}	
	else
	{
		tf.action = "/goform/selGooseLimit";
		tf.submit();
	}
}

function checkup()
{
	var tf = document.vlan_port;
	var aaa = document.vlan_port.appid.value.substring(0, 4);
	var bbb = document.vlan_port.appid.value.substring(5, 9);
	var ccc = document.vlan_port.appid.value.substring(10);
	var ddd = document.vlan_port.limit_value.value;

	if (parseInt(aaa, 16) != 268 )
	{
		alert("Invaild input, effective range: 010C.CD01.0000~010C.CD01.FFFF!");
		return false;
	}
	if (parseInt(bbb, 16) != 52481 )
	{
		alert("Invaild input, effective range: 010C.CD01.0000~010C.CD01.FFFF!");
		return false;
	}

	/*
	if ((parseInt(ccc, 16) < 0 )  || (parseInt(ccc, 16) > 511 ))
	{
		alert("Invaild input, effective range: 010C.CD01.0000~010C.CD01.01FF!");
		return false;
	}
	*/
	
	if (isNaN(parseInt(ddd)) || (parseInt(ddd) < 16 )  || (parseInt(ddd) > 1000000 ))
	{
		alert("Invaild input, effective range: 16~1000000!");
		return false;
	}	


	tf.action = "/goform/selGooseLimit";
	tf.submit();
}

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var target = document.getElementById("port_range");
	var avid = document.getElementById("avid");
	var dvid = document.getElementById("dvid");
	var porttype = document.getElementById("porttype");
	var filter = document.getElementById("filter");
	var type_value = document.getElementById("type_value");
	var filter_value = document.getElementById("filter_value");
	var vlan_port_id = document.getElementById("vlan_port_id");
	var p = obj.value;

	if(obj.checked){
		target.value =target.value + p + " ";
		vlan_port_id.value =vlan_port_id.value + p + " , ";
	}
	else
	{
		target.value = target.value.replace(p + " ","");
		vlan_port_id.value = vlan_port_id.value.replace(p + " , ","");
	}

}

function P(index,portId,appid,avg)
{
    var narr=4;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+portId);	


    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		
	    tbtr.appendChild(tbtd);
    }
	            
	tbtr.cells[0].abbr = 0;
	tbtr.cells[0].innerHTML = index;
	tbtr.cells[1].innerHTML = appid;
	tbtr.cells[2].innerHTML = avg;
	
	tbtr.cells[3].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick=del_goose('"+portId+"','"+appid+"')>";
}

function getPage(page)
{
   location.href="goose_limit.asp?page="+page+"&ltime="+<% write(lltime); %>;
}

function jumptoPage()
{
	var topage = document.getElementById("jump2page");
   location.href="goose_limit.asp?page="+topage.value+"&ltime="+<% write(lltime); %>;
}

function dofirst(){location.href="goose_limit.asp?page=1"+"&ltime="+<% write(lltime); %>;}


function refreshpage()
{
  location.href='goose_limit.asp?page=1&ltime='+<% write(lltime); %>;
}



function AddOption(portname){

	var selectObject = document.getElementById("rang_monitor");
	var y=document.createElement('option');
  	y.text=portname;
	y.value = portname;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  	catch(ex)
    {
   	selectObject.add(y); // IE only
  	}
}


</script>
</head>

<body    onload="display()" ><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="vlan_port" method="POST" action="">

<% var  qoscfg; getQoSCfg(); %>
	                  <input type="hidden" name="qos_cfg"  id="qos_cfg"   value="<% write(qoscfg); %>">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="vlan_port_id" id="vlan_port_id" >
<input type="hidden" name="flag" id="flag" >
<input type="hidden" name="type_value" id = "type_value"  value="@trunk_config#">
<input type="hidden" name="filter_value" id = "filter_value"  value="@trunk_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="pvid_config"  value="@pvid_config#">
<input type="hidden" name="egress_tagged" id="egress_tagged" value="@egress_tagged#">

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">

<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
     	 

 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>电网报文设置/GOOSE报文限速</b></font></td></tr>
 </table>
 
<!--    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
	     <tr height="25">
	       <td align="left" colspan="4"  class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"GOOSE限速");</script></td>
	     </tr>
        </table>
        -->
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%" id="table1" class="tablebord">

	
	     	     <tr height="25">
	     	  <td width="18%" class="crons">&nbsp;MAC地址</td>
	     	  <td width="32%" class="crons" >&nbsp;
	     	    <input name="appid" type="text" class="input_x" >(格式:010C.CD01.xxxx)	     	    </td>
	     	  <td width="18%" class="crons">&nbsp;阀值(16的倍数: 16~1000000)</td>
	     	  <td width="32%" class="crons" >&nbsp;
	     	    <input name="limit_value" type="text" value="2048" class="input_x" >(Kbps)	     	    </td>
     	    	      </tr>	
     	    	      <!--
		     	     <tr height="25">
	     	  <td class="crons">&nbsp;端口</td>
	     	  <td class="crons"  colspan="3">&nbsp;<select id="rang_monitor" name="rang_monitor"></select>
	 	<script>
						<% AppendOptionForfil(); %>	
					</script>				 
				 </td>
	     	    	     </tr>	    
	     	    	     -->
	    <tr height="25">

	     	  <td colspan="4" align="middle" class="crons">
	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify_bu","checkup()");</script>
&nbsp;&nbsp;&nbsp;</td>
	     </tr>	  
			 
   <tr>
	    	<td colspan="4">
	    		<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
	    		 	<tr align="center" height="25" class="crons">
	    		 		<th class="td2" width="20%">序号</th>
	    		 		<th class="td2" width="20%"><font color="#333333"><b>&nbsp;MAC</b></font></th>
	    		 		<th class="td2" width="20%">阀值</th>						
	    		 		<th class="td2" width="30%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"删除");</script></b></font></th>						
	    		 	</tr>
					<script>  <%  var errorcode; showGooseLimitPage("list"); %></script>
	    		</table>	    	</td>
   </tr>
     </table>
   </td></tr>
   <tr>
	  	   <td colspan="2" align="center" height="35">
<!--	  	   
	  	   <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
-->	  	   
	  	   <script>writebutton(1,<% write(lang); %>,"首  页","button","button","firstpage","dofirst()");</script>
				<%showGooseLimitPage("pagebutton");%>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<%showGooseLimitPage("pagenum");%>
				<%showGooseLimitPage("allpage");%> 
	  		</td>
   </tr>
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>

</form>  
<script>
changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

<form name="macdel" method="POST" action="">
	    <input type="hidden" name="ltime" value=<% write(lltime); %>>
	    <input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

	<input type="hidden" name="port_value"  value="" >
	<input type="hidden" name="appid_value"  value="" >
	

</form>
</body>
</html>

