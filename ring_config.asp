<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
	<% var lltime,lang; getltime_lanflag(); %>
		<meta http-equiv="Content-Type" content="text/html; charset=utf8">
		<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
		<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
		<link href="css/display_web.css" rel="stylesheet" type="text/css" />
		<title>&nbsp;
			<script>writemsg(<% write(lang); %>, "端口管理");</script>
		</title>
		<script>

			function showHelp(helpname, lang) {
				var tmp = lang + "_help.html#" + helpname;
				window.open(tmp);
			}

			function isINT(str) {
				var re = /^[0-9]*[0-9][0-9]*$/;

				var result = re.test(str);   //测试 返回true或false

				return result;

			}

			function changebgcolor() {
				var tab = document.all.table1;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							// tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables";
						}
						else {
							// tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables all_tables3";
						}

					}

				}
			}
			function changebgcolor2() {
				var tab = document.all.table2;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables";
						}
						else {
							tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables";
						}

					}

				}
			}
			function changebgcolor3() {
				var tab = document.all.table3;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables";
						}
						else {
							tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables";
						}

					}

				}
			}
			function changebgcolor4() {
				var tab = document.all.table4;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables";
						}
						else {
							tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables";
						}

					}

				}
			}


			function MSTPBRIINSTVLAN(v1, v2, v3) {
				document.write("<tr height='30'><td align='left' class='crons'>" + v1 + "</td><td align='left' class='crons'>" + v2 + "</td><td align='left' class='crons'>" + v3 + "</td></tr>");
				return;
			}
			function checkData() {
				var tf = document.webForm;
				tf.action = "/goform/saveComm?name=mstpcfg";
				tf.submit();
			}
			function checkMessage() {
				var hid = document.webForm;
				if (!isINT(hid.prioritycom.value)) {
					alert(putmsg(<% write(lang); %>, "优先级必须是整数!"));
					return false;
				}
				if (((hid.prioritycom.value % 4096) != 0) || (hid.prioritycom.value < 0 || hid.prioritycom.value > 61440)) {
					alert(putmsg(<% write(lang); %>, "优先级必须是4096的倍数,范围必须在0-61440之间!"));
					return false;
				}
				if (!isINT(hid.forwardtime.value)) {
					alert(putmsg(<% write(lang); %>, "转发时延必须是整数!"));
					return false;
				}
				if ((2 * (hid.forwardtime.value - 1) < (hid.maxage.value)) || (hid.forwardtime.value < 4) || (hid.forwardtime.value > 30) || (hid.maxage.value < 6) || (hid.maxage.value > 40)) {
					alert(putmsg(<% write(lang); %>, "必须满足关系:2*(forwardtime-1)>=max-age,且4<=forwardtime<=30,6<=maxage<=40!"));
					return false;
				}
				if (!isINT(hid.maxhops.value)) {
					alert(putmsg(<% write(lang); %>, "最大跳数必须是整数!"));
					return false;
				}
				if ((1 > hid.maxhops.value) || (hid.maxhops.value > 40)) {
					alert(putmsg(<% write(lang); %>, "最大跳数的范围必须在1~40之间!"));
					return false;
				}
				if (!isINT(hid.hellotime.value)) {
					alert(putmsg(<% write(lang); %>, "HELLO时延必须是整数!"));
					return false;
				}
				if ((1 > hid.hellotime.value) || (hid.hellotime.value > 10)) {
					alert(putmsg(<% write(lang); %>, "HELLO时延的范围必须在1~10之间!"));
					return false;
				}
				if (hid.region.value.length > 20) {
					alert(putmsg(<% write(lang); %>, "域名的长度不能超过20个字符!"));
					return false;
				}
				if (!isINT(hid.revision.value)) {
					alert(putmsg(<% write(lang); %>, "修订版本必须是整数!"));
					return false;
				}
				if ((hid.revision.value > 255) || (hid.revision.value < 0)) {
					alert(putmsg(<% write(lang); %>, "修订版本的范围必须在0~255之间!"));
					return false;
				}
				if (!isINT(hid.interval.value)) {
					alert(putmsg(<% write(lang); %>, "周期必须是整数!"));
					return false;
				}
				if (hid.interval.value != 1) {
					if ((hid.interval.value < 10) || (hid.interval.value > 1000000)) {
						alert(putmsg(<% write(lang); %>, "周期的范围必须在10~1000000之间或者默认值1!"));
						return false;
					}
				}
				hid.submit();
				return true;

			}

			function messageCheck2() {
				var tf = document.webForm;
				var checkbox_index = document.getElementsByName("checkbox_index");
				var i;
				var target = document.getElementById("portNum");
				target.value = "";
				j = 0;

				for (i = 0; i < checkbox_index.length; i++) {

					if (checkbox_index[i].checked == false)
						target.value = target.value + checkbox_index[i].id + "|0|";
					else
						target.value = target.value + checkbox_index[i].id + "|1|";

				}

				tf.action = "/goform/setRingCfg";
				tf.submit();
			}



			function refreshpage() {
				location.href = 'ring_config.asp?ltime=' +<% write(lltime); %>;
			}

			function showHelpinfo() {
				showHelp('ring_config',<% write(lang); %>);
			}

<%  var authmode; checkCurMode(); %>
				function display() {
					changebgcolor();
					var wValue = document.getElementById("ring_cfg").value.split(',');

					if (wValue[0] == "enable") {
						document.getElementById("ms1").checked = true;
					}
					else {
						document.getElementById("ms2").checked = true;
					}

					document.getElementById("ringpri").value = wValue[1];

				}

			function changebgcolor5() {
				var tab = document.all.table5;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							// tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables all_tables3";
						}
						else {
							// tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables all_tables3";
						}

					}

				}
			}

			function MSTPCISTBRIBASEINFO(v1, v2, v3, v4, v5, v6, v7, v8, v9) {
				document.write("<tr height='30'><td align=left' class='crons'>" + v2 + "</td><td align=left' class='crons'>" + v3 + "</td><td align=left' class='crons'>" + v4 + "</td><td align=left' class='crons'>" + v5 + "</td><td align=left' class='crons'>" + v6 + "</td><td align=left' class='crons'>" + v8 + "</td><td align=left' class='crons'>" + v9 + "</td></tr>");
				return;
			}



			function P(m1, m2, m3, m4) {
				var narr = 4;
				var tbtd;
				var i;
				var opt;
				var gtrunk = 0;
				var tbtr = document.getElementById("table5").insertRow(-1);

				tbtr.classname = "crons";
				tbtr.height = "30";

				tbtr.setAttribute("height", "30");
				tbtr.setAttribute("class", "crons");
				tbtr.setAttribute("className", "crons");
				//tbtr.setAttribute("id", "tr_"+portId);

				for (i = 0; i < narr; i++) {
					tbtd = document.createElement("td");

					tbtd.align = "center";
					tbtd.setAttribute("class", "td2");
					tbtd.setAttribute("className", "td2");
					tbtr.appendChild(tbtd);
				}


				tbtr.cells[0].innerHTML = m1;
				tbtr.cells[1].innerHTML = m2;
				tbtr.cells[2].innerHTML = m3;
				tbtr.cells[3].innerHTML = m4;

			}

		</script>
</head>

<body onload="display()"><br>
	<% web_get_stat(); %>
		<script>
			checktop(<% write(lang); %>);
		</script>
		<form name="webForm" method="post" action="/goform/setRingCfg">
			<input type="hidden" name="idx" id="idx">
			<input type="hidden" name="ltime" value=<% write(lltime); %> >
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<% var errorcode, l2pro, ringswitch,ringcfg; getringCfg(); %>
				<input type="hidden" name="ring_cfg" id="ring_cfg" value="<% write(ringcfg); %>">
				<input type="hidden" name="portNum" id="portNum">
				<div class="formContain">
					<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0"
						cellspacing="0">
						<tr>
							<td>
								<table width="100%" border="0" align="left" cellpadding="0" cellspacing="0">
									<tr>
										<td valign="top">
											<table width="100%" border="0" align="center" cellpadding="0"
												cellspacing="0" class="cword09">
												<tr>
													<td>
														<table width="100%" align="center" border="0" cellspacing="0"
															cellpadding="0">
															<tr>
																<td height="30px">
																	<font size="5" color="#404040">
																		<div class="bot">环网信息</div>
																	</font>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td valign="top">
														<table width="100%" border="0" align="center" cellpadding="0"
															cellspacing="0">

															<tr>
																<td valign="top">

																	<table width="100%" border="0" align="center"
																		cellpadding=0 cellspacing=0 class="tablebord"
																		id="table5">
																		<TR align="center" height=22>
																			<td class="all_tables_list" align="center">
																				端口
																			</td>
																			<td class="all_tables_list">状态</td>
																			<td class="all_tables_list">模式</td>
																			<td class="all_tables_list">转发状态
																			</td>

																		</TR>
																		<script>
																						<% RingPortShow(); %>
																		</script>
																	</table>
																</td>
															</tr>
															<tr>
																<td align="center" height="35" style="padding: 5px;">
																	<script>writebutton(1,<% write(lang); %>, "刷  新", "button", "button", "Refresh", "refreshpage()");</script>
																</td>
															</tr>

													</td>
												</tr>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
					</td>
					</tr>
					</table>
					</table>
				</div>

				<div class="formContain" style="margin-top: 15px;">
					<div class="">
						<font size="5" color="#404040">
							<div class="bot">环网配置</div>
						</font>
					</div>


					<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table1"
						class="tablebord">
						<tr height="25">
							<td align="left" width="22%" class="crons">
								&nbsp;
								<script>writemsg(<% write(lang); %>, "环网开关");</script>
								:
							</td>
							<td width="78%" align="left" class="crons">
								<input type="radio" id="ms1" name="ringswitch" value="enable">Enable
								<input type="radio" id="ms2" name="ringswitch" value="disable">Disable
							</td>
						</tr>
						<tr height="25">
							<td class="crons">&nbsp;
								<script>writemsg("ch", "优先级");</script>
							</td>
							<td class="crons" colspan="3">&nbsp;
								<select name="ringpri" id="ringpri" class="select1">
									<option value="0">0</option>
									<option value="1">1</option>
									<option value="2">2</option>
									<option value="3">3</option>
									<option value="4">4</option>
									<option value="5">5</option>
									<option value="6">6</option>
									<option value="7">7</option>
								</select>
							</td>
						</tr>
						<tr height="1">
							<td width="15%" height="30" class="crons" rowspan="3" >
								端口
							</td>
							<td align="left" class="crons" rowspan="3">
								&nbsp;<% AppendOptionring(); %>
											</td>
										</tr>
										<tr>
											<td colspan=" 3" align="left" class="crons">
								<div align="center">
									<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "修  改", "buttons_apply", "button", "", "messageCheck2()");</script>
								</div>
							</td>
						</tr>
					</table>



				</div>
		</form>
		<br>
		<br>
		<script>
			changebgcolor();
			changebgcolor5();
<% if (errorcode != "") { write_errorcode(errorcode); } %>
		</script>
</body>

</html>