(function ($) {
  // $(".main > .loading").fadeOut();

  /**
   * trim text, Remove spaces, wrap
   * @param text
   * @returns {string}
   */
  function trimText(text) {
    return text.replace(/[ \t\n\r]+/g, " ");
  }

  var tree = undefined;
  var lastNode = undefined;
  var mainNodeName = undefined;
var timeout= 15*60*1000
  /**
   * get the current node by <PERSON><PERSON><PERSON> (primary)
   * @returns {boolean} success?
   */
  function getCurrentNodeByUrl() {
    var ret = false;
    // var getUrlNode = function (href) {
    //   var linkPos = href.indexOf(";");
    //   if (linkPos == -1) {
    //     return "login";
    //   } else {
    //     linkPos = href.indexOf("/", linkPos);
    //     if (linkPos == -1) {
    //       return "overview";
    //     } else {
    //       var link = href.substr(linkPos);
    //       if (link == "/") return "overview";
    //       else return link;
    //     }
    //   }
    // };
    // var currentNode = getUrlNode(window.location.pathname);
    // if (currentNode == "login") {
    //   tree = ["Main", "Login"];
    //   return false;
    // } else if (currentNode == "overview") {
    //   // tree = ["基本配置", "设备信息"];
    //   lastNode = $(
    //     $($(".main > .main-left > .nav > .slide > .menu")[0])
    //       .next()
    //       .find("a")[0],
    //   ).parent();
    //   currentNode = "/admin/base_info/sysinfo";
    //   // return false;
    // }

    // $(".main > .main-left > .nav > .slide > .menu").each(function () {
    //   var ulNode = $(this);
    //   ulNode
    //     .next()
    //     .find("a")
    //     .each(function () {
    //       var that = $(this);
    //       var href = that.attr("href");
    //       if (currentNode.indexOf(getUrlNode(href)) != -1) {
    //         ulNode.click();
    //         lastNode = that.parent();
    //         tree = [
    //           trimText(ulNode.data("title")),
    //           trimText(that.data("title")),
    //         ];
    //         lastNode.addClass("active");
    //         ret = true;
    //         return true;
    //       }
    //     });
    // });
    return ret;
  }

  /**
   * menu click
   */
  $(".main > .main-left > .nav > .slide > .menu").click(function () {
    var ul = $(this).next(".slide-menu");
    var menu = $(this);
    if (!ul.is(":visible")) {
      if ($(".main").hasClass("reduce")) {
        return;
      }
      menu.addClass("active");
      ul.addClass("active");
      menu.find(".glyphicon-plus-sign").addClass("glyphicon-minus-sign");
      ul.stop(true).slideDown();
    } else {
      ul.slideUp(function () {
        menu.removeClass("active");
        ul.removeClass("active");
        menu.find(".glyphicon-plus-sign").removeClass("glyphicon-minus-sign");
      });
    }
  });

  /**
   * hook menu click and add the hash
   */
  $(".main > .main-left > .nav > .slide > .slide-menu > li > a").click(
    function () {
      if (lastNode != undefined) lastNode.removeClass("active");
      $(this).parent().addClass("active");
      lastNode = $(this).parent();
      $(".main > .loading").fadeIn();
   let lasttimeHandle=localStorage.getItem('lasttimeHandle')
    let now=Date.now()
   if(lasttimeHandle){
        if(lasttimeHandle<now){
          localStorage.clear('lasttimeHandle')
          		alert("您超时了，请重新登录!");	

              location.href = "login_ch2.asp";
          }else{
          	localStorage.setItem("lasttimeHandle",timeout+now );
          }
   }else{localStorage.setItem("lasttimeHandle",timeout+now );
   }
      return true;
    }
  );
$('.main > .main-left > .nav > .systemMenu > a').click(function(){
  if (lastNode != undefined) lastNode.removeClass("active");
  $(this).parent().addClass("active");
  lastNode = $(this).parent()
   let lasttimeHandle=localStorage.getItem('lasttimeHandle')
    let now=Date.now()
   if(lasttimeHandle){
        if(lasttimeHandle<now){
          localStorage.clear('lasttimeHandle')
          		alert("您超时了，请重新登录!");	

              location.href = "login_ch2.asp";
          }else{
          	localStorage.setItem("lasttimeHandle",timeout+now );
          }
   }else{localStorage.setItem("lasttimeHandle",timeout+now );
   }
       

  return true;
})
  /**
   * fix menu click
   */
  $(".main > .main-left > .nav > .slide > .slide-menu > li").click(function () {
    // if (lastNode != undefined) lastNode.removeClass("active");
    // $(this).addClass("active");
    // lastNode = $(this);
    // $(".main > .loading").fadeIn();

    return;
  });

  /**
   * get current node and open it
   */
  if (!getCurrentNodeByUrl()) {
    if (tree != undefined && tree[0] == "Status" && tree[1] == "Overview") {
      //overview
      lastNode.addClass("active");
      $($(".main > .main-left > .nav > .slide > .menu")[0]).click();
    }
  }
  if (tree != undefined) {
    mainNodeName = "node-" + tree[0] + "-" + tree[1];
    mainNodeName = mainNodeName.replace(/[ \t\n\r\/]+/g, "_").toLowerCase();
    $("body").addClass(mainNodeName);
  }
  $(".cbi-button-up").val("?");
  $(".cbi-button-down").val("?");

  /**
   * hook other "A Label" and add hash to it.
   */
  $("#maincontent > .container")
    .find("a")
    .each(function () {
      var that = $(this);
      var onclick = that.attr("onclick");
      if (onclick == undefined || onclick == "") {
        that.click(function () {
          var href = that.attr("href");
          if (href.indexOf("#") == -1) {
            $(".main > .loading").fadeIn();
            return true;
          }
        });
      }
    });

  /**
   * Sidebar reduce
   */
  $("#nav-toggle").click(function () {
    var ul = $(".main > .main-left > .nav > .slide > ul > li.active").parent();
    $(".main").toggleClass("reduce");
    if ($(".main").hasClass("reduce")) {
      $(".main > .main-left > .nav > .slide > ul.active").each(function () {
        $(this).slideUp();
        if (!$(this).children("li.active").length) {
          $(this).prev().find("small").removeClass("glyphicon-minus-sign");
        }
      });
    } else {
      ul.slideDown();
      ul.addClass("active");
    }
  });
  /**
   * Sidebar expand
   */
  var showSide = false;
  $(".showSide").click(function () {
    if (showSide) {
      $(".darkMask").stop(true).fadeOut();
      $(".main-left").stop(true).animate(
        {
          width: "0",
        },
        "fast"
      );
      showSide = false;
    } else {
      $(".darkMask").stop(true).fadeIn();
      $(".main-left").stop(true).animate(
        {
          width: "15rem",
        },
        "fast"
      );
      showSide = true;
    }
  });

  $(".darkMask").click(function () {
    if (showSide) {
      showSide = false;
      $(".darkMask").stop(true).fadeOut();
      $(".main-left").stop(true).animate(
        {
          width: "0",
        },
        "fast"
      );
    }
  });

  $(window).resize(function () {
    if ($(window).width() > 921) {
      $(".main-left").css("width", "");
      $(".darkMask").stop(true);
      $(".darkMask").css("display", "none");
      showSide = false;
    }
  });

  /**
   * fix legend position
   */
  $("legend").each(function () {
    var that = $(this);
    that.after("<span class='panel-title'>" + that.text() + "</span>");
  });

  $(".main-right").focus();
  $(".main-right").blur();

  if (mainNodeName != undefined) {
    // console.log(mainNodeName);
    $("#iBread ul").append(
      '<li><a href="#2">' +
        tree[0] +
        '</a></li><li><a href="#3" class="current">' +
        tree[1] +
        "</a></li>"
    );
    switch (mainNodeName) {
      case "node-status-system_log":
      case "node-status-kernel_log":
        $("#syslog").focus(function () {
          $("#syslog").blur();
          $(".main-right").focus();
          $(".main-right").blur();
        });
        break;
    }
  }
})(jQuery);
