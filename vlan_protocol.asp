<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_rtl.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"协议 VLAN设置");</script></title>

<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 

<script language="JavaScript" type="text/JavaScript">

function dofirst(){location.href="vlan_protocol.asp?page=1";}
function check()
{
	var vid=document.getElementById("mvid").value;
	var rid=document.getElementById("rid").value;
	var proto=document.getElementById("proto").value;
	var tf=document.vlan_proto;
	var encap=document.getElementById("encap").value;
	if(proto=="")
	{
	     alert(putmsg(<% write(lang); %>,"协议类型不能为空!"));
		return;
	}
	if(isINT(proto))
	{
	  if(proto<0||proto>65535)
	  {
	  	 alert(putmsg(<% write(lang); %>,"协议类型输入的数字不合法!"));
	    return;
	  }
	}else if(proto!="arp"&&proto!="atalkaarp"&&proto!="atalkddp"&&proto!="atmmulti"&&proto!="atmtransport"&& proto!="dec"&&proto!="deccustom"&&proto!="decdiagnostics"&&proto!="decdnadumpload"&&proto!="decdnaremoteconsole"&&proto!="decdnarouting"&&proto!="declat"&&proto!="decsyscomm"&&proto!="g8bpqx25"&&proto!="ieeeaddrtrans"&&proto!="ieeepup"&&proto!="ip"&&proto!="ipv6"&&proto!="ipx"&&proto!="pppdiscovery"&&proto!="pppsession"&&proto!="rarp"&&proto!="x25"&&proto!="xeroxaddrtrans"&&proto!="xeroxpup")
	{
	     alert(putmsg(<% write(lang); %>,"协议类型输入不合法!"));
	    return;
	}
	if(encap=="")
	{
		alert(putmsg(<% write(lang); %>,"ENCAP不能为空!"));
		return;
	}
	if((rid<=2099) && (rid>=2000))
	{
     if((vid<=4094) && (vid>1))
	 {
	 	tf.submit();
     }
	 else
	 {
	   	alert(putmsg(<% write(lang); %>,"VID的范围必须在2-4094之间"));
	 }
	}
	else
	{
	   alert(putmsg(<% write(lang); %>,"协议vlan的配置范围为2000-2099"));
	}
}

function checkdel()
{
	var tf=document.vlan_proto;
	var rid=document.getElementById("rid").value;
	var vid=document.getElementById("mvid").value;	
	var del = document.getElementById("del");

	if(rid=="")
	{
	   alert(putmsg(<% write(lang); %>,"请选择Rule ID"));
	   return;
	}
	if((rid<=2099) && (rid>=2000))
	{	
	    del.value = "1";
	    tf.submit();
	}
	else
	{
	   alert(putmsg(<% write(lang); %>,"协议vlan的配置范围为2000-2099"));
	}	
}  

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var vid = document.getElementById("rid");
	var vlan_enable = document.getElementById("proto");
	var encap = document.getElementById("encap");
	var vlan_name=document.getElementById("mvid");
	var p = obj.value;

	if(obj.checked)
	{
		vid.value=trobj.cells[1].innerHTML;
		vlan_enable.value=trobj.cells[2].innerHTML;
		encap.value=trobj.cells[3].innerHTML;
		vlan_name.value=trobj.cells[4].innerHTML;		
	}
}

function p(vid,macaddr,encap,vlanid)
{
    var narr=5;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_proto_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+vid);	

    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	            
  	
    
    tbtr.cells[0].abbr = 0;
    tbtr.cells[1].abbr = vid;
	
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+vid+"\" onclick=\"addToPortRange(this)\"/>";
    tbtr.cells[1].innerHTML = vid;
    tbtr.cells[2].innerHTML = macaddr;
    tbtr.cells[3].innerHTML = encap;
    tbtr.cells[4].innerHTML = vlanid;	
}

function checkData()
{
	var tf=document.vlan_proto;
	tf.action = "/goform/saveComm?name=vlan_protocol";
	tf.submit();
}

function refreshpage()
{
  location.href='vlan_protocol.asp?ltime='+<% write(lltime); %>;
}

function getPage(page)
{
   location.href="vlan_protocol.asp?page="+page+"&ltime="+<% write(lltime); %>;
}


function showHelpinfo()
{
   showHelp('vlan_protocol',<% write(lang); %>);
}

</script>
</head>

<body  >
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="vlan_proto" method="POST" action="/goform/vlanProtoConfig">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="mac_vlan_config"  value="@mac_vlan_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="90%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
         <tr>
           <td colspan="2" align="left" class="cword09">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN设置");</script> <b><font color="#FF7F00">&gt;&gt;</font></b>&nbsp;<script>writemsg(<% write(lang); %>,"协议 VLAN设置");</script> </td>
         </tr>
        </table>
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%">
	     <tr height="25">
	       <th align="left" colspan="2">&nbsp;<script>writemsg(<% write(lang); %>,"协议VLAN");</script></td>
	     </tr>
	      <tr height="25">
	     	  <td class="crons">&nbsp;Rule ID</td>
	     	  <td class="crons">
	     	    &nbsp;<input type="text" id="rid" name="rid" class="input_board5" maxlength="4">&nbsp;(2000-2099)
	     	  </td>
	     </tr>			 
	     <tr height="25">
	     	  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"协议类型");</script></td>
	     	  <td class="crons">
	     	    &nbsp;<input type="text" id="proto" name="proto" class="input_board3" maxlength="25"/>&nbsp;<script>writemsg(<% write(lang); %>,"(例如：0-65535，arp,ip,ipv6等)");</script></td>
	     </tr>
	     <tr height="25">
	     	  <td class="crons">&nbsp;ENCAP</td>
	     	  <td class="crons">
	     	  	&nbsp;<select name="encap" id="encap" class="select1">
                    <option value="ethv2" selected>&nbsp;<script>writemsg(<% write(lang); %>,"ethv2");</script></option>
                    <option value="nosnapllc">&nbsp;<script>writemsg(<% write(lang); %>,"nosnapllc");</script></option>
                    <option value="snapllc">&nbsp;<script>writemsg(<% write(lang); %>,"snapllc");</script></option>
                  </select> 
	     	  </td>
	     </tr>		 
	      <tr height="25">
	     	  <td class="crons">&nbsp;VID</td>
	     	  <td class="crons">
	     	    &nbsp;<input type="text" id="mvid" name="mvid" class="input_board5" maxlength="4">&nbsp;(2-4094)
	     	  </td>
	     </tr>
	   	     <tr height="25">

	     	  <td colspan="2" align="middle" class="crons">
              <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","add","check()");</script>
    		    				&nbsp;
			  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","delete","checkdel()");</script>
	     	  </td>
	     </tr>
<tr><td colspan="2" height="8"></td></tr>
   <tr>
	    	<td colspan="2">
	    		<table id="table_proto_vlan" border="0" cellspacing="0" cellpadding="0" width="100%" class="table2">
	    		 	<tr align="center" height="25" class="crons">
	    		 		<th class="td2" width="15%"></th>
	    		 		<th class="td2" width="15%"><font color="#333333"><b>Rule ID</b></font></th>
	    		 		<th class="td2" width="25%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"协议类型");</script></b></font></th>
						<th class="td2" width="25%"><font color="#333333"><b>ENCAP</b></font></th>	
	    		 		<th class="td2" width="15%"><font color="#333333"><b>VID</b></font></th>
	    		 	</tr>
	    		 	<script><%  var errorcode; showVlanProtoByPage("protocol_vlan"); %></script>
	    		</table>
	    	</td>
   </tr>
   <tr>
	  	   <td colspan="2" align="center" height="35">
	  	   	  	<script>writebutton(1,<% write(lang); %>,"首  页","button","button","firstpage","getPage(1)");</script>
         	    <%showVlanProtoByPage("pagebutton");%>
	  		    &nbsp;
	  	       <%showVlanProtoByPage("pagenum");%>
	  	       <%showVlanProtoByPage("allpage");%>
	  		</td>
   </tr>
	    
     </table>
   </td></tr>
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>
<INPUT type="hidden" name="del" id= "del" value="0">
</form>   
<script>
changebgcolor();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</html>
