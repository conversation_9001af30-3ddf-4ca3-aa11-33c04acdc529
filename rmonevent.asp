
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_rtl.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"GMRP");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script language="JavaScript">
 
var portStaList=[<%rmonevent_viewSetShow();%>];

function delmac(i)
{

	var hid = document.formaa;
	
	hid.rmoneventviewName.value = portStaList[4*i];


	hid.action="/goform/rmonevent_viewSet";
	hid.submit();
	return 0;

}

function writeLines()
{
var j = 0;
for(var i=0;i<portStaList.length/4;i++)
{
document.write(" <tr  class='tables_all'>");



document.write("    <td class='inputsyslog1'>"+portStaList[j]+"</td>");

j++
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;

			document.write("    <td  class='inputsyslog1'>");

			document.write("      <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick='delmac("+i+")'  />");

		document.write("      </td>");




document.write("  </tr>");

}
}
 
 
 
/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true;  
			 addToPortRange(i);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRange(i);
			}
             
        }
    } 
    
}


function checkup()
{
	//var tf = document.vlan_port;
	//tf.action = "/goform/selportsecurity";
	//tf.submit();
}


function checking2()
{
 	//var port_range = document.getElementById("port_range").value;
 
  	//var checkbox_index = document.getElementsByName("checkbox_index");
	//var table_port = document.getElementById("table_port");
	var tf=document.vlan_port;
	//var i,j;


	 
			tf.submit();
 

	//if(port_txrate.length==0)port_txrate="-";
	////if(port_txburst.length==0)port_txburst="-";
	//if(port_rxrate.length==0)port_rxrate="-";	    
	//if(port_rxburst.length==0)port_rxburst="-";	
	 

}


function AddOption(portname){

	var selectObject = document.getElementById("port_range");
	var y=document.createElement('option');
  	y.text=portname;
	y.value = portname;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  	catch(ex)
    {
   	selectObject.add(y); // IE only
  	}
}


function checkSub()
{
/*
	if(!tdOIDCheck(document.getElementById("rmoneventsubTree").value))
	{
		alert("OID 错误");
		return;
	}
	if(document.getElementById("rmoneventsubTree").value.length > 128 ||document.getElementById("rmoneventsubTree").value.length == 0)
	{
		alert("OID 不能为空，长度也不能超过128");
		return;
	}
	if(!checkname(document.getElementById("rmoneventviewName"),"视图名为空","视图名错误","视图名错误",1))
	//if(!tdCheckASCIICode(document.getElementById("rmoneventviewName").value))
	{
		//alert("View Name 错误");
		return;
	}
	
	if(document.getElementById("rmoneventviewName").value.length > 32 || document.getElementById("rmoneventviewName").value.length == 0)
	{
		alert("视图名不能为空，长度也不能超过32");
		return;
	}
*/
	document.vlan_port.submit();
}

function refreshpage()
{

  location.href='rmonevent.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('vlan_port',<% write(lang); %>);
}

</script>
</head>

<body  ><br>

<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>

<form  name="vlan_port" method="post" action="/goform/rmonevent_viewSet">

<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
<tr>
    <td  class=Tablelist id=tabs name=tabs>RMON事件</td>
    <td   class="tablenew" id=tabs name=tabs><div align="right">
      <input type="hidden" name="rmoneventDelViewID" id="rmoneventDelViewID"/>
      <input type="hidden" name="rmoneventDelViewOid" id="rmoneventDelViewOid">
      <input name="b_submit2" class="buttons_apply" type="button" id="b_submit" value="提 交"  onclick="checking2()" >
        <input name="b_reset2" class="buttons_apply" type="button" onClick="javascript:window.location.reload() " id="b_reset2" value="取消">
    </div></td>
    
  </tr>
</table>
<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
  <TBODY>
    <TR height=22>
      <TD width="18%" valign="top"><div align="left">&nbsp;&nbsp;<span class="td25">事件</span></div></TD>
      <TD width="32%" ><span class="crons">
      <input type="text" name="rmoneventviewName" id="rmoneventviewName" size="20" maxlength="32">
&lt;1-16&gt;</span></TD>
      <TD width="18%" >&nbsp;&nbsp;描述</TD>
      <TD width="32%" ><span class="crons">
        <input type="text" name="rmoneventdescription" id="rmoneventdescription" size="20" maxlength="128">
      </span></TD>
    </TR>
    <TR height=22>
      <TD valign="top">&nbsp;&nbsp;<span class="td25">类型</span></TD>
      <TD ><span class="crons">
        <select name="rmoneventviewType" id="rmoneventviewType" size="1">
          <option value="log"  >LOG</option>
          <option value="trap"  >TRAP</option>
          <option value="all"  >ALL</option>
        </select>
      </span></TD>
      <TD >&nbsp;<span class="td25">&nbsp;&nbsp;团体名/用户名</span></TD>
      <TD ><span class="crons">
        <input type="text" name="rmoneventpubname" id="rmoneventpubname" size="20" maxlength="128">
      </span></TD>
    </TR>
</TABLE>



<br>
<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
  <tr>
    <td width="51%"  class=Tablelist id=tabs name=tabs>RMON事件表</td>
    <td width="49%"   class="tablenew" id=tabs name=tabs><div align="right"></div></td>
  </tr>
</table>
<table width="98%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table_port_vlan"  >
    <TR align="center" height=22>
      <TD width="17%"   nowrap class="all_tables_list">事件</TD>
      <TD width="17%"   nowrap class="all_tables_list"><span class="partition">描述</span></TD>
      <TD width="17%"   nowrap class="all_tables_list"><span class="partition">类型</span></TD>
      <TD width="17%"   nowrap class="all_tables_list"><span class="partition">团体名/用户名</span></TD>
      <TD width="32%"   nowrap class="all_tables_list"><span class="partition">删除</span></TD>
    </TR>
  <script language="javascript">
  writeLines();
  </script>
  </table>
</form>
<form name="formaa" method="POST" action="">
<input name="rmoneventviewName" type="hidden" class="input_x"  >
<input name="del_flag" type="hidden" class="input_x"  value="1">
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

</form> 
<script>
changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</html>
