<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
    <% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
    <% var KEY; getDot1xRadiusServerKey();%>
    <% var RETRY, DEADTIME, TIMEOUT; getDot1xRadiusRetry(); getDot1xRadiusDeadtime(); getDot1xRadiusTimeout();%>
        <meta http-equiv="Content-Type" content="text/html; charset=utf8">
        <script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
        <script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
        <script language="javascript" type="text/javascript" src="js/alpinejs.min.js" defer></script>
        <link href="css/display_web.css" rel="stylesheet" type="text/css" />
        <title>
            <script>writemsg(<% write(lang); %>, "802.1X");</script>
        </title>
        <script language="JavaScript" type="text/JavaScript">

function jsSetDot1xGlobalStatus() {
    var hid=document.dot1xConfig;
	let ms=document.getElementsByName("dot1xStatus");
    console.log(ms);
    hid.submit();
    return true;
}

function jsSetDot1xPortConfig(dot1xPortObj) {
    var tf = document.setDot1xPortConfig;
    tf.dot1xPortParam.value = JSON.stringify(dot1xPortObj);
    tf.submit();
    return true;
}

function jsSetDot1xPortEnable(row) {
    let dot1xPortObj = {
        pageName: 'dot1x.asp',
    }
    dot1xPortObj.dot1xPortConfig = [{
        portName: row.portName,
        configKey: 'portEnabled',
        configValue: row.portEnabled
    }]
    jsSetDot1xPortConfig(dot1xPortObj);
    return true;
}

function jsSetDot1xPortReAuthEnable(row) {
    let dot1xPortObj = {
        pageName: 'dot1x.asp',
    }
    dot1xPortObj.dot1xPortConfig = [{
        portName: row.portName,
        configKey: 'portDot1xReAuthEnabled',
        configValue: row.portDot1xReAuthEnabled
    }]
    jsSetDot1xPortConfig(dot1xPortObj);
    return true;
}

function jsSetDot1xPortReAuthPeriod(row) {
    let dot1xPortObj = {
        pageName: 'dot1x.asp',
    }
    dot1xPortObj.dot1xPortConfig = [{
        portName: row.portName,
        configKey: 'portDot1xReAuthPeriod',
        configValue: row.portDot1xReAuthPeriod
    }]
    jsSetDot1xPortConfig(dot1xPortObj);
    return true;
}

function jsGetUrlParamActive() {
    let urlSearch = window.location.search
    const search = new URLSearchParams(urlSearch)
    const params = Object.fromEntries(search.entries())
    try {
        if (params.active) return params.active
    } catch { }
    return 'tab1'
}

function display(){
    var hid1 = document.dot1xServerConfig;
    var hid2 = document.dot1xTimeConfig;

    allvalue = "<% write(KEY); %>";
    hid1.serverkey.value = allvalue;

    allvalue = "<% write(RETRY); %>";
    hid2.retransmit.value = allvalue;

    allvalue = "<% write(DEADTIME); %>";
    hid2.deadtime.value = allvalue;

    allvalue = "<% write(TIMEOUT); %>";
    hid2.timeout.value = allvalue;
}

function tdIpCheck(textValue) {
    re1 = /(\d+)(\W)(\d+)(\W)(\d+)(\W)(\d+)/
    Check = textValue.search(re1);
    if (Check == -1) {
        return false;
    }
    else {
        ipSplit = textValue.split('.');
        if (ipSplit.length != 4) {
            return false;
        }

        for (i = 0; i < ipSplit.length; i++) {
            if (isNaN(ipSplit[i])) return false;
            if (ipSplit[i].length > 1 && ipSplit[i].charAt(0) == "0") return false;
        }

        for (i = 0; i < ipSplit.length; i++) {
            if (ipSplit[i] > 255) {
                return false;
            }
            if (ipSplit[i] < 0) {
                return false;
            }
        }
        if ((ipSplit[0] == 255) && (ipSplit[1] == 255) && (ipSplit[2] == 255) && (ipSplit[3] == 255)) {
            return false;
        }

        if ((ipSplit[0] == 0) || (ipSplit[0] == 127) || (ipSplit[3] == 0) || (ipSplit[3] == 255)) {
            return false;
        }

        if (ipSplit[0] >= 224) {
            return false;
        }
        return true;
    }
}

function messageCheck0() {
    var hid = document.dot1xServerConfig;
    var dplen;

    if(hid.serverip.value==''){
        alert("IP不能为空")
        return false
    }
    if ((tdIpCheck(hid.serverip.value) == false) && (hid.serverip.value != "")) {
        alert("IP输入非法！格式：A.B.C.D");
        return false;
    }

    hid.action = "/goform/setDot1xRadiusServer";
    alert("radiusIp配置成功!");
    hid.submit();

    return true;
}

function messageCheck1() {
    var hid = document.dot1xServerConfig;
    var dplen;

    if(hid.serverkey.value==''){
        alert("KEY不能为空")
        return false
    }

    hid.action = "/goform/setDot1xRadiusKey";
    alert("radiusKey配置成功!");
    hid.submit();

    return true;
}

function messageCheck2() {
    var hid = document.dot1xTimeConfig;
    var dplen;

    if(hid.retransmit.value==''){
        alert("重传次数不能为空")
        return false
    }
    if(hid.deadtime.value==''){
        alert("静默时间不能为空")
        return false
    }
    if(hid.timeout.value==''){
        alert("超时时间不能为空")
        return false
    }
    if(hid.retransmit.value < 1 || hid.retransmit.value > 100){
        alert("重传次数的范围必须在1-100之间")
        return false
    }
    if(hid.deadtime.value < 0 || hid.deadtime.value > 1440){
        alert("静默时间的范围必须在0-1440之间")
        return false
    }
    if(hid.timeout.value < 1 || hid.timeout.value > 5){
        alert("超时时间的范围必须在1-5之间")
        return false
    }

    alert("Radius定时器配置成功!");
    hid.submit();

    return true;
}

function jsDelDot1xRadiusServer(row) {
	var tf = document.dot1xServerConfig;
	var forfil_ip = document.getElementById("serverip");
	
	forfil_ip.value = row.ipaddr;
	
	tf.action="/goform/delDot1xRadiusServer";
	tf.submit();
	return 0;
}

        </script>
</head>

<body x-data="{active:'tab1'}" x-init="active=jsGetUrlParamActive()" onload=display()></body><br>
    <% web_get_stat(); %>
    <% var authmode; checkCurMode(); %>
    <!-- page title -->
    <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
        <tr>
            <td height="30px">
                <font size="5" color="#404040">
                    <div class="bot">802.1x</div>
                </font>
            </td>
        </tr>
    </table>

    <!-- 802.1x menu -->
    <div>
        <ul class="tabmenu">
            <li id="tab1" :class="active=='tab1'?'tab':'tab-disable'">
                <a herf="#" x-on:click="active='tab1'">802.1x 全局配置</a>
            </li>
            <li id="tab2" :class="active=='tab2'?'tab':'tab-disable'">
                <a herf="#" x-on:click="active='tab2'">802.1x 端口配置</a>
            </li>
        </ul>   
    </div>

    <!-- 802.1x 全局配置 -->
    <form style="margin-top: 20px;" x-show="active=='tab1'" name="dot1xConfig" method="POST" action="/goform/setDot1xGlobalStatus">
        <input type="hidden" name="ltime" value=<% write(lltime); %>>
        <input type="hidden" name="lastts" value=<% write(serverts); %>>
        <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

        <!-- 802.1x 全局使能 -->
        <div class="formContain">
            <script><% var errorcode, l2pro, dot1xStatus; getDot1xGlobalStatus(); %></script>
            <div style="display: flex;">
                <div width="20%" align="left" class="all_tables3 all_tables" style="border: none;text-align: left;">
                    <script>writemsg(<% write(lang); %>, "802.1x 全局使能:");</script>
                </div>
                <div width="40%" align="left" class="crons" colspan="2">
                    <select name="dot1xStatus" id="dot1xStatus" @change="jsSetDot1xGlobalStatus()" style="width: 200px;">
                        <option value="disabled" <% if (dot1xStatus=="disabled") write("selected"); %>>
                            关闭
                        </option>
                        <option value="basePort" <% if (dot1xStatus=="basePort") write("selected"); %>>
                            基于端口认证
                        </option>
                        <option value="baseMAC" <% if (dot1xStatus=="baseMAC") write("selected"); %>>
                            基于MAC认证
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </form>

    <!-- radius 服务器 -->
    <form style="margin-top: 20px;" x-show="active==='tab1'" name="dot1xServerConfig" method="post" action="">
        <input type="hidden" name="ltime" value=<% write(lltime); %>>
        <input type="hidden" name="lastts" value=<% write(serverts); %>>
        <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
        <div class="formContain">
            <script>
                retValue = <% var responseRadiusJsonStr; getDot1xRadiusServer(); %>
                responseRadiusStr = <% write(responseRadiusJsonStr); %>
                // console.log(responseRadiusStr);
                const radiusServer = {
                    headers: ['序号', 'ip地址'],
                    server: [
                        {
                            index: null,
                            ipaddr: null,
                        },
                    ],
                selectedValues: {}
                };
                radiusServer.server = [...responseRadiusStr.radiusServer];
                console.log(radiusServer)
            </script>
            <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
                <tr>
                    <td height="30px">
                        <font size="5" color="#404040">
                            <div class="bot">IP 地址列表</div>
                        </font>
                    </td>
                </tr>
            </table>
            <table id="SedTab_tbl" name="SedTab_tbl" border="0" cellspacing="0" cellpadding="0" width="100%"  class="tablebord">
                <tr>
                    <th width="10%" height="25" class="all_tables">序号</th>
                    <th width="18%" height="25" class="all_tables">IP</th>
                    <th width="12%" height="25" class="all_tables">删除</th>
                </tr>
                <template x-for="(row,rowIndex) in radiusServer.server">
                    <tr>
                        <td width="10%" height="25" style="text-align: center; font-weight: normal" class="all_tables" x-text="row.index"></td>
                        <td width="18%" height="25" style="text-align: center; font-weight: normal" class="all_tables" x-text="row.ipaddr"></td>
                        <td width="18%" height="25" style="text-align: center; font-weight: normal" class="all_tables" > <input type="button" name='button3' id='button3' class='botton_under_line' value="删除" x-on:click="jsDelDot1xRadiusServer(row)"> </td>
                    </tr>
                </template>
            </table>
            <br />
            <br />
            <script><% var errorcode, serverip, serverkey;%></script>
            <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
                <tr>
                    <td height="30px">
                        <font size="5" color="#404040">
                            <div class="bot">Radius 服务器配置</div>
                        </font>
                    </td>
                </tr>
            </table>
            <table width="100%" align="center" cellpadding=0 cellspacing=0 id="mainTb" class="tablebord">
                <tr>
                    <td width="42%" class="all_tables4 all_tables3" style="text-align: right; border-style:solid none solid none;">
                        <script>writemsg(<% write(lang); %>, "IP 地址: ");</script>&nbsp;&nbsp;&nbsp;
                    </td>
                    
                    <td class="all_tables4" width="50%" style="text-align: left; border-style:solid none solid none;">
                        <input type="text" name="serverip" id="serverip" value="">
                    </td>
                </tr>

                <tr>
                    <td align="center" class="all_tables4" width="30%" colspan="3" style="border-style:solid none solid none;">
                        <script>writebutton(<% write(authmode); %>, <% write(lang); %>, "应  用", "buttons_apply", "button", "modify1", "messageCheck0()");</script>
                    </td>
                </tr>

                <tr>
                    <td width="42%" class="all_tables4 all_tables3" style="text-align: right; border-style:solid none solid none;">
                        <script>writemsg(<% write(lang); %>, "Shared Key: ");</script>&nbsp;&nbsp;&nbsp;
                    </td>
                    
                    <td class="all_tables4" width="50%" style="text-align: left; border-style:solid none solid none;">
                        <input type="text" name="serverkey" id="serverkey" value="">
                    </td>
                </tr>
                
                <tr>
                    <td align="center" class="all_tables4" width="30%" colspan="3" style="border-style:solid none solid none;">
                        <script>writebutton(<% write(authmode); %>, <% write(lang); %>, "应  用", "buttons_apply", "button", "modify1", "messageCheck1()");</script>
                    </td>
                </tr>
            </table>
        </div>
    </form>

    <!-- 802.1x 服务 -->
    <form style="margin-top: 20px;" x-show="active==='tab1'" name="dot1xTimeConfig" method="post" action="/goform/setDot1xRadiusTime">
        <input type="hidden" name="ltime" value=<% write(lltime); %>>
        <input type="hidden" name="lastts" value=<% write(serverts); %>>
        <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
        <div class="formContain">
            <script><% var errorcode, retransmit, deadtime, timeout; %></script>
            <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
                <tr>
                    <td height="30px">
                        <font size="5" color="#404040">
                            <div class="bot">Radius 定时器配置</div>
                        </font>
                    </td>
                </tr>
            </table>
            <table width="100%" align="center" cellpadding=0 cellspacing=0 id="mainTb" class="tablebord">
                <tr>
                    <td width="42%" class="all_tables4 all_tables3" style="text-align: right; border-style:solid none solid none;">
                        <script>writemsg(<% write(lang); %>, "重传次数: ");</script>&nbsp;&nbsp;&nbsp;
                    </td>
                    
                    <td class="all_tables4" width="50%" style="text-align: left; border-style:solid none solid none;">
                        <input type="text" name="retransmit" id="retransmit" value="">&nbsp;&nbsp;(1 - 100)
                    </td>
                </tr>
                <tr>
                    <td width="42%" class="all_tables4 all_tables3" style="text-align: right; border-style:solid none solid none;">
                        <script>writemsg(<% write(lang); %>, "静默时间: ");</script>&nbsp;&nbsp;&nbsp;
                    </td>
                    
                    <td class="all_tables4" width="50%" style="text-align: left; border-style:solid none solid none;">
                        <input type="text" name="deadtime" id="deadtime" value="">&nbsp;&nbsp;(单位：分； 0 - 1440)
                    </td>
                </tr>
                <tr>
                    <td width="42%" class="all_tables4 all_tables3" style="text-align: right; border-style:solid none solid none;">
                        <script>writemsg(<% write(lang); %>, "超时时间: ");</script>&nbsp;&nbsp;&nbsp;
                    </td>
                    
                    <td class="all_tables4" width="50%" style="text-align: left; border-style:solid none solid none;">
                        <input type="text" name="timeout" id="timeout" value="">&nbsp;&nbsp;(单位：秒； 1 - 5)
                    </td>
                </tr>
                <tr>
                    <td align="center" class="all_tables4" width="30%" colspan="3" style="border-style:solid none solid none;">
                        <script>writebutton(<% write(authmode); %>, <% write(lang); %>, "应  用", "buttons_apply", "button", "modify2", "messageCheck2()");</script>
                    </td>
                </tr>
            </table>
        </div>
    </form>


    <!-- 802.1x 端口配置 -->
    <form style="margin-top: 20px;" x-show="active=='tab2'" name="setDot1xPortConfig" method="POST" action="/goform/jw_set_dot1xPortConfig">
        <input type="hidden" name="ltime" value=<% write(lltime); %>>
        <input type="hidden" name="lastts" id="lastts" value=<% write(serverts); %>>
        <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
        <input type="hidden" name="dot1xPortParam" id="dot1xPortParam" />
        
        <script>
        retValue = <% var responseJsonStr; jw_get_dot1xPortConfig(); %>
        responseStr = <% write(responseJsonStr); %>
        const appData = {
            headers: ['端口', '端口启用', '端口重认证', '重认证时间(s)', '端口鉴权状态'],
            dot1xPortConfig: [
                {
                    portName: null,
                    portDot1xEnabled: null,
                    portDot1xReAuthEnabled: null,
                    portDot1xReAuthPeriod: null,
                    portDot1xStatus: null
                },
            ],
        selectedValues: {}
        };
        appData.dot1xPortConfig = [...responseStr.dot1xPortConfig]
        </script>

        <div class="formContain">
            <div x-data="appData">
                <table class="tab1 tablebord" width="96%" border="0" align="center" cellpadding="0" cellspacing="0">
                    <thead>
                        <tr>
                          <template x-for="(header, index) in headers" :key="index + 1">
                            <th class="all_tables" x-text="header"></th>
                          </template>
                        </tr>
                    </thead>
                    <tbody>
                        <template x-for="(row, rowIndex) in appData.dot1xPortConfig" :key="rowIndex">
                            <tr style="text-align: center">
                                <td class="all_tables" x-text="row.portName" style="width: 10%;"></td>
                                <td class="all_tables" style="width: 20%;">
                                    <select x-model="row.portEnabled"  @change="jsSetDot1xPortEnable(row)"  <% if (dot1xStatus=="disabled") write(disabled="disabled"); %>>
                                      <option value="disabled">
                                          关闭
                                      </option>
                                      <option value="enabled">
                                          开启
                                      </option>
                                    </select>
                                  </td>
                                <td class="all_tables" style="width: 20%;">
                                  <select x-model="row.portDot1xReAuthEnabled"  @change="jsSetDot1xPortReAuthEnable(row)"  <% if (dot1xStatus!="basePort") write(disabled="disabled"); %>>
                                    <option value="disabled">
                                        关闭
                                    </option>
                                    <option value="enabled">
                                        开启
                                    </option>
                                  </select>
                                </td>
                                <td class="all_tables">
                                    <input type="number"  max="4294967295" min="1" x-model="row.portDot1xReAuthPeriod" @change="jsSetDot1xPortReAuthPeriod(row)" <% if (dot1xStatus!="basePort") write(disabled="disabled"); %>/>
                                </td>
                                <td class="all_tables" x-text="row.portStatus" style="width: 30%;"></td>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
    </form>
</body>
</html>