<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<style   type= "text/css "> 
.tableiframe {
 border-right-width: 1px;
 border-right-style: solid;
 border-right-color: #777777;
 color: #000000;
 font-family:"宋体";
 font-size:12px;
 line-height:12pt
} 
</style>
<title>Frame</title>
<script> 
function showHint()
{
    xmlHttp=GetXmlHttpObject();
    
    if (xmlHttp==null)
    {
        alert ("Browser does not support HTTP Request");
        return;
    } 
    var url="testtxt.asp";
    xmlHttp.onreadystatechange = updatePage;
    xmlHttp.open("GET",url,true);
    xmlHttp.setRequestHeader("If-Modified-Since","0");
    xmlHttp.send();
 }
 
 function GetXmlHttpObject()
 { 
     var objXMLHttp=null;
     if (window.XMLHttpRequest)
     {
        objXMLHttp=new XMLHttpRequest();
     }
     else if (window.ActiveXObject)
     {
        objXMLHttp = new ActiveXObject("Microsoft.XMLHTTP");
      }
      return objXMLHttp;
 }
 
 function updatePage()
 {   
 if (xmlHttp.readyState==4)
      { 
         document.getElementById("txtHint").innerHTML=xmlHttp.responseText; 
      }
 }
function init()
 {
 
 	showHint();
 	var browser=navigator.appName
	var b_version=navigator.appVersion
	var version=b_version.split(";");
	var trim_Version=version[1].replace(/[ ]/g,"");
	if(browser=="Microsoft Internet Explorer" && trim_Version=="MSIE6.0")
	{
	    setInterval("showHint();",10000);
	}
	else 
	{
    	setInterval("showHint();",1000);
	}

 }
</script>
</head>
<body  onload="init();" bgcolor="#3F3F3F">
<table id="mainTbl" width="100%" height="100%" border="0" align="top" cellpadding="0" cellspacing="0">
<tr><td align="left" valign="top">
	<table width="100%" border="0" align="top" cellpadding="0" cellspacing="0"  class="tableiframe">
	               <tr>
	               <td bgcolor="#3F3F3F" >

	                 <div id="txtHint"></div>
				</td>
   </tr>
  </table>

</td></tr>
</table>
</body>
</html>
