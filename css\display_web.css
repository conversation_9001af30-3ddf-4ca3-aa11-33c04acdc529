body {margin:0px; font-family : Verd<PERSON>, Arial, Helvetica; background-color:#ffffff}
.link {font-family : Verdana, Arial, Helvetica;font-size : 12px;font-weight: bold; }

td {font-family : Verdana, Arial, Helvetica;font-size : 12px;color:#000000}


input {font-size: 13px; font-family: Verdana, Arial, Helvetica; padding: 5px 8px; border: 1px solid #ccc;}
input.button {height: 35px; width: 100px;font-size: 16px; font-family: Verdana, Arial, Helvetica;text-align: center; color: #fff; border: none; border-radius: 4px;background-color:#5bc0de;}

input.buttonlong {height: 24px; width: 68px;font-size: 12px; font-family: Verdana, Arial, Helvetica;background-color:#fff;text-align: center;}

select {border: 1px solid #ccc;
	border-radius: 4px; font-size: 13px; padding: 5px 8px;
	font-family: Verdana, Arial, Helvetica}

.inputsyslog{font-family : Verdana, Arial, Helvetica;font-size: 12px;border:0px solid white;background-color: transparent;color:#000000}
.inputsyslog1{font-family : Verdana, Arial, Helvetica;font-size: 12px;color:#000000;background-color: transparent;text-align:left;}


tr.title td{font-family: Verdana, Arial, Helvetica;
	background-color: #A5C5C3;
	font-size: 12px;
	font-weight: bold;
	text-align: left;
}

tr.tables {font-family: Verdana, Arial, Helvetica;

}
td.tables {background-color:#fff;font-family: Verdana, Arial, Helvetica;font-size: 16px;font-weight: normal;
line-height: 34px;text-align: center;border-top-style: solid; border-top-width: 1px; border-top-color: #ddd;
padding: 5px;
}

.tablebord {
	BORDER: #eee 1px solid;
}

tr.tables_all td{background-color:#fff;font-family: Verdana, Arial, Helvetica;font-size: 16px;font-weight: normal;
height: 22px;text-align: center;
padding: 5px; border-top: 1px solid #ddd;
}

.all_tables_list  {
	text-align: center; border-top-style: solid; border-top-width: 1px; border-top-color: #ddd;
	font-size: 16px; padding: 5px;
}

body.main {background-color: #CCCDE1; margin:10px}
.left {
	text-align: left;
	font-size: 12px;
	font-weight: normal;
}
.right {
	text-align: right;
	font-size: 12px;
	font-weight: normal;
}
.center {
	text-align: center;BORDER-RIGHT: medium none; BORDER-TOP: #ffffff 1px solid; PADDING-LEFT: 2px; FONT-WEIGHT: bold; FONT-SIZE: 12px; BORDER-LEFT: #ffffff 1px solid; COLOR: #000000; BORDER-BOTTOM: #ffffff 1px solid; FONT-FAMILY: Verdana, Arial, Helvetica, sans-serif;BACKGROUND-COLOR: #bdd9ee
}
a:link {
	color: #000000;
	TEXT-DECORATION: none;
}
.tobemodify{
	color: #FF0000;
}
a:visited {
	color: #000099;
	TEXT-DECORATION: none;
}
a:hover {
	color: #FF0000;
	TEXT-DECORATION: none;
}
input.buttons_apply {  height: 35px; width: 100px;font-size: 16px; font-family: Verdana, Arial, Helvetica;background-color:#f0ad4e;text-align: center; color: #fff; border: none; border-radius: 4px;}
input.buttons_del { VERTICAL-ALIGN: middle; height: 35px; width: 100px;font-size: 16px; font-family: Verdana, Arial, Helvetica;background-color:#d9534f;text-align: center; color: #fff; border: none; border-radius: 4px;}

input.buttons_add { VERTICAL-ALIGN: middle; height: 35px; width: 100px;font-size: 16px; font-family: Verdana, Arial, Helvetica;background-color:#337AB7;text-align: center; color: #fff; border: none; border-radius: 4px;}
TD.Tablelist {
	FONT-WEIGHT: normal;	color: #404040; FONT-SIZE: 24px; VERTICAL-ALIGN: center;  FONT-FAMILY: Verdana, Arial, Helvetica;text-align: left; padding-bottom: 22px;
}

.all_tables  {
	border-top-style: solid; border-top-width: 1px; 		border-top-color: #ddd; font-size: 16px; padding: 5px;
}

.menuTitle {
	FONT-WEIGHT: bold; FONT-SIZE: 12px; COLOR: #164674; FONT-FAMILY: Verdana, Arial, Helvetica, sans-serif; HEIGHT: 22px
}

.mainHeader {
	MARGIN-TOP: 0px; PADDING-LEFT: 3px; FONT-WEIGHT: bold; FONT-SIZE: 14px; VERTICAL-ALIGN: bottom; COLOR: #be6301; BORDER-TOP-STYLE: none;  FONT-FAMILY: Geneva, Arial, Helvetica, sans-serif; BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; HEIGHT: 22px; TEXT-ALIGN: left
}
.botton_under_line{height: 35px; width: 100px;font-size: 16px; font-family: Verdana, Arial, Helvetica;background-color:#d9534f;text-align: center; color: #fff; border: none; border-radius: 4px;}
.htblod{font-size: 15px;font-weight: bold;color: #000000;text-decoration: none;line-height: 25px;letter-spacing:3px;}
.htred{font-size: 15px;font-weight: bold;color: #ff0000;text-decoration: none;}

.disableText {
	 BORDER-BOTTOM: #fff 1px solid; BORDER-LEFT: #fff 1px solid; BORDER-RIGHT: #fff 1px solid; BORDER-TOP: #fff 1px solid; width:125px;
}
.disableTextStp {
	 BORDER-BOTTOM: #fff 1px solid; BORDER-LEFT: #fff 1px solid; BORDER-RIGHT: #fff 1px solid; BORDER-TOP: #fff 1px solid; width:425px;
}
.enableText {
	 BORDER-BOTTOM: #fff 1px; BORDER-LEFT: #fff 1px; BORDER-RIGHT: #fff 1px; BORDER-TOP: #fff 1px; width:78px;
}
.buttonTag { width:25px; BACKGROUND-COLOR: #fff; BORDER-BOTTOM: #183ead 1px solid; BORDER-LEFT: #183ead 1px solid; BORDER-RIGHT: #183ead 1px solid; BORDER-TOP: #183ead 1px solid; COLOR: #000000;}

.update_ok{font-size: 14px;font-weight: bold; text-decoration: none;}

.bot {
	margin-bottom: 22px;
}


.tablebord tr:nth-child(odd) {
	background-color: #f9f9f9;
}
.tablebord1 {
	BORDER: #eee 1px solid;
}
.tablebord1 tr:nth-child(even) {
	background-color: #f9f9f9;
}
.tablebord2 {
	margin-bottom: 20px;
}
.tablebord3 {
	BORDER: #eee 1px solid;
	border-top: 0;
	border-bottom: 0;
}
.tablebord4 {
	border: 1px solid #eee;
	border-top: 0;
}
tr.tables_all1 td{font-family: Verdana, Arial, Helvetica;font-size: 16px;font-weight: normal;
	height: 22px;text-align: center;
	padding: 5px; border-top: 1px solid #ddd;
	}

.formContain {
	border: 1px solid #e9e9e9;
	padding: 16px;
	box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .16), 0 0 2px 0 rgba(0, 0, 0, .12);
	/* margin-bottom: 32px; */
}

.all_tables1 {
	font-weight: bold;
	text-align: right;
}
.all_tables2 {
	padding: 5px 5px 5px 32px;
}
.all_tables3 {
	font-weight: bold;
	text-align: center;
}
.all_tables4  {
	border: 1px solid #ddd;
	border-top: 0;
	font-size: 16px; padding: 5px;
}
.all_tables5 {
	font-weight: bold;
	text-align: left;
}
.tableTd{
      font-size:16px;
     font-weight: bold;
      padding:5px;
}
.all_table3-right{
font-weight: bold;
	text-align: left;
}
.tables_allLLDP{
	background-color: #f9f9f9;
}
tr.tables_allLLDP td{background-color:#f9f9f9;font-family: Verdana, Arial, Helvetica;font-size: 16px;font-weight: normal;
	height: 22px;text-align: center;
	padding: 5px; border-top: 1px solid #ddd;
	}
input:focus,
textarea:focus {
	outline: 0;
	border-color: rgba(82, 168, 236, 0.8);
	-webkit-box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1),
		0 0 8px rgba(82, 168, 236, 0.6);
	-moz-box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1),
		0 0 8px rgba(82, 168, 236, 0.6);
	box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1),
		0 0 8px rgba(82, 168, 236, 0.6);
}
.partition {
	font-weight: bold;
}
.btn input.button {
	background-color:#f0ad4e;
}
.btn2 input.button {
	background-color:#d9534f;
}
.btn3 input.button {
	background-color:#337ab7;
}
.addBorder td{
	border: 1px solid #eee;
	border-top: 0px;
	padding: 5px;
}
.titLog {
	font-size: 24px;
	font-weight: normal;
	padding-bottom: 22px;
	border-bottom: 0;
}
.tit {
	font-size: 35px;
}
.bg1 {
	background-color: #f9f9f9;
}
.txtCent {
	text-align: center;
	font-weight: bold;
}
.applyBtn {
	height: 35px; width: 100px;font-size: 16px; font-family: Verdana, Arial, Helvetica;text-align: center; color: #fff; border: none; border-radius: 4px;background-color:#5bc0de;cursor: pointer;
}
.tabmenu {
	background-color: #D4D4D4;
	border-top: 1px solid #D4D4D4;
	border-left: 1px solid #D4D4D4;
	border-right: 1px solid #D4D4D4;
 padding:0
  }

  .tabmenu>li {
	display: inline-block;
	padding: 0.9em 0em;
	padding: 0.9rem 0rem;
  }

  .tabmenu>li[class~="tab"] {
	background-color: white;
  }

  .tabmenu>li>a {
	/* text-decoration: none; */
	color: #404040;
	padding: 0.9em 1.5em;
	padding: 0.9rem 1.5rem;
	text-decoration: none;
	cursor: pointer;
  }
  .inpBtn {height: 35px; width: 100px;font-size: 16px; font-family: Verdana, Arial, Helvetica;text-align: center; color: #fff; border: none; border-radius: 4px;background-color:#5bc0de;}
    .inpBtn1 {height: 35px; width: 100px;font-size: 16px; font-family: Verdana, Arial, Helvetica;text-align: center; color: #fff; border: none; border-radius: 4px;background-color:#337ab7;}
    .inpBtn2 {height: 35px; width: 100px;font-size: 16px; font-family: Verdana, Arial, Helvetica;text-align: center; color: #fff; border: none; border-radius: 4px;background-color:#d9534f;}
	.inpBtn3 {height: 35px; width: 100px;font-size: 16px; font-family: Verdana, Arial, Helvetica;text-align: center; color: #fff; border: none; border-radius: 4px;background-color:#f0ad4e;}