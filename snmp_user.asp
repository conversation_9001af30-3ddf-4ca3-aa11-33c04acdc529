<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<% var authmode; checkCurMode(); %>

<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"GMRP");</script></title>

<script language="JavaScript">
	var boardType = <% getSysCfg(); %>;
</script>

<script language="JavaScript">

function writebutton2(priv,lang,key,bclass,btype,bname,benable,bfunc)
{
	var outputstr ="";
	if((priv == 1)||(priv == 5))
	  outputstr="<input class="+bclass+" id='"+bname+"' "+benable+" name='"+bname+"' type='"+btype+"' value='"+putmsg(lang,key)+"' onClick='return "+bfunc+";'>";
	else
		outputstr="&nbsp;";
	document.write(outputstr);
}

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}
 
var portStaList=[<%snmp_userSetShow();%>];

function delmac(i)
{
	var hid = document.formaa;
	hid.snmpuserName.value = portStaList[5*i];
	hid.snmpusergroupName.value = portStaList[5*i+1];
	hid.action="/goform/snmp_userSet";
	hid.submit();
	return 0;
}

function changebgcolor(){
	var tab = document.all.table1;
	var len = tab.rows.length ;
	for (var i=0; i<len; i++)
	{
		var lencol = tab.rows[i].cells.length
		for (var j=0; j<lencol; j++)
		{
			if (j % 2 == 1){
				tab.rows[i].cells[j].bgColor = "efefef";
				tab.rows[i].cells[j].className = "all_tables" ;
			}
			else{
				tab.rows[i].cells[j].bgColor = "efefef";
				tab.rows[i].cells[j].className = "all_tables" ;
			}
		}
	}
}

function changebgcolor_name(value)
{
 	var tab = document.getElementById(value);
	var len = tab.rows.length ;
	for (var i=0; i<len; i++)
	{
		var lencol = tab.rows[i].cells.length
		for (var j=0; j<lencol; j++)
		{
			if (j % 2 == 1){
				tab.rows[i].cells[j].bgColor = "efefef";
				tab.rows[i].cells[j].className = "all_tables" ;
			}
			else{
				tab.rows[i].cells[j].bgColor = "efefef";
				tab.rows[i].cells[j].className = "all_tables" ;
			}
		}
	}
}

function writeLines()
{
	var j = 0;
	for(var i=0;i<portStaList.length/5;i++)
	{
		document.write(" <tr  class='tables_all'>");
		document.write("    <td class='inputsyslog1'>"+portStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>");

		if (<% write(authmode); %> == 1)
			document.write("      <input type='button' name='button"+i+"' id='button"+i+"' class='botton_under_line' value='删除' onclick='delmac("+i+")'  />");
		document.write("      </td>");
		document.write("  </tr>");
	}
}
 
/*select ALL*/
function selectToAll() 
{  
	var cf = document.forms[0];
	var objs = document.getElementsByName("checkbox_index"); 
	var i;
	if(cf.check_all.checked == true)
	{
		for(i = 0; i < objs.length; i++) 
		{    
			if(objs[i].disabled==false && objs[i].checked==false){
				objs[i].checked = true;  
				addToPortRange(i);
			}
		}
	}
	else
	{
		for(i = 0; i < objs.length; i++) 
		{    
			if(objs[i].checked==true){
				objs[i].checked = false;  
				addToPortRange(i);
			}
		}
	} 
}

function checkup()
{
	//var tf = document.vlan_port;
	//tf.action = "/goform/selportsecurity";
	//tf.submit();
}


function checking2()
{
 	//var port_range = document.getElementById("port_range").value;
  	//var checkbox_index = document.getElementsByName("checkbox_index");
	//var table_port = document.getElementById("table_port");
	var tf=document.vlan_port;
	//var i,j;

	var auth_pro = document.getElementById("snmpauthProtocol").value;
	var priv_pro = document.getElementById("snmpprivProtocol").value;
	var auth_passwd = document.getElementById("snmpauthPassword").value;
	var cf_auth_passwd = document.getElementById("cfsnmpauthPassword").value;
	var priv_passwd = document.getElementById("snmpprivPassword").value;
	var cf_priv_passwd = document.getElementById("cfsnmpprivPassword").value;

	if ((auth_pro != 0) && ((auth_passwd == "") || (auth_passwd != cf_auth_passwd)))
	{
		alert("Inconsistent authentication password!");
		return false;
	}

	if ((priv_pro != 0 ) && ((priv_passwd == "") || (priv_passwd != cf_priv_passwd)))
	{
		alert("Inconsistent encryption password!");
		return false;
	}

	//if (auth_pro == 0 && priv_pro != 0)
	//alert("");
	if (auth_pro != 0 || priv_pro == 0)
		tf.submit();
	//if(port_txrate.length==0)port_txrate="-";
	////if(port_txburst.length==0)port_txburst="-";
	//if(port_rxrate.length==0)port_rxrate="-";	    
	//if(port_rxburst.length==0)port_rxburst="-";	
}

function AddOption(portname){
	var selectObject = document.getElementById("port_range");
	var y=document.createElement('option');
	y.text=portname;
	y.value = portname;
	try
	{
		selectObject.add(y,null); // standards compliant
	}
	catch(ex)
	{
		selectObject.add(y); // IE only
	}
}

function checkname(field,err_blank,err_space,err_invalid,flag)
{
	if ((field.value.indexOf(" ") >=0)&&(flag == 1))
   {
		alert(err_space);
		field.focus();
		field.select();
		return false;
	}

	left_trim(field);
	right_trim(field);

	if ((field.value =="" | field.value == null)&&(flag == 1))
	{
		alert(err_blank);
		field.focus();
		field.select();
		return false;
	}

	if (checkOtherChar(field.value, err_invalid)==false)
	{
		field.focus();
		field.select();
		return false;
	}
	/*	
		if (isChinese(field.value)==true)
		{
			field.focus();
			field.select();
			return false;
		}
	*/
	return true;
}

function checkSub()
{
	if(!tdOIDCheck(document.getElementById("snmpsubTree").value))
	{
		alert("OID 错误");
		return;
	}
	if(document.getElementById("snmpsubTree").value.length > 128 ||document.getElementById("snmpsubTree").value.length == 0)
	{
		alert("OID 不能为空，长度也不能超过128");
		return;
	}
	if(!checkname(document.getElementById("snmpuserName"),"用户名为空","用户名错误","用户名错误",1))
	//if(!tdCheckASCIICode(document.getElementById("snmpuserName").value))
	{
		//alert("user Name 错误");
		return;
	}
	
	if(document.getElementById("snmpuserName").value.length > 32 || document.getElementById("snmpuserName").value.length == 0)
	{
		alert("用户名不能为空，长度也不能超过32");
		return;
	}

	document.vlan_port.submit();
}

function refreshpage()
{
	location.href='snmp_user.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
	showHelp('vlan_port',<% write(lang); %>);
}

</script>
</head>

<body  >
<br>
<script>
	checktop(<% write(lang); %>);
</script>

<form  name="vlan_port" method="post" action="/goform/snmp_userSet">
<input name="del_flag" type="hidden" class="input_x"  value="0">

<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
      <input type="hidden" name="snmpDeluserID" id="snmpDeluserID"/>
      <input type="hidden" name="snmpDeluserOid" id="snmpDeluserOid">

 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>用户</b></font></td></tr>
 </table>
 

<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
  <TBODY>
    <tr class="table_maintable">
      <td width="15%" class="td25">用户名:</td>
      <td colspan="5"  class="crons"><input type="text" name="snmpuserName"  id="snmpuserName"  size="20" maxlength="31">
        (范围：1~31个字符) </td>
    </tr>
    <tr class="table_maintable">
      <td width="15%" class="td25">群组:</td>
      <td colspan="5" class="crons"><input type="text" name="snmpusergroupName"  id="snmpusergroupName" size="20" maxlength="32">
        (范围：1~32个字符) </td>
    </tr>
    <tr class="table_maintable">
      <td width="15%" class="td25">SNMP V3 加密:</td>
      <td colspan="5" class="crons"><span style="display:none"><input type="checkbox" name="snmpencrypt"  id="snmpencrypt" onclick="changeType()" value="ON" / >
加密</span>&nbsp;</td>
    </tr>
    <tr class="table_maintable">
      <td width="15%" class="td25">鉴权协议:</td>
      <td width="15%" class="crons"><SELECT NAME="snmpauthProtocol" id="snmpauthProtocol" SIZE="1">
      	  <option value="0">None</option>
         <OPTION VALUE="1">MD5</OPTION>
          <OPTION VALUE="2">SHA</OPTION>
      </SELECT></td>
      <td width="15%" class="td25">&nbsp;&nbsp;鉴权密码:</td>
      <td width="20%" class="crons"><input type="password" name="snmpauthPassword" id="snmpauthPassword" size="20" maxlength="16" />
      </td>
      <td width="15%" class="td25">&nbsp;&nbsp;确认鉴权密码:</td>
      <td width="20%" class="crons"><input type="password" name="cfsnmpauthPassword" id="cfsnmpauthPassword" size="20" maxlength="16" />
      </td>
    </tr>
    <tr class="table_maintable">
      <td width="15%" class="td25">加密协议:</td>
      <td width="15%" class="crons"><select name="snmpprivProtocol" id="snmpprivProtocol" size="1" >
     	  <option value="0">None</option>
          <option value="1">AES</option>
          <option value="2">DES</option>
        </select>
      </td>
      <td width="15%" class="td25">&nbsp;&nbsp;加密密码:</td>
      <td width="20%" class="crons"><input type="password" name="snmpprivPassword" id="snmpprivPassword" size="20" maxlength="15" />
      </td>
      <td width="15%" class="td25">&nbsp;&nbsp;确认加密密码:</td>
      <td width="20%" class="crons"><input type="password" name="cfsnmpprivPassword" id="cfsnmpprivPassword" size="20" maxlength="15" />
      </td>
    </tr>

<tr class="table_maintable">
	<td colspan="6" class="crons" align="center">
		<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"提  交","button","button","modify","checking2()");</script>
	</td>
</tr>

</TABLE>


<br>
 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#ABD8EF" height="20px" ><font size="2" color="#FFFFFF"><b>SNMP用户表</b></font></td></tr>
 </table>

<table width="98%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table_port_vlan"  >
    <TR align="center" height=22>
      <TD width="13%"   nowrap class="all_tables_list"><span class="partition">用户名</span></TD>
      <TD width="16%"   nowrap class="all_tables_list"><span class="partition">群组</span></TD>
      <TD width="14%"   nowrap class="all_tables_list"><span class="partition">模式</span></TD>
      <TD width="15%"   nowrap class="all_tables_list"><span class="td25">认证协议</span></TD>
      <TD width="15%"   nowrap class="all_tables_list"><span class="td25">加密协议</span></TD>
      <TD width="27%"   nowrap class="all_tables_list"><span class="partition">删除</span></TD>
    </TR>
  <script language="javascript">
  writeLines();
  </script>
  </table>
</form>
<form name="formaa" method="POST" action="">
<input name="snmpusergroupName" type="hidden" class="input_x"   >
<input name="snmpuserName" type="hidden" class="input_x"  >
<input name="del_flag" type="hidden" class="input_x"  value="1">
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

</form> 
<script>
changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
 
</body>
</html>
