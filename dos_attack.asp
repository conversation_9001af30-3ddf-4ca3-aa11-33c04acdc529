<!DOCTYPE HTML PUBLIC"-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_rtl.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"lldp管理");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<style>
	/* 设置开关按钮的样式 */
	.switch {
		position: relative;
		display: inline-block;
		width: 60px;
		height: 34px;
	}

	/* 设置开关按钮的滑块样式 */
	.switch input {
		opacity: 0;
		width: 0;
		height: 0;
	}

	/* 设置开关按钮的轨道样式 */
	.slider {
		position: absolute;
		cursor: pointer;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #ccc;
		-webkit-transition: .4s;
		transition: .4s;
	}

	/* 设置开关按钮的滑块样式 */
	.slider:before {
		position: absolute;
		content: "";
		height: 26px;
		width: 26px;
		left: 4px;
		bottom: 4px;
		background-color: white;
		-webkit-transition: .4s;
		transition: .4s;
	}

	/* 设置开关按钮的状态为选中时的样式 */
	input:checked + .slider {
		background-color: #2196F3;
	}

	input:checked + .slider:before {
		-webkit-transform: translateX(26px);
		-ms-transform: translateX(26px);
		transform: translateX(26px);
	}

	/* 设置开关按钮的圆形样式 */
	.slider.round {
		border-radius: 34px;
	}

	.slider.round:before {
		border-radius: 50%;
	}
</style>
<% var errorcode,attackcfg;getAttackInfo(); %>

<script language="javascript">

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables btn" ;
		}

     }

  }
}

function display()
{
	
		tmp = "<% write(attackcfg); %>";
	array_cfg = tmp.split(",");
	
		for(var i=1;i<10;i++)
		{
			if(array_cfg[i-1]=="enable")
			 	document.getElementById("DosAttack" + i).checked = true;
			else
				document.getElementById("DosAttack" + i).checked = false;
	
		}
	

	  document.forms[0].synFloodNum.value = array_cfg[9];
	changebgcolor();
	changestat('DosAttack','select_ss');

}


function check()
{
	var tf=document.webForm;
	//var tmp = 1, aattack = 0;



	for(var i = 1; i <= 9; i++)
	{
		//var idName = eval("document.webForm.DosattackMask"+i);

//alert(idName.value);
	  if(document.getElementById("DosAttack" + i).checked) 
	  { 
	 	document.getElementById("DosattackMask" + i).value ="enable";
		 //aaa = eval("document.webForm.DosattackMask"+i);
		// alert(aaa);
	  }
	  else
	  {
	 	document.getElementById("DosattackMask" + i).value ="disable";
	 	//document.getElementById(idName).value ="disable";
		 //eval("document.forms[0].DosattackMask"+i).value  ="disable";
	  }
	}
	
/*

	var vid1 = doc.tPingFloodNum.value;
		if ( vid1 < 0 || vid1 > 2048 )
		{
			alert('速率错误，有效范围是 0-2048！');
			doc.tPingFloodNum.focus();
			return false;
		}

*/	
	
	var vid2 = document.forms[0].synFloodNum.value;

		if ( vid2 < 512 || vid2 > 16384 )
		{
			alert('门限值错误，有效范围是 512-16384！');
			document.forms[0].synFloodNum.focus();
			return false;
		}



			 		tf.submit();
}


function select_all(flag)
{
	for(i=1;i<=9;i++)
	{		
		if(!document.getElementById("DosAttack"+i).disabled)
		{
			
			if(flag)
			{
				document.getElementById("DosAttack"+i).checked = true;		
			}
			else
			{
				document.getElementById("DosAttack"+i).checked = false;		
			}	
		}		

	}
}

var attack = 9 ;
function changestat(str,id)
{
	var checked = 0;
	var valid_attack = 0;
	console.log(document.getElementById(id))
	for(i=1;i<=attack;i++)
	{		
		if(!document.getElementById(str+i).disabled)	
		{
			if(document.getElementById(str+i).checked)
			{
				checked = checked + 1;
			}
			valid_attack = valid_attack + 1;
		}
	}
	
	if(checked == valid_attack)
	{
		document.getElementById(id).checked = true ;
	}
	else
	{
		document.getElementById(id).checked = false ; 
	}
}



</script>
</HEAD>
<body    onLoad="display()"><br>


<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setDosAttack" class="formContain">
<input type="hidden" name="flag">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
        <input type="hidden" name="DosattackMask1" id="DosattackMask1"  value=""/>
        <input type="hidden" name="DosattackMask2" id="DosattackMask2"  value=""/>
        <input type="hidden" name="DosattackMask3" id="DosattackMask3"  value=""/>
        <input type="hidden" name="DosattackMask4" id="DosattackMask4"  value=""/>
        <input type="hidden" name="DosattackMask5" id="DosattackMask5"  value=""/>
        <input type="hidden" name="DosattackMask6" id="DosattackMask6"  value=""/>
        <input type="hidden" name="DosattackMask7" id="DosattackMask7"  value=""/>
        <input type="hidden" name="DosattackMask8" id="DosattackMask8"  value=""/>
        <input type="hidden" name="DosattackMask9" id="DosattackMask9"  value=""/>

 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="30px" ><font size="5" color="#404040"><div class="bot">DoS攻击配置</div></font></td></tr>
 </table>

<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
  
  <TBODY>
    <!-- <TR height=22>
      <TD colspan="2" valign="top"><span class="crons">
        <input type="checkbox" name="select_ss" id="select_ss" value="checkbox"  onclick="select_all(this.checked)"/>
全选</span></TD>
      </TR> -->
   <TR >
      <TD colspan="2" >
		<span class="crons">
     
      报文源IP地址等于目的IP地址</span></TD>
	  <TD>
		<label class="switch">
			<input type="checkbox"  name="DosAttack8" id="DosAttack8" value="ON"     onclick="changestat('DosAttack','select_ss');">
			<span class="slider round"></span>
		</label>
		
	  </TD>
      </TR>
   <TR >
      <TD colspan="2"><span class="crons">
     
TCP SYN报文源端口小于1024</span></TD>
<TD>
	<label class="switch">
		<input type="checkbox"  name="DosAttack4" id="DosAttack4" value="ON"     onclick="changestat('DosAttack','select_ss');">
		<span class="slider round"></span>
	</label>
	
  </TD>
      </TR>
    <TR >
      <TD colspan="2" ><span class="crons">
        
        UDP报文源端口等于目的端口</span></TD>
		<TD>
			<label class="switch">
				<input type="checkbox"    name="DosAttack9" id="DosAttack9" value="ON"   onclick="changestat('DosAttack','select_ss');">
				<span class="slider round"></span>
			</label>
			
		  </TD>
      </TR>
    <TR >
      <TD colspan="2" ><span class="crons">
        
        TCP报文置位FIN和URG和PSH标志位且序列号为0</span></TD>
		<TD>
			<label class="switch">
				<input type="checkbox"    name="DosAttack2" id="DosAttack2" value="ON"   onclick="changestat('DosAttack','select_ss');">
				<span class="slider round"></span>
			</label>
			
		  </TD>
      </TR>
    <TR >
      <TD colspan="2" ><span class="crons">
       
TCP报文SYN和FIN标志位被置位</span>        </TD>
<TD>
	<label class="switch">
		<input type="checkbox"   name="DosAttack1" id="DosAttack1" value="ON"   onclick="changestat('DosAttack','select_ss');">
		<span class="slider round"></span>
	</label>
	
  </TD>
      </TR>
    <TR >
      <TD colspan="2" ><span class="crons">
        
TCP报文SYNC和RST标志位被置位</span></TD>
<TD>
	<label class="switch">
		<input type="checkbox"    name="DosAttack3" id="DosAttack3" value="ON"    onclick="changestat('DosAttack','select_ss');">
		<span class="slider round"></span>
	</label>
	
  </TD>
      </TR>
    <TR >
      <TD colspan="2" ><span class="crons">
        
        TCP报文源端口等于目的端口</span></TD>
		<TD>
			<label class="switch">
				<input type="checkbox"   name="DosAttack5" id="DosAttack5" value="ON"    onclick="changestat('DosAttack','select_ss');">
				<span class="slider round"></span>
			</label>
			
		  </TD>
      </TR>
    <TR >
      <TD colspan="2" ><span class="crons">
       
        ICMP分片报文</span></TD>
		<TD>
			<label class="switch">
				<input type="checkbox"  name="DosAttack6" id="DosAttack6" value="ON"     onclick="changestat('DosAttack','select_ss');">
				<span class="slider round"></span>
			</label>
			
		  </TD>
      </TR>
    <TR  style="display:none">
      <td width="40%"  class="crons"><input type="checkbox" name="DosAttack7" id="DosAttack7" value="ON"     onclick="changestat('DosAttack','select_ss');"/>
        ICMP PING报文超过限定长度</td>
      <td  class="crons">指定长度:
        <label>
          <input name="synFloodNum" type="text" id="synFloodNum" size="20" />
        </label></td>
		<td>

		</td>
	
      </TR>
	  <TR  style="display:none">
		<TD colspan="3" valign="top">&nbsp;</TD>
	  </TR>
    <TR >
      <TD colspan="3" valign="top">&nbsp;</TD>
    </TR>
    <TR >
      <TD colspan="3" valign="top"><div align="center">	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"提  交","button","button","add","check()");</script>
</div></TD>
      </TR>
</TABLE>


</FORM>
<script>
<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</BODY></HTML>
