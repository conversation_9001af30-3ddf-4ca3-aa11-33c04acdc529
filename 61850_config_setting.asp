<HTML>
<HEAD>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<TITLE>page</TITLE>
<META http-equiv=Content-Type content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script>
<% var Allipaddr,errorcode;getIpAddr(); %>

function tdIpCheck(textValue)
{
    re1=/(\d+)(\W)(\d+)(\W)(\d+)(\W)(\d+)/
    Check=textValue.search(re1);
    if(Check==-1)
    {
        return false;
    }
    else
    {
        ipSplit=textValue.split('.');
        if(ipSplit.length!=4)
        {
            return false;
        }

        for(i=0; i<ipSplit.length; i++)
        {
            if(isNaN(ipSplit[i]))  return false;
			if(ipSplit[i].length > 1 && ipSplit[i].charAt(0) == "0") return false;
        }

        for(i=0; i<ipSplit.length; i++)
        {
            if(ipSplit[i] > 255)
            {
                return false;
            }
            if(ipSplit[i] < 0)
            {
                return false;
            }
        }
        if((ipSplit[0]==255) && (ipSplit[1]==255) && (ipSplit[2]==255) && (ipSplit[3]==255))
        {
            return false;
        }

        if((ipSplit[0]==0) || (ipSplit[0]==127) || (ipSplit[3]==0) || (ipSplit[3]==255))
        {
            return false;
        }

        if(ipSplit[0] >= 224)
        {
            return false;
        }
        return true;
    }
}

function IpCheckAndMask(ip_addr)
{
/*by FC-fcy 2012-4-23 start*/
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])(\u002F)(([0-9])|([1-2][0-9])|(3[0-2]))$/;
/*by FC-fcy 2012-4-23 end*/
	if(myRE.test(ip_addr))
	{
        return true;
	}
	else
	{
		return false;
	}
}

function showHelp(helpname,lang)
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function changebgcolor3(){
 var tab = document.all.table3;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor4(){
 var tab = document.all.table4;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function changebgcolor5(){
 var tab = document.all.table5;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor6(){
 var tab = document.all.table6;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor7(){
 var tab = document.all.table7;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor8(){
 var tab = document.all.table8;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor9(){
 var tab = document.all.table9;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function messageCheck1()
{
 	var hid=document.webForm;
	var fName = hid.config61850up_filename.value;
	var dplen;
	var dpvalue;
	var reauthn;
	var reauthpd;


 	if(tdIpCheck(hid.config61850up_ip.value)==false)
	{
		alert("IP地址输入非法！格式：A.B.C.D");
		return false;
	}

 	if(fName.length <= 4)
	{
    	alert("文件必须以 .cid 为扩展名！");
    	return false;
  	}


 	if(fName.indexOf(".cid") != fName.length - 4)
	{
    	alert("文件必须以 .cid 为扩展名！");
    	return false;
  	}

	var name = prompt("操作认证-用户名", "");
	document.getElementById("reauthn").value = name;
	var pwd = prompt("操作认证-密码", "");
	document.getElementById("reauthpd").value = pwd;


	reauthn = document.getElementById("reauthn").value;
	reauthpd = document.getElementById("reauthpd").value;

	dplen = parseInt(reauthn.length/2);
	dpvalue = reauthn;
	reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(reauthpd.length/2);
	dpvalue = reauthpd;
	document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
	document.getElementById("reauthn").value = "Ju*H@n*n89sT!EYd";


	document.getElementById("aaa").disabled = true;
	document.getElementById("bbb").disabled = true;
	document.getElementById("ccc").disabled = true;
	document.getElementById("ddd").disabled = true;
    hid.action="/goform/upload61850Config";
	hid.submit();
	return true;

}

function messageCheck2()
{
 	var hid=document.webForm;
	var fName = hid.config61850down_filename.value;
	var dplen;
	var dpvalue;
	var reauthn;
	var reauthpd;


 	if(tdIpCheck(hid.config61850down_ip.value)==false)
	{
		alert("IP地址输入非法！格式：A.B.C.D");
		return false;
	}

 	if(fName.length <= 4)
	{
    	alert("文件必须以 .cid 为扩展名！");
    	return false;
  	}


 	if(fName.indexOf(".cid") != fName.length - 4)
	{
    	alert("文件必须以 .cid 为扩展名！");
    	return false;
  	}

	var name = prompt("操作认证-用户名", "");
	document.getElementById("reauthn").value = name;
	var pwd = prompt("操作认证-密码", "");
	document.getElementById("reauthpd").value = pwd;


	reauthn = document.getElementById("reauthn").value;
	reauthpd = document.getElementById("reauthpd").value;

	dplen = parseInt(reauthn.length/2);
	dpvalue = reauthn;
	reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(reauthpd.length/2);
	dpvalue = reauthpd;
	document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
	document.getElementById("reauthn").value = "m0&XXSz4A0P273bQ";


 	document.getElementById("aaa").disabled = true;
	document.getElementById("bbb").disabled = true;
	document.getElementById("ccc").disabled = true;
	document.getElementById("ddd").disabled = true;
   hid.action="/goform/download61850Config";
	hid.submit();
	return true;

}


function messageCheck3()
{
 	var hid=document.webForm;
	var fName = hid.upload61850icd_filename.value;
	var dplen;
	var dpvalue;
	var reauthn;
	var reauthpd;


 	if(tdIpCheck(hid.upload61850icd_ip.value)==false)
	{
		alert("IP地址输入非法！格式：A.B.C.D");
		return false;
	}

 	if(fName.length <= 4)
	{
    	alert("文件必须以 .icd 为扩展名！");
    	return false;
  	}


 	if(fName.indexOf(".icd") != fName.length - 4)
	{
    	alert("文件必须以 .icd 为扩展名！");
    	return false;
  	}

	var name = prompt("操作认证-用户名", "");
	document.getElementById("reauthn").value = name;
	var pwd = prompt("操作认证-密码", "");
	document.getElementById("reauthpd").value = pwd;


	reauthn = document.getElementById("reauthn").value;
	reauthpd = document.getElementById("reauthpd").value;

	dplen = parseInt(reauthn.length/2);
	dpvalue = reauthn;
	reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(reauthpd.length/2);
	dpvalue = reauthpd;
	document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
	document.getElementById("reauthn").value = "XabY$JVw9pJHst%j";


	document.getElementById("aaa").disabled = true;
	document.getElementById("bbb").disabled = true;
	document.getElementById("ccc").disabled = true;
	document.getElementById("ddd").disabled = true;
    hid.action="/goform/upload61850ICD";
	hid.submit();
	return true;

}

function messageCheck4()
{
 	var hid=document.webForm;
	var fName = hid.download61850icd_filename.value;
	var dplen;
	var dpvalue;
	var reauthn;
	var reauthpd;


 	if(tdIpCheck(hid.download61850icd_ip.value)==false)
	{
		alert("IP地址输入非法！格式：A.B.C.D");
		return false;
	}

 	if(fName.length <= 4)
	{
    	alert("文件必须以 .icd 为扩展名！");
    	return false;
  	}


 	if(fName.indexOf(".icd") != fName.length - 4)
	{
    	alert("文件必须以 .icd 为扩展名！");
    	return false;
  	}

	var name = prompt("操作认证-用户名", "");
	document.getElementById("reauthn").value = name;
	var pwd = prompt("操作认证-密码", "");
	document.getElementById("reauthpd").value = pwd;


	reauthn = document.getElementById("reauthn").value;
	reauthpd = document.getElementById("reauthpd").value;

	dplen = parseInt(reauthn.length/2);
	dpvalue = reauthn;
	reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(reauthpd.length/2);
	dpvalue = reauthpd;
	document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
	document.getElementById("reauthn").value = "dxPLSwaO9Ztp%^XX";


 	document.getElementById("aaa").disabled = true;
	document.getElementById("bbb").disabled = true;
	document.getElementById("ccc").disabled = true;
	document.getElementById("ddd").disabled = true;
   hid.action="/goform/download61850ICD";
	hid.submit();
	return true;

}


function showHelpinfo()
{
   showHelp('ip',<% write(lang); %>);
}


</script>
</HEAD>
<BODY>
<br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/uploadConfig">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" id="reauthn" name="reauthn" value="">
<input type="hidden" id="reauthpd" name="reauthpd" value="">

 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>61850配置文件管理</b></font></td></tr>
 </table>


	<br />

 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#ABD8EF" height="20px" ><font size="2" color="#FFFFFF"><b>导出61850 cid配置文件</b></font></td></tr>
 </table>

	<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
	<TBODY>
	<TR height=22>
		<TD width="27%" valign="top">&nbsp;&nbsp;TFTP Server IP</TD>
		<td >
			<span class="crons">
			<input type="text" name="config61850up_ip"  id="config61850up_ip"  value="">
			</span>
		</td>
	</TR>
	<TR height=22>
		<TD valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"文件名");</script></TD>
		<td >
			<input type="text" name="config61850up_filename"  id="config61850up_filename"  value="">例如:xxx.cid
		</td>
	</TR>
	<TR height=22>
		<TD colspan="2" valign="top">
			<div align="center">
			<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"导出","buttons_apply","button","aaa","messageCheck1()");</script>
			</div>
		</TD>
	</TR>
	</TABLE>


	<br />
 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#ABD8EF" height="20px" ><font size="2" color="#FFFFFF"><b>导入61850 cid配置文件</b></font></td></tr>
 </table>

	<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table2" class="tablebord">
	<TBODY>
	<TR height=22>
		<TD width="27%" valign="top">&nbsp;&nbsp;TFTP Server IP</TD>
		<td >
			<span class="crons">
			<input type="text" name="config61850down_ip"  id="config61850down_ip"  value="">
			</span>
		</td>
	</TR>
	<TR height=22>
		<TD valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"文件名");</script></TD>
		<td >
			<input type="text" name="config61850down_filename"  id="config61850down_filename"  value="">例如:xxx.cid
		</td>
	</TR>
	<TR height=22>
		<TD colspan="2" valign="top">
			<div align="center">
	  		<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"导入","buttons_apply","button","bbb","messageCheck2()");</script>
			</div>
		</TD>
	</TR>
	</TABLE>

	<br />
 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#ABD8EF" height="20px" ><font size="2" color="#FFFFFF"><b>导出61850 icd配置文件</b></font></td></tr>
 </table>

	<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table3" class="tablebord">
	<TBODY>
	<TR height=22>
		<TD width="27%" valign="top">&nbsp;&nbsp;TFTP Server IP</TD>
		<td >
			<span class="crons">
			<input type="text" name="upload61850icd_ip"  id="upload61850icd_ip"  value="">
			</span>
		</td>
	</TR>
	<TR height=22>
		<TD valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"文件名");</script></TD>
		<td >
			<input type="text" name="upload61850icd_filename"  id="upload61850icd_filename"  value="">例如:xxx.icd
		</td>
	</TR>
	<TR height=22>
		<TD colspan="2" valign="top">
			<div align="center">
	  		<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"导出","buttons_apply","button","ccc","messageCheck3()");</script>
			</div>
		</TD>
	</TR>
	</TABLE>


	<br />
 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#ABD8EF" height="20px" ><font size="2" color="#FFFFFF"><b>导入61850 icd配置文件</b></font></td></tr>
 </table>

	<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table4" class="tablebord">
	<TBODY>
	<TR height=22>
		<TD width="27%" valign="top">&nbsp;&nbsp;TFTP Server IP</TD>
		<td >
			<span class="crons">
			<input type="text" name="download61850icd_ip"  id="download61850icd_ip"  value="">
			</span>
		</td>
	</TR>
	<TR height=22>
		<TD valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"文件名");</script></TD>
		<td >
			<input type="text" name="download61850icd_filename"  id="download61850icd_filename"  value="">例如:xxx.icd
		</td>
	</TR>
	<TR height=22>
		<TD colspan="2" valign="top">
			<div align="center">
	  		<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"导入","buttons_apply","button","ddd","messageCheck4()");</script>
			</div>
		</TD>
	</TR>
	</TABLE>

	<br />

<script>
changebgcolor();
changebgcolor2();
changebgcolor3();
changebgcolor4();
//changebgcolor5();
//changebgcolor6();
//changebgcolor7();
//changebgcolor8();
//changebgcolor9();

<% if (errorcode=="3") { write("alert(putmsg(");write(lang);write(",'不具备执行该操作的权限!'));");  } %>
<% if (errorcode!="") { if (errorcode!="3") { write_errorcode(errorcode); } } %>
</script>

</form>
</BODY></HTML>


