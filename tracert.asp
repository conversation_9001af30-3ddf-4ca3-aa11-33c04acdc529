<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_3.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"tracert");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script>

function messageCheck()
{
	if(check_ip(document.webForm.ipaddr1)==false)
		{
			alert(putmsg(<% write(lang); %>,"ip地址不合法,请检测!"));
			return ;
		}
	document.webForm.ipaddr.value=document.webForm.ipaddr1.value;
	document.webForm.ipaddr1.disabled=true;
	document.webForm.submit();
}
function showHelpinfo()
{
   showHelp('tracert',<% write(lang); %>);
}
</script>
</head>
<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/tracerouteAction">
<input type="hidden" name="ipaddr">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0"><tr><td>
	<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" ><tr><td valign="top" >
		<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
	      <tr>
	        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
	            <tr height="25">
	              <td colspan="3" align="left" class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"tracert测试");</script></td>
	            </tr>
	        </table></td>
	      </tr>
	      <tr>
	        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">
	            <tr height="25">
	              <td width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"IP地址");</script></td>
	              <td width="30%" align="left" class="crons">&nbsp;
	                  <input type="text" name="ipaddr1" id="ipaddr1" class="input_board3">
	                 </td>
                <td width="30%" align="left" class="crons">&nbsp;
               	<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"开  始","button","button","apply","messageCheck()");</script>
               </td>
	            </tr>
	            <tr height="25">
	            <% var errorcode;showLinecheck(); %>
	            </tr>
	        </table></td>
	      </tr>
	      <tr>
    <td align="center" height="35">
         </td>
	</tr>
    </table></td>
  </tr> 
</table>
<script>
changebgcolor();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</td></tr></table>
</body>
</form>
</html>
