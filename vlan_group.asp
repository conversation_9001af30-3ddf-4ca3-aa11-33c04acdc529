<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_rtl.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"VLAN分类设置");</script></title>

<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 

<script language="JavaScript" type="text/JavaScript">

function check()
{
	var vid=document.getElementById("rid").value;
	var gid=document.getElementById("gid").value;
	var tf=document.vlan_group;

  if((vid<=2099) && (vid>0))
	{
	 	if((gid>=1) && (gid<=30))
	 	{
			tf.submit();
		}
		else
		{
			alert(putmsg(<% write(lang); %>,"Group ID的范围必须在1-30之间"));
		}
  }
	else
	{
	   	alert(putmsg(<% write(lang); %>,"Rule ID的范围必须在1-2099之间"));
	}
}

function checkdel()
{
	var tf=document.vlan_group;
	var vid=document.getElementById("rid").value;
	var gid=document.getElementById("gid").value;
	var del = document.getElementById("del");
	if(vid=="" || gid=="")
	{
		alert(putmsg(<% write(lang); %>,"请选择ID"));
		return 0;
	}
    if((vid<=2999) && (vid>0))
	{
	    del.value = "1";
	 	tf.submit();
    }
}  

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var vid = document.getElementById("gid");;
	var vlan_name=document.getElementById("rid");
	var p = obj.value;

	if(obj.checked)
	{
		vid.value=trobj.cells[1].innerHTML;
		vlan_name.value=trobj.cells[2].innerHTML;	
	}
}

function p(vid,vlanid)
{
    var narr=3;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_group_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+vid);	

    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
    
    tbtr.cells[0].abbr = 0;
    tbtr.cells[1].abbr = vid;
	
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+vid+"\" onclick=\"addToPortRange(this)\"/>";
    tbtr.cells[1].innerHTML = vid;
    tbtr.cells[2].innerHTML = vlanid;	
}

function checkData()
{
	var tf=document.vlan_group;
	tf.action = "/goform/saveComm?name=vlan_group";
	tf.submit();
}

function refreshpage()
{
  location.href='vlan_group.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('vlan_group',<% write(lang); %>);
}

</script></head>

<body  >
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="vlan_group" method="POST" action="/goform/vlanGroupConfig">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="90%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
         <tr>
           <td colspan="2" align="left" class="cword09">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN设置");</script> <b><font color="#FF7F00">&gt;&gt;</font></b> &nbsp;<script>writemsg(<% write(lang); %>,"VLAN 组分类设置");</script></td>
         </tr>
        </table>
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%">
	     <tr height="25">
	       <th align="left" colspan="2">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN 组分类");</script></td>
	     </tr>
	      <tr height="25">
	     	  <td class="crons">&nbsp;Group ID</td>
	     	  <td class="crons">
	     	    &nbsp;<input type="text" id="gid" name="gid" class="input_board5" maxlength="4">
	     	    &nbsp;(1-30)
	     	  </td>
	     </tr>		 
	     <tr height="25">
	     	  <td class="crons">&nbsp;Rule ID</td>
	     	  <td class="crons">
	     	    &nbsp;<input type="text" id="rid" name="rid" class="input_board3" maxlength="25"/>&nbsp;(1-2099)
	     	  </td>
	     </tr>
	     <tr height="25">

	     	  <td colspan="2" align="middle" class="crons">
	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","add","check()");</script>
              &nbsp;
			  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","delete","checkdel()");</script>
	     	  </td>
	     </tr>	   
<tr><td colspan="2" height="8"></td></tr>
   <tr>
	    	<td colspan="2">
	    		<table id="table_group_vlan" border="0" cellspacing="0" cellpadding="0" width="100%" class="table2">
	    		 	<tr align="center" height="25" class="crons">
	    		 		<th class="td2" width="15%"></th>
						<th width="15%" class="td2"><strong><font color="#333333">Group Id</font></strong></th>
	    		 		<th width="25%" class="td2"><strong><font color="#333333">Rule Id</font></strong></th>
	    		 	</tr>
	    		 	<script><%  var errorcode; showVlanGroup(); %></script>
	    		</table>
	    	</td>
   </tr>
   <tr>
	  	   <td colspan="2" align="center" height="35">
	  		    <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
	  		</td>
   </tr>
	    
     </table>
   </td></tr>
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>
<INPUT type="hidden" name="del" id= "del" value="0">
</form>   
<script>
changebgcolor();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</html>
