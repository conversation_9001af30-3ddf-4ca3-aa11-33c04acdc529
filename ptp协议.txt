PTP 配置协议

版本：V1.0

说明：本文档为 PTP 协议配置说明文档，用于配置 PTP 协议的各项参数。


[
  {
    "portNum":1,              // 端口号：取值范围[1-28]
    "enable": 1,               // 端口使能状态：1-启用(Enable)，0-禁用(Disable)
    "mapChn": 5,               // 映射通道号：取值范围[0-31]（需与硬件通道匹配）
    "mapChnEnable": 1,         // 映射通道使能：1-启用，0-禁用（控制通道绑定状态）

    "portType": 1,             // 端口角色：0-SLAVE，1-MASTER（主从模式配置）
    "tranSpec": 0,             // 协议类型：0-IEEE 1588，1-IEEE 802.1AS（默认0）
    "encapType": 0,            // 封装类型：0-ETH，1-ETH&VLAN, 2-IPV4, 3-IPV4&VLAN
    "clockType": 1,            // 时钟模式：0-OC（普通时钟），1-BC（边界时钟），2-TC（透明时钟），3-MIX-CLK（混合模式）  ----- portType 为MASTER 时可以选择BC，MIX-CLK
    "delayMechanism": 0,       // 延迟机制：0-P2P，1-E2E（端到端或点到点延迟测量）
    "stepMode": [0, 1],        // 步模式：[SYNC包, PDELAY-RESP包]（0-ONE-STEP，1-TWO-STEP）

    "pdelayTcEn": 1,            // Pdelay透传使能：1-启用（TC模式下透传Pdelay报文），0-禁用            ---------clockType为2，3时显示，默认0
    "delayValue": [100, -50, 20, -30],  // 补偿值：[RX-DELAY, TX-DELAY, RX-ASYM, TX-ASYM]（有符号整数，单位：纳秒）


    //clockType 为TC模式时下列(一直到这个json的末尾)都隐藏
    // macAddr参数
    "dMacmode": [1, 0, 1, 0],   // MAC地址模式：0-COPY，1-CONFIG（控制是否手动配置）
    "dMac": [                  // 目的MAC地址（4种包类型）                             ----- dMacmode为1时显示
      [0x00, 0x11, 0x22, 0x33, 0x44, 0x55], // REQ包
      [0x00, 0xAA, 0xBB, 0xCC, 0xDD, 0xEE], // RESP包
      [0x11, 0x22, 0x33, 0x44, 0x55, 0x66], // PDELAY包
      [0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC]  // SYNC包
    ],
    // ipAddr参数 -- encapType为2，3时显示
    "dIpmode": [1, 1, 0, 0],    // IP地址模式：0-COPY，1-CONFIG（控制是否手动配置）                          ----- encapType为2，3时显示
    "dIp": [********, ********, ********, ********], // 目的IP地址（点分十进制：********等）        ----- dIpmode为1时显示
    "unicastFlag": [1, 0, 1, 1],// 单播模式：0-MULTICAST，1-UNICAST，2-COPY（针对SYNC/ANNOUNCE/REQ/RESP包） ----- encapType为2，3时显示，默认0


    // PTP基本参数
    "ptpHead": {
      "domainNumber": 1,       // 域编号：取值范围[0-255]（标识时钟所属域）
      "flagField": 0x123,      // 标志字段：取值范围[0-65535]（协议扩展标志）
      "sourcePortIdentity": {  // 源端口标识
        "clock_id": [0x12345678, 0x87654321], // 时钟ID（8字节组合，高位在前）
        "port_number": 100     // 端口号：取值范围[0-65535]
      },
      "msgInterval": [10, 20, 30, 40, 50] // 消息间隔日志：[SYNC, ANNOUNCE, DELAY, PREQ, PRESP]（单位：日志级别）
      "ttl": [64, 128],           // TTL配置：[PDELAY, NON_PDELAY]（数据包生存时间）                         ----- encapType为2，3时显示
    },
    "selfPortIdentityValue": {  // 本端口标识（用于标识自身时钟）
        "clock_id": [0x55555555, 0x66666666],
        "port_number": 400
      },
    "announceReceiptTimeout": { // Announce超时配置             ---portType 为 SLAVE 时显示
        "timeoutSecond": 3,       // 超时秒数：取值范围[0-1023]
        "timeoutMicrosecond": 500  // 超时微秒数：取值范围[0-1048576]（单位：1024ns）
    },
    "txInterval": [             // 发送间隔配置（对应SYNC/ANNOUNCE/REQ包）
        {
            "txIntervalSecond": 1,   // SYNC包秒数            ---portType 为MASTER 时显示
            "txIntervalMicrosecond": 100 // SYNC包微秒数
        },
        {
            "txIntervalSecond": 2,   // ANNOUNCE包秒数        ---portType 为MASTER 时显示
            "txIntervalMicrosecond": 200 // ANNOUNCE包微秒数
        },
        {
            "txIntervalSecond": 3,   // REQ包秒数             ---portType 为 SLAVE 时显示
            "txIntervalMicrosecond": 300 // REQ包微秒数
        }
    ],


    //报文校验配置
    "rxBypassEn": 0,            // 接收透传使能：1-启用（直接转发PTP报文不处理），0-禁用
    "rxEncapType": 0,           // 接收封装类型：0-ETH，1-IPV4（检查报文封装格式）
    "pktCheck": {               // 接收报文检查配置
      "rxVlanCheckEn": 1,       // VLAN检查使能：1-启用，0-禁用
      "rxVlanCheckValue": 100,  // VLAN值：取值范围[1-4094]（匹配报文VLAN标签）
      "rxDipCheckEn": 1,        // 目的IP检查使能：1-启用，0-禁用                   ---- rxEncapType为1时显示
      "rxDipCheckValue": 0x0A000005, // 目的IP：********（点分十进制）              ---- rxEncapType为1时显示
      "rxDmacCheckEn": 1,       // 目的MAC检查使能：1-启用，0-禁用
      "rxDmacCheckValue": [0x00, 0x22, 0x44, 0x66, 0x88, 0xAA], // 目的MAC地址
      "domainCheckEn": [1, 1, 0], // 域名检查使能：[NON-REQ, REQ, ANNOUNCE]包（1-启用，0-禁用）
      "domainValue": [1, 1, 0],   // 域名检查值：对应三种包类型的合法域编号
      "transportCheckEn": 1,     // 传输层检查使能：1-启用（检查协议类型）
      "portIdentityCheckEn": [1, 0, 1], // 端口标识检查使能：[PDELAY, SYNC, ANNOUNCE]包
      "portIdentityValue": [     // 合法端口标识列表（对应检查使能的包类型）
        {
          "clock_id": [0x11111111, 0x22222222],
          "port_number": 200
        },
        {
          "clock_id": [0x33333333, 0x44444444],
          "port_number": 300
        }
      ]
    },

    // 时间同步配置 --------master模式下有效,slave模式下隐藏
    "announce": {              // Announce消息参数配置
      "currentUtcOffset": 0,   // UTC偏移：取值范围[0-65535]（时钟与UTC的时间差）
      "priority1": 10,         // 优先级1：取值范围[0-255]（主时钟优先级）
      "priority2": 20,         // 优先级2：取值范围[0-255]（备用优先级）
      "clockQuality": {        // 时钟质量参数
        "clockClass": 64,      // 时钟等级：取值范围[0-255]（如64表示普通时钟）
        "clockAccuracy": 100,  // 时钟精度：取值范围[0-255]（单位：ppm）
        "clockVariance": 200   // 时钟稳定度：取值范围[0-65535]（长期频率偏差）
      },
      "identity1": 0x11111111, // 时钟ID高位：与identity2组成完整8字节ID
      "identity2": 0x22222222, // 时钟ID低位
      "stepsRemoved": 5,       // 跳数：取值范围[0-65535]（距离时间源的路径长度）
      "timeSource": 1          // 时间源：取值范围[0-255]（如1表示GPS）
    },
  }
]



TC示例：
    [
        {
            "enable": 1,               // 端口使能状态：1-启用(Enable)，0-禁用(Disable)
            "mapChn": 5,               // 映射通道号：取值范围[0-31]（需与硬件通道匹配）
            "mapChnEnable": 1,         // 映射通道使能：1-启用，0-禁用（控制通道绑定状态）

            "portType": 0,             // 端口角色：0-SLAVE，1-MASTER（主从模式配置）
            "tranSpec": 0,             // 协议类型：0-IEEE 1588，1-IEEE 802.1AS（默认0）
            "encapType": 0,            // 封装类型：0-ETH，1-ETH&VLAN, 2-IPV4, 3-IPV4&VLAN
            "clockType": 2,            // 时钟模式：0-OC（普通时钟），1-BC（边界时钟），2-TC（透明时钟），3-MIX-CLK（混合模式）
            "delayMechanism": 0,       // 延迟机制：0-P2P，1-E2E（端到端或点到点延迟测量）
            "stepMode": [0, 0],        // 步模式：[SYNC包, PDELAY-RESP包]（0-ONE-STEP，1-TWO-STEP）

            "pdelayTcEn": 1,            // Pdelay透传使能：1-启用（TC模式下透传Pdelay报文），0-禁用   ---------clockType为2，3时显示，默认0
            "delayValue": [100, -50, 20, -30], // 补偿值：[RX-DELAY, TX-DELAY, RX-ASYM, TX-ASYM]（有符号整数，单位：纳秒）  -----延迟补偿配置,portType为 SLAVE 时有效


        }
    ]