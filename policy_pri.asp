<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_rtl.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"GOOSE");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script language="JavaScript">

function display(){
var wValue = document.getElementById("qos_cfg").value.split(',');

	if(wValue[0] == "disable")
	{
	
		document.getElementById("modify_bu1").disabled = true;
		document.getElementById("modify_bu2").disabled = true;
		document.getElementById("modify_bu3").disabled = true;
		alert("QoS未开启!");
	}
	var allvalue = document.vlan_port.allpri.value;
	
	document.vlan_port.goosepri.value = allvalue.split(",")[0];
	document.vlan_port.gsepri.value = allvalue.split(",")[1];
	document.vlan_port.svpri.value = allvalue.split(",")[2];
	document.vlan_port.globalpri.value = allvalue.split(",")[3];

	if(allvalue.split(",")[3] == "0")
	{
	
		document.getElementById("modify_bu1").disabled = true;
		document.getElementById("modify_bu2").disabled = true;
		document.getElementById("modify_bu3").disabled = true;
	}

	
	
}


function del_goose(portId,aclname,classname,policyname)
{
		
		//alert(portId);
		//alert(aclname);
		//alert(classname);
		//alert(policyname);
		
		var hid = document.macdel;
		
		hid.port_value.value=portId;
		hid.acl_value.value=aclname;
		hid.class_value.value=classname;
		hid.policy_value.value=policyname;
		
		
		hid.action="/goform/delPolicyPri"
		hid.submit();
		return 0;
}


function selectToAll() 
{  
		var cf = document.forms[0];
		var objs = document.getElementsByName("checkbox_index"); 
		var i;
		if(cf.check_all.checked == true)
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
		       if(objs[i].disabled==false && objs[i].checked==false){
		         objs[i].checked = true;  
			 		addToPortRange(objs[i]);
			 }
		    }
		}
		else
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
					if(objs[i].checked==true){
					objs[i].checked = false;  
					 addToPortRange(objs[i]);
			}
		         
		    }
		} 
    
}


function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function checkup(act)
{
	var tf = document.vlan_port;
	
	document.vlan_port.detect_act.value = act;
	
	tf.action = "/goform/selPolicyPri";
	tf.submit();
}

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var target = document.getElementById("port_range");
	var avid = document.getElementById("avid");
	var dvid = document.getElementById("dvid");
	var porttype = document.getElementById("porttype");
	var filter = document.getElementById("filter");
	var type_value = document.getElementById("type_value");
	var filter_value = document.getElementById("filter_value");
	var vlan_port_id = document.getElementById("vlan_port_id");
	var p = obj.value;

	if(obj.checked){
		target.value =target.value + p + " ";
		vlan_port_id.value =vlan_port_id.value + p + " , ";
		
	

	}
	else
	{
		target.value = target.value.replace(p + " ","");
		vlan_port_id.value = vlan_port_id.value.replace(p + " , ","");
	}

}

function P(portId,appid,avg,aclname,classname,policyname)
{
    var narr=4;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+portId);	


    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		
	        tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
   // tbtr.cells[0].abbr = portId;
    
   
	

		//tbtr.cells[0].innerHTML = "<input type=\"checkbox\" id=\"input_"+portId+"\" name=\"checkbox_index\" value=\""+portId+"\" onclick=\"addToPortRange(this)\"/>";
	
		tbtr.cells[0].innerHTML = appid;

    tbtr.cells[1].innerHTML = avg;
    tbtr.cells[2].innerHTML = portId;


		tbtr.cells[3].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick=del_goose('"+portId+"','"+aclname+"','"+classname+"','"+policyname+"')>";
}


function refreshpage()
{
  location.href='policy_pri.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('vlan_port',<% write(lang); %>);
}

function AddOption(portname){

	var selectObject = document.getElementById("rang_monitor");
	var y=document.createElement('option');
  	y.text=portname;
	y.value = portname;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  	catch(ex)
    {
   	selectObject.add(y); // IE only
  	}
}


</script>
</head>

<body    onload="display()" ><br>

<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="vlan_port" method="POST" action="">

<% var  qoscfg; getQoSCfg(); %>
	                  <input type="hidden" name="qos_cfg"  id="qos_cfg"   value="<% write(qoscfg); %>">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="vlan_port_id" id="vlan_port_id" >
<input type="hidden" name="flag" id="flag" >
<input type="hidden" name="type_value" id = "type_value"  value="@trunk_config#">
<input type="hidden" name="filter_value" id = "filter_value"  value="@trunk_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="pvid_config"  value="@pvid_config#">
<input type="hidden" name="egress_tagged" id="egress_tagged" value="@egress_tagged#">
<input type="hidden" name="detect_act" id="detect_act" value="">
<input type="hidden" name="allport" id="allport" value="<% showAllPortForPri(); %>">
<input type="hidden" name="allpri" id="allpri" value="<% showAllPortPri(); %>">

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">

<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
     	 

 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>电网报文设置/电网报文优先级设置</b></font></td></tr>
 </table>
 
<!--    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
	     <tr height="25">
	       <td align="left" colspan="4"  class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"电力报文优先级");</script></td>
	     </tr>
        </table>
        -->
        
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%" id="table1" class="tablebord">

		<tr height="25">
			<td width="18%" class="crons">&nbsp;电力报文优先级全局开关</td>
			<td width="7%" class="crons" >&nbsp;
			<select name="globalpri" id="globalpri">
			<option value="0" >Disable</option>
			<option value="1" >Enable</option>
			</select></td>
			<td class="crons">&nbsp;<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify_bu4","checkup(4)");</script>
			</td>
		</tr>	
	
	     	     <tr height="25">
	     	  <td width="18%" class="crons">&nbsp;GOOSE</td>
	     	  <td width="7%" class="crons" >&nbsp;
	     	    <select name="goosepri" id="goosepri">
                  <option value="8" >Disable</option>
                  <option value="0" >0</option>
                  <option value="1" >1</option>
                  <option value="2" >2</option>
                  <option value="3" >3</option>
                  <option value="4" >4</option>
                  <option value="5" >5</option>
                  <option value="6" >6</option>
                  <option value="7" >7</option>
                </select></td>
	     	  <td class="crons">&nbsp;<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify_bu1","checkup(1)");</script>
</td>
	     	  </tr>	
		     	     <tr height="25">
		     	       <td class="crons">&nbsp;GSE</td>
		     	       <td class="crons" >&nbsp;
                           <select name="gsepri" id="gsepri">
                             <option value="8" >Disable</option>
                             <option value="0" >0</option>
                             <option value="1" >1</option>
                             <option value="2" >2</option>
                             <option value="3" >3</option>
                             <option value="4" >4</option>
                             <option value="5" >5</option>
                             <option value="6" >6</option>
                             <option value="7" >7</option>
                         </select></td>
		     	       <td class="crons">&nbsp;<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify_bu2","checkup(2)");</script>
</td>
	     	          </tr>
		     	     <tr height="25">
<td class="crons">&nbsp;SV</td>
	     	    	     <td class="crons">&nbsp;
	     	    	       <select name="svpri" id="svpri">
                             <option value="8" >Disable</option>
                             <option value="0" >0</option>
                             <option value="1" >1</option>
                             <option value="2" >2</option>
                             <option value="3" >3</option>
                             <option value="4" >4</option>
                             <option value="5" >5</option>
                             <option value="6" >6</option>
                             <option value="7" >7</option>
                           </select></td>	     	  
     	    	        <td class="crons">&nbsp;<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify_bu3","checkup(3)");</script>
&nbsp;&nbsp;&nbsp;&nbsp;</td>
	     	  </tr>	     	 
	   
	   	  
     </table>
   </td></tr>
   <tr>
	  	   <td colspan="2" align="center" height="35">
	  	   <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
	  		</td>
   </tr>
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>

</form>  
<script>
changebgcolor();
//changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

<form name="macdel" method="POST" action="">
	    <input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

	<input type="hidden" name="port_value"  value="" >
	<input type="hidden" name="acl_value"  value="" >
	

	<input type="hidden" name="class_value"  value="" >
	<input type="hidden" name="policy_value"  value="" >
	
	

	

</form>
</body>
</html>

