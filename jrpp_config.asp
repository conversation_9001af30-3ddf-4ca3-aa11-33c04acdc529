<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
	<% var lltime,lang; getltime_lanflag(); %>
		<meta http-equiv="Content-Type" content="text/html; charset=utf8">
		<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
		<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
		<script src="js/alpinejs.min.js" defer></script>
		<link href="css/display_web.css" rel="stylesheet" type="text/css" />
		<style>
			.input-group {
				position: relative;
				display: table;
				border-collapse: separate;
			}

			.input-group-addon {
				padding: 6px 12px;
				line-height: 1;
				color: #555;
				text-align: center;
				background-color: #eee;
				border: 1px solid #ccc;
				white-space: nowrap;
				vertical-align: middle;
				display: table-cell;
			}

			.form-control {
				width: 100%;

				line-height: 1.42857143;
				max-width: 138px;
			}
		</style>
		<title>&nbsp;
			<script>writemsg(<% write(lang); %>, "端口管理");</script>
		</title>
		<script>



			//环网运行信息显示
			retValue = <% var responseJsonStr; jw_get_jrpp_ring_info(); %>
				RingNetwork = <% write(responseJsonStr); %>;


		</script>
		<script>
			function changebgcolor() {
				var tab = document.all.table1;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							// tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables";
						}
						else {
							// tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables all_tables1";
						}

					}

				}
			}
			function changebgcolor3() {
				var tab = document.all.table3;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {


							tab.rows[i].cells[j].className = "all_tables";
						}
						else {

							tab.rows[i].cells[j].className = "all_tables all_tables3";
						}

					}

				}
				var tab = document.all.table6;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {


							tab.rows[i].cells[j].className = "all_tables";
						}
						else {

							tab.rows[i].cells[j].className = "all_tables all_tables3";
						}

					}

				}
			}
			function changebgcolor4() {
				var tab = document.all.table4;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						tab.rows[i].cells[j].className = "all_tables all_tables3";
					}
				}
				var tab5 = document.all.table5;
				var len5 = tab5.rows.length;
				for (var i = 0; i < len5; i++) {
					var lencol = tab5.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						tab5.rows[i].cells[j].className = "all_tables all_tables3";
					}
				}
				var tab7 = document.all.table7;
				var len7 = tab7.rows.length;
				for (var i = 0; i < len7; i++) {
					var lencol = tab7.rows[i].cells.length
					if (i == 0) {
						for (var j = 0; j < lencol; j++) {
							tab7.rows[i].cells[j].className = "all_tables all_tables3";
						}
					} else {
						for (var j = 0; j < lencol; j++) {
							tab7.rows[i].cells[j].className = "all_tables";
							tab7.rows[i].cells[j].style = 'text-align: center;'
						}
					}

				}

			}
			function refreshpage() {
				location.href = 'jrpp_config.asp?ltime=' +<% write(lltime); %>;
			}


<%  var authmode; checkCurMode(); %>
				function display() {
					changebgcolor();
					changebgcolor2();
				}

			function changebgcolor6() {
				var tab = document.all.table6;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					if (i == 0) {
						for (var j = 0; j < lencol; j++) {

							// tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables all_tables3";


						}
					} else {
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables";
							}
							else {
								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables all_tables3";
							}

						}
					}


				}
			}
			function changebgcolor2() {
				var tab = document.all.table2;

				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							// tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables";
						}
						else {
							// tab.rows[i].cells[j].bgColor = "efefef";
							tab.rows[i].cells[j].className = "all_tables all_tables3";
						}

					}

				}
			}
			function getUrlParamTab() {
				let urlSearch = window.location.search
				const search = new URLSearchParams(urlSearch)
				const params = Object.fromEntries(search.entries())
				try {
					if (params.tab) return params.tab
				} catch { }
				return 'tab1'
			}
			function getUrlParamShowFlag() {
				let urlSearch = window.location.search
				const search = new URLSearchParams(urlSearch)
				const params = Object.fromEntries(search.entries())
				try {
					if (params.showFlag) return params.showFlag
				} catch { }
				return 'false'
			}
			function getUrlParamSelFlag() {
				let urlSearch = window.location.search
				const search = new URLSearchParams(urlSearch)
				const params = Object.fromEntries(search.entries())
				try {
					if (params.sel)
						return +params.sel
				} catch { }
				return 1
			}
			//环网显示信息
			retValue = <% var responseJsonStr; jw_get_jrpp_config(); %>
				responseStr = <% write(responseJsonStr); %>;
			const pagePath = 'jrpp_config.asp?ltime=' +<% write(lltime); %>;
			function setRingInfo(row) {
				var hid = document.ringNetworkDisplayInformation;
				let params = {
					pagePath: pagePath + '&tab=tab2',
					pathName: 'jrpp_config.asp',
					...row,
					priority: Number(row.priority),
					ringId: Number(row.id),
					state: Number(row.state),
					timeBase: Number(row.timeBase)

				}
				hid.param1.value = JSON.stringify(params);

				hid.action = "/goform/jw_set_jrpp_ringMod";
				hid.submit();
				return true;
			}
			function delRingInfo(row) {
				var hid = document.ringNetworkDisplayInformation;
				let params = {
					pagePath: pagePath + '&tab=tab2',
					pathName: 'jrpp_config.asp',
					ringId: Number(row.id),


				}
				hid.param1.value = JSON.stringify(params);

				hid.action = "/goform/jw_del_jrpp_ring";
				hid.submit();
				return true;
			}
			//环网运行信息显示
			function setRingNetwork(row) {
				var hid = document.RingNetwork;
				let params = {
					pagePath: pagePath + '&tab=tab2',
					pathName: 'jrpp_config.asp',
					...row,
					priority: Number(row.priority),
					ringId: Number(row.id),
					state: Number(row.state),
					timeBase: Number(row.timeBase)
				}

				hid.param1.value = JSON.stringify(params);

				hid.action = "/goform/jw_set_jrpp_ringMod";
				hid.submit();
				return true;
			}
			function filterData(data, id) {

				return data.filter(item => item.ring_id === Number(id))[0]
			}
			function filterData1(data, id) {

				return data.filter(item => item.ringId == Number(id))[0]
			}
			//筛选状态
			function filterState(state) {
				let obj = {
					'0': 'DISABLE',
					'1': 'LISTENING',
					'2': 'LEARNING',
					'3': 'FORWARDING',
					'4': 'BLOCKING',
				}
				return obj[state]
			}
			//拓扑显示
			retValue = <% var responseJsonStr; jw_get_jrpp_topo_info(); %>
				topologyDisplay = <% write(responseJsonStr); %>;
			//拓扑设置
			function changeTopology(val) {
				//let sel = document.getElementById('topoState')

				// sel.value = topologyDisplay.topo.find(item => item.ringId == Number(val)).state
			}
			function changeStateTopo() {
				let sel = document.getElementById('topoState')
				let params = {
					pagePath: pagePath + '&tab=tab3',
					pathName: 'jrpp_config.asp',
					ringId: Number(document.getElementById('topoId').value),
					state: Number(sel.value)
				}
				let hid = document.ringTopology;
				hid.param1.value = JSON.stringify(params);
				hid.action = "/goform/jw_set_jrpp_topo";
				hid.submit();
				return true;
			}
			//环网配置
			function addRing() {
				let params = {
					pagePath: pagePath + '&tab=tab1',
					pathName: 'jrpp_config.asp',
					ringId: Number(document.getElementById('ring_id').value),
					lport_p: Number(document.getElementById('lport_p').value),
					lport_s: Number(document.getElementById('lport_s').value),
					timeBase: Number(document.getElementById('timeBase').value),
					priority: Number(document.getElementById('ring_priority').value),
					state: Number(document.getElementById('ring_state').value)
				}
				let hid = document.ringNetworkSettings;
				hid.param1.value = JSON.stringify(params);
				hid.action = "/goform/jw_set_jrpp_ringConfig";
				hid.submit();
				return true;
			}
			//刷新
			function refresh(tab, showFlag, sel) {
				location.href = 'jrpp_config.asp?ltime=' +<% write(lltime); %> +'&tab=' + tab + '&showFlag=' + showFlag + '&sel=' + sel;
			}
		</script>
</head>

<body onload="display()" x-data="{active:'tab1'}" x-init="active=getUrlParamTab()"><br>
	<% web_get_stat(); %>
		<div>
			<ul class="tabmenu">
				<li id="tab1" :class="active==='tab1'?'tab':'tab-disable'">
					<a herf="#" x-on:click="active='tab1'">环网配置</a>
				</li>
				<li id="tab2" :class="active==='tab2'?'tab':'tab-disable'">
					<a herf="#" x-on:click="active='tab2'">环网运行信息显示</a>
				</li>
				<li id="tab3" :class="active==='tab3'?'tab':'tab-disable'">
					<a herf="#" x-on:click="active='tab3'">环网拓扑显示</a>
				</li>

			</ul>
		</div>
		<form x-show="active==='tab1'" name="ringNetworkSettings" method="POST" action="sedtab.asp"
			style="min-height: 780px;">
			<input type="hidden" name="left_menu_id" value="">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input type="hidden" name="param1" id="param1">
			<div class="formContain">
				<table width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
					<tr>
						<td>
							<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td height="30px">
										<font size="5" color="#404040">
											<div class="bot">环网配置</div>
										</font>
									</td>
								</tr>
							</table>
							<br>
						</td>
					</tr>
					<tr>
						<td>
							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table1"
								x-data="{portList:[]}"
								x-init="portList = JSON.parse(localStorage.getItem('portList')).map(item => item.portName)"
								class="tablebord1">
								<tr>
									<td align="right" width="43%">环ID</td>
									<td style="padding-left: 15px;">
										<div class="input-group">
											<div class="input-group-addon">ring</div>
											<input type="text" class="form-control" id="ring_id" value="1">
										</div>
									</td>
								</tr>
								<tr height="30">
									<td align="right" width="43%">主端口</td>
									<td style="padding-left: 15px;">
										<select id="lport_p" style="width:170px">
											<template x-for="(port,idx) in portList" :key="idx">
												<option :value="port.substring(2)" x-text="port"></option>
											</template>
										</select>
									</td>
								</tr>
								<tr height="30">
									<td align="right" width="43%">副端口</td>
									<td style="padding-left: 15px;">
										<select id="lport_s" style="width:170px">
											<template x-for="(port,idx) in portList" :key="idx">
												<option :value="port.substring(2)" x-text="port"></option>
											</template>
										</select>
									</td>
								</tr>
								<tr height="30">
									<td align="right" width="43%">时间周期</td>
									<td style="padding-left: 15px;">
										<select id="timeBase" style="width:170px">
											<option value="1">1 秒</option>
											<option value="2">2 秒</option>
											<option value="3">3 秒</option>
										</select>
									</td>
								</tr>
								<tr height="30">
									<td align="right" width="43%">优先级</td>
									<td style="padding-left: 15px;">
										<select id="ring_priority" style="width:170px">
											<option value="0">HIGH</option>
											<option value="1">MID</option>
											<option value="2">LOW</option>
										</select>
									</td>
								</tr>
								<tr height="30">
									<td align="right" width="43%">状态</td>
									<td style="padding-left: 15px;">
										<select id="ring_state" style="width:170px">
											<option value="0">禁用</option>
											<option value="1">启用</option>

										</select>
									</td>
								</tr>
								<tr>
									<td align="center" height="35" style="padding: 5px;text-align: center;" colspan="2">
										<script>writebutton(1,<% write(lang); %>, "应  用", "button", "button", "Refresh", "addRing()");</script>
									</td>
								</tr>
							</table>
						</td>
					</tr>

				</table>
			</div>
		</form>
		<form x-show="active==='tab2'" name="ringNetworkDisplayInformation" method="POST" action="sedtab.asp">
			<input type="hidden" name="left_menu_id" value="">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input type="hidden" name="param1" id="param1">
			<div class="formContain" x-data="{ring:[]}" x-init="()=>{data=responseStr}">
				<table width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
					<tr>
						<td>
							<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td height="30px">
										<font size="5" color="#404040">
											<div class="bot">环网显示信息</div>
										</font>
									</td>
								</tr>
							</table>
							<br>
						</td>
					</tr>
					<tr>
						<td>统计数:<span x-text="data.ring.length"></span> </td>
					</tr>
					<tr>
						<td>
							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table2"
								class="tablebord1">

								<tr>
									<td align="center">序号</td>
									<td align="center" style="font-weight: bold;">环ID</td>
									<td align="center" style="font-weight: bold;">主端口</td>
									<td align="center" style="font-weight: bold;">副端口</td>
									<td align="center" style="font-weight: bold;">时间周期</td>
									<td align="center" style="font-weight: bold;">优先级</td>
									<td align="center" style="font-weight: bold;">状态</td>
									<td align="center" style="font-weight: bold;">修改</td>

								</tr>
								<template x-for="(val,index) in data.ring" :key="val.id">
									<tr>
										<td x-text="index+1" style="font-weight: 400;"></td>
										<td x-text="val.id" align="center" style="font-weight: 400;"></td>
										<td x-text="val.lport_p" style="font-weight: 400;"></td>
										<td x-text="val.lport_s" align="center" style="font-weight: 400;"></td>
										<td x-text="val.timeBase" align="center" style="font-weight: 400;"></td>
										<td align="center">
											<select id="ring_priority" style="width:128px" x-model="val.priority">
												<option value="0">HIGH</option>
												<option value="1">MID</option>
												<option value="2">LOW</option>
											</select>
										</td>
										<td align="center" style="font-weight: 400;">
											<select id="ring_priority" style="width:138px" x-model="val.state">
												<option value="0">禁用</option>
												<option value="1">启用</option>
											</select>
										</td>
										<td style="font-weight: 400; display: flex;justify-content: center;"
											align="center">
											<div class="applyBtn" style="line-height: 35px;background-color: #F0AD4E;"
												@click="setRingInfo(val)">
												修 改
											</div>
											<div class="applyBtn"
												style="line-height: 35px;background-color: #D9534F;margin-left: 5px;"
												@click="delRingInfo(val)">
												删 除
											</div>

										</td>
									</tr>
								</template>

							</table>
						</td>
					</tr>

				</table>
			</div>
		</form>
		<form x-show="active==='tab2'" name="RingNetwork" method="POST" action="sedtab.asp"
			style="margin-top: 15px;min-height: 400px;">
			<input type="hidden" name="left_menu_id" value="">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<div class="formContain" x-data="{data:{ring:[]},flag:'false',sel:''}"
				x-init="()=>{data=RingNetwork,sel=getUrlParamSelFlag(),flag=getUrlParamShowFlag()}">

				<table width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
					<tr>
						<td>
							<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td height="30px">
										<font size="5" color="#404040">
											<div class="bot">环网运行信息显示</div>
										</font>
									</td>
								</tr>
							</table>
							<br>
						</td>
					</tr>
					<tr>
						<td>
							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table3"
								class="tablebord1">

								<tr>
									<td align="center">环ID</td>
									<td align="center" style="font-weight: bold;">操作</td>
									<td></td>

								</tr>

								<tr>
									<td align="center">
										<select id="ring_priority" style="width:128px" x-model="sel">

											<template x-for="(val,index) in data.ring" :key=val.ring_id>
												<option :value="val.ring_id" x-text="val.ring_id"
													:selected="val.ring_id==sel"></option>
											</template>

										</select>

									</td>
									<td align="center">
										<select x-model="flag">
											<option value=false>隐藏</option>
											<option value=true>显示</option>

										</select>
									</td>
									<td align="center" height="35" style="padding: 5px;text-align: center;" colspan="1">
										<div class="applyBtn"
											style="line-height: 35px;background-color: #5BC0DE;margin: 0 auto;"
											@click="refresh(`tab2`,`${flag}`,`${sel}`)">
											刷 新
										</div>

									</td>

								</tr>


								<tr x-show="flag=='true'">
									<td style="text-align: right;">
										环模式: <span></span>
									</td>

									<td style="text-align: left;" colspan="2"
										x-text="filterData(data.ring,sel).rpp_mode ? '增强模式' : '兼容模式'">

									</td>

								</tr>
								<tr x-show="flag=='true'">
									<td style="text-align: right;">
										环状态:
									</td>
									<td style="text-align: left;" colspan="2"
										x-text="filterData(data.ring,sel).ring_state ? '启用' : '禁用'"></td>
								</tr>
								<tr x-show="flag=='true'">
									<td style="text-align: right;">
										当前节点ID:
									</td>

									<td style="text-align: left;" x-text="filterData(data.ring,sel)['node_id'].addr"
										colspan="2">
									</td>
								</tr>
								<tr x-show="flag=='true'">
									<td style="text-align: right;">
										主节点ID:
									</td>
									<td style="text-align: left;" x-text="filterData(data.ring,sel).master_id.addr"
										colspan="2">
									</td>
								</tr>
								<tr x-show="flag=='true'">
									<td style="text-align: right;">
										运行状态: <span></span>
									</td>
									<td style="text-align: left;"
										x-text="filterData(data.ring,sel).ring_status ? '健康' :'破裂'" colspan="2"></td>
								</tr>
								<tr x-show="flag=='true'">
									<td style="text-align: right;">
										节点角色:
									</td>
									<td style="text-align: left;" colspan="2"
										x-text="filterData(data.ring,sel).node_role ? '传输节点' :'主节点'"></td>
								</tr>

							</table>
						</td>
					</tr>
					<tr x-show="flag=='true'">
						<td>
							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table6"
								class="tablebord1">
								<tr>
									<td align="center">端口号</td>
									<td align="center" style="font-weight: bold;">端口角色</td>
									<td align="center" style="font-weight: bold;">STP</td>
									<td align="center" style="font-weight: bold;">Link</td>
									<td align="center">邻居端口</td>
									<td align="center" style="font-weight: bold;">邻居MAC</td>
									<td align="center">选举ID</td>
								</tr>
								<tr>
									<td align="center" style="font-weight:400;">
										<span x-text="filterData(data.ring,sel).primary.port_no "></span>
									</td>
									<td align="center" style="font-weight:400;">
										主端口
									</td>
									<td align="center" style="font-weight:400;">
										<span
											x-text="filterState(filterData(data.ring,sel).primary.dot1d_state)  "></span>
									</td>
									<td align="center" style="font-weight:400;"><span
											x-text="filterData(data.ring,sel).primary.link_state ? 'DOWN' :'UP'"></span>
									</td>
									<td align="center" style="font-weight:400;"><span
											x-text="filterData(data.ring,sel).primary.neighbor_port_no "></span></td>
									<td align="center" style="font-weight:400;"><span
											x-text="filterData(data.ring,sel).primary.neighbor_mac "></span></td>
									<td align="center" style="font-weight:400;"><span
											x-text="filterData(data.ring,sel).primary.ballot_id.addr "></span></td>
								</tr>
								<tr>
									<td align="center" style="font-weight:400;">
										<span x-text="filterData(data.ring,sel).secondary.port_no "></span>
									</td>
									<td align="center" style="font-weight:400;">
										副端口
									</td>
									<td align="center" style="font-weight:400;">
										<span
											x-text="filterState(filterData(data.ring,sel).secondary.dot1d_state)"></span>
									</td>
									<td align="center" style="font-weight:400;"><span
											x-text="filterData(data.ring,sel).secondary.link_state ? 'DOWN' :'UP' "></span>
									</td>
									<td align="center" style="font-weight:400;"><span
											x-text="filterData(data.ring,sel).secondary.neighbor_port_no "></span></td>
									<td align="center" style="font-weight:400;"><span
											x-text="filterData(data.ring,sel).secondary.neighbor_mac "></span></td>
									<td align="center" style="font-weight:400;"><span
											x-text="filterData(data.ring,sel).secondary.ballot_id.addr "></span></td>
							</table>
						</td>
					</tr>
				</table>
			</div>
		</form>
		<form x-show="active==='tab3'" name="topologyDisplay" method="POST" action="sedtab.asp">
			<input type="hidden" name="left_menu_id" value="">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<div class="formContain" x-data="{data:{topo:[]},flag:'false',sel:1}"
				x-init="()=>{data=topologyDisplay,sel=getUrlParamSelFlag(),flag=getUrlParamShowFlag()}">
				<table width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
					<tr>
						<td>
							<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td height="30px">
										<font size="5" color="#404040">
											<div class="bot">环网拓扑显示</div>
										</font>
									</td>
								</tr>
							</table>
							<br>
						</td>
					</tr>
					<tr>
						<td>
							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table4"
								class="tablebord1">

								<tr>
									<td align="center">环ID</td>
									<td align="center" style="font-weight: bold;" width="400">操作</td>
									<td width="300"></td>
								</tr>

								<tr>
										<td align="center">
											<select id="ring_priority" style="width:128px" x-model="sel">
												<template x-for="val in data.topo" :key="val.ringId">
													<option :value="val.ringId" x-text="val.ringId"
														:selected="val.ringId==sel"></option>
												</template>
											</select>
										</td>
									<td align="center">
										<select x-model="flag">
											<option value=false>隐藏</option>
											<option value=true>显示</option>

										</select>
									</td>
									<td align="center" height="35" style="padding: 5px;text-align: center;" colspan="2">
										<div class="applyBtn"
											style="line-height: 35px;background-color: #5BC0DE;margin: 0 auto;"
											@click="refresh(`tab3`,`${flag}`,`${sel}`)">
											刷 新
										</div>

									</td>

								</tr>
							</table>
						</td>
					</tr>
					<tr x-show="flag=='true'">
						<td>
							统计数:<span x-text="filterData1(topologyDisplay.topo,sel).topoNode.length"></span>

						</td>
					</tr>
					<tr x-show="flag=='true'">
						<td>
							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table7"
								class="tablebord1">
								<tr>
									<td>

										当前节点ID
									</td>
									<td>
										节点优先级
									</td>
									<td>
										环状态
									</td>
									<td>
										环模式
									</td>
									<td>
										运行状态
									</td>
									<td>
										节点角色
									</td>
									<td>主节点ID</td>
									<td>端口号*角色*状态*邻居</td>
									<td>端口号*角色*状态*邻居</td>
								</tr>
								<template x-for="(val,idx) in filterData1(topologyDisplay.topo,sel).topoNode"
									:key="idx">
									<tr style="text-align: center;" align="center" height="30px">
										<td x-text="val.node_id.addr" class="all_tables" style="font-size: 16px;"></td>
										<td class="all_tables" style="font-size: 16px;"
											x-text="val.node_id.prio == '0' ? 'HIGH':val.node_id.prio =='1' ? 'MID': 'LOW' ">
										</td>
										<td x-text="val.state == '1' ? '启用' : '禁用'" class="all_tables"
											style="font-size: 16px;"></td>
										<td x-text="val.rpp_mode== '1' ? '增强' : '兼容'" class="all_tables"
											style="font-size: 16px;"></td>
										<td x-text="val.status=='1' ? '健康' : '破裂'" class="all_tables"
											style="font-size: 16px;"></td>
										<td x-text="val.node_role=='1' ? '传输节点' : '主节点'" class="all_tables"
											style="font-size: 16px;"></td>
										<td x-text="val.master_id.addr" class="all_tables"></td>
										<td class="all_tables" style="font-size: 16px;">
											<span x-text="val.primary.port_no"></span>*
											<span x-text="val.primary.role=='0' ? '主端口' : '副端口'"></span>*
											<span x-text="filterState(val.primary.stp)"></span>*
											<span x-text="val.primary.neighbor_mac"></span>
										</td>
										<td class="all_tables" style="font-size: 16px;">
											<span x-text="val.secondary.port_no"></span>*
											<span x-text="val.secondary.role=='0' ? '主端口' : '副端口'"></span>*
											<span x-text="filterState(val.secondary.stp)"></span>*
											<span x-text="val.secondary.neighbor_mac"></span>
										</td>
									</tr>
								</template>

							</table>

						</td>
					</tr>
				</table>
			</div>
		</form>
		<form x-show="active==='tab3'" name="ringTopology" method="POST" action="sedtab.asp" style="margin-top: 20px;">
			<input type="hidden" name="left_menu_id" value="">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input type="hidden" name="param1" id="param1">
			<div class="formContain" x-data="{data:{topo:[]},sel:'',stateSel:''}"
				x-init="()=>{data=topologyDisplay,sel=topologyDisplay.topo[0]['ringId']}">
				<table width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
					<tr>
						<td>
							<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td height="30px">
										<font size="5" color="#404040">
											<div class="bot">环网拓扑设置</div>
										</font>
									</td>
								</tr>
							</table>
							<br>
						</td>
					</tr>
					<tr>
						<td>
							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table5"
								class="tablebord1">

								<tr>
									<td align="center">环ID</td>
									<td align="center" width="400">操作</td>
									<td align="center" width="300"></td>

								</tr>

								<tr>
									<td align="center">
										<select style="width:128px" x-model="sel" @change="changeTopology(sel)"
											id="topoId">
											<template x-for="val in data.topo" :key="val.ringId">
												<option :value="val.ringId" x-text="val.ringId"></option>
											</template>


										</select>
									</td>
									<td align="center">
										<select id="topoState" style="width:128px" x-model="stateSel">
											<option value="1">启用</option>
											<option value="0">禁用</option>
										</select>
									</td>
									<td align="center" height="35" style="padding: 5px;text-align: center;" colspan="2">
										<script>writebutton(1,<% write(lang); %>, "应  用", "button", "button", "Refresh", "changeStateTopo(`stateSel`)");</script>
									</td>

								</tr>
							</table>
						</td>
					</tr>

				</table>
			</div>
		</form>
		<br>
		<br>
		<script>
			changebgcolor();
			changebgcolor3()
			changebgcolor4()
				<% if (errorcode != "") { write_errorcode(errorcode); } %>
		</script>
</body>

</html>