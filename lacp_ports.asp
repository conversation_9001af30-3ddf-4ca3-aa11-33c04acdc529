<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_2.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"端口管理");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script>

function showTrunkPortcfg(portNo,lltime)
{
	window.location.href="lacp_ports.asp?portNo="+portNo+"&ltime="+lltime;
}
function RefreshInfor()
{
 if(document.getElementById("instance")==null) 
 {
  location.href="lacp_ports.asp";
 }
 else
 {
  location.href="lacp_ports.asp?portNo="+document.webForm.instance.value;
 }
}
function refreshpage()
{
  location.href='lacp_ports.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('lacp_ports',<% write(lang); %>);
}
</script>
</head>
<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="">
<input type="hidden" name="ltime" value=<% write(lltime); %> >
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
            <tr>
              <td colspan="2" align="left" class="cword09">&nbsp;LACP&nbsp<b><font color="#FF7F00">&gt;&gt;</font></b> &nbsp;<script>writemsg(<% write(lang); %>,"端口信息");</script></td>
            </tr>
        </table></td>
      </tr>     
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
		      <tr>
		        <td colspan="6"><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
		            <tr height="25">
		              <td colspan="7" align="left">&nbsp;<script>writemsg(<% write(lang); %>,"汇聚端口信息");</script></td>
		            </tr>
		            <tr><td width="12%"></td><td width="13%"></td><td width="13%"></td><td width="13%"></td><td width="13%"></td><td width="13%"></td><td width="13%"></td></tr>
		              <% showLacpPortsTable(); %>
		        </table></td>
		      </tr>
  
  			</table>
			</td></tr>
			  <tr>
			    <td align="center" height="35">
			    <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
		        &nbsp;
		        &nbsp;
		        </td>
				</tr>
        </table></td>
      </tr>
    </table></td>
  </tr> 
</table>
</td></tr>
<tr><td>
</td></tr></table>
</form>
</body>
</html>

