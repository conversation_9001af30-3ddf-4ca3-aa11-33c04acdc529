<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<title>端口管理</title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/msg.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script>

function MSTPPADVANCEINFO(v1,v2,v3,v4,v5,v6,v7,v8)
{
	document.write("<tr height='30'><td align=left' class='crons'>"+v1+"</td><td align=left' class='crons'>"+v2+"</td><td align=left' class='crons'>"+v3+"</td><td align=left' class='crons'>"+v4+"</td><td align=left' class='crons'>"+v5+"</td><td align=left' class='crons'>"+v6+"</td><td align=left' class='crons'>"+v7+"</td><td align=left' class='crons'>"+v8+"</td></tr>");
	return ;
}
function MSTPPTIMEINFO(v1,v2,v3,v4,v5,v6,v7,v8,v9)
{
	document.write("<tr height='30' ><td align='left width='11%' class='crons'>"+v1+"</td><td align='left width='11%' class='crons'>"+v2+"</td><td align='left width='11%' class='crons'>"+v3+"</td><td align='left width='11%' class='crons'>"+v4+"</td><td align='left width='11%' class='crons'>"+v5+"</td><td align='left width='11%' class='crons'>"+v6+"</td><td align='left width='11%' class='crons'>"+v7+"</td><td align='left width='11%' class='crons'>"+v8+"</td><td align='left width='11%' class='crons'>"+v9+"</td></tr>");
	return 0;
}
function MSTPPINSTINFO(v1,v2,v3,v4,v5,v6,v7,v8,v9)
{
	document.write("<tr height='30'><td align='left width='11%' class='crons'>"+v1+"</td><td align='left width='11%' class='crons'>"+v2+"</td><td align='left width='11%' class='crons'>"+v3+"</td><td>"+v4+"</td><td align='left width='11%' class='crons'>"+v5+"</td><td align='left width='11%' class='crons'>"+v6+"</td><td align='left width='11%' class='crons'>"+v7+"</td><td>"+v8+"</td><td align='left width='11%' class='crons'>"+v9+"</td></tr>");
	return 0;
}
function messageCheck()
{
	var hid = document.webForm;
			hid.submit();

	return true;
}
</script>
</head>
<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop();
</script>
<form  name="webForm" method="post" action="/goform/setMstpPortCfg">
<%  %>
<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
            <tr>
              <td colspan="2" align="left" class="cword09">&nbspLACP&nbsp<b><font color="#FF7F00">&gt;&gt;</font></b> LACP</td>
            </tr>
        </table></td>
      </tr>     
      <tr>
      
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
            <tr height="25">
              <td colspan="4" align="left">&nbsp;LACP配置</th>
            </tr>
            <tr height="25">
              <td colspan="2" align="left" class="crons">&nbsp;LACP系统优先级:
              	<input type="text" name="sys_priority"  >
               </td> 
            </tr>
            
            <tr height="25">
              <td  colspan="4" align="left" class="crons">&nbsp;创建静态汇聚组:</td>
              </td>
            </tr>
            <tr height="25">
              <td  width="25%" align="left" class="crons">&nbsp;汇聚组ID:
              	<input type="text" name="sys_priority"  >
               </td>
            </tr>
            
            <tr height="25">
              <td  colspan="4" align="left" class="crons" >&nbsp;端口选择:
              	全部选择<input type="checkbox" name="pAll" checked>
              </td>
            </tr> 
            <tr height="25" >
              <td  colspan="4" align="left" class="crons" >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p2" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p4" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p6" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p8" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p10" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p12" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p14" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p16" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p18" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p20" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p22" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p24" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </td>
            </tr> 
            <tr>
             <td  colspan="4" align="left" class="crons">
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;6&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;8&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;10&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;12&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;14&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;16&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;18&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;20&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;22&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;24
              </td>
            </tr> 
            <tr height="25">
              <td  colspan="4" align="left" class="crons" >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p1" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p3" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p5" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p7" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p9" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p11" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p13" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p15" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p17" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p19" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p21" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="checkbox" name="p23" checked>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </td>
            </tr>
            <tr >
              <td  colspan="4" align="left" class="crons">
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;5&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;7&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;9&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;11&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;13&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;15&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;17&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;19&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;21&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;23
              </td>
            </tr>  
			<tr><td colspan="2" align="middle" class="crons"> <input align="middle" name="modify"  type="button" class="button" value="创建" onClick="return messageCheck();"><input align="middle" name="modify"  type="button" class="button" value="修 改" onClick="return messageCheck();"> <input align="middle" name="modify"  type="button" class="button" value="删除" onClick="return messageCheck();"></td></tr>
        </table></td>
      </tr>
  	<tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">      
      <tr>     
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
            <tr height="25">
				<td width="100%">
					<table width="100%">
					<tr height="25"><td colspan="8" align="left" width="100%">静态汇聚组列表</th></tr>
					<tr><td align="left" width="10%" class="crons">端口</td><td align="left" width="5%" class="crons">bb</td><td align="left" width="15%" class="crons">cc</td><td align="left" width="15%" class="crons">dd</td><td align="left" width="15%" class="crons">ee</td><td align="left" width="15%" class="crons">ff</td><td align="left" width="15%" class="crons">gg</td><td align="left" width="10%" class="crons">hh</td></tr>
					<script>
					<% showMstpPortAdvanceTable(); %>
					</script>
					</table>
				</td>                     
            </tr>
        </table></td>
      </tr>      
    </td>
  </tr> 
  <tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">      
      <tr>     
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
            <tr height="25">
				<td width="100%">
					<table width="100%">
					<tr height="25"><td colspan="9" align="left" width="100%">时间配置列表</th></tr>
					<tr><td align="left" width="12%" class="crons">端口</td><td align="left" width="11%" class="crons">bb</td><td align="left" width="11%" class="crons">cc</td><td align="left" width="11%" class="crons">dd</td><td align="left" width="11%" class="crons">ee</td><td align="left" width="11%" class="crons">ff</td><td align="left" width="11%" class="crons">gg</td><td align="left" width="11%" class="crons">hh</td><td align="left" width="11%" class="crons">gg</td></tr>
					<script>
					<% showMstpPortTimeTable(); %>
					</script>
					</table>
				</td>                     
            </tr>
        </table></td>
      </tr>      
    </td>
  </tr> 
  <tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">      
      <tr>     
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
            <tr height="25">
				<td width="100%">
					<table width="100%">
					<tr height="25"><td colspan="2" align="left" width="100%">基本信息列表</th></tr>
					<tr><td align="left" width="15%" class="crons">端口</td><td align="left" width="17%" class="crons">bb</td><td align="left" width="17%" class="crons">cc</td><td align="left" width="17%" class="crons">dd</td><td align="left" width="17%" class="crons">ee</td><td align="left" width="17%" class="crons">ff</td></tr>
					<script>
					<% showMstpPortInstInfoTable(); %>
					</script>
					</table>
				</td>                     
            </tr>
        </table></td>
      </tr>      
    </td>
  </tr> 
  			</table>
			</td></tr>
        </table></td>
      </tr>
      <tr>
        <td height="8"></td>
      </tr>

    </table></td>
  </tr> 
</table>
</td></tr>
<tr><td>
</td></tr></table>
</form>
</body>
</html>

