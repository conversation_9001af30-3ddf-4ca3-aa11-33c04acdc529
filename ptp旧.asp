<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

    <head>
        <% var lltime,lang; getltime_lanflag(); %>
        <meta http-equiv="Content-Type" content="text/html; charset=utf8">
        <script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
        <script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
        <script src="js/alpinejs.min.js" defer></script>
        <title>
            <script>writemsg(<% write(lang); %>, "端口管理");</script>
        </title>
        <link href="css/display_web.css" rel="stylesheet" type="text/css"/>
        <style>
            .select1 {
                width: 197px;
            }

            /* PTP表格样式优化 */
            #table_ptp {
                font-size: 11px;
                width: 100%;
                table-layout: fixed;
            }

            #table_ptp th, #table_ptp td {
                padding: 3px 1px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                text-align: center;
            }

            /* 设置各列的具体宽度 */
            #table_ptp th:nth-child(1), #table_ptp td:nth-child(1) { width: 5%; }   /* 选择 */
            #table_ptp th:nth-child(2), #table_ptp td:nth-child(2) { width: 7%; }   /* 端口号 */
            #table_ptp th:nth-child(3), #table_ptp td:nth-child(3) { width: 8%; }   /* 映射通道号 */
            #table_ptp th:nth-child(4), #table_ptp td:nth-child(4) { width: 8%; }   /* 端口角色 */
            #table_ptp th:nth-child(5), #table_ptp td:nth-child(5) { width: 10%; }  /* 封装类型 */
            #table_ptp th:nth-child(6), #table_ptp td:nth-child(6) { width: 8%; }   /* 延迟机制 */
            #table_ptp th:nth-child(7), #table_ptp td:nth-child(7) { width: 8%; }   /* 使能状态 */
            #table_ptp th:nth-child(8), #table_ptp td:nth-child(8) { width: 8%; }   /* 时钟模式 */
            #table_ptp th:nth-child(9), #table_ptp td:nth-child(9) { width: 12%; }  /* 协议类型 */
            #table_ptp th:nth-child(10), #table_ptp td:nth-child(10) { width: 8%; } /* 接收封装 */
            #table_ptp th:nth-child(11), #table_ptp td:nth-child(11) { width: 12%; } /* 步模式 */
            #table_ptp th:nth-child(12), #table_ptp td:nth-child(12) { width: 6%; } /* 操作 */

            #table_ptp .text-success {
                color: #28a745;
                font-weight: bold;
            }

            #table_ptp .text-muted {
                color: #6c757d;
            }

            /* 表格容器样式 */
            .table-container {
                width: 100%;
                overflow-x: auto;
            }

            /* 针对较小屏幕的响应式调整 */
            @media (max-width: 1200px) {
                #table_ptp {
                    font-size: 10px;
                }
                #table_ptp th, #table_ptp td {
                    padding: 2px 1px;
                }
            }
        </style>
        <script language="JavaScript">
            var boardType = <% getSysCfg(); %>;

            // 获取PTP配置数据
            var ptpRetValue =
            <% var responseJsonStr; jw_get_portPtpConfig(); %>
            var ptpResponseStr = <% write(responseJsonStr); %>;

            // 打印返回值到控制台
            console.log('PTP配置返回值:', ptpRetValue)
            console.log('PTP响应字符串:', ptpResponseStr)

            // 处理PTP配置数据
            if (ptpResponseStr && ptpResponseStr.ptpConfig)
            {
                localStorage.setItem('ptpConfigList', JSON.stringify(ptpResponseStr.ptpConfig))
                console.log('PTP配置已存储到localStorage:', ptpResponseStr.ptpConfig)

                // 全局变量，供Alpine.js使用
                window.ptpConfigData = ptpResponseStr.ptpConfig
                console.log('PTP配置数据:', window.ptpConfigData)
            }
            else
            {
                console.log('PTP配置数据格式:', typeof ptpResponseStr, ptpResponseStr)
                window.ptpConfigData = []
            }
        </script>
        <script language="JavaScript">
            var tabActive = 'tab1'

            function isValidString(str) {
                var validc = "/'%`\"\\><"
                var a,
                    c
                for (i = 0; i < validc.length; i++)
                {
                    c = validc.substring(i, i + 1)
                    if (str.indexOf(c) > -1)
                    {
                        return true
                    }
                }
                return false
            }

            function AddOption(port_name) {
                var selectObject = document.getElementById("rl_port")
                var selectObject2 = document.getElementById("rpl_port")
                var selectObject3 = document.getElementById("rl_port2")
                var selectObject4 = document.getElementById("rpl_port2")
                var y = document.createElement('option')
                var y2 = document.createElement('option')
                var y3 = document.createElement('option')
                var y4 = document.createElement('option')
                y.text = port_name
                y.value = port_name
                y2.text = port_name
                y2.value = port_name
                y3.text = port_name
                y3.value = port_name
                y4.text = port_name
                y4.value = port_name
                try
                {
                    selectObject.add(y, null) // standards compliant3
                    selectObject2.add(y2, null) // standards compliant3
                    selectObject3.add(y3, null) // standards compliant3
                    selectObject4.add(y4, null) // standards compliant3
                }
                catch (ex)
                {
                    selectObject.add(y) // IE only
                    selectObject2.add(y2) // IE only
                    selectObject3.add(y3) // IE only
                    selectObject4.add(y4) // IE only
                }
            }

            function checkavidhybrid(arguments) {
                var arr = new Array()
                var arry = new Array()
                arry = arguments.split("")
                arr = arguments.split(",")
                var i
                for (i = 0; i < arry.length; i++)
                {
                    if (arry[i] != "," && isNaN(arry[i]))
                    {
                        return false
                    }
                }

                for (i = 0; i < arr.length; i++)
                {
                    if (isNaN(arr[i]) || (arr[i] < 1 || arr[i] > 4094))
                    {
                        return false
                    }
                }
                return true
            }

            function ring_en(ring, erps) {
                var tf = document.erps_setting
                var ring_id = ring
                var erps_id = erps

                tf.ring_id.value = ring_id
                tf.erps_id.value = erps_id
                tf.tabName.value = tabActive
                tf.action = "/goform/ringenable"
                tf.submit()

            }

            function ring_dis(ring, erps) {
                var tf = document.erps_setting
                var ring_id = ring
                var erps_id = erps

                tf.ring_id.value = ring_id
                tf.erps_id.value = erps_id
                tf.tabName.value = tabActive
                tf.action = "/goform/ringdisable"
                tf.submit()
            }

            function ring_del(ring, erps) {
                var tf = document.erps_setting
                var ring_id = ring
                var erps_id = erps

                tf.ring_id.value = ring_id
                tf.erps_id.value = erps_id
                tf.tabName.value = tabActive
                tf.action = "/goform/ringdel"
                tf.submit()
            }

            function checkingerps() {
                var tf = document.erps_setting
                var erps_id = document.getElementById("erps_id2").value

                if (erps_id == "")
                {
                    alert(putmsg(<% write(lang); %>, "配置不能为空!"))
                    return
                }

                if (erps_id > 8 || erps_id < 1)
                {
                    alert(putmsg(<% write(lang); %>, "erps id invalid (1-8)!"))
                    return
                }

                tf.action = "/goform/ringmodifyerps"
                tf.submit()

            }

            function checking() {
                var tf = document.ptp_setting
                //tf.action = "/goform/ringmodify";
                tf.tabName.value = "tab1"
                console.log(ptpData, 123)
                //tf.submit();

            }

            // 测试函数：手动获取和打印PTP配置
            function testPtpConfig() {
                console.log('=== 手动测试PTP配置获取 ===')
                console.log('ptpRetValue:', ptpRetValue)
                console.log('ptpResponseStr:', ptpResponseStr)

                // 检查localStorage中的数据
                var storedPtpConfig = localStorage.getItem('ptpConfigList')
                if (storedPtpConfig)
                {
                    console.log('localStorage中的PTP配置:', JSON.parse(storedPtpConfig))
                }
                else
                {
                    console.log('localStorage中没有找到PTP配置数据')
                }

                // 如果响应数据存在，尝试解析
                if (ptpResponseStr)
                {
                    try
                    {
                        var parsedData = typeof ptpResponseStr === 'string' ? JSON.parse(ptpResponseStr) : ptpResponseStr
                        console.log('解析后的PTP数据:', parsedData)
                    }
                    catch (e)
                    {
                        console.log('解析PTP数据时出错:', e)
                        console.log('原始数据类型:', typeof ptpResponseStr)
                        console.log('原始数据内容:', ptpResponseStr)
                    }
                }
            }

            // PTP配置操作函数
            function selectPtpConfig(item, event) {
                console.log('选择PTP配置:', item, event.target.checked)
                // 可以在这里处理选择逻辑，比如填充表单
                if (event.target.checked)
                {
                    fillPtpForm(item)
                }
            }

            function deletePtpConfig(item) {
                console.log('删除PTP配置:', item)
                // 确认删除
                if (confirm('确定要删除端口' + item.portNum + ' 的PTP配置吗？'))
                {
                    console.log('执行删除操作 - 端口:', item.portNum)

                    // 构建删除参数，格式与快速配置相同，但使用portNum替代ptpConfig
                    var deleteConfig = {
                        pageName: "ptp.asp",
                        portNum: item.portNum
                    }

                    console.log('发送删除请求:', deleteConfig)

                    // 使用param1传递删除参数
                    var tf = document.ptp_setting
                    tf.param1.value = JSON.stringify(deleteConfig)
                    tf.action = "/goform/jw_del_portPtpConfig"
                    tf.submit()
                }
            }

            function fillPtpForm(item) {
                console.log('填充PTP表单:', item)
                // 使用Alpine.js的数据绑定来填充表单
                // 这里需要触发Alpine.js的数据更新
                if (window.Alpine && window.Alpine.store)
                {
                    // 如果使用Alpine.js store
                    window.Alpine.store('ptpForm', item)
                }
                else
                {
                    // 直接更新表单字段
                    document.getElementById('portNum').value = item.portNum
                    document.getElementById('mapChn').value = item.mapChn
                    // ... 更多字段
                }
            }

            // 快速配置PTP - 使用动态参数并检查重复
            function applyPtpConfig() {
                console.log('快速配置PTP')

                // 获取表单数据
                var formData = {
                    portNum: parseInt(document.getElementById('portNum').value) || 0,
                    mapChn: parseInt(document.getElementById('mapChn').value) || 0,
                    portType: parseInt(document.querySelector('[x-model="portType"]').value) || 0,
                    encapType: parseInt(document.querySelector('[x-model="encapType"]').value) || 0,
                    delayMechanism: parseInt(document.querySelector('[x-model="delayMechanism"]').value) || 0
                }

                // 验证必填字段
                if (!formData.portNum || formData.portNum < 1 || formData.portNum > 28)
                {
                    alert('请输入有效的端口号 (1-28)')
                    return
                }

                if (formData.mapChn < 0 || formData.mapChn > 31)
                {
                    alert('请输入有效的映射通道号 (0-31)')
                    return
                }

                // 检查端口号和映射通道号是否与现有配置重复
                if (window.ptpConfigData && window.ptpConfigData.length > 0) {
                    var existingPorts = window.ptpConfigData.map(item => item.portNum)
                    var existingMapChns = window.ptpConfigData.map(item => item.mapChn)

                    if (existingPorts.includes(formData.portNum)) {
                        alert('端口号 ' + formData.portNum + ' 已经配置过PTP，请选择其他端口号')
                        return
                    }

                    if (existingMapChns.includes(formData.mapChn)) {
                        alert('映射通道号 ' + formData.mapChn + ' 已经被使用，请选择其他通道号')
                        return
                    }
                }

                // 使用动态参数构建PTP配置对象
                var ptpConfig = {
                    pageName: "ptp.asp",
                    ptpConfig: [{
                        "portNum": formData.portNum,
                        "enable": 1,  // 默认启用
                        "mapChn": formData.mapChn,
                        "mapChnEnable": 1,  // 默认启用映射通道
                        "portType": formData.portType,
                        "tranSpec": 0,  // 默认IEEE 1588
                        "encapType": formData.encapType,
                        "clockType": 0,  // 默认OC
                        "delayMechanism": formData.delayMechanism,
                        "stepMode": [0, 0],  // 默认ONE-STEP
                        "pdelayTcEn": 0,
                        "rxBypassEn": 0,
                        "rxEncapType": formData.encapType,  // 与发送封装类型相同
                        "delayValue": [0, 0, 0, 0],
                        "ptpHead": {
                            "domainNumber": 0,
                            "flagField": 0,
                            "sourcePortIdentity": {
                                "clock_id[0]": 0,
                                "clock_id[1]": 0,
                                "port_number": formData.portNum
                            },
                            "msgInterval": [0, 0, 0, 0, 0],
                            "ttl": [0, 0]
                        },
                        "dMac": [
                            [0, 0, 0, 0, 0, 0],
                            [0, 0, 0, 0, 0, 0],
                            [0, 0, 0, 0, 0, 0],
                            [0, 0, 0, 0, 0, 0]
                        ],
                        "dMacmode": [0, 0, 0, 0],
                        "dIp": [],
                        "dIpmode": [0, 0, 0, 0],
                        "unicastFlag": [0, 0, 0, 0],
                        "selfPortIdentityValue": {
                            "clock_id[0]": 0,
                            "clock_id[1]": 0,
                            "port_number": formData.portNum
                        },
                        "announceReceiptTimeout": {
                            "timeoutSecond": 0,
                            "timeoutMicrosecond": 0
                        },
                        "txInterval": [
                            {
                                "txIntervalSecond": 0,
                                "txIntervalMicrosecond": 0
                            },
                            {
                                "txIntervalSecond": 0,
                                "txIntervalMicrosecond": 0
                            },
                            {
                                "txIntervalSecond": 0,
                                "txIntervalMicrosecond": 0
                            }
                        ],
                        "pktCheck": {
                            "rxVlanCheckEn": 0,
                            "rxVlanCheckValue": 0,
                            "rxDipCheckEn": 0,
                            "rxDipCheckValue": "0.0.0.0",
                            "rxDmacCheckValue": [0, 0, 0, 0, 0, 0],
                            "domainCheckEn": [0, 0, 0],
                            "domainValue": [0, 0, 0],
                            "transportCheckEn": 0,
                            "transportValue": 0,
                            "portIdentityCheckEn": [0, 0, 0],
                            "portIdentityValue": [
                                {
                                    "clock_id[0]": 0,
                                    "clock_id[1]": 0,
                                    "port_number": 0
                                },
                                {
                                    "clock_id[0]": 0,
                                    "clock_id[1]": 0,
                                    "port_number": 0
                                }
                            ]
                        },
                        "announce": {
                            "currentUtcOffset": 0,
                            "priority1": 0,
                            "priority2": 0,
                            "clockQuality": {
                                "clockClass": 0,
                                "clockAccuracy": 0,
                                "clockVariance": 0
                            },
                            "identity1": 0,
                            "identity2": 0,
                            "stepsRemoved": 0,
                            "timeSource": 0
                        }
                    }]
                }

                console.log('发送PTP配置:', ptpConfig)

                // 参考mirror.asp的提交方式，使用param1传递JSON
                var tf = document.ptp_setting
                tf.param1.value = JSON.stringify(ptpConfig)
                tf.action = "/goform/jw_set_portPtpConfig"
                tf.submit()
            }

            function fastchecking() {
                var tf = document.erps_setting
                var traffic_vlan = document.getElementById("traffic_vlan2").value

                if (traffic_vlan == "")
                {
                    alert(putmsg(<% write(lang); %>, "配置不能为空!"))
                    return
                }

                if (!checkavidhybrid(traffic_vlan))
                {
                    alert(putmsg(<% write(lang); %>, "traffic vlan格式必须为: X,X,X,X . "))
                    return 0
                }

                tf.action = "/goform/fasterps"
                tf.submit()

            }

            function addToERPS(obj) {
                var tmpPos
                var trid = "tr_" + obj.value
                var trobj = document.getElementById(trid)

                if (obj.checked)
                {
                    document.getElementById("erps_id2").value = trobj.cells[1].innerHTML
                    if (trobj.cells[2].innerHTML == "none")
                    {
                        document.getElementById("node_role").value = "none-interconnection"
                    }
                    else
                    {
                        document.getElementById("node_role").value = "interconnection"
                    }
                    document.getElementById("ring_id").value = trobj.cells[3].innerHTML
                    document.getElementById("ring_enable").value = trobj.cells[4].innerHTML
                    document.getElementById("ring_mode").value = trobj.cells[5].innerHTML
                    document.getElementById("node_mode").value = trobj.cells[6].innerHTML
                    document.getElementById("raps_vlan").value = trobj.cells[8].innerHTML
                    document.getElementById("traffic_vlan").value = trobj.cells[9].innerHTML

                    tmpPos = trobj.cells[10].innerHTML.indexOf(':')
                    if (tmpPos == -1)
                    {
                        document.getElementById("rpl_port").value = trobj.cells[10].innerHTML
                    }
                    else
                    {
                        document.getElementById("rpl_port").value = trobj.cells[10].innerHTML.substring(0, tmpPos)
                    }

                    tmpPos = trobj.cells[11].innerHTML.indexOf(':')
                    if (tmpPos == -1)
                    {
                        document.getElementById("rl_port").value = trobj.cells[11].innerHTML
                    }
                    else
                    {
                        document.getElementById("rl_port").value = trobj.cells[11].innerHTML.substring(0, tmpPos)
                    }

                    document.getElementById("guartime").value = trobj.cells[12].innerHTML
                    document.getElementById("wtrtime").value = trobj.cells[13].innerHTML
                }
                return true
            }

            /*display function*/
            function P(erps, inter, ring, ringen, ringm, nodem, nodes, rapsv, trafficv, rplport, rlport, guardt, wtrt) {

                var narr = 16
                var arr = ""
                var speed = ""
                var Desc = ""
                var tbtd
                var i
                var opt
                var tbtr = document.getElementById("table_erps").insertRow(-1)

                tbtr.classname = "td7"
                tbtr.height = "30"

                tbtr.setAttribute("height", "30")
                tbtr.setAttribute("class", "td7")
                tbtr.setAttribute("className", "td7")
                tbtr.setAttribute("id", "tr_" + ring)

                /*Cycle respectively and setting attributes*/
                for (i = 0; i < narr; i++)
                {
                    tbtd = document.createElement("td")

                    tbtd.align = "center"
                    tbtd.setAttribute("class", "td2")
                    tbtd.setAttribute("className", "td2")
                    tbtr.appendChild(tbtd)
                }

                //ring,erps,ringen,ringm,nodem,rapsv,trafficv,rplport,rlport)

                /*display*/
                let index = document.getElementById("table_erps").rows.length - 2
                tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"checkbox_index\" value=\"" + index + "\" onchange=\"addToERPS(this)\"/>"

                tbtr.cells[1].innerHTML = erps
                tbtr.cells[2].innerHTML = inter
                tbtr.cells[3].innerHTML = ring
                tbtr.cells[4].innerHTML = ringen
                tbtr.cells[5].innerHTML = ringm
                tbtr.cells[6].innerHTML = nodem
                tbtr.cells[7].innerHTML = nodes
                tbtr.cells[8].innerHTML = rapsv
                tbtr.cells[9].innerHTML = trafficv
                tbtr.cells[10].innerHTML = rplport
                tbtr.cells[11].innerHTML = rlport
                tbtr.cells[12].innerHTML = guardt
                tbtr.cells[13].innerHTML = wtrt
                if (ringen != "enable")
                {
                    tbtr.cells[14].innerHTML = " <input type='button' name='button2' id='button2' class='botton_under_line' value='启用' onclick='ring_en(" + ring + "," + erps + ")'>"
                }
                else
                {
                    tbtr.cells[14].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='关闭' onclick='ring_dis(" + ring + "," + erps + ")'>"
                }

                tbtr.cells[15].innerHTML = " <input type='button' name='button4' id='button4' class='botton_under_line' value='删除' onclick='ring_del(" + ring + "," + erps + ")'>"

            }

            function refreshpage() {
                location.href = 'ptp.asp?ltime=' + <% write(lltime); %> +'&tab=' + tabActive
            }

            function changebgcolor_name(value) {
                var tab = document.getElementById(value)
                var len = tab.rows.length

                if (value == 'table_erps')
                {

                    for (var i = 0; i < len; i++)
                    {
                        var lencol = tab.rows[i].cells.length
                        for (var j = 0; j < lencol; j++)
                        {
                            if (j % 2 == 1)
                            {

                                // tab.rows[i].cells[j].bgColor = "F9F9F9";
                                tab.rows[i].cells[j].className = " all_tables"
                            }
                            else
                            {
                                // tab.rows[i].cells[j].bgColor = "FFF";
                                tab.rows[i].cells[j].className = "all_tables"
                            }

                        }

                    }
                }
                else
                {
                    for (var i = 0; i < len; i++)
                    {
                        var lencol = tab.rows[i].cells.length
                        for (var j = 0; j < lencol; j++)
                        {
                            if (j % 2 == 1)
                            {

                                // tab.rows[i].cells[j].bgColor = "F9F9F9";
                                tab.rows[i].cells[j].className = " all_tables"
                            }
                            else
                            {
                                // tab.rows[i].cells[j].bgColor = "FFF";
                                tab.rows[i].cells[j].className = "all_tables all_table3-right"
                            }

                        }

                    }
                }

            }

            function changebgcolor() {
                var tab = document.all.table1
                var len = tab.rows.length
                for (var i = 0; i < len; i++)
                {
                    var lencol = tab.rows[i].cells.length
                    for (var j = 0; j < lencol; j++)
                    {
                        if (j % 2 == 1)
                        {

                            // tab.rows[i].cells[j].bgColor = "F9F9F9";
                            tab.rows[i].cells[j].className = "all_tables"
                        }
                        else
                        {
                            // tab.rows[i].cells[j].bgColor = "FFFFFF";
                            tab.rows[i].cells[j].className = "all_tables all_table3-right"
                        }

                    }

                }
            }

            function getUrlParamTab() {
                let urlSearch = window.location.search
                const search = new URLSearchParams(urlSearch)

                const params = Object.fromEntries(search.entries())

                try
                {
                    if (params.tab)
                    {
                        tabActive = params.tab
                        return params.tab
                    }
                }
                catch
                {
                }
                return 'tab1'
            }

            var ptpData1 = {
                portType: 1,
                portNum: 1,
                enable: 1,
                mapChn: 5,
                mapChnEnable: 1,//1:enable,0:disable
                tranSpec: 0,  // 协议类型：0-IEEE 1588，1-IEEE 802.1AS
                encapType: 1,
                clockType: 0,// 时钟模式：0-OC（普通时钟），1-BC（边界时钟），2-TC（透明时钟），3-MIX-CLK（混合模式）  ----- portType 为MASTER 时可以选择BC，MIX-CLK
                delayMechanism: 0,
                stepMode: [0, 0],  // 步模式：[SYNC包, PDELAY-RESP包]（0-ONE-STEP，1-TWO-STEP）
                pdelayTcEn: 0,
                delayValue1: 0,
                delayValue: 0,
                dMacmode: 0,
                dIpmode: 0,
                dIp1: "",
                dip: [],
                unicastFlag: 0,
                ptpHead: {
                    domainNumber: 0,
                    flagField: 0,
                    sourcePortIdentity: {
                        clock_id: '',
                        clock_id1: '',
                        port_number: 0,
                    },
                    msgInterval: 10,
                    ttl: 64,
                    ttl1: 64          // TTL配置：[PDELAY, NON_PDELAY]（数据包生存时间）                         ----- encapType为2，3时显示

                },
                selfPortIdentityValue: {
                    clock_id: '',
                    clock_id1: '',
                    port_number: 400,
                },
                announceReceiptTimeout: {
                    timeoutSecond: 3,
                    timeoutMicrosecond: 500
                },
                txInterval: [
                    {
                        txIntervalSecond: 1,
                        txIntervalMicrosecond: 0
                    }
                ],
                txInterval1: 1,
                rxBypassEn: 0,
                rxEncapType: 0,           // 接收封装类型：0-ETH，1-IPV4（检查报文封装格式）
                pktCheck: {               // 接收报文检查配置
                    rxVlanCheckEn: 1,       // VLAN检查使能：1-启用，0-禁用
                    rxVlanCheckValue: 100,  // VLAN值：取值范围[1-4094]（匹配报文VLAN标签）
                    rxDipCheckEn: 1,        // 目的IP检查使能：1-启用，0-禁用                   ---- rxEncapType为1时显示
                    rxDipCheckValue: 0x0A000005, // 目的IP：********（点分十进制）              ---- rxEncapType为1时显示
                    rxDmacCheckEn: 1,       // 目的MAC检查使能：1-启用，0-禁用
                    rxDmacCheckValue: [], // 目的MAC地址
                    rxDmacCheckValue1: '', // 目的MAC地址
                    domainCheckEn: [
                        1,
                        1,
                        0
                    ], // 域名检查使能：[NON-REQ, REQ, ANNOUNCE]包（1-启用，0-禁用）
                    domainValue: '',   // 域名检查值：对应三种包类型的合法域编号
                    transportCheckEn: 1,     // 传输层检查使能：1-启用（检查协议类型）
                    portIdentityCheckEn: [
                        1,
                        0,
                        1
                    ], // 端口标识检查使能：[PDELAY, SYNC, ANNOUNCE]包
                    portIdentityValue: [     // 合法端口标识列表（对应检查使能的包类型）
                    ]
                }, // 时间同步配置 --------master模式下有效,slave模式下隐藏
                announce: {              // Announce消息参数配置                               
                    currentUtcOffset: 0,   // UTC偏移：取值范围[0-65535]（时钟与UTC的时间差）
                    priority1: 10,         // 优先级1：取值范围[0-255]（主时钟优先级）
                    priority2: 20,         // 优先级2：取值范围[0-255]（备用优先级）
                    clockQuality: {        // 时钟质量参数
                        clockClass: 64,      // 时钟等级：取值范围[0-255]（如64表示普通时钟）
                        clockAccuracy: 100,  // 时钟精度：取值范围[0-255]（单位：ppm）
                        clockVariance: 200   // 时钟稳定度：取值范围[0-65535]（长期频率偏差）
                    },
                    identity1: 0x11111111, // 时钟ID高位：与identity2组成完整8字节ID
                    identity2: 0x22222222, // 时钟ID低位
                    stepsRemoved: 5,       // 跳数：取值范围[0-65535]（距离时间源的路径长度）
                    timeSource: 1          // 时间源：取值范围[0-255]（如1表示GPS）
                },
            }
            document.addEventListener('alpine:init', () => {
                Alpine.data('ptpData', () => ({
                    // 数据、方法、生命周期逻辑...
                    ...ptpData1,
                    init() {
                        this.$watch('dIp1', (value) => {
                            this.dip = this.dIp1.split(',')
                        })
                        this.$watch('delayValue1', (value) => {
                            this.delayValue = [this.delayValue1]
                            console.log(this.delayValue, 'this.delayValue')
                        })
                        this.$watch('selfPortIdentityValue.clock_id1', (value) => {
                            this.selfPortIdentityValue.clock_id = this.selfPortIdentityValue.clock_id1.split(',')
                            console.log(this.selfPortIdentityValue.clock_id, 'this.selfPortIdentityValue.clock_id')
                        })
                        this.$watch('ptpHead.sourcePortIdentity.clock_id1', (value) => {
                            this.ptpHead.sourcePortIdentity.clock_id = value.split(',')
                        })
                        this.$watch('txInterval1', (value) => {
                            this.txInterval = [
                                {
                                    txIntervalSecond: this.txInterval1,
                                    txIntervalMicrosecond: this.txInterval + '00'
                                }
                            ]
                        })
                        this.$watch('pktCheck.rxDmacCheckValue1', (value) => {
                            this.pktCheck.rxDmacCheckValue = [value]
                        })
                    },
                }))
            })
        </SCRIPT>
    </head>

    <body onload="" x-data="{active:'tab1'}" x-init="active=getUrlParamTab()"><br>
        <% var authmode; checkCurMode(); %>
        <script>
            checktop(<% write(lang); %>)

            function changePortType() {
                console.log(ptpData1.portType)
            }

            function changeEncapType() {

            }
        </script>
        <div>
            <ul class="tabmenu">
                <li id="tab1" :class="active==='tab1'?'tab':'tab-disable'">
                    <a herf="#" x-on:click="active='tab1',tabActive='tab1'">PTP快速设置</a>
                </li>

            </ul>
        </div>
        <form name="ptp_setting" method="POST" action="" class="formContain" style="min-height: 560px;">
            <input type="hidden" name="left_menu_id" value="@left_menu_id#">
            <input type="hidden" name="ltime" value=<% write(lltime); %>>
            <input type="hidden" name="lastts" value=<% write(serverts); %>>
            <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
            <!-- <input type="hidden" name="erps_id"> -->
            <input type="hidden" name="tabName" id="tabName">
            <table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
                <tr>
                    <td>
                        <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
                            <tr>
                                <td valign="top">
                                    <table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"
                                           class="cword09">
                                        <tr>
                                            <td>
                                                <table width="100%" align="center" border="0" cellspacing="0"
                                                       cellpadding="0">
                                                    <tr>
                                                        <td height="30px">
                                                            <font size="5" color="#404040">
                                                                <div class="bot">PTP快速设置</div>
                                                            </font>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>


                        </table>
                    </td>
                </tr>
            </table>
            <input type="hidden" name="todo" value="save">
            <INPUT type="hidden" name="this_file" value="ptp.asp">
            <INPUT type="hidden" name="next_file" value="ptp.asp">
            <input type="hidden" name="message" value="@msg_text#">
            <input type="hidden" name="param1" id="param1">
            <div class="" x-show="active==='tab1'" x-data="ptpData">
                <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table2"
                       class="tablebord">

                    <tr height="30">
                        <td width="15%" align="left" class="td7">&nbsp;
                            <script>writemsg(<% write(lang); %>, "端口号:")</script>
                        </td>
                        <td width="35%" align="left" class="td7">
                            <input id="portNum" type="text" name="portNum" class="input_board4"
                                   x-model="portNum"/>&nbsp;
                            <script>writemsg(<% write(lang); %>, "(1-28)")</script>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;
                            <script> writemsg(<% write(lang); %>, "映射通道号:") </script>
                        </td>
                        <td width="35%" align="left" class="td7">
                            <input name="mapChn" type="text" class="input_board4" id="mapChn" x-model="mapChn"/>&nbsp;
                            <script>writemsg(<% write(lang); %>, "(0-31)")</script>
                        </td>
                    </tr>

                    <tr height="30">
                        <td width="15%" align="left" class="td7">&nbsp;
                            <script>writemsg(<% write(lang); %>, "端口角色:")</script>
                        </td>
                        <td class="td7" width="35%" align="left">
                            <select x-model="portType" @change="changePortType">
                                <option value="0">SLAVE</option>
                                <option value="1">MASTER</option>
                            </select>
                        </td>
                        <td width="15%" align="left" class="td7">&nbsp;
                            封装类型:
                        </td>
                        <td class="all_tables" width="35%" align="left" class="td7">
                            <select x-model="encapType" @change="changeEncapType">
                                <option value="0">ETH</option>
                                <option value="1">ETH&VLAN</option>
                                <option value="2">IPV4</option>
                                <option value="3">IPV4&VLAN</option>
                            </select>
                        </td>
                    </tr>

                    <tr height="30">
                        <td width="15%" align="left" class="td7">&nbsp;
                            延迟机制:
                        </td>
                        <td class="all_tables" width="35%" align="left" class="td7">
                            <select x-model="delayMechanism">
                                <option value="0">P2P</option>
                                <option value="1">E2E</option>
                            </select>
                        </td>
                        <td width="35%" align="left" class="td7" colspan="2">
                            <!-- 空白区域 -->
                        </td>
                    </tr>

                    <tr height="30">
                        <td colspan="4" align="center" class="td7" style="text-align: center;">
                            <script>writebutton(<% write(authmode); %>, <% write(lang); %>, "快速配置", "buttons_add", "button", "modify4", "applyPtpConfig()")</script>
                        </td>
                    </tr>
                </table>
            </div>
            <div style="margin-top: 20px;" x-data="{ ptpList: window.ptpConfigData || [] }" x-init="console.log('PTP列表数据:', ptpList)">
                <font size="5" color="#404040">
                    <div class="bot">PTP信息</div>
                </font>

                <div class="table-container">
                    <table width="100%" border="0" cellpadding="0" cellspacing="0" class="tablebord " id="table_ptp">
                    <thead height="30" align="center" class="td7">
                        <th align="center" class="td2">选择</th>
                        <th align="center" class="td2">端口号</th>
                        <th align="center" class="td2">映射通道</th>
                        <th align="center" class="td2">端口角色</th>
                        <th align="center" class="td2">封装类型</th>
                        <th align="center" class="td2">延迟机制</th>
                        <th align="center" class="td2">使能状态</th>
                        <th align="center" class="td2">时钟模式</th>
                        <th align="center" class="td2">协议类型</th>
                        <th align="center" class="td2">接收封装</th>
                        <th align="center" class="td2">步模式</th>
                        <th align="center" class="td2">操作</th>
                    </thead>
                    <tbody>
                        <template x-for="(item, index) in ptpList" :key="index">
                            <tr height="30" class="td7" :id="'tr_ptp_' + item.portNum">
                                <td align="center" class="td2">
                                    <input type="checkbox" :name="'checkbox_ptp_' + index" :value="index"
                                           @change="selectPtpConfig(item, $event)"/>
                                </td>
                                <td align="center" class="td2" x-text="item.portNum"></td>
                                <td align="center" class="td2" x-text="item.mapChn"></td>
                                <td align="center" class="td2" x-text="item.portType ? 'MASTER' : 'SLAVE'"></td>
                                <td align="center" class="td2">
                                    <span x-text="['ETH', 'ETH&V', 'IPV4', 'IPV4&V'][item.encapType] || 'ETH'"></span>
                                </td>
                                <td align="center" class="td2" x-text="item.delayMechanism ? 'E2E' : 'P2P'"></td>
                                <td align="center" class="td2">
                                    <span x-text="item.enable ? '启用' : '禁用'"
                                          :class="item.enable ? 'text-success' : 'text-muted'"></span>
                                </td>
                                <td align="center" class="td2">
                                    <span x-text="['OC', 'BC', 'TC', 'MIX'][item.clockType] || 'OC'"></span>
                                </td>
                                <td align="center" class="td2">
                                    <span x-text="item.tranSpec ? '802.1AS' : '1588'"></span>
                                </td>
                                <td align="center" class="td2">
                                    <span x-text="['ETH', 'IPV4'][item.rxEncapType] || 'ETH'"></span>
                                </td>
                                <td align="center" class="td2">
                                    <span x-text="item.stepMode && item.stepMode.length >= 2 ?
                                        (item.stepMode[0] ? '2S' : '1S') + '/' +
                                        (item.stepMode[1] ? '2S' : '1S') : 'N/A'"></span>
                                </td>
                                <td align="center" class="td2">
                                    <button type="button" class="botton_under_line"
                                            @click="deletePtpConfig(item)"
                                            style="color: white; background-color: #dc3545;">删除
                                    </button>
                                </td>
                            </tr>
                        </template>
                        <tr x-show="ptpList.length === 0">
                            <td colspan="12" align="center" class="td2" style="padding: 20px; color: #999;">
                                暂无PTP配置数据
                            </td>
                        </tr>
                    </tbody>
                </table>
                </div>
                <div align="center" style="padding: 5px;">
                    <script>writebutton(1, <% write(lang); %>, "刷  新", "button", "button", "Refresh", "refreshpage()")</script>
                </div>

            </div>
        </form>
        <script>
            <% if (errorcode != "") { write_errorcode(errorcode); } %>
            // changebgcolor();
            changebgcolor_name("table2")
            changebgcolor_name("table3")
            changebgcolor_name("table_ptp")
        </script>
    </body>

</html>