<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<title>&nbsp;<script>writemsg(<% write(lang); %>,"端口光功率");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
</script>

<script language="JavaScript" type="text/JavaScript">

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){
			
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

var allinfostr = [<% showSpfinfo(); %>];


function writeList()
{
		var tempBuf = "";
		var i;
		var j;
		//alert(allinfostr.length);
		for(i=0;i<allinfostr.length;i++)
		{
				var allinfo = allinfostr[i].split(",");
				tempBuf += "<tr class='tables_all1'>";	
					//	alert(allinfo.length);

				for(j=0;j<allinfo.length;j++)
			  {
						tempBuf += "<td class='inputsyslog1' >";
						tempBuf += allinfo[j];
						tempBuf += "</td>";
				}	
				
				tempBuf += "</tr>"	;
		}
		tempBuf += "<tr class='tables_all1'>";
		tempBuf += "<td  align='left' colspan='5' class='inputsyslog1' >";
		//tempBuf += "Notice: 光功率 dBm = -40+10*log10(mW)";
		tempBuf += "</td>";
		tempBuf += "</tr>"	;
				document.write(tempBuf);

}

function display(){
changebgcolor();
//displayForm();
}

</script>


</HEAD>

<BODY  onload="" >
<br>

<%  var authmode; checkCurMode(); %>

  <form id="form1" name="form1" method="post" action="" class="formContain" >

 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="30px" ><font size="5" color="#404040"><div class="bot">光纤模块信息</div></font></td></tr>
 </table>
 
<!--
<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
  <tr>
    <td  class=Tablelist id=tabs name=tabs>端口光功率</td>
    <td   class="tablenew" id=tabs name=tabs><div align="right"></div></td>
  </tr>
</table>
-->
<table width="98%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord1" id="table1">
    <TR  align="center" height=22 class="partition bg1">
      <TD width="15%"  nowrap >端口</TD>
      <TD width="25%"   nowrap >温度(℃)</TD>
      <TD width="20%"   nowrap >发送光功率(dBm)</TD>
      <TD width="20%"   nowrap >接收光功率(dBm)</TD>
      <TD width="20%"   nowrap >电压(mV)</TD>
    </TR>
 
 
<script language="javascript">

writeList();
</script>
    
  </table>
</form>

<script>
changebgcolor();
</script>

<style>



</style>


</BODY>
</HTML>




