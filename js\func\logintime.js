//  shared  messages - used in more than 1 file
var timeHandle;
var timeout=15*60*1000;
var language="ch";
function checktop(lang)
{
	language=lang;
	if (top.location == location)
	{
		top.location.href = "login.asp";
	}
}
//checktop(language);
function Keydown()
{
let now=Date.now()

    let lasttimeHandle=localStorage.getItem('lasttimeHandle')
   
   if(lasttimeHandle){
        if(lasttimeHandle<now){
          localStorage.clear('lasttimeHandle')
          		alert("您超时了，请重新登录!");	

              location.href = "login_ch2.asp";
          }else{
          	localStorage.setItem("lasttimeHandle",timeout+now );
          }
   }else{
   localStorage.setItem("lasttimeHandle",timeout+now );
   }
}
function Click()
{
 
	   let lasttimeHandle=localStorage.getItem('lasttimeHandle')
    let now=Date.now()
   if(lasttimeHandle){
        if(lasttimeHandle<now){
          localStorage.clear('lasttimeHandle')
          		alert("您超时了，请重新登录!");	

              location.href = "login_ch2.asp";
          }else{
          	localStorage.setItem("lasttimeHandle",timeout+now );
          }
   }else{
   localStorage.setItem("lasttimeHandle",timeout+now );
   }
}
function Check_click_key_mouse()
{
    document.onkeydown=Keydown;
    document.onclick=Click;
    
}

function overtime()
{
	//if(language=="en")
		//alert("Idle timeout. Please login again");	
	//else
		alert("您超时了，请重新登录!");	
	//parent.frames[2].menuForm.submit();
	//top.location.href="login_"+language+".asp";
  location.href = "login_ch2.asp";
}


function setSysOverTime(obj)
{
  let lasttimeHandle=localStorage.getItem('lasttimeHandle')
  let now=Date.now()
  if(lasttimeHandle){
  console.log(lasttimeHandle,now)
          if(lasttimeHandle<now){
          localStorage.clear('lasttimeHandle')
          overtime()
                 
          }
  }
}



setSysOverTime();
Check_click_key_mouse();




