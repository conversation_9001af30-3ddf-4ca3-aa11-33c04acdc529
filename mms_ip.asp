<HTML>
<HEAD>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<TITLE>page</TITLE>
<META http-equiv=Content-Type content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script>
<% var outbandip, Allipaddr,errorcode;getIpAddr(); %>


function tdIpCheck(textValue)
{
    re1=/(\d+)(\W)(\d+)(\W)(\d+)(\W)(\d+)/
    Check=textValue.search(re1);
    if(Check==-1)
    {
        return false;
    }
    else
    {
        ipSplit=textValue.split('.');
        if(ipSplit.length!=4)
        {
            return false;
        }

        for(i=0; i<ipSplit.length; i++)
        {
            if(isNaN(ipSplit[i]))  return false;
			if(ipSplit[i].length > 1 && ipSplit[i].charAt(0) == "0") return false;
        }

        for(i=0; i<ipSplit.length; i++)
        {
            if(ipSplit[i] > 255)
            {
                return false;
            }
            if(ipSplit[i] < 0)
            {
                return false;
            }
        }
        if((ipSplit[0]==255) && (ipSplit[1]==255) && (ipSplit[2]==255) && (ipSplit[3]==255))
        {
            return false;
        }

        if((ipSplit[0]==0) || (ipSplit[0]==127) || (ipSplit[3]==0) || (ipSplit[3]==255))
        {
            return false;
        }

        if(ipSplit[0] >= 224)
        {
            return false;
        }
        return true;
    }
}

function IpCheckAndMask(ip_addr)
{
/*by FC-fcy 2012-4-23 start*/
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])(\u002F)(([0-9])|([1-2][0-9])|(3[0-2]))$/;
/*by FC-fcy 2012-4-23 end*/
	if(myRE.test(ip_addr))
	{
        return true;
	}
	else
	{
		return false;
	}
}

function showHelp(helpname,lang)
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor3(){
 var tab = document.all.table3;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function display(){
 	var hid=document.webForm;
 	mms = "<% write(Allipaddr); %>";

 	hid.ipaddr.value = mms.split(",")[0];
 	hid.ipaddrmask.value = mms.split(",")[1];
 	hid.ipaddrB.value  = mms.split(",")[2];
 	hid.ipaddrBmask.value  = mms.split(",")[3];
 	hid.gateway.value  = mms.split(",")[4];
 	hid.bgateway.value  = mms.split(",")[5];

}

function IpCheckMask(mask)
{
/*by FC-fcy 2012-4-23 start*/
	var myRE = /^(254|252|248|240|224|192|128|0)\.0\.0\.0|255\.(254|252|248|240|224|192|128|0)\.0\.0|255\.255\.(254|252|248|240|224|192|128|0)\.0|255\.255\.255\.(254|252|248|240|224|192|128|0)$/;
/*by FC-fcy 2012-4-23 end*/
	if(myRE.test(mask))
	{
        return true;
	}
	else
	{
		return false;
	}
}

function messageCheck()
{
 	var hid=document.webForm;

 	if((tdIpCheck(hid.gateway.value)==false) && (hid.gateway.value != ""))
	{
			alert("gateway输入非法！格式：A.B.C.D");
		return false;

	}

	if (tdIpCheck(hid.ipaddr.value)==false)
	{
		alert("IP地址输入非法！格式：A.B.C.D");
		return false;
	}
	if (IpCheckMask(hid.ipaddrmask.value)==false)
	{
		alert("子网掩码输入非法！格式：A.B.C.D");
		return false;
	}

	if(confirm("此操作可能会导致当前连接断掉,确定是否修改?"))
	{
		hid.submit();
		return true;
	}
	else
		return false;

	/*
 	if(IpCheckAndMask(hid.ipaddr.value)!=false)
	{
		if(confirm("此操作可能会导致当前连接断掉,确定是否修改?"))
		{
			hid.submit();
			return true;
		}
		else
			return false;
	}
	else
	{
		alert("IP地址输入非法！格式：A.B.C.D/M");
		return false;
	}
	*/
	return false;
}

function messageCheck2()
{
 	var hid=document.webForm;


 	if(IpCheckAndMask(hid.debugport.value)!=false)
	{
		if(confirm("此操作可能会导致当前连接断掉,确定是否修改?"))
		{
			hid.action="/goform/setOutbandIp";
			hid.submit();
			return true;
		}
		else
			return false;
	}
	else
	{
		alert("IP地址输入非法！格式：A.B.C.D/M");
		return false;
	}

	return false;
}

function messageCheck3()
{
 	var hid=document.webForm;


	if (tdIpCheck(hid.ipaddrB.value)==false)
	{
		alert("IP地址输入非法！格式：A.B.C.D");
		return false;
	}
	if (IpCheckMask(hid.ipaddrBmask.value)==false)
	{
		alert("子网掩码输入非法！格式：A.B.C.D");
		return false;
	}

	if(confirm("此操作可能会导致当前连接断掉,确定是否修改?"))
	{
		hid.action="/goform/setIpAddrMMSB";
		hid.submit();
		return true;
	}
	else
		return false;

	/*
 	if(IpCheckAndMask(hid.ipaddrB.value)!=false)
	{
		if(confirm("此操作可能会导致当前连接断掉,确定是否修改?"))
		{
			hid.action="/goform/setIpAddrMMSB";
			hid.submit();
			return true;
		}
		else
			return false;
	}
	else
	{
		alert("IP地址输入非法！格式：A.B.C.D/M");
		return false;
	}
	*/
	return false;
}


function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm?name=ip";
	tf.submit();
}


function showHelpinfo()
{
   showHelp('ip',<% write(lang); %>);
}


</script>
</HEAD>
<BODY  onload=display() >
<br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setIpAddr">
<div id="view_help"  style="display:none">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>


<TABLE width="98%" align="center" cellpadding=0 cellspacing=0   class="tablebord" bgcolor="efefef">



    </TABLE>
</div>



 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>MMS IP</b></font></td></tr>
 </table>


</br>
<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
<tr><td bgcolor="#ABD8EF" height="20px" ><font size="2" color="#FFFFFF"><b>MMS-A</b></font></td></tr>
</table>

<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table2" class="tablebord">
<TR height=22>
	<TD width="27%" valign="top">&nbsp;&nbsp;IP地址</TD>
	<td >
		<span class="crons"><input type="text" name="ipaddr"  id="ipaddr"  value="">&nbsp;必填</span>
	</td>
</TR>

<TR height=22>
	<TD width="27%" valign="top">&nbsp;&nbsp;子网掩码</TD>
	<td >
		<span class="crons"><input type="text" name="ipaddrmask"  id="ipaddrmask"  value="">&nbsp;必填</span>
	</td>
</TR>

<TR height=22>
	<TD valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"网关");</script></TD>
	<td ><input type="text" name="gateway"  id="gateway"  value=""></td>
</TR>

<TR height=22>
	<TD colspan="2" valign="top">
		<div align="center">
		<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","buttons_apply","button","bbb","messageCheck()");</script>
		</div>
	</TD>
</TR>
</TABLE>


</br>
<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
<tr><td bgcolor="#ABD8EF" height="20px" ><font size="2" color="#FFFFFF"><b>MMS-B</b></font></td></tr>
</table>

<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table3" class="tablebord">
<TR height=22>
	<TD width="27%" valign="top">&nbsp;&nbsp;IP地址</TD>
	<td >
		<span class="crons"><input type="text" name="ipaddrB"  id="ipaddrB"  value="">&nbsp;必填</span>
	</td>
</TR>
<TR height=22>
	<TD width="27%" valign="top">&nbsp;&nbsp;子网掩码</TD>
	<td >
		<span class="crons"><input type="text" name="ipaddrBmask"  id="ipaddrBmask"  value="">&nbsp;必填</span>
	</td>
</TR>

	<TR height=22>
		<TD valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"网关");</script></TD>
		<td ><input type="text" name="bgateway"  id="bgateway"  value=""></td>
	</TR>

<TR height=22>
	<TD colspan="2" valign="top">
		<div align="center">
		<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","buttons_apply","button","bbb","messageCheck3()");</script>
		</div>
	</TD>
</TR>
</TABLE>




<script>
//changebgcolor();

changebgcolor2();
changebgcolor3();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</form>
</BODY></HTML>


