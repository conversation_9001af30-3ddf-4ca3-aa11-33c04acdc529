<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_2.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"端口管理");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script>

function MSTPMSTBRIBASEINFO(v1, v2, v3, v4, v5,v6,v7)
{
	document.write("<tr height='30'><td align='left' class='crons'>"+v1+"</td><td align='left' class='crons'>"+v2+"</td><td align='left' class='crons'>"+v3+"</td><td align='left' class='crons'>"+v4+"</td><td align='left' class='crons'>"+v5+"</td><td align='left' class='crons'>"+v6+"</td><td align='left' class='crons'>"+v7+"</td></tr>");
	return ;
}

function showInstancecfg(instanceNum,lltime)
{
	window.location.href="mstpinstance.asp?instanceNum="+instanceNum+"&ltime="+lltime;
}
function showportcfg(portNo,instanceNum,lltime)
{
	window.location.href="mstpinstance.asp?portNo="+portNo+"&instanceNum="+instanceNum+"&ltime="+lltime;
}
function messageCheck()
{
	var hid = document.webForm;
			hid.submit();

	return true;
}
function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm";
	tf.submit();
}

function refreshpage()
{
  location.href='mstpinstance.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('mstpinstance',<% write(lang); %>);
}

</script>
</head>
<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setMstpPortCfg">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0"><tr><td>
	<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" ><tr><td valign="top" >
		<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
							<tr><td>
								<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
							            <tr>
							              <td colspan="2" align="left" class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"实例信息");</script></td>
							            </tr>
								</table>
							</td></tr>   
							<tr>
						    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">      
						      <tr>     
						        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  >
						            <tr height="25">
										<td width="100%">
											<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">
											<tr height="25"><td colspan="7" align="left" width="100%">&nbsp;<script>writemsg(<% write(lang); %>,"实例桥信息");</script></td></tr>
											<tr><td align="left" width="10%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"实例ID");</script></td><td align="left" width="5%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥名");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"实例桥优先级");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"实例根端口");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"实例根开销");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"根桥ID");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"实例桥ID");</script></td></tr>
											<script>
											<% showMstpMstBriTable(); %>
											</script>
											</table>
										</td>                     
						            </tr>
						        </table></td>
						      </tr>      
						    </td>
						  </tr> 
						  	<tr>
						    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0"  >      
						      <tr>     
						        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
						            <tr height="25">
										<td width="100%">
											<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table2" class="tablebord">
											<tr height="25"><td colspan="4" align="left" width="100%">&nbsp;<script>writemsg(<% write(lang); %>,"端口实例信息");</script></td></tr>
											<tr height="1"><td width="20%"></td><td width="15%"></td><td width="20%"></td><td ></td></tr>
											<% showMstpInstancePortInfo(); %>
											</table>
										</td>
						            </tr>
						        </table></td>
						      </tr>      
						    </td>
						  </tr> 
		 	</table>
	</td></tr>
	<tr>
	        <td align="center" height="35"><script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
		        &nbsp;
		        &nbsp;
		        </td>
	      </tr>
	</table>
 </td></tr></table>
</form>


<script>
changebgcolor();
changebgcolor2();

</script>
</body>
</html>
