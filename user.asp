<HTML>
<HEAD>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<TITLE>page</TITLE>
<META http-equiv=Content-Type content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script>

function writemsgb(lang,key)
{
	document.write("<b>"+putmsg(lang,key)+"</b>");
}

function writebutton2(priv,lang,key,bclass,btype,bname,benable,bfunc)
{
	var outputstr ="";
	if((priv == 1)||(priv == 5))
	  outputstr="<input class="+bclass+" id='"+bname+"' "+benable+" name='"+bname+"' type='"+btype+"' value='"+putmsg(lang,key)+"' onClick='return "+bfunc+";'>";
	else
		outputstr="&nbsp;";
	document.write(outputstr);

}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables all_tables2" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables all_tables1" ;
		}

     }

  }
}

function changebgcolor3(){
 var tab = document.all.table3;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables all_tables2" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables all_tables1" ;
		}

     }

  }
}

function changebgcolor4(){
 var tab = document.all.table4;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function display(){

}

function encode(f,j){f=btoa(escape(f));var l="";for(var c=0;c<j.length;c++){l+=j.charCodeAt(c).toString()}var g=Math.floor(l.length/5);var b=parseInt(l.charAt(g)+l.charAt(g*2)+l.charAt(g*3)+l.charAt(g*4)+l.charAt(g*5));var a=Math.ceil(j.length/2);var h=Math.pow(2,31)-1;var d=Math.round(Math.random()*1000000000)%100000000;l+=d;while(l.length>10){l=(parseInt(l.substring(0,10))+parseInt(l.substring(10,l.length))).toString()}l=(b*l+a)%h;var e="";var k="";for(c=0;c<f.length;c++){e=parseInt(f.charCodeAt(c)^Math.floor((l/h)*255));if(e<16){k+="0"+e.toString(16)}else{k+=e.toString(16)}l=(b*l+a)%h}d=d.toString(16);while(d.length<8){d="0"+d}k+=d;return k};

function encode_2weight()
{
}

function messageCheck()
{
	var dplen;
	var dpvalue;

    var hid=document.webForm;
    var v3=(hid.v3.value).replace(/[ ]/g,"");
    var v4=(hid.v4.value).replace(/[ ]/g,"");
    if(v3.length!=hid.v3.value.length||v4.length!=hid.v4.value.length)
    {
        alert(putmsg(<% write(lang); %>,"密码中不能含有空格!"));
        return;
    }

    if((hid.v1.value.length==0)||(hid.v2.value.length==0)||(hid.v3.value.length==0)||(hid.v4.value.length==0))
	{
		alert(putmsg(<% write(lang); %>,"密码不能为空,请重新输入!"));
		return false;
	}
	if((hid.v3.value!=hid.v4.value))
	{
		alert(putmsg(<% write(lang); %>,"两次输入的用户名或密码不一致,请重新输入!"));
		return false;
	}
	document.getElementById("nuflag").value = "N";

	encode_2weight();


	dplen = parseInt(hid.v2.value.length/2);
	dpvalue = hid.v2.value;
	hid.v2.value = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ hid.v2.value.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(hid.v3.value.length/2);
	dpvalue = hid.v3.value;
	hid.v4.value = hid.v2.value + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ hid.v3.value.length + ":E95ca4DA" + dpvalue.substring(dplen);

	hid.v2.value = "dER4Y%eRnsu&2S@q";
	hid.v3.value = "e@bJzBvNw&@4O1@!";

	hid.submit();
	return true;
}



async function messageNewCheck()
{
	var dplen;
	var dpvalue;
    var hid=document.form2;
    var v32=(hid.v32.value).replace(/[ ]/g,"");
    var v42=(hid.v42.value).replace(/[ ]/g,"");


    if(v32.length!=hid.v32.value.length||v42.length!=hid.v42.value.length)
    {
        alert(putmsg(<% write(lang); %>,"密码中不能含有空格!"));
        return;
    }

    if((hid.v32.value.length==0)||(hid.v42.value.length==0))
	{
		alert(putmsg(<% write(lang); %>,"密码不能为空,请重新输入!"));
		return false;
	}
	if((hid.v32.value!=hid.v42.value))
	{
		alert(putmsg(<% write(lang); %>,"两次输入的用户名或密码不一致,请重新输入!"));
		return false;
	}

	//var name = prompt("操作认证-用户名", "");
	document.getElementById("reauthn").value =await userNamePrompt();
	// var pwd = prompt("操作认证-密码", "");
	// document.getElementById("reauthpd").value = pwd;
	document.getElementById("reauthpd").value = await testThePrompt();
	document.getElementById("nuflag").value = "P";

	encode_2weight();



	dplen = parseInt(hid.v12.value.length/2);
	dpvalue = hid.v12.value;
	hid.v12.value = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ hid.v12.value.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(hid.v32.value.length/2);
	dpvalue = hid.v32.value;
	hid.v42.value = hid.v12.value + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ hid.v32.value.length + ":E95ca4DA" + dpvalue.substring(dplen);

	hid.v12.value = "DHa8t$cv9GHTZRdl";
	hid.v32.value = "FZCE^vripsjeSTFH";


	dplen = parseInt(hid.reauthn.value.length/2);
	dpvalue = hid.reauthn.value;
	hid.reauthn.value = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ hid.reauthn.value.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(hid.reauthpd.value.length/2);
	dpvalue = hid.reauthpd.value;
	hid.reauthpd.value = hid.reauthn.value + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ hid.reauthpd.value.length + ":E95ca4DA" + dpvalue.substring(dplen);
	hid.reauthn.value = "&LbcOCfjm*Pbb0TE";


	hid.submit();
	return true;
}
function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm11";
	tf.submit();
}
function refreshpage()
{
  location.href='user.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('user',<% write(lang); %>);
}

<% var errorcode, usrcfg; getUsrCfg(); %>

//var userNameList = ["1111","22222","tt"];
//var userLeveList = [0,1,8];
<% var errorcode,logintime,oldusername;getUsername(); %>

var userNameList = [<% write(usrcfg); %>];

var curUser = "<% write(oldusername); %>";


function gotoAddUser()
{
window.location.href = "addadmin.html";
}

function getUserLeve(leve)
{
	if(leve == 1)
		return "admin";
	else if(leve == 2)
		return "guest";
	else if(leve == 3)
		return "logger";

}

function delUser(i)
{
	var dplen;
	var dpvalue;
	var reauthn;
	var reauthpd;


	document.getElementById("v5").value = userNameList[i].split(",")[0];
	document.form1.action="/goform/delUsername";

		var name = prompt("操作认证-用户名", "");
		document.getElementById("reauthn2").value = name;
		var pwd = prompt("操作认证-密码", "");
		document.getElementById("reauthpd2").value = pwd;


	reauthn = document.getElementById("reauthn2").value;
	reauthpd = document.getElementById("reauthpd2").value;

	dplen = parseInt(reauthn.length/2);
	dpvalue = reauthn;
	reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(reauthpd.length/2);
	dpvalue = reauthpd;
	document.getElementById("reauthpd2").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
	document.getElementById("reauthn2").value = "eNEtdsCu8Y7#O^0s";


	reauthn = document.getElementById("v5").value;
	reauthpd = document.getElementById("v5").value;
	dplen = parseInt(reauthn.length/2);
	dpvalue = reauthn;
	reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(reauthpd.length/2);
	dpvalue = reauthpd;
	document.getElementById("v5").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);


	document.form1.submit();
}

function editeUser(i)
{
	document.getElementById("userListEditName").value = userNameList[i];
	//alert(document.getElementById("userListEditName").value);
	document.form1.action="/goform/delUsername";

	document.form1.submit();

}

</script>
</HEAD>
<BODY  onload=display() >
<br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>

<script>
function writeUserLine()
{
	for(var i=0;i<userNameList.length;i++)
	{
		document.write("<tr  class='tables_all'>");
		document.write("    <td height='32' class='inputsyslog1'>"+userNameList[i].split(",")[0]+"</td>");
		document.write("    <td  class='inputsyslog1'>"+getUserLeve(userNameList[i].split(",")[1])+"</td>");
		document.write("    <td  class='inputsyslog1'>");


		if((userNameList[i].split(",")[0] != "administrator")&&(userNameList[i].split(",")[0] != curUser)&&(<% write(authmode); %>==1))
		{
			document.write("      <input type='submit' name='button"+i+"' id='button"+i+"' class='botton_under_line' value='删除' onclick='delUser("+i+")'  />");
		}
		document.write("      </td>");
		document.write("  </tr>");
	}
}

checktop(<% write(lang); %>);
</script>

<form  name="webForm" method="post" action="/goform/setUsername">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>



 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="30px" class="tit"><font color="#0069d6"><div>用户管理</div></font></td></tr>
 </table>

<br>
<div class="formContain">
 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="20px" ><font size="5" color="#404040"><div class="bot">用户密码设置</div></font></td></tr>
 </table>
<!--
<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
<tr>
    <td  class=Tablelist id=tabs name=tabs><nobr>&nbsp;<script>writemsg(<% write(lang); %>,"用户参数配置");</script></nobr></td>


  </tr>
</table>
-->

<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
  <TBODY>

    <TR height=22>
      <TD width="42%" valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"用户名:");</script></TD>
      <td ><span class="crons">
			<input type="text" name="v1" disabled   value=<% write(oldusername); %> >

      </span></td>
    </TR>
    <TR height=22 >
      <TD valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"密码:");</script></TD>
      <td ><span class="crons">
		                  <input type="password" name="v2"  >
      </span></td>
    </TR>
    <TR height=22 >
      <TD valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"新密码:");</script></TD>
      <td ><span class="crons">
        		                  <input type="password" name="v3"   >

      </span></td>
    </TR>
    <TR height=22>
      <TD valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"确认新密码:");</script></TD>
      <td ><span class="crons">
		                  <input type="password" name="v4" >
      </span></td>
    </TR>
    <TR height=22>
      <TD colspan="2" valign="top"><div align="center">
        <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","buttons_apply","button","bbb","messageCheck()");</script>
        </div></TD>
      </TR>
</TABLE>
</div>
</form>
<script>
changebgcolor();

<% if (errorcode=="1") { write("alert(putmsg(");write(lang);write(",'用户密码修改成功,请重新登录!'));");write("top.location.href = 'login.asp");write("';");} %>
<% if (errorcode=="2") { write("alert(putmsg(");write(lang);write(",'该用户的原密码有误,请重新输入!'));");  } %>
<% if (errorcode=="3") { write("alert(putmsg(");write(lang);write(",'不具备执行该操作的权限!'));");  } %>
<% if (errorcode!="") { if (errorcode!="1") { if (errorcode!="2") { if (errorcode!="3") { write_errorcode(errorcode); } } } } %>
</script>
<br>
<form  name="form2" method="form2" action="/goform/setNewUsername" class="formContain">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" id="reauthn" name="reauthn" value="">
<input type="hidden" id="reauthpd" name="reauthpd" value="">
<input type="hidden" id="nuflag" name="nuflag" value="N">


<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
<tr><td height="20px" ><font size="5" color="#404040"><div class="bot">新增用户</div></font></td></tr>
</table>
<!--

<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
<tr>
    <td  class=Tablelist id=tabs name=tabs><nobr>&nbsp;<script>writemsg(<% write(lang); %>,"新增");</script></nobr></td>


  </tr>
</table>
-->
<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table3" class="tablebord">
  <TBODY>

    <TR height=22>
      <TD width="42%" valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"用户名:");</script></TD>
      <td ><span class="crons">
        		                  <input type="text" name="v12"    value="" >

      </span></td>
    </TR>

    <TR height=22 >
      <TD valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"权限:");</script></TD>
      <td ><span class="crons">
<select name="up" id="up">
            <option value="1" >admin</option>
            <option value="2" >guest</option>
<!--            <option value="3" >logger</option> -->
          </select>
      </span></td>
    </TR>
    <TR height=22 >
      <TD valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"密码:");</script></TD>
      <td ><span class="crons">
        		                  <input type="password" name="v32"   >

      </span></td>
    </TR>
    <TR height=22>
      <TD valign="top">&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"确认密码:");</script></TD>
      <td ><span class="crons">
		                  <input type="password" name="v42" >
      </span></td>
    </TR>
    <TR height=22>
      <TD colspan="2" valign="top"><div align="center">
        <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"创  建","buttons_apply","button","bbb2","messageNewCheck()");</script>
        </div></TD>
   </TR>
</TABLE>
</form>

<br>
<form id="form1" name="form1" method="post" action="admin.html" class="formContain">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" id="reauthn2" name="reauthn2" value="">
<input type="hidden" id="reauthpd2" name="reauthpd2" value="">


  <table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
    <tr>
<!--
      <td  class=Tablelist id=tabs name=tabs>用户列表</td>
      -->
      <td   class="tablenew" id=tabs name=tabs><div align="right"> <span class="td25">
        <input type="hidden" name="v5" id="v5"  value="">
        <input type="hidden" name="userListEditName" id="userListEditName" value="">
        </span>
&nbsp;      </div></td>
    </tr>
  </table>

<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
<tr><td height="20px" ><font size="5" color="#404040"><div class="bot">用户列表</div></font></td></tr>
</table>



  <tr>
    <table width="98%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id=""  >
      <TR align="center" height=22 class="partition">
        <TD   nowrap class="all_tables_list">用户名</TD>
        <TD   nowrap class="all_tables_list">权限</TD>
        <TD   nowrap class="all_tables_list">删除</TD>
      </TR>
      <script language="javascript">
  writeUserLine();
  document.getElementById("v5").value = "";
  document.getElementById("userListEditName").value = "";
changebgcolor3();
  </script>
    </table>
</form>

</BODY></HTML>



