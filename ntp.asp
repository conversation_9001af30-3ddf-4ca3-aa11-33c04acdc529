<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"SNTP管理");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script  language="JavaScript">

function IpCheck(ip_addr)
{
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])$/;
	if(myRE.test(ip_addr))
	{
        return true;
	}
	else
	{
		return false;
	}
}
function TimeCheck(val)
{
	var str=/^((((19[7-9][1-9])|(1980)|(1990)|(1970)|(([2-9]\d)\d{2}))-(0[13578]|1[02])-(0[1-9]|[12]\d|3[01]))|(((19[7-9][1-9])|(1980)|(1990)|(1970)|(([2-9]\d)\d{2}))-(0[13456789]|1[012])-(0[1-9]|[12]\d|30))|(((19[7-9][1-9])|(1980)|(1990)|(1970)|(([2-9]\d)\d{2}))-02-(0[1-9]|1\d|2[0-8]))|(((19|[2-9]\d)(0[48]|[2468][048]|[13579][26])|((16|[2468][048]|[3579][26])00))-02-29-)) ((20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d) ?$/;
	if(str.test(val))
	{
		return true;
	}
	else
	{
		return false;
	}
}

function TimeZoneCheck(val)
{
	var str=/^(\+|\-)(([0-1]\d)|(2[0-3])|(\d)):([0-5]\d)$/;	
	if(str.test(val))
	{
		return true;
	}
	else
	{
		return false;
	}	
}

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function messageCheck(v)
{
    var hid=document.webForm;

    if(v==1)
    {
    	/*check time format*/
    	if(TimeCheck(hid.systime.value)==false)
    	{
    		alert(putmsg(<% write(lang); %>,"时间的格式不合法,应该为:yyyy-mm-dd hh:mm:ss!"));
    		return false;
    	}
    	hid.flag.value=1;
    	hid.submit();
    }
    else if(v==2)
    {
    	/*check timezone format*/
    	if(hid.timezone.value.length!=0)
    	{
	    	if(TimeZoneCheck(hid.timezone.value)==false)
	    	{
	    		alert(putmsg(<% write(lang); %>,"时区的格式不合法,格式如:+08:00!"));
	    		return false;
	    	}
    	}
    	hid.flag.value=2;
    	hid.submit();
    }
    else if(v==3)
    {
    	if(hid.sntppri.value.length!=0)
    	{
	    	if(IpCheck(hid.sntppri.value)!=true)
	    	{
	    		alert(putmsg(<% write(lang); %>,"IP地址格式错误!"));
	    	}
	    	else
	    	{
		    	hid.flag.value=3;
		    	hid.submit();
	    	}
    	}
    	else
    	{
    		hid.flag.value=3;
		    hid.submit();
    	}
    }
    else if(v==4)
    {
    	if(hid.sntpsec.value.length!=0)
    	{
	    	if(IpCheck(hid.sntpsec.value)!=true)
	    	{
	    		alert(putmsg(<% write(lang); %>,"IP地址格式错误!"));
	    	}
	    	else
	    	{
		    	hid.flag.value=4;
		    	hid.submit();
	    	}
    	}
    	else
    	{
    		hid.flag.value=4;
		    hid.submit();
    	}
    }
    else if (5 == v)
    {
		if(hid.sntpinterval.value >99999 || hid.sntpinterval.value < 30 && hid.sntpinterval.value != "")
		{
	    	alert(putmsg(<% write(lang); %>,"轮询间隔设置范围(30~99999s)"));
	    }
		else
		{
		    hid.flag.value = 5;
		    hid.submit();
		}
    }
    else if (6 == v)
    {
	    hid.flag.value = 6;
	    hid.submit();
    }
    else if (7 == v)
    {
	    hid.flag.value = 7;
	    hid.submit();
    }
    else if (8 == v)
    {
	    hid.flag.value = 8;
	    hid.submit();
    }
   
	return true;
}
function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm?name=sntp";
	tf.submit();
}
function refreshpage()
{
  location.href='sntp.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('sntp',<% write(lang); %>);
}

function check_ntp_state()
{
	    var hid=document.webForm;

		if (hid.hntpeanble.value == "enable")
			document.getElementById("ntp_enable").value = 1;
		else
			document.getElementById("ntp_enable").value = 2;

		if (hid.hntpport.value == "debug")
			document.getElementById("ntp_port").value = 1;
		else
			document.getElementById("ntp_port").value = 2;
}


</script>
</head>
<body  onload="display()"><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
function display()
{
	if(document.getElementById("isauthA"))
	{
		if(<% write(authmode); %> != 1)
			document.getElementById("isauthA").style.display = "none";
		
	}

}checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setSysTime">
<input type="hidden" name="flag">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<% var errorcode,hntpeanble,hntpport,hsntpeanble,systime,timezone,sntppri,sntpsec,sntpinterval;getSysTime(); %>
<input type="hidden" name="hsntpeanble" value=<% write(hsntpeanble); %>>
<input type="hidden" name="hntpeanble" value=<% write(hntpeanble); %>>
<input type="hidden" name="hntpport" value=<% write(hntpport); %>>

		

 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>SNTP及系统时间</b></font></td></tr>
 </table>
 
<br />
 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#ABD8EF" height="20px" ><font size="2" color="#FFFFFF"><b>NTP</b></font></td></tr>
 </table>

<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" id="table1" class="tablebord">

        <tr height="25">
			<td width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"ntp启用");</script></td>
			<td  align="left" class="crons"> 
				<select id="ntp_enable" name="ntp_enable" class="select1">
					<option value="1" selected><script>writemsg("ch","开启");</script></option>
					<option value="2"><script>writemsg("ch","关闭");</script></option>
				</select>
			</td>
			<td  align="middle" class="crons"> 
				<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify5","messageCheck(7)");</script>
			</td>
        </tr>
        
        <tr style="display:none" height="25">
			<td width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"ntp port");</script></td>
			<td  align="left" class="crons"> 
				<select id="ntp_port" name="ntp_port" class="select1">
					<option value="1" selected><script>writemsg("ch","debug");</script></option>
					<option value="2"><script>writemsg("ch","mms");</script></option>
				</select>
			</td>
			<td  align="middle" class="crons"> 
				<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify5","messageCheck(8)");</script>
			</td>
        </tr>
        
</table>
  


<script>
changebgcolor();
check_ntp_state();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</form>

</html>


