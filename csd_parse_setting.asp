<HTML>
<HEAD>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<TITLE>page</TITLE>
<META http-equiv=Content-Type content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<script>
<% var csdparseState, piedname, pswitchid, errorcode; getcsdparseState(); %>

function tdIpCheck(textValue)
{
    re1=/(\d+)(\W)(\d+)(\W)(\d+)(\W)(\d+)/
    Check=textValue.search(re1);
    if(Check==-1)
    {
        return false;
    }
    else
    {
        ipSplit=textValue.split('.');
        if(ipSplit.length!=4)
        {
            return false;
        }

        for(i=0; i<ipSplit.length; i++)
        {
            if(isNaN(ipSplit[i]))  return false;
			if(ipSplit[i].length > 1 && ipSplit[i].charAt(0) == "0") return false;
        }

        for(i=0; i<ipSplit.length; i++)
        {
            if(ipSplit[i] > 255)
            {
                return false;
            }
            if(ipSplit[i] < 0)
            {
                return false;
            }
        }
        if((ipSplit[0]==255) && (ipSplit[1]==255) && (ipSplit[2]==255) && (ipSplit[3]==255))
        {
            return false;
        }

        if((ipSplit[0]==0) || (ipSplit[0]==127) || (ipSplit[3]==0) || (ipSplit[3]==255))
        {
            return false;
        }

        if(ipSplit[0] >= 224)
        {
            return false;
        }
        return true;
    }
}

function IpCheckAndMask(ip_addr)
{
/*by FC-fcy 2012-4-23 start*/
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])(\u002F)(([0-9])|([1-2][0-9])|(3[0-2]))$/;
/*by FC-fcy 2012-4-23 end*/
	if(myRE.test(ip_addr))
	{
        return true;
	}
	else
	{
		return false;
	}
}

function showHelp(helpname,lang)
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function changebgcolor3(){
 var tab = document.all.table3;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor4(){
 var tab = document.all.table4;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function changebgcolor5(){
 var tab = document.all.table5;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor6(){
 var tab = document.all.table6;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor7(){
 var tab = document.all.table7;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor8(){
 var tab = document.all.table8;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor9(){
 var tab = document.all.table9;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor10(){
 var tab = document.all.table10;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}



function messageCheck()
{
 	var hid=document.webForm;
	var fName = hid.configup_filename.value;


 	if(tdIpCheck(hid.configup_ip.value)==false)
	{
		alert("IP地址输入非法！格式：A.B.C.D");
		return false;
	}

 	if(fName.length <= 4)
	{
    	alert("文件必须以 .cfg 为扩展名！");
    	return false;
  	}


 	if(fName.indexOf(".cfg") != fName.length - 4)
	{
    	alert("文件必须以 .cfg 为扩展名！");
    	return false;
  	}

	hid.submit();
	return true;

}

function messageCheck2()
{
 	var hid=document.webForm;
	var fName = hid.configdown_filename.value;


 	if(tdIpCheck(hid.configdown_ip.value)==false)
	{
		alert("IP地址输入非法！格式：A.B.C.D");
		return false;
	}

 	if(fName.length <= 4)
	{
    	alert("文件必须以 .cfg 为扩展名！");
    	return false;
  	}


 	if(fName.indexOf(".cfg") != fName.length - 4)
	{
    	alert("文件必须以 .cfg 为扩展名！");
    	return false;
  	}

    hid.action="/goform/downloadConfig";
	hid.submit();
	return true;

}

function messageCheck3()
{
 	var hid=document.webForm;
	var fName = hid.config61850up_filename.value;


 	if(tdIpCheck(hid.config61850up_ip.value)==false)
	{
		alert("IP地址输入非法！格式：A.B.C.D");
		return false;
	}

 	if(fName.length <= 4)
	{
    	alert("文件必须以 .cid 为扩展名！");
    	return false;
  	}


 	if(fName.indexOf(".cid") != fName.length - 4)
	{
    	alert("文件必须以 .cid 为扩展名！");
    	return false;
  	}

    hid.action="/goform/upload61850Config";
	hid.submit();
	return true;

}

function messageCheck4()
{
 	var hid=document.webForm;
	var fName = hid.config61850down_filename.value;


 	if(tdIpCheck(hid.config61850down_ip.value)==false)
	{
		alert("IP地址输入非法！格式：A.B.C.D");
		return false;
	}

 	if(fName.length <= 4)
	{
    	alert("文件必须以 .cid 为扩展名！");
    	return false;
  	}


 	if(fName.indexOf(".cid") != fName.length - 4)
	{
    	alert("文件必须以 .cid 为扩展名！");
    	return false;
  	}

    hid.action="/goform/download61850Config";
	hid.submit();
	return true;

}

function messageCheck5()
{
 	var hid=document.webForm;
	var fName = hid.uploadsystemlog_filename.value;


 	if(tdIpCheck(hid.uploadsystemlog_ip.value)==false)
	{
		alert("IP地址输入非法！格式：A.B.C.D");
		return false;
	}

 	if(fName.length <= 4)
	{
    	alert("文件必须以 .csd 为扩展名！");
    	return false;
  	}


 	if(fName.indexOf(".csd") != fName.length - 4)
	{
    	alert("文件必须以 .csd 为扩展名！");
    	return false;
  	}

    hid.action="/goform/uploadcsdfile";
	hid.submit();
	return true;

}

function messageCheck6()
{
 	var hid=document.webForm;
	var fName = hid.uploadalarmlog_filename.value;


 	if(tdIpCheck(hid.uploadalarmlog_ip.value)==false)
	{
		alert("IP地址输入非法！格式：A.B.C.D");
		return false;
	}

 	if(fName.length <= 4)
	{
    	alert("文件必须以 .csd 为扩展名！");
    	return false;
  	}


 	if(fName.indexOf(".csd") != fName.length - 4)
	{
    	alert("文件必须以 .csd 为扩展名！");
    	return false;
  	}

    hid.action="/goform/downloadcsdfile";
	hid.submit();
	return true;

}


function messageCheck7()
{
	var hid = document.webForm;

	if(document.getElementById("d1").checked == true)
		document.forms[0].para1.value = "enable";
	else
		document.forms[0].para1.value = "disable";

    hid.action="/goform/csdparseswitchID";
	hid.submit();
	return true;

}

function messageCheck8()
{
 	var hid=document.webForm;
	var fName = hid.uploadcsdlocal_filename.value;


 	if(tdIpCheck(hid.uploadcsdlocal_ip.value)==false)
	{
		alert("IP地址输入非法！格式：A.B.C.D");
		return false;
	}

 	if(fName.length <= 4)
	{
    	alert("文件必须以 .csd 为扩展名！");
    	return false;
  	}


 	if(fName.indexOf(".csd") != fName.length - 4)
	{
    	alert("文件必须以 .csd 为扩展名！");
    	return false;
  	}

    hid.action="/goform/uploadcsdlocalfile";
	hid.submit();
	return true;

}

function messageCheck9()
{
 	var hid=document.webForm;

    hid.action="/goform/erasecsdfileFunc";
	hid.submit();

	return true;
}



function showHelpinfo()
{
   showHelp('ip',<% write(lang); %>);
}

function display()
{
	tmp = "<% write(csdparseState); %>";
	array_cfg = tmp.split(",");

	if (array_cfg[0] == "enable")
		document.getElementById("d1").checked = true;
	else
		document.getElementById("d2").checked = true;

	document.getElementById("csdparase_enable").innerText = array_cfg[0];

	//document.getElementById("csdparase_switchid").value = array_cfg[1];

	tmp = "<% write(piedname); %>";
	document.getElementById("csdparase_iedname").value =  tmp.split(",")[0];

	tmp = "<% write(pswitchid); %>";
	document.getElementById("csdparase_switchid").innerText =  tmp.split(",")[0];
}

</script>
</HEAD>
<BODY  onload="display();">
<br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/uploadConfig">
<input type="hidden" name="para1">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>CSD配置文件管理</b></font></td></tr>
 </table>

	<br />
 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#ABD8EF" height="20px" ><font size="2" color="#FFFFFF"><b>CSD文件解析</b></font></td></tr>
 </table>

	<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table7" class="tablebord">
	<TBODY>
	<TR height=22>
		<TD width="27%" valign="top">&nbsp;&nbsp;State</TD>
		<td colspan="3"align="left" class="crons">&nbsp;
			<input type="radio" name="rw" value="rw"  id="d1">Enable
			<input type="radio" name="rw" value="ro"  id="d2">Disable
		</td>
	</TR>
	<TR height=22>
		<TD width="27%" valign="top">&nbsp;&nbsp;IED name</TD>
		<td >
			<span class="crons">
			<input type="text" name="csdparase_iedname"  id="csdparase_iedname"  value="" readonly>
			</span>
		</td>
	</TR>
	<TR height=22>
		<TD colspan="2" valign="top">
			<div align="center">
	  		<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"解析","buttons_apply","button","aaa","messageCheck7()");</script>
			</div>
		</TD>
	</TR>
	</TABLE>

	<br />
 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#ABD8EF" height="20px" ><font size="2" color="#FFFFFF"><b>CSD当前配置</b></font></td></tr>
 </table>

	<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table10" class="tablebord">
	<TBODY>
	<TR height=22>
		<TD width="27%" valign="top">&nbsp;&nbsp;State</TD>
		<td colspan="3" align="left" class="crons">
<!--
			<input type="text" name="csdparase_enable"  id="csdparase_enable"  value="" readonly>
-->
			&nbsp;&nbsp;<label id="csdparase_enable"></label>
		</td>
	</TR>
	<TR height=22>
		<TD width="27%" valign="top">&nbsp;&nbsp;IED name</TD>
		<td >
			<span class="crons">
<!--
			<input type="text" name="csdparase_switchid"  id="csdparase_switchid"  value="" readonly>
-->
			&nbsp;&nbsp;<label id="csdparase_switchid"></label>

			</span>
		</td>
	</TR>
	</TABLE>


	<br />
 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#ABD8EF" height="20px" ><font size="2" color="#FFFFFF"><b>删除CSD配置文件</b></font></td></tr>
 </table>

	<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table9" class="tablebord">
	<TBODY>
	<TR height=22>
		<TD width="27%" valign="top">&nbsp;&nbsp;删除CSD配置文件</TD>
		<td >
			<div align="center">
	  		<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","buttons_apply","button","aaa","messageCheck9()");</script>
			</div>
		</td>
	</TR>
	</TABLE>
	<br />
<script>
//changebgcolor();
//changebgcolor2();
//changebgcolor3();
//changebgcolor4();
//changebgcolor5();
//changebgcolor6();
changebgcolor7();
//changebgcolor8();
changebgcolor9();
changebgcolor10();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</form>
</BODY></HTML>




