<!DOCTYPE html>
<html>

<head>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<TITLE>Device</TITLE>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<meta http-equiv="Content-Type" content="text/html; charset=utf8" />
		<link rel="stylesheet" href="./css/style.css">

		<script src="js/jquery.min.js"></script>
		<style>
			iframe {
				border: 0;
				min-height: 400px;
			}
		</style>
</head>

<body>
	<header>
		<div class="container">
			<span class="showSide"></span>
			<img src="./images/JOYWARE.png" alt="">
			<div class="pull-right">
			</div>
		</div>
	</header>
	<div class="main">
		<div class="main-left">
			<div class="nav-toggle" id="nav-toggle">
				<a>
					<i class="glyphicon glyphicon-menu-hamburger"></i>
					<span>WEB管理菜单</span>
				</a>
			</div>
			<ul class="nav">
				<li class="slide">
					<a class="menu" data-title="设备状态" href="#">
						<i class="glyphicon glyphicon-1"></i>
						<span>设备状态</span>
						<small class="glyphicon glyphicon-plus-sign"></small>
					</a>
					<ul class="slide-menu" style="display: none;">
						<li @click="clickMenu"><a data-title="设备信息"
								onClick="mainctrl.location='sysinfo.asp?ltime='+<% write(lltime); %>;" href="#">设备信息</a>
						</li>
						<li><a data-title="网络配置" onClick="mainctrl.location='ip.asp?ltime='+<% write(lltime); %>;"
								href="#">网络配置</a>
						</li>
						<li><a data-title="SNMP设置"
								onClick="mainctrl.location='snmp_config.asp?ltime='+<% write(lltime); %>;"
								href="#">SNMP设置</a>
						</li>
					</ul>
				</li>
				<li class="slide">
					<a class="menu" data-title="端口管理" href="#">
						<i class="glyphicon glyphicon-2"></i>
						<span>端口管理</span>
						<small class="glyphicon glyphicon-plus-sign"></small>
					</a>
					<ul class="slide-menu" style="display: none;">
						<li><a data-title="端口配置" onClick="mainctrl.location='port.asp?ltime='+<% write(lltime); %>;"
								href="#">端口配置</a>
						</li>
						<li><a data-title="端口镜像" onClick="mainctrl.location='mirror.asp?ltime='+<% write(lltime); %>;"
								href="#">端口镜像</a>
						</li>
						<li><a data-title="速率限制"
								onClick="mainctrl.location='port_linerate.asp?ltime='+<% write(lltime); %>;"
								href="#">速率限制</a>
						</li>
						<li><a data-title="风暴抑制" onClick="mainctrl.location='storm.asp?ltime='+<% write(lltime); %>;"
								href="#">风暴抑制</a>
						</li>
						<li><a data-title="流量统计"
								onClick="mainctrl.location='portstatus.asp?ltime='+<% write(lltime); %>;"
								href="#">流量统计</a>
						</li>
						<!--<li><a data-title="POE 配置"
							onClick="mainctrl.location='poe.asp?ltime='+<% write(lltime); %>;"
							href="#">POE配置</a>
						</li>-->
					</ul>
				</li>
				<li class="slide">
					<a class="menu" data-title="高级配置" href="#">
						<i class="glyphicon glyphicon-1"></i>
						<span>高级配置</span>
						<small class="glyphicon glyphicon-plus-sign"></small>
					</a>
					<ul class="slide-menu" style="display: none;">
						<li><a data-title="VLAN配置"
								onClick="mainctrl.location='vlan_fwd.asp?ltime='+<% write(lltime); %>;"
								href="#">VLAN配置</a>
						</li>
						<li><a data-title="端口汇聚"
								onClick="mainctrl.location='lacp_static.asp?ltime='+<% write(lltime); %>;"
								href="#">端口汇聚</a>
						</li>
						<li><a data-title="MAC配置" onClick="mainctrl.location='sedtab.asp?ltime='+<% write(lltime); %>;"
								href="#">MAC配置</a>
						</li>
						<li><a data-title="环网配置"
							onClick="mainctrl.location='jrpp_config.asp?ltime='+<% write(lltime); %>;"
							href="#">环网配置</a>
						</li>
						<li><a data-title="ERPS配置" onClick="mainctrl.location='erps.asp?ltime='+<% write(lltime); %>;"
								href="#">ERPS配置</a>
						</li>
						<li><a data-title="PTP配置" onClick="mainctrl.location='ptp.asp?ltime='+<% write(lltime); %>;"
							href="#">PTP配置</a>
					</li>
						<li><a data-title="RSTP配置"
								onClick="mainctrl.location='mstpcfg.asp?ltime='+<% write(lltime); %>;"
								href="#">RSTP配置</a>
						</li>
						<li><a data-title="LLDP配置"
								onClick="mainctrl.location='lldpconfig.asp?ltime='+<% write(lltime); %>;"
								href="#">LLDP配置</a>
						</li>
						<li><a data-title="TRAP配置" onClick="mainctrl.location='alarm.asp?ltime='+<% write(lltime); %>;"
								href="#">TRAP配置</a>
						</li>
						<li><a data-title="IGMP配置" onClick="mainctrl.location='igmp.asp?ltime='+<% write(lltime); %>;"
								href="#">IGMP配置</a>
						</li>
						<li><a data-title="静态组播" onClick="mainctrl.location='mmac.asp?ltime='+<% write(lltime); %>;"
								href="#">静态组播</a>
						</li>
						<li><a data-title="QOS配置"
								onClick="mainctrl.location='qos_portConfig.asp?ltime='+<% write(lltime); %>;"
								href="#">QOS配置</a>
						</li>
					</ul>
				</li>
				<li class="slide">
					<a class="menu" data-title="安全管理" href="#">
						<i class="glyphicon glyphicon-1"></i>
						<span>安全管理</span>
						<small class="glyphicon glyphicon-plus-sign"></small>
					</a>
					<ul class="slide-menu" style="display: none;">
						<li><a data-title="802.1x"
								onClick="mainctrl.location='dot1x.asp?ltime='+<% write(lltime); %>;"
								href="#">802.1x</a>
						</li>
						<li><a data-title="端口安全"
								onClick="mainctrl.location='securityTp.asp?ltime='+<% write(lltime); %>;"
								href="#">端口安全</a>
						</li>
						<li><a data-title="端口隔离" onClick="mainctrl.location='portiso.asp?ltime='+<% write(lltime); %>;"
								href="#">端口隔离</a>
						</li>
						<li><a data-title="端口关闭" onClick="mainctrl.location='autodown.asp?ltime='+<% write(lltime); %>;"
								href="#">端口关闭</a>
						</li>
						<!-- <li><a data-title="IPMAC绑定"
								onClick="mainctrl.location='ipmactab.asp?ltime='+<% write(lltime); %>;"
								href="#">IPMAC绑定</a>
						</li> -->
						<li><a data-title="DoS攻击配置"
								onClick="mainctrl.location='dos_attack.asp?ltime='+<% write(lltime); %>;"
								href="#">DoS攻击配置</a>
						</li>
						<li><a data-title="非法访问控制"
								onClick="mainctrl.location='illegalAccessCtl.asp?ltime='+<% write(lltime); %>;"
								href="#">非法访问控制</a>
						</li>
						<!-- <li><a data-title="VLAN MAC限制"
								onClick="mainctrl.location='vlansecuritymax.asp?ltime='+<% write(lltime); %>;"
								href="#">VLAN MAC限制</a>
						</li>  -->
					</ul>
				</li>
				<li class="slide">
					<a class="menu" data-title="系统运行" href="#">
						<i class="glyphicon glyphicon-1"></i>
						<span>系统运行</span>
						<small class="glyphicon glyphicon-plus-sign"></small>
					</a>
					<ul class="slide-menu" style="display: none;">
						<li><a data-title="日志主机" onClick="mainctrl.location='syslog.asp?ltime='+<% write(lltime); %>;"
								href="#">日志主机</a>
						</li>
						<li><a data-title="系统日志" onClick="mainctrl.location='log.asp?ltime='+<% write(lltime); %>;"
								href="#">系统日志</a>
						</li>
						<li><a data-title="告警日志"
								onClick="mainctrl.location='log_alarm.asp?ltime='+<% write(lltime); %>;"
								href="#">告警日志</a>
						</li>
						<li><a data-title="光纤模块" onClick="mainctrl.location='port_sfp.asp?ltime='+<% write(lltime); %>;"
								href="#">光纤模块</a>
						</li>
						<li><a data-title="用户管理" onClick="mainctrl.location='user.asp?ltime='+<% write(lltime); %>;"
								href="#">用户管理</a>
						</li>
						<li><a data-title="网络诊断" onClick="mainctrl.location='ping.asp?ltime='+<% write(lltime); %>;"
								href="#">网络诊断</a>
						</li>
						<li><a data-title="软件升级"
								onClick="mainctrl.location='upgradeswitch.asp?ltime='+<% write(lltime); %>;"
								href="#">软件升级</a>
						</li>
						<li><a data-title="配置管理"
								onClick="mainctrl.location='config_setting.asp?ltime='+<% write(lltime); %>;"
								href="#">配置管理</a>
						</li>
						<li><a data-title="时间同步" onClick="mainctrl.location='sntp.asp?ltime='+<% write(lltime); %>;"
								href="#">时间同步</a>
						</li>
						<li><a data-title="远程配置"
								onClick="mainctrl.location='remoteclient_config.asp?ltime='+<% write(lltime); %>;"
								href="#">远程配置</a>
						</li>
					</ul>
				</li>
				<li class="systemMenu">
					<a data-title="恢复默认" onClick="mainctrl.location='upgrade.asp?ltime='+<% write(lltime); %>;"
						href="#">
						<i class="glyphicon glyphicon-6"></i>
						<span>恢复默认</span>
					</a>
				</li>
				<li class="systemMenu">
					<a data-title="Save changes" onClick="mainctrl.location='reset.asp?ltime='+<% write(lltime); %>;"
						href="#">
						<i class="glyphicon glyphicon-6"></i>
						<span>保存重启</span>
					</a>
				</li>
				<li class="systemMenu">
					<a data-title="Logout" onClick="mainctrl.location='logout.asp?ltime='+<% write(lltime); %>;"
						href="#">
						<i class="glyphicon glyphicon-9"></i>
						<span>退出</span>
					</a>
				</li>
			</ul>
		</div>
		<div class="main-right">
			<div class="iBread" id="iBread">
				<ul></ul>
			</div>
			<div id="maincontent">
				<div class="container">
					<iframe name="mainctrl" id="mainctrl" title="mainctrl"
						onload="this.height = this.contentWindow.document.body.scrollHeight;" width="100%"
						src=<%write("'sysinfo.asp?ltime="); write(lltime); write("'"); %>></iframe>
				</div>
				<footer class="mobile-hide">
					<a href="http://www.joyware.com">Powered by www.joyware.com</a> /
					<a href="http://www.joyware.com">中威电子</a>
				</footer>
			</div>
		</div>
	</div>
</body>
<script src="js/menu.js"></script>
<script>
	//window.addEventListener("beforeunload", function () {
	//	localStorage.clear(' lasttimeHandle'); //}); 
	let mainDom=document.getElementsByClassName('main')[0];
	window.mainDom = mainDom;
	 </script>

</html>