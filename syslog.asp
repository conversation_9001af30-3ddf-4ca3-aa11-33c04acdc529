<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"802.1Q VLAN设置");</script></title>


<script language="JavaScript" type="text/JavaScript">

<% var errorcode, syslogcfg; getSyslogCfg(); %>


function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables all_tables2" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables btn3 all_tables1" ;
		}

     }

  }
}

function tdIpCheck(textValue)
{
    re1=/(\d+)(\W)(\d+)(\W)(\d+)(\W)(\d+)/
    Check=textValue.search(re1);
    if(Check==-1)
    {
        return false;
    }
    else
    {
        ipSplit=textValue.split('.');
        if(ipSplit.length!=4)
        {
            return false;
        }

        for(i=0; i<ipSplit.length; i++)
        {
            if(isNaN(ipSplit[i]))  return false;
			if(ipSplit[i].length > 1 && ipSplit[i].charAt(0) == "0") return false;
        }
    
        for(i=0; i<ipSplit.length; i++)
        {
            if(ipSplit[i] > 255)
            {
                return false;
            }
            if(ipSplit[i] < 0)
            {
                return false;
            }
        }
        if((ipSplit[0]==255) && (ipSplit[1]==255) && (ipSplit[2]==255) && (ipSplit[3]==255))
        {
            return false;
        }

        if((ipSplit[0]==0) || (ipSplit[0]==127) || (ipSplit[3]==0) || (ipSplit[3]==255))
        {
            return false;
        }

        if(ipSplit[0] >= 224)
        {
            return false;
        }
        return true;
    }
}


function display()
{
changebgcolor();

}

var syslogHostIpaddrList = [<% write(syslogcfg); %>];


function enableSyslog(ID,oprationType)
{
	//alert(ID+"and"+oprationType);
	document.getElementById("isDelSyslog").value = oprationType;
	document.getElementById("SyslogHostId").value = ID;
	
	document.form1.submit();
}

function delHost( i )
{
	
	//alert(ipstr);
	document.form1.flag.value = 2;
	
	
	document.form1.realip.value = syslogHostIpaddrList[i];
	
	document.form1.submit();
}

function checkSub()
{
	
	var serIP = document.getElementById("tsyslogIP").value;
	
	if(serIP == "")
	{
		alert("IP不能为空！");
		return false;
	}
	
	if(!tdIpCheck(serIP))
	{
		alert("IP地址错误");
		return false;
	}
	
	document.form1.flag.value = 1;
	document.form1.realip.value = serIP;
	
 	document.form1.submit();

}

</script>


</HEAD>

<BODY  onload=display() >
<br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>

<script>
function writeList()
{
var tempBuf = "";
var i;

for(i=0;i<syslogHostIpaddrList.length;i++)
	{
	tempBuf += "<tr class='tables_all'><td class='inputsyslog1' >";
	tempBuf += syslogHostIpaddrList[i];
	tempBuf += "</td>";

	tempBuf += "<td class='inputsyslog1'>";
	if(<% write(authmode); %> == 1)
		tempBuf += "<input type='button' name='button2' class='botton_under_line' id=button2 value='删 除' onclick='delHost("+ i +")' />";
	else
		tempBuf += "&nbsp;";
		
	tempBuf += "</td><tr >";
	}
	document.write(tempBuf);
}
</script>





<form id="form1" name="form1" method="post"  action="/goform/setSyslogHost"  onSubmit="" class="formContain">
<input type="hidden" name="flag" class="input_board3" value="">
<input type="hidden" name="realip" class="input_board3" value="">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>


 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="30px" ><font size="5" color="#404040"><div class="bot">日志主机设置</div></font></td></tr>
 </table>
 
<!--<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
<tr>
    <td  class=Tablelist id=tabs name=tabs>系统日志主机</td>
    
    
  </tr>
</table>
-->

<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
  <TBODY>
    
    <TR height=22>
      <TD width="42%" valign="top">&nbsp;&nbsp;<span class="td25">主机IP:</span></TD>
      <TD width="58%" ><span class="inputsyslog1">
        <input type="text" name="tsyslogIP" id="tsyslogIP">
      </span></TD>
      </TR>
    <TR height=22>
      <TD colspan="2" valign="top">
             <div align="center">
                 <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","modify2","checkSub()");</script>    
                 </div></TD>
</TR>
</TABLE>


<br>
<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
  <tr>
    <td width="51%"  class=Tablelist id=tabs name=tabs><div class="titLog">日志主机表</div></td>
    <td width="49%"   class="tablenew" id=tabs name=tabs><div align="right"></div></td>
  </tr>
</table>
<table width="98%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table3"  >
    <TR align="center" height=22>
	
      <TD width="24%"   nowrap class="all_tables_list"><span class="partition">主机IP设置</span></TD>
      <TD width="76%"   nowrap class="all_tables_list"><span class="partition">操作</span></TD>	
    </TR>
<script language="javascript">

writeList();
</script>
  </table> 
    
    
</form>
<style>
</style>

<script>
<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</BODY></HTML>
