<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_rtl.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"静态MAC与过滤");</script></title>

<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script language="JavaScript" type="text/JavaScript">

function getPage(page)
{
   location.href="forfil.asp?page="+page+"&ltime="+<% write(lltime); %>;
}

function dofirst(){
    location.href="forfil.asp?page=1&ltime="+<% write(lltime); %>;
}

function AddOption(port_name){
	var selectObject = document.getElementById("forfil_port");
	var y=document.createElement('option');
  	y.text=port_name;
	y.value=port_name;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  catch(ex)
    {
    selectObject.add(y); // IE only
    }
}

function addToPortRangeMirror(obj){
	var nodeArray;
	var tdobj = obj.parentNode;
	var trobj =	tdobj.parentNode;
	var forfil_mac = document.getElementById("forfil_mac");
	var forfil_vlan = document.getElementById("forfil_vlan");
	var mac_forfil = document.getElementsByName("mac_forfil");
	var forfil_port = document.getElementById("forfil_port");
	var i;
	forfil_vlan.value = trobj.cells[1].innerHTML;
	forfil_mac.value = trobj.cells[2].innerHTML;
	if(trobj.cells[4].innerHTML!="drop"){
		mac_forfil[0].checked = true;
		forfilChange();
		for(i=0;i<forfil_port.options.length;i++){
			if(forfil_port.options[i].value==trobj.cells[4].innerHTML){
				forfil_port.options[i].selected = true;
				break;
			}
			forfil_port.options[i].selected = false;
		}
	}
	else{
		mac_forfil[1].checked = true;
		forfilChange();
	}
}


function p(forfil_vlan, forfil_mac, forfil_type, forfil_port)
{
    var narr=5;
	var monitor;
    var tbtd;
    var i;
    var tbtr = document.getElementById("forfil_tbl").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");

	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
		
	/*display*/
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" onclick=\"addToPortRangeMirror(this)\"/>";
	tbtr.cells[1].innerHTML = forfil_vlan;
	tbtr.cells[2].innerHTML = forfil_mac;
    tbtr.cells[3].innerHTML = forfil_type;
	tbtr.cells[4].innerHTML = forfil_port;
}


function AddOption(portname){

	var selectObject = document.getElementById("forfil_port");
	var y=document.createElement('option');
  	y.text=portname;
	y.value = portname;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  	catch(ex)
    {
   	selectObject.add(y); // IE only
  	}
}

function selectForfil()
{
	var tf = document.forfilsetting;
	var forfil_mac = document.getElementById("forfil_mac").value;
	var forfil_vlan = document.getElementById("forfil_vlan").value;
	if(MacCheck(forfil_mac))
	{
		if(DataScope(forfil_vlan,4094,1))
		{
			tf.action = "/goform/AddForfil";
			tf.submit();
		}
		else{
			alert(putmsg(<% write(lang); %>,"vlan 的范围在1-4094"));
		}
	}
	else
	{
		alert(putmsg(<% write(lang); %>,"输入MAC地址非法  格式为16进制：HHHH.HHHH.HHHH"));
	}
	
}

function delForfil()
{
	var tf = document.forfilsetting;
	var forfil_mac = document.getElementById("forfil_mac").value;
	var forfil_vlan = document.getElementById("forfil_vlan").value;
	var mac_forfil = document.getElementsByName("mac_forfil");
	var forfil_port = document.getElementById("forfil_port");
	var forfil_tbl;
	var forfilvalue;
	var mac_forfilvalue;
	var i,j=0;
	
	if(forfil_vlan=="" || forfil_mac=="")
	{
		j++;
	}
	
	for(i=0;i<2;i++)
	{
		
		if(mac_forfil[i].checked==true){
			mac_forfilvalue = mac_forfil[i].value;
		}
	}
	if(mac_forfilvalue == "drop")
	{
		forfil_tbl = document.getElementById("forfil_tbl");
		for(i=1;i<forfil_tbl.rows.length;i++){
			if(forfil_tbl.rows[i].cells[1].innerHTML==forfil_vlan && forfil_tbl.rows[i].cells[2].innerHTML==forfil_mac && forfil_tbl.rows[i].cells[4].innerHTML == mac_forfilvalue){
				j=0;
				break;
			}
			j++;
		}	
	}
	else
	{
		for(i=0;i<forfil_port.options.length;i++){
			if(forfil_port.options[i].selected==true){
				forfilvalue=forfil_port.options[i].value;
				break;
			}
			j++;
		}
		forfil_tbl = document.getElementById("forfil_tbl");
		for(i=1;i<forfil_tbl.rows.length;i++){
			if(forfil_tbl.rows[i].cells[1].innerHTML==forfil_vlan && forfil_tbl.rows[i].cells[2].innerHTML==forfil_mac && forfil_tbl.rows[i].cells[4].innerHTML==forfilvalue){
				j=0;
				break;
			}
			j++;
		}
	}

	if(j==0){
		tf.action = "/goform/DelForfil";
		tf.submit();
	}
	else{
		alert(putmsg(<% write(lang); %>,"你要删除的数据不存在!"));
	}
	
}
function checkData()
{
	var tf=document.forfilsetting;
	tf.action = "/goform/saveComm?name=forfil";
	tf.submit();	
}

function forfilChange()
{
	var mac_forfil = document.getElementsByName("mac_forfil");
	var forfil_port = document.getElementById("forfil_port");
	if(mac_forfil[0].checked==true)
	{
		forfil_port.disabled=false;
	}
	if(mac_forfil[1].checked==true)
	{
		forfil_port.disabled=true;
	}
}

function initSel()
{
	var mac_forfil = document.getElementsByName("mac_forfil");
	var forfil_port = document.getElementById("forfil_port");
	mac_forfil[0].checked=true;
	forfil_port.disabled=false;
}

function refreshpage()
{
  location.href='forfil.asp?page=1&ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('forfil',<% write(lang); %>);
}

</script>
</head>

<body  onLoad="initSel();"><br>

<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>

	<form name="forfilsetting" method="POST" action="mac.asp">
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
	<input type="hidden" name="left_menu_id" value="">
  
<table id="mainTb2" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     <tr><td>
  		<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
         <tr>
       <td height="30px" colspan="6" align="left"   class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"静态MAC");</script>      </th>
       </tr>
       </table>
       </td></tr>
       <tr><td> 
     <table  width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">
   
	   
	   	<tr>
       		<td height="30px" align="left" class="crons">&nbsp;VLAN</td>
        	<td colspan="5" align="left" class="crons">
         	&nbsp;<input type="text" name="forfil_vlan" id="forfil_vlan" >&nbsp;(1-4094)          	</td>
       </tr>
	   
       <tr>
       		<td width="15%" height="30px" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"MAC地址");</script></td>
       		<td colspan="5" align="left" class="crons">
        		&nbsp;<input type="text" name="forfil_mac" id="forfil_mac" >&nbsp;<script>writemsg(<% write(lang); %>,"(格式为16进制：HHHH.HHHH.HHHH)");</script>      		</td>
       </tr>
	   
        
		<tr>
			<td height="30px" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"功能选择");</script></td>
			<td colspan="5" align="left" class="crons"><label></label>                 <label></label>                 <label>
               <input type="radio" name="mac_forfil"  id="mac_forfil2" value="interface" onClick="forfilChange()">
               </label> <font id="static">&nbsp;<script>writemsg(<% write(lang); %>,"静态MAC");</script></font>
               &nbsp;
			   		<font id="out_data">&nbsp;<script>writemsg(<% write(lang); %>,"转发端口");</script></font>
               <label><select id="forfil_port" name="forfil_port"></select>
				 	<script>
						<% AppendOptionForfil(); %>	
					</script>
               </label>
               <div  style='display:none'>
			   <label>
			   		<input type="radio" name="mac_forfil" id="mac_forfil1" value="drop" onClick="forfilChange()" >
			   </label><font id="all_data">&nbsp;<script>writemsg(<% write(lang); %>,"MAC过滤");</script></font>
			   </div>
               </td>
		</tr> 
		
       <tr>
		 	<td colspan="6" align="center" class="crons">
		 	<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","add_arp","selectForfil()");</script>
		 	<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","det_arp","delForfil()");</script>
			</td>
		</tr>
             </table>
             </td>
       </tr>
       <tr>
         <td height="8"></td>
      </tr>
	   <tr>
	   <td>
	   <table id="forfil_tbl" name="forfil_tbl"	 border="0" cellspacing="0" cellpadding="0" width="100%"  class="tablebord">
	      <tr>
		      <td width="15%"  height="25" align="center"  class="td6"></td>
			  <th width="21%"  height="25" align="center" class="td6">VLAN</th>
			  <th width="21%" height="25" align="center" class="td6"> &nbsp;<script>writemsg(<% write(lang); %>,"MAC地址");</script></td>
			  <th width="21%"  height="25" align="center" class="td6">&nbsp;<script>writemsg(<% write(lang); %>,"类型");</script></td>
			  <th width="21%"  height="25" align="center" class="td6">&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></td>
		  </tr>
		  <script>
		  		<%  var errorcode; ForfilShow("mac"); %>	
		  </script>
	   </table>
	   </td>
	   </tr>
    <tr>
	  	   <td colspan="6" align="center" height="23">
	  	       <script>writebutton(1,<% write(lang); %>,"首  页","button","button","firstpage","dofirst()");</script>
				<%ForfilShow("pagebutton");%>
				 &nbsp;
	  		    <%ForfilShow("pagenum");%>
			    <%ForfilShow("allpage");%>
	  		    
    </tr>  
    </table>
 </td></tr> 
 
</table>
</td></tr>

</table>
<input type="hidden" name="h_arp_enable" value="@h_arp_enable#">
<input type="hidden" name="todo" value="save">
<INPUT type="hidden" name="this_file" value="port.html">
<INPUT type="hidden" name="next_file" value="port.html">
<input type="hidden" name="message" value="@msg_text#">
</form>  
<script>
changebgcolor();
changebgcolor_name("forfil_tbl");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</html>


