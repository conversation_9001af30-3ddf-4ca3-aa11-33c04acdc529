<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"端口管理");</script></title>
<script>

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function changebgcolor3(){
 var tab = document.all.table3;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function MSTPCISTBRIBASEINFO(v1, v2, v3, v4, v5, v6, v7, v8, v9)
{
	document.write("<tr height='30'><td align=left' class='crons'>"+v2+"</td><td align=left' class='crons'>"+v3+"</td><td align=left' class='crons'>"+v4+"</td><td align=left' class='crons'>"+v5+"</td><td align=left' class='crons'>"+v6+"</td><td align=left' class='crons'>"+v8+"</td><td align=left' class='crons'>"+v9+"</td></tr>");
	return ;
}



function P(m1,m2,m3,m4,m5,m6,m7)
{
    var narr=6;
    var tbtd;
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table3").insertRow(-1);
	
    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");
	//tbtr.setAttribute("id", "tr_"+portId);
	
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	
	
    tbtr.cells[0].innerHTML = m1;
    tbtr.cells[1].innerHTML = m2;
    tbtr.cells[2].innerHTML = m3;
    tbtr.cells[3].innerHTML = m4;
    tbtr.cells[4].innerHTML = m5;
    tbtr.cells[5].innerHTML = m6;
}





function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm";
	tf.submit();
}
function RefreshInfor()
{
 location.href="mstpinfor.asp?portNo="+document.webForm.portforinstance.value;
}

function refreshpage()
{
  location.href='mstpinfor.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('mstpinfor',<% write(lang); %>);
}


var stpPortInfoPortList = [];

function writeLines()
{
var j=0;
for(var i=0;i<stpPortInfoPortList.length/7;i++)
{
	document.write("      <tr class='tables_all'>");
	
		document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	
	j++;
	document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	j++;
	document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	j++;
	document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	j++;
	document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	j++;
	document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	j++;
	document.write("        <td class='inputsyslog1'>"+stpPortInfoPortList[j]+"</td>");
	j++;
	document.write("      </tr>");
	
	
}
	
}

</script>
</head>
<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setMstpPortCfg">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0"><tr><td>
	<table width="98%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" ><tr><td valign="top" >
		<table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
	      <tr>
	        <td>
	        
 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>spanning tree信息</b></font></td></tr>
 </table>
 
<!--
	        <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
	            <tr>
	              <td colspan="2" align="left" class="cword09">&nbsp<script>writemsg(<% write(lang); %>,"生成树信息");</script></td>
	            </tr>
	        </table>
	        -->

	        </td>
	      </tr>     
		  	<tr>
		    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0">      
		      <tr>     
		        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
		            <tr height="25">
						<td width="100%">
							<table width="100%"  id="table1" class="tablebord" border="0" align="center" cellpadding="0" cellspacing="0" >
							<tr height="25"><td colspan="9" align="left" width="100%">&nbsp;<script>writemsg(<% write(lang); %>,"桥基本信息");</script></td></tr>
							<tr><td align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥状态");</script></td><td align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"协议状态");</script></td><td align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥优先级");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥ID");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"根桥ID");</script></td><td align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"根端口");</script></td><td align="left" width="9%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"根开销");</script></td></tr>
							<script>
							<% showMstpCistBriTable(); %>
							</script>
							</table>
						</td>                     
		            </tr>
		        </table></td>
		      </tr>      
		    </td>
		  </tr> 
		 
		  <tr>
		    <td valign="top" >
			
	
<br>
	        
 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#ABD8EF" height="20px" ><font size="3" color="#FFFFFF"><b>STP 端口状态</b></font></td></tr>
 </table>
 
<!--

<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
  <tr>
    <td width="51%"  class=Tablelist id=tabs name=tabs>STP 端口状态</td>
    <td width="49%"   class="tablenew" id=tabs name=tabs><div align="right"></div></td>
  </tr>
</table>
-->
<table width="100%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table3"  >
    <TR align="center" height=22>
	    <td class="all_tables_list" width="14%"  align="center">端口</td>
        <td width="14%"  class="all_tables_list">端口角色</td>
        <td width="14%"  class="all_tables_list">端口状态</td>
        <td width="14%"  class="all_tables_list">路径开销</td>
        <td width="14%"  class="all_tables_list">优先级</td>
        <td width="16%"  class="all_tables_list">类型</td>
    </TR>
 	<script>
							<% StpPortShow(); %>
							</script>
  </table>		
			
			
			
			</td></tr>
  		<tr>
	        <td align="center" height="35"><script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
		        &nbsp;
		        &nbsp;
		        </td>
	      </tr>
        </table>
     </td></tr></table>
</form>
<br>


<script>
changebgcolor();

changebgcolor3();

</script>
</body>
</html>
