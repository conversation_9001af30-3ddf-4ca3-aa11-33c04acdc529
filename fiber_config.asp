<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<title><script>writemsg(<% write(lang); %>,"光口千百兆设置");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript">

var portNum = 24;
var trunkNum = 4;
var timeHandle;
var timeout=10*60*1000;
var language="ch";
function checktop(lang)
{
	language=lang;
	if (top.location == location)
	{
		top.location.href = "login_"+lang+".asp";
	}
}
checktop(language);

function Keydown()
{
	localStorage.setItem("lasttimeHandle", 30000);
}
function Mousemove()
{
	localStorage.setItem("lasttimeHandle", 30000);
}

function Check_click_key_mouse()
{
    document.onkeydown=Keydown;
    document.onmousemove=Mousemove;
}

function overtime()
{
	if(language=="en")
		alert("Idle timeout. Please login again");	
	else
		alert("您超时了，请重新登录!");	
	//parent.frames[2].menuForm.submit();
	top.location.href="login_"+language+".asp";
}


function setOverTime(obj)
{
    timeHandle = setTimeout("overtime()",timeout);
}

Check_click_key_mouse();
//setOverTime();

var _msgTable_en={

"输入中发现非法字符!":"Illegal characters found in input!",

"优先级必须是4096的倍数!":"Priority must be multiple of 4096!",

"当前没有日志记录!":"No log yet!",

"当前版本":"Current Version",
"版本升级":"Software",

"当前第&nbsp;0&nbsp;页":"Current 0",
"当前第&nbsp;1&nbsp;页":"Current 1",
"当前第&nbsp;2&nbsp;页":"Current 2",
"当前第&nbsp;3&nbsp;页":"Current 3",
"当前第&nbsp;4&nbsp;页":"Current 4",
"当前第&nbsp;5&nbsp;页":"Current 5",
"当前第&nbsp;6&nbsp;页":"Current 6",
"当前第&nbsp;7&nbsp;页":"Current 7",
"当前第&nbsp;8&nbsp;页":"Current 8",

"总共&nbsp;0&nbsp;页":"Total 0",
"总共&nbsp;1&nbsp;页":"Total 1",
"总共&nbsp;2&nbsp;页":"Total 2",
"总共&nbsp;3&nbsp;页":"Total 3",
"总共&nbsp;4&nbsp;页":"Total 4",
"总共&nbsp;5&nbsp;页":"Total 5",
"总共&nbsp;6&nbsp;页":"Total 6",
"总共&nbsp;7&nbsp;页":"Total 7",

/*button start*/
"浏览...":"Browse...",
"保  存":"Save",
"下  载":"Download",
"上  传":"Upload",
"刷  新":"Refresh",
"帮  助":"Help",
"修  改":"Modify",
"取  消":"Cancel",
"删  除":"Delete",
"添  加":"Add",
"首  页":"First",
"创  建":"Create",
"开  始":"Start",
"重启系统":"Reboot",
"清  空":"Clear",
"上传配置文件":"UpLoad configuration File",
"下载配置文件":"Download configuration File",
"配置文件":"Configure File",
"下载当前版本":"Download the current version",
"升  级":"Upgrade",
"上一页":"Previous",
"下一页":"Next",
"尾  页":"Last",
"清  除":"Clear",
"恢  复":"Reset",
"&nbsp;&nbsp;&nbsp;服务热线：xxx-xxx-xxxx&nbsp;&nbsp;&nbsp;建议使用IE 6.0以上版本浏览器":"&nbsp;&nbsp;&nbsp;Service hotline：xxx-xxx-xxxx&nbsp;&nbsp;&nbsp;It is recommended to use IE6 or newer version",
"南京 　版权所有":" 　Copyright: nanjing.",

"ZZZ":"ZZZ"
};


var _msgTable_ch={
"端口管理":"",
"文件管理":"",
"语言":"",
"中文":"",
"英文":"",
"系统管理":"",
"请选择升级映像文件。":"",
"系统剩余内存过小,不能升级!":"",
"确定要升级吗?":"",
"整个升级过程将会持续几分钟，请勿在期间切断电源或者拔掉网线!":"",
"请选择配置文件。":"",
"确定要升级配置文件吗?":"",
"系统正在升级,请稍侯！":"",
"系统正在升级":"",
"正在上传配置":"",
"系统正在上传配置文件":"",
"请耐心等候":"",
"当前版本":"",
"版本升级":"",
"配置文件":"",
"下载":"",
"请耐心等候":"",

"ZZZ":"ZZZ"
};
function putmsg(lang,key)
{
try {
      eval("var m=_msgTable_"+lang+"[key];");
      if (!m)
      {
       return key;
      }
       return m;
	}
	catch (e) {
	   return key;
	}
}

function writemsg(lang,key)
{
	document.write(putmsg(lang,key));
}

function writebutton(priv,lang,key,bclass,btype,bname,bfunc)
{
	var outputstr ="";
	
	if((priv == 1)||(priv == 5))
	  outputstr="<input class="+bclass+" id='"+bname+"' name='"+bname+"' type='"+btype+"' value='"+putmsg(lang,key)+"' onClick='return "+bfunc+";'>";
	else
		outputstr="&nbsp;";
	document.write(outputstr);
}

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function isValidString(str)
{
	var validc = "/'%`\"\\><";
	var a,c;
	for(i = 0 ; i < validc.length; i ++)
	{
		c = validc.substring(i, i + 1);
		if(str.indexOf(c) > -1)
		{
			return true;
		}
	}
	return false;
}


/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true;  
			 addToPortRange(objs[i]);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRange(objs[i]);
			}
             
        }
    } 
    
}

/*MTU judgment*/
function checking2()
{
	var port_range = document.getElementById("port_range").value;
	var port_speed = document.getElementById("port_speed");
	var tf=document.port_setting;
	var i,j;
	
	if(((port_range.indexOf("ge")>=0) && (port_range.indexOf("xe")>=0))||
		((port_range.indexOf("ge")>=0) && (port_range.indexOf("sa")>=0))||
		((port_range.indexOf("ge")>=0) && (port_range.indexOf("po")>=0))||
		((port_range.indexOf("xe")>=0) && (port_range.indexOf("sa")>=0))||
		((port_range.indexOf("xe")>=0) && (port_range.indexOf("po")>=0))||
		((port_range.indexOf("po")>=0) && (port_range.indexOf("sa")>=0)))
	{
        alert(putmsg(<% write(lang); %>,"不同类型的端口不能同时配置!"));
		return;
	}

	tf.submit();
}

/*display function*/
function P(portId, cspeed)
{

    var narr=3;
	var arr="";
	var speed="";
	var Desc="";
    var tbtd;
    var i;
    var opt;
    var tbtr = document.getElementById("table_port").insertRow(-1);

	
    tbtr.classname = "td7";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "td7");
    tbtr.setAttribute("className", "td7");
	tbtr.setAttribute("id", "tr_"+portId);

	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	

        tbtr.appendChild(tbtd);
    }

	/*display*/
	tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"checkbox_index\" value=\""+portId+"\" onchange=\"addToPortRange(this)\"/>";
	tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = cspeed;

}

/*
	Show all check true port, and will last a port data displayed
*/
function addToPortRange(obj){
	var trid="tr_"+obj.value;
	var nodeArray;
	var trobj = document.getElementById(trid);
	var target = document.getElementById("port_range");
	var port_speed = document.getElementById("port_speed");

	var p = obj.value;
	var i;

	if(obj.checked)
	{
		target.value = target.value  + p + " ";


		for(i=0;i<port_speed.options.length;i++)
		{
			if(port_speed.options[i].text==trobj.cells[2].innerHTML)
			{
				port_speed.options[i].selected=true;
				break;
			}
		}			
	}else{

		target.value = target.value.replace(p+" ","");
	}

}

function refreshpage()
{
  location.href='fiber_config.asp?ltime='+<% write(lltime); %>;
}

function changebgcolor_port(){
 var tab = document.all.table_port;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

</SCRIPT>
</head>

<body  onload=""><br>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>

<form name="port_setting" method="POST" action="/goform/fiberconfigPortChange">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr>
<td>

	<table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
	<tr>
	<td>

		<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
			<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>光口千百兆设置</b></font></td></tr>
		</table>

	</td>
	</tr>
	
	<tr>
	<td>
			
		<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">
			<tr height="30">
				<td align="left" class="td7">&nbsp;<script>writemsg(<% write(lang); %>,"端口选择");</script></td>
				<td width="81%" align="left" class="td7">&nbsp;
					<input name="port_range" type="text" class="input_x" id="port_range" readonly="true"/>
				</td>
			</tr>		

			<tr height="25"  >
				<td width="20%" align="left" class="td7">&nbsp;<script>writemsg(<% write(lang); %>,"端口速率");</script></td>
				<td align="left" class="td7">&nbsp;
					<select name="port_speed" id="port_speed" class="select1"  >
					<option value="1000M" selected><script>writemsg(<% write(lang); %>,"1g");</script></option>
					<option value="100M"><script>writemsg(<% write(lang); %>,"100m");</script></option>
					</select>               
				</td>
			</tr>

			<tr>
				<td colspan="2" align="center" class="td7">
					<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify","checking2()");</script>
				</td>
			</tr>
		</table>
		
	</td>
	</tr>
	
	<tr>
		<td height="8"></td>
	</tr>

	<tr>
	<td>
		<table width="100%" height="35"  border="0" cellpadding="0" cellspacing="0"  class="tablebord" id="table_port">
			<tr height="30" align="center" class="td7">
				<th align="center" class="td2" width="20%" ></th>
				<th align="center" class="td2" width="40%" ><script>writemsg(<% write(lang); %>,"端口");</script></th>
				<th align="center" class="td2" width="40%" ><script>writemsg(<% write(lang); %>,"端口速率");</script></th>
			</tr>
            <script>
            	<% var errorcode;  fiberconfigShow();%>
            </script>
		</table>
	</td>
	</tr>

	<tr>
	<td align="center" height="35">
		<table> 
			<tr>
				<script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
				&nbsp;
				&nbsp;
			</tr>
		</table>
	</td>
	</tr> 
	</table>
	
</td>
</tr>
</table>

</form> 

<script>
changebgcolor();
changebgcolor_port();
<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>

</html>


