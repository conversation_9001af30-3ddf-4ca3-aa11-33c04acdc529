<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<script src="js/alpinejs.min.js" defer ></script>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">

<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>

<title>&nbsp;<script>writemsg(<% write(lang); %>,"GMRP");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript">

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function selectToAll() 
{  
		var cf = document.forms[0];
		var objs = document.getElementsByName("checkbox_index"); 
		var i;
		if(cf.check_all.checked == true)
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
		       if(objs[i].disabled==false && objs[i].checked==false){
		         objs[i].checked = true;  
			 		addToPortRange(objs[i]);
			 }
		    }
		}
		else
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
					if(objs[i].checked==true){
					objs[i].checked = false;  
					 addToPortRange(objs[i]);
			}
		         
		    }
		} 
    
}


function checkup()
{
	var tf = document.vlan_port;
	tf.action = "/goform/selGmrpPort";
	tf.submit();
}

function checkup2()
{
	var tf = document.vlan_port;

	// if (document.getElementById("c1").checked == true)
	// 	var ggb = document.getElementById("c1").value;
	// else
	// 	var ggb = document.getElementById("c2").value;
	/*
	if (document.getElementById("lp1").checked == true)
		var l2p = document.getElementById("lp1").value;
	else
		var l2p = document.getElementById("lp2").value;
		
	if ( ggb == "enable" && l2p != "pass")
	{
		alert("使用GMRP时请将L2 Protocol设为Enable状态");
		return;	
	}
	*/
	
	tf.action = "/goform/selGmrpGlobal";
	tf.submit();
}


function checkup3()
{
	var tf = document.vlan_port;
	tf.action = "/goform/selGmrpPort_leaveall";
	tf.submit();
}

function checkup4()
{
	var tf = document.vlan_port;
	tf.action = "/goform/selGmrpPort_leave";
	tf.submit();
}

function checkup5()
{
	var tf = document.vlan_port;
	tf.action = "/goform/selGmrpPort_join";
	tf.submit();
}

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var target = document.getElementById("port_range");
	var avid = document.getElementById("avid");
	var dvid = document.getElementById("dvid");
	var porttype = document.getElementById("porttype");
	var filter = document.getElementById("filter");
	var type_value = document.getElementById("type_value");
	var filter_value = document.getElementById("filter_value");
	var vlan_port_id = document.getElementById("vlan_port_id");
	var p = obj.value;

	if(obj.checked){
		target.value =target.value + p + " ";
		vlan_port_id.value =vlan_port_id.value + p + " , ";
	}
	else
	{
		target.value = target.value.replace(p + " ","");
		vlan_port_id.value = vlan_port_id.value.replace(p + " , ","");
	}

}

function P(portId,join,leave,leaveall,enable)
{
    var narr=5;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+portId);	


    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		
	        tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
    tbtr.cells[0].abbr = portId;
    
   
	

		//tbtr.cells[0].innerHTML = "<input type=\"checkbox\" id=\"input_"+portId+"\" name=\"checkbox_index\" value=\""+portId+"\" onclick=\"addToPortRange(this)\"/>";
	
		tbtr.cells[0].innerHTML = portId;

    tbtr.cells[1].innerHTML = join;
    tbtr.cells[2].innerHTML = leave;
    tbtr.cells[3].innerHTML = leaveall;
    tbtr.cells[4].innerHTML = enable;

}


function refreshpage()
{
  location.href='gmrp.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('vlan_port',<% write(lang); %>);
}


function display()
{
	var wValue = document.getElementById("gmrpglobal_cfg").value.split(',');
	let selectDom=document.getElementById("globalstateSelect");
	if(wValue[0] == "enable")
	selectDom[0].selected=true;
		
	else
	selectDom[1].selected=true;
		// document.getElementById("c2").checked = true;
}
var gmrpEnable,PortGmrp;
	function data(){

		return{
			gmrpEnable:responseStr.gmrpEnable,
			PortGmrp:responseStr.PortGmrp.map(val=>{
				return {
					portName:val.portName,
					joinTimer:val.joinTimer,
					leaveTimer:val.leaveTimer,
					allTimer:val.allTimer,
					portEnable:val.portEnable=='Disabled' ? 'disable' : 'enable'
				}
			})
		}
	}
</script>
</head>

<body  onload="display()" x-data="data()">
<br>

<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
retValue = <% var responseJsonStr; jw_get_gmrpConfig(); %>
 responseStr = <% write(responseJsonStr); %>;
 
	  function changeGmrp(val){
		var tf = document.vlan_port;
	let gmrpEnable=val.indexOf(',') ? val.split(',')[0] : val;
		let obj={
			pageName:'gmrp.asp',
			gmrpEnable,
			PortGmrp:[]	

		}
		tf.param1.value=JSON.stringify(obj);
		tf.action = "/goform/jw_set_gmrpConfig";

		tf.submit();
	  }1
	  function changeGmrpItem(row,index){
		var tf = document.vlan_port;
			
		let obj={
			pageName:'gmrp.asp',
			gmrpEnable:data().gmrpEnable.indexOf(',') ? data().gmrpEnable.split(',')[0] : data().gmrpEnable,
			PortGmrp:[{...row}].map(item=>{
				return {
					portName:item.portName,
					joinTimer:Number(item.joinTimer) ,
					leaveTimer:Number(item.leaveTimer) ,
					allTimer:Number(item.allTimer) ,
					portEnable:item.portEnable
				}
			})	

		}
		tf.param1.value=JSON.stringify(obj);
		tf.action = "/goform/jw_set_gmrpConfig";

		tf.submit();
	  }
</script>

<form name="vlan_port" method="POST" action="">
<% var l2pro, gmrpglobalCfg; getGMRPglobalCfg(); %>
<input type="hidden" name="gmrpglobal_cfg"  id="gmrpglobal_cfg"   value="<% write(gmrpglobalCfg); %>">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="vlan_port_id" id="vlan_port_id" >
<input type="hidden" name="flag" id="flag" >
<input type="hidden" name="type_value" id = "type_value"  value="@trunk_config#">
<input type="hidden" name="filter_value" id = "filter_value"  value="@trunk_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="pvid_config"  value="@pvid_config#">
<input type="hidden" name="egress_tagged" id="egress_tagged" value="@egress_tagged#">
<input type="hidden" name="param1" id="param1" >

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0"  class="formContain">
<tr>
	<td>
	
	<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" >
  	<tr>
    	<td valign="top" >

    	<table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	<tr>
     		<td>


			<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
			<tr>
				<td  
				colspan="2"
				width="30%"
				height="30px" ><font size="5" color="#404040"><div class="bot">GMRP功能配置</div></font></td>
				
			</tr>
			<div >
				<tr height="25">
				<td class="all_tables all_tables3" style="text-align: left;border: none;">
					
				<script>writemsg(<% write(lang); %>,"GMRP全局开关:");</script>
				</td>
				<td class="crons" colspan="3" >
					<select name="globalstate" id="globalstateSelect" x-model="gmrpEnable" @change="changeGmrp(gmrpEnable)" style="width: 200px;">
						<option value="enable"  >开启</option>
						<option value="disable"  >关闭</option>
					</select>
					
				</td>
				</tr>
				
				<tr height="25" x-show="gmrpEnable=='enable'">
					<td class="all_tables all_tables3" style="text-align: left;border: none;">
						提示:
					</td>
					<td colspan="" align="left" class="crons" style="font-weight: normal;font-size: 16px;">
							3 *Join Timer &lt;= &nbsp;Leave Timer &lt;Leave All Timer ,
						 default Timer value Join 20, Leave 60, Leave All 1000
					 </td>
				 </tr>
				 <tr height="10">
					<td></td>
				 </tr>
				<tr x-show="gmrpEnable=='enable'">
					<td colspan="2">
						<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" class="tablebord">
							<tr  height="32" align="center">
								<td class="tableTd">
									端口
								</td>
								<td class="tableTd">
									是否启用
								</td>
								<td class="tableTd">
									Leave All Timer(centiseconds)
								</td>
								<td class="tableTd">
									Leave Timer(centiseconds)
								</td>
								<td class="tableTd">
									Join Timer(centiseconds)
								</td>
								<td class="tableTd">
									操作
								</td>
							</tr>
							<template x-for="(row,index) in PortGmrp" :key="index+1">
								<tr class="tableTd" height="45" align="center">
									<td x-text="row.portName"></td>
									<td>
										<select x-model="row.portEnable" id="porttype" class="select1"  @change="changeGmrpItem(row)">
											<option value="enable" >开启</option>
											<option value="disable" >关闭</option>
										</select>
									</td>
									<td>
										<input  x-model.number="row.allTimer" type="number"  >
									</td>
									<td>
										<input  x-model.number="row.leaveTimer" type="number"  >
									</td>
									<td>
										<input  x-model.number="row.joinTimer" type="number"  >
									</td>
									<td>
										<div class="applyBtn" @click="changeGmrpItem(row)" style="line-height: 35px; background-color: #F0AD4E;cursor: pointer;font-family: Verdana, Arial, Helvetica">应  用</div>
										
									</td>

								</tr>

							</template>
						</table>
					</td>
				</tr>
			</div>
			</table>
         
			</td>
		</tr>
             
	
		
	
		</table>
		
		</td>
	</tr> 
	</table>
	
	</td>
</tr>
</table>
		<div class="formContain" x-show="gmrpEnable=='enable'" style="margin-top: 10px;">
			<div class="">
				<font size="5" color="#404040"><div class="bot">信息展示</div></font>
			</div>
			<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
				<tr align="center" height="25" class="crons">
					<th class="td2" width="10%" >&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></th>
					<th class="td2" width="20%">&nbsp;Join Timer</b></font></th>
					<th class="td2" width="20%">&nbsp;Leave Timer</b></font></th>						
					<th class="td2" width="20%">&nbsp;Leave All Timer</b></font></th>
					<th class="td2" width="30%">&nbsp;<script>writemsg(<% write(lang); %>,"端口启用");</script></th>						
				</tr>
				   <script>  <%  var errorcode; showGmrpPort(); %></script>
			   </table>
			   <div  align="center" style="padding: 5px;">
				<script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
			   </div>
		</div>
</form> 


<script>
// changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>


</body>
</html>

