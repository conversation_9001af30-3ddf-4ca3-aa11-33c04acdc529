<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"MAC地址绑定")</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>

<script language="JavaScript" type="text/JavaScript">

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function isINT(str){
var re = /^[0-9]*[0-9][0-9]*$/ ;

var result = re.test(str);   //测试 返回true或false

return result;

}


function getPage(page)
{
   location.href="mac_bind.asp?page="+page+"&ltime="+<% write(lltime); %>;
}

function del_boundmac(port, mac, vlan)
{
	var hid = document.macdel;
	
		hid.port_value.value=port;
		hid.mac_value.value=mac;
		hid.vlan_value.value=vlan;
	
	
	hid.action="/goform/MACBindDel";
	hid.submit();
	return 0;
}

function p(index, macbind_port, macbind_mac, macbind_vlan)
{
    var narr=5;
    var tbtd;
    var i;
    var tbtr = document.getElementById("table2").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");

	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
		
	/*display*/
	tbtr.cells[0].innerHTML = index;
	tbtr.cells[1].innerHTML = macbind_port;
	tbtr.cells[2].innerHTML = macbind_mac;
	tbtr.cells[3].innerHTML = macbind_vlan;
	tbtr.cells[4].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick=del_boundmac('"+macbind_port+"','"+macbind_mac+"','"+macbind_vlan+"')>";

}

function AddOption(port_name)
{
	var selectObject = document.getElementById("arp_port");
	var y=document.createElement('option');
	y.text=port_name;
	y.value=port_name;
	try
	{
    	selectObject.add(y,null); // standards compliant
  	}
  	catch(ex)
  	{
    	selectObject.add(y); // IE only
  	}
}

function MacCheck(mac_addr)
{
	var myRE =/^[0-9a-fA-F]{1,4}\.[a-fA-F0-9]{1,4}\.[a-fA-F0-9]{1,4}$/;
	if(myRE.test(mac_addr))
	{
		var val = mac_addr.split(".",1);
		var vals = val[0].split("");
			if(val[0].length==3){
				if(vals[0]<10 && vals[0]>=0){
					if(vals[0]%2==1){
						return false;
					}
				}
				else{
					if((vals[0].charCodeAt()+1)%2==1){
						return false;
					}
				}
			}
			if(val[0].length==4){
				if(vals[1]<10 && vals[1]>=0){
					if(vals[1]%2==1){
						return false
					}
				}
				else{
					if((vals[1].charCodeAt()+1)%2==1){
						return false
					}
				}
			}
			return true;
	}
	else
	{
		return false;
	}
	
}

function DataScope(value,max,min)
{
	if(!isNaN(value) && value>=min && value<=max)
		return true;
	else
		return false;
}


/*isNaN判断是否是数字，包括整数与浮点数*/
function MacBindAdd()
{
	var tf=document.macbindsetting;
	var arp_limited_vlan = document.getElementById("arp_limited_vlan").value;
	var mac_bind_addr = document.getElementById("mac_bind_addr").value;

	if(MacCheck(mac_bind_addr))
	{
		if(DataScope(arp_limited_vlan,4094,1))
		{
			tf.action = "/goform/MACBindAdd";
			tf.submit();
		}
		else{
			alert(putmsg(<% write(lang); %>,"vlan 的范围在1-4094"));
		}
	}
	else
	{
		alert(putmsg(<% write(lang); %>,"输入MAC地址非法  格式为16进制：HHHH.HHHH.HHHH"));
	}

}

function refreshpage()
{
  location.href='mac_bind.asp?ltime='+<% write(lltime); %>;
}

<% var maclimitedcfg; getMacBindCfg(); %>

var macBindList = [<% write(macbindcfg); %>];

function writeMacBindLine()
{
	for(var i=0;i<macBindList.length;i++)
	{
		document.write("<tr  class='tables_all'>");
		document.write("    <td height='32' class='inputsyslog1'>"+macBindList[i].split(",")[0]+"</td>");
		document.write("    <td height='32' class='inputsyslog1'>"+macBindList[i].split(",")[1]+"</td>");
		document.write("    <td height='32' class='inputsyslog1'>"+macBindList[i].split(",")[2]+"</td>");
		document.write("    <td height='32' class='inputsyslog1'>"+macBindList[i].split(",")[2]+"</td>");

		document.write("  </tr>");
	}
}


</script>
</head>
<body  >
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
	
<form name="macbindsetting" method="POST" action="">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="left_menu_id" value="">

<table id="table111" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
	<tr>
  	<td>
  		<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" >
  			<tr>
  			<td valign="top" >
  				<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
  					<tr>
  					<td>
  						<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
  							<tr>
              				<td colspan="5" align="left" class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"MAC地址绑定");</script></td>
       						</tr>
       					</table>
       				</td>
       				</tr>
       		
       				<tr>
       				<td> 
     					<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table1" class="tablebord">      						
			       			<tr height="25">
			       				<td width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></td>
			       				<td colspan="4" align="left" class="crons">&nbsp;<select id="arp_port" name="arp_port"><script><% var errorcode;AppendOption(); %></script></select></td>
			       			</tr>
			       			
			       			<tr height="25">
			       				<td width="20%" align="left" class="crons">&nbsp;Mac Addr</td>
			        			<td colspan="4" align="left" class="crons">&nbsp;<input type="text" name="mac_bind_addr" id="mac_bind_addr" >&nbsp;(格式为16进制：HHHH.HHHH.HHHH)</td>
							</tr>
			       			
			       			<tr height="25">
			       				<td width="20%" align="left" class="crons">&nbsp;Vlan ID</td>
			        			<td colspan="4" align="left" class="crons">&nbsp;<input type="text" name="arp_limited_vlan" id="arp_limited_vlan" >&nbsp;(1-4094)</td>
							</tr>
							
							<tr height="25">
								<td colspan="5" align="center" class="crons">
									<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","add_limited_arp","MacBindAdd()");</script>
<!--					 	            <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","del_limited_arp","delmac_bind()");</script>
-->
								</td>
							</tr>
						</table>
					</td>
					</tr>
			
					<tr>
						<td height="8"></td>
					</tr>
			
					<tr>
					<td>
						<table width="100%" border="0" align="center" cellspacing="0" cellpadding="0" id="table2" class="tablebord">
							<TR align="center" height=22>
			  					<th width="10%"  height="25" align="center" class="td6">序号</th>
			  					<th width="18%"  height="25" align="center" class="td6">&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></td>
								<TD width="17%"  nowrap class="all_tables_list">Mac Addr</TD>
								<TD width="17%"  nowrap class="all_tables_list">VlanName</TD>
				  				<th width="18%"  height="25" align="center" class="td6">&nbsp;<script>writemsg(<% write(lang); %>,"删除");</script></td>
							</TR>
							  <script>
							  <% MACBindTabShow("mac"); %>
							  </script>
					
						</table> 
					</td>
					</tr>
					
					<tr>
					<td colspan="5" align="center" height="23">
						<script>writebutton(1,<% write(lang); %>,"首  页","button","button","firstpage","getPage(1)");</script>
						<%MACBindTabShow("pagebutton");%>
						&nbsp;
						<%MACBindTabShow("pagenum");%>
						<%MACBindTabShow("allpage");%>
					</td>
					</tr>  

				</table>
			</td>
			</tr>
		</table>
	</td>
	</tr>
</table>


<input type="hidden" name="h_arp_enable" value="@h_arp_enable#">
<input type="hidden" name="todo" value="save">
<INPUT type="hidden" name="this_file" value="port.html">
<INPUT type="hidden" name="next_file" value="port.html">
<input type="hidden" name="message" value="@msg_text#">

</form>


<script>
changebgcolor();
changebgcolor2();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>


<form name="macdel" method="POST" action="">
	    <input type="hidden" name="ltime" value=<% write(lltime); %>>
	    <input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

	<input type="hidden" name="port_value"  value="" >
	<input type="hidden" name="mac_value"  value="" >
	<input type="hidden" name="vlan_value"  value="" >

</form>
<br><br><br>

</body>
</html>

