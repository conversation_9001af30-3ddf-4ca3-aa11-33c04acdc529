<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"SNTP管理");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script>

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function check_ip(fobj) 
{ 	
		var tmp = fobj.value;
    var ip = new RegExp("^([0-9]+).([0-9]+).([0-9]+).([0-9]+)$");
    if (tmp.match(ip) == null)
    		return false;
	
    var ipaddr = tmp.split(".");
    if(ipaddr[0] >0 && ipaddr[0] <255 && ipaddr[1]>=0 && ipaddr[1]<=255 && ipaddr[2]>=0 && ipaddr[2]<=255 && ipaddr[3]>0 && ipaddr[3]<255) 
        return true;
   
    return false; 
}

function messageCheck()
{
	if(check_ip(document.webForm.ipaddr1)==false)
		{
			alert(putmsg(<% write(lang); %>,"ip地址不合法,请检测!"));
			return ;
		}
	document.webForm.ipaddr.value=document.webForm.ipaddr1.value;
	document.webForm.ipaddr1.disabled=true;
	document.webForm.submit();
}

function showHelpinfo()
{
   showHelp('ping',<% write(lang); %>);
}
</script>
</head>
<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/pingAction" class="formContain">
<input type="hidden" name="ipaddr">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0"><tr><td>
	<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" ><tr><td valign="top" >
		<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
	      <tr>
	        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
	            <tr height="25">
	              <td colspan="3" align="left" class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"ping测试");</script></td>
	            </tr>
	        </table></td>
	      </tr>
	      <tr>
	        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">
	            <tr height="25">
	              <td width="20%" align="center" class="crons" style="font-weight: bold;">&nbsp;<script>writemsg(<% write(lang); %>,"IP地址:");</script></td>
	              <td width="30%" align="center" class="crons">&nbsp;
	                  <input type="text" name="ipaddr1" id="ipaddr1" class="input_board3">
	                 </td>
                <td width="30%" align="center" class="crons">&nbsp;
                <script>writebutton(1,<% write(lang); %>,"开  始","button","button","apply","messageCheck()");</script>
               </td>
	            </tr>
	            <tr height="25">
	            <% var errorcode;showLinecheck(); %>
	            </tr>
	        </table></td>
	      </tr>
	      <tr>
    <td align="center" height="35">
    </td>
	</tr>
    </table></td>
  </tr> 
</table>
<script>
changebgcolor();

</script>
</td></tr></table>
</form>

</body>
</html>
