<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_2.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"端口管理");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script>

function MSTPBRICFGINFO(v1, v2, v3, v4, v5)
{
	document.write("<tr height='30'><td align='left' class='crons'>"+v1+"</td><td align='left' class='crons'>"+v2+"</td><td align='left' class='crons'>"+v3+"</td><td align='left' class='crons'>"+v4+"</td><td align='left' class='crons'>"+v5+"</td></tr>");
	return ;
}
function MSTPBRIINSTVLAN(v1, v2, v3)
{
	document.write("<tr height='30'><td align='left' class='crons'>"+v1+"</td><td align='left' class='crons'>"+v2+"</td><td align='left' class='crons'>"+v3+"</td></tr>");
	return ;
}
function MSTPPINSTINFO(v1,v2,v3,v4,v5,v6,v7)
{
	document.write("<tr height='30'><td align='left'  class='crons'>"+v1+"</td><td align='left'  class='crons'>"+v2+"</td><td align='left'  class='crons'>"+v3+"</td><td align='left'  class='crons'>"+v4+"</td><td align='left' class='crons'>"+v5+"</td><td align='left' class='crons'>"+v6+"</td><td align='left' class='crons'>"+v7+"</td></tr>");
	return 0;
}
function showportcfg(portNo)
{
	window.location.href="mstpbasic.asp?portNo="+portNo;
}
function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm";
	tf.submit();
}

function refreshpage()
{
  location.href='mstpbasic.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('mstpbasic',<% write(lang); %>);
}

</script>
</head>
<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0"><tr><td>
	<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" ><tr><td valign="top" >
		<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
		     	<tr><td>
		     		<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
		            	<tr>
		            	  <td colspan="2" align="left" class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"MSTP基本信息");</script></td>
		            	</tr>
		       		</table></td>
		      	</tr>     
			  	<tr>
			    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">      
			      <tr>     
			        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
			            <tr height="25">
							<td width="100%">
								<table width="100%"  id="table1" class="tablebord"  border="0" align="center" cellpadding="0" cellspacing="0" >
								<tr height="25"><td colspan="5" align="left" width="100%">&nbsp;<script>writemsg(<% write(lang); %>,"域配置信息");</script></td></tr>
								<tr height="25"><td width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥名");</script></td><td width="20%" align="left" class="crons">Format id</td><td width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"域名");</script></td><td width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"修订版本");</script></td><td width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"摘要信息");</script></td></tr>			
								<script>
								<% showMstpBriConfigTable(); %>
								</script>
								</table>
							</td>
			            </tr>
			        </table></td>
			      </tr>      
			    </td>
			  </tr> 
			   <tr>
			    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">      
			      <tr>     
			        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" >
			            <tr height="25">
							<td width="100%">
								<table width="100%" id="table2" class="tablebord"  border="0" align="center" cellpadding="0" cellspacing="0" >
								<tr height="25"><td colspan="3" align="left" width="100%">&nbsp;<script>writemsg(<% write(lang); %>,"实例vlan关系");</script></td></tr>
								<tr height="25"><td width="10%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"桥名");</script></td><td width="15%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"实例ID");</script></td><td width="70%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN信息");</script></td></tr>			
								<script>
								<% showMstpBriInstVlanTable(); %>
								</script>
								</table>
							</td>
			            </tr>
			        </table></td>
			      </tr>      
			    </td>
			  </tr> 
			  <tr>
			    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">      
			      <tr>     
			        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" >
			            <tr height="25">
							<td width="100%">
								<table width="100%" id="table3" class="tablebord"  border="0" align="center" cellpadding="0" cellspacing="0" >
								<tr height="25"><td colspan="7" align="left" width="100%">&nbsp;<script>writemsg(<% write(lang); %>,"端口-实例关系");</script></td></tr>
								<tr><td align="left" width="10%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"端口名");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"实例ID");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"角色");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"状态");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"实例开销");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"实例端口优先级");</script></td><td align="left" width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN信息");</script></td></tr>
								<script>
								<% showMstpPortInstInfoTable(); %>
								</script>
								</table>
							</td>                     
			            </tr>
			        </table></td>
			      </tr>      
			    </td>
			  </tr> 
	
  			</table>
	</td></tr>
	<tr>
	        <td align="center" height="35">
	            <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
		        &nbsp;
		        &nbsp;
		        </td>
	      </tr>
	</table>
 </td></tr></table>
</form>


<script>
changebgcolor();
changebgcolor2();
changebgcolor3();

</script>

</body>
</html>

