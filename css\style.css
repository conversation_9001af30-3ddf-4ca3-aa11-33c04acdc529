/**
 *  Material is a clean HTML5 theme for LuCI. It is based on luci-theme-bootstrap and MUI
 *
 *  luci-theme-material
 *      Copyright 2015 <PERSON><PERSON> <<EMAIL>>
 *
 *  Have a bug? Please create an issue here on GitHub!
 *      https://github.com/LuttyYang/luci-theme-material/issues
 *
 *  luci-theme-bootstrap:
 *      Copyright 2008 <PERSON> <<EMAIL>>
 *      Copyright 2008 <PERSON><PERSON><PERSON> <<EMAIL>>
 *      Copyright 2012 <PERSON> <<EMAIL>>
 *
 *  MUI:
 *      https://github.com/muicss/mui
 *
 *  Licensed to the public under the Apache License 2.0
 */

/*
 *  Font generate by Icomoon<icomoon.io>
 */
/*@font-face {
    font-family: 'icomoon';
    src: url('../fonts/font.eot');
    src: url('../fonts/font.eot') format('embedded-opentype'),
    url('../fonts/font.ttf') format('truetype'),
    url('../fonts/font.woff') format('woff'),
    url('../fonts/font.svg') format('svg');
    font-weight: normal;
    font-style: normal;
}*/
/* IE 8
 * ---------------------------------------------------------------------*/
.node-main-login .lte8 .main > .main-left,
.node-main-login .lte8 .main > .main-right .iBread {
  display: none;
}
.node-main-login .lte8 .main-right {
  margin-left: 0em;
  display: table;
  width: 100%;
  border-top: 1px solid #eee;
}
.node-main-login > .lte8 #maincontent {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
}
.node-main-login > .lte8 #maincontent .container {
  display: inline-block;
  min-width: 16em;
  /*   border: 1px solid #404040;*/
  padding: 1em 2em;
  margin-bottom: 15em;
}
.node-main-login .lte8 .main .loginBox fieldset {
  background: none;
  padding: 0;
}
.node-main-login .lte8 .loginBox .input .icon-append {
  top: 2px;
}
.lte8 .footer {
  color: #ddd;
  text-align: right;
  padding: 1em;
}
.lte8 .footer a {
  color: #bbb;
}
.node-main-login .lte8 .footer {
  bottom: 40px;
  padding: 0.5em;
  position: absolute;
  width: 100%;
  background-color: #113560;
  text-align: center;
}

.lte8 > .container {
  padding: 5px;
  border-bottom: 2px solid #ddd;
}
.lte8 > .container > img {
  height: 30px;
}
.lte8 .main {
  height: 100%;
  position: fixed;
}
.lte8 .main-right {
  height: 97%;
}
.lte8 .main > .main-left > .nav > .slide > .menu,
.lte8 .main > .main-left > .nav > li > a,
.lte8 .main > .main-left > .nav-toggle a {
  padding: 0.5em 1em;
  font-size: 1.1em;
}
.lte8 .main > .main-left > .nav > .slide > .slide-menu > li {
  padding: 0.4em 3.5em;
}

.lte8 .main > .main-left > .nav > .slide > .menu small {
  font-size: 0.7em;
  left: 0px;
  top: 0px;
}
.lte8 .main-right .iBread {
  padding: 0em;
  height: 38px;
}

/* 字体图标
 * ---------------------------------------------------------------------*/
@font-face {
  font-family: "Glyphicons Halflings";
  src: url("./glyphicons-halflings-regular.woff2") format("woff2");
}

.glyphicon {
  padding-right: 0.8em;
  position: relative;
  top: 2px;
  display: inline-block;
  font-family: "Glyphicons Halflings";
  font-style: normal;
  font-weight: normal;
  /*line-height: 1;*/
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.glyphicon-menu-hamburger:before {
  content: "\e236";
}
.glyphicon-4:before {
  /*安全管理*/
  content: "\e185";
}
.glyphicon-1:before {
  /*设备状态*/
  content: "\e008";
}
.glyphicon-10:before {
  content: "\e030";
}
.glyphicon-5:before {
  /*系统运行*/
  content: "\e141";
}
.glyphicon-2:before {
  /*端口管理*/
  content: "\e019";
}
.glyphicon-3:before {
  /*高级配置*/
  content: "\e136";
}
.glyphicon-6:before {
  /*保存修改*/
  content: "\e166";
}
.glyphicon-7:before {
  /*恢复默认*/
  content: "\e178";
}
.glyphicon-8:before {
  /*重启*/
  content: "\e165";
}
.glyphicon-9:before {
  /*退出*/
  content: "\e017";
}
.glyphicon-plus-sign:before {
  content: "\e081";
}
.glyphicon-minus-sign:before {
  /*减号*/
  content: "\e082";
}
.glyphicon-user:before {
  content: "\e008";
}
.glyphicon-lock:before {
  content: "\e033";
  display: block;
}
.glyphicon-warning-sign:before {
  content: "\e107";
}
/*
 * ------------------------------------------------------------------------------------*/
.cbi-button-up,
.cbi-button-down,
.cbi-value-helpicon,
.showSide,
.main > .loading > span {
  font-family: "Glyphicons Halflings";
  speak: none;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  line-height: 1;

  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}

html,
body {
  margin: 0px;
  padding: 0px;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, Microsoft Yahei, Hiragino Sans GB,
    WenQuanYi Micro Hei, sans-serif;
}
select {
  padding: 0.4em 0.8em;
  padding: 0.4rem 0.8rem;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: inherit;
  font-size: 0.8rem;
}
select option {
  padding-left: 1em;
}
input {
  outline: none;
  padding: 0.36em 0.5em;
  padding: 0.36rem 0.5rem;
  border: 1px solid #ccc;
  -webkit-transition: border linear 0.2s, box-shadow linear 0.2s;
  -moz-transition: border linear 0.2s, box-shadow linear 0.2s;
  -ms-transition: border linear 0.2s, box-shadow linear 0.2s;
  -o-transition: border linear 0.2s, box-shadow linear 0.2s;
  transition: border linear 0.2s, box-shadow linear 0.2s;
  -webkit-box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}
input[type="checkbox"],
input[type="radio"] {
  border: none;
  box-shadow: none;
}
input[disabled],
select[disabled],
textarea[disabled],
input[readonly],
select[readonly],
textarea[readonly] {
  background-color: #f5f5f5;
  border-color: #ddd;
}
input:focus,
textarea:focus {
  outline: 0;
  border-color: rgba(82, 168, 236, 0.8);
  -webkit-box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1),
    0 0 8px rgba(82, 168, 236, 0.6);
  -moz-box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1),
    0 0 8px rgba(82, 168, 236, 0.6);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1),
    0 0 8px rgba(82, 168, 236, 0.6);
}
a {
  color: #0069d6;
  text-decoration: none;
}
code {
  /*color: #0099CC;*/
  color: #105dad;
}

abbr {
  /*color: #0099CC;*/
  color: #105dad;
  text-decoration: underline;
  cursor: help;
}

header,
.main {
  width: 100%;
  position: absolute;
}

header {
  /*height: 4rem;*/
  height: 2.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.26);
  transition: box-shadow 0.2s;
  float: left;
  position: fixed;
  z-index: 101;
}

footer {
  text-align: right;
  padding: 1rem;
  color: #aaa;
  font-size: 0.8rem;
  text-shadow: 0px 0px 2px #bbb;
}

footer > a {
  color: #aaa;
}

.main {
  top: 2.5em;
  top: 2.5rem;
  bottom: 0em;
  bottom: 0rem;
}

.main > .loading {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: block;
  background-color: rgb(240, 240, 240);
}

.main > .loading > span {
  display: block;
  text-align: center;
  margin-top: 2rem;
  color: #888;
  font-size: 1.3rem;
}

.main > .loading > span:before {
  content: "\e603";
  -webkit-animation: anim-rotate 2s infinite linear;
  animation: anim-rotate 2s infinite linear;
  margin-right: 0.2rem;
}

@-webkit-keyframes anim-rotate {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes anim-rotate {
  0% {
    -webkit-transform: rotate(0);
    -ms-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.main-left {
  float: left;
  /*width: 15%;*/
  width: calc(0% + 17rem);
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  white-space: nowrap;
  /*background-color: white;*/
  color: #fff;
  background: #105dad; /*#3a3633;#2a2725*/
  background: -moz-linear-gradient(left, #105dad 93%, #103469 100%);
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    color-stop(93%, #105dad),
    color-stop(100%, #103469)
  );
  background: -webkit-linear-gradient(left, #105dad 93%, #103469 100%);
  background: -o-linear-gradient(left, #105dad 93%, #103469 100%);
  background: -ms-linear-gradient(left, #105dad 93%, #103469 100%);
  background: linear-gradient(to right, #105dad 93%, #103469 100%);
  -webkit-transition: width 200ms;
  transition: width 200ms;
}

.main-right {
  /*width: 85%;*/
  /*width: calc(100% - 17rem);*/
  /*float: right;*/
  overflow-y: auto;
  height: 100%;
  background-color: #fff;
  margin-left: 9em;
  margin-left: 9rem;

  transition: margin 200ms;
  -webkit-transition: margin 200ms;
}

.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

header {
  /*background: #0099CC;*/
  background: #fff;
  color: white;
}

header > .container {
  /*margin-top: 0.5rem;*/
  /*padding: 0.5rem 1rem 0 1rem;*/
  padding: 0.25rem 1rem 0 1rem;
}
header > .container img {
  height: 30px;
}
header > .container > .brand {
  font-size: 1.5rem;
  color: white;
  text-decoration: none;
  cursor: default;
  vertical-align: text-bottom;
}

.warning {
  background-color: #ff7d60 !important;
  color: #fff;
}

.errorbox,
.alert-message {
  margin-top: 2em;
  margin: 2rem 0 0 0;
  padding: 2em;
  padding: 2rem;
  border: 0;
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  font-family: inherit;
  min-width: inherit;
  overflow: auto;
  border-radius: 0;
  background-color: #fff;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.16), 0 0 2px 0 rgba(0, 0, 0, 0.12);
}

.errorbox {
  color: #fff;
  background-color: #f0ad4e;
  border-color: #eea236;
}

.main-right .iBread {
  /*border-left: 1px solid #113079;*/
  /*padding: 0.65rem 1.5rem;*/
  /*color: #FFF;*/
  background: #113560;
  position: fixed;
  width: 100%;
  height: 2.65rem;
}
#maincontent > .container > div:nth-child(1).alert-message.warning > a {
  font: inherit;
  overflow: visible;
  text-transform: none;
  display: inline-block;
  margin-bottom: 0;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  touch-action: manipulation;
  /*cursor: pointer;*/
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  min-width: 6rem;
  padding: 0.5rem 1rem;
  font-size: 1.1rem;
  line-height: 1.42857143;
  color: #fff;
  background-color: #5bc0de;
  border-color: #46b8da;
  margin-top: 2rem;
  text-decoration: inherit;
}

.main > .main-left > .nav {
  /*margin-top: 0.5rem;*/
}
.main > .main-left > .nav-toggle {
  /*margin-top: 0.5rem;*/
  border-bottom: 1px solid #5387be;
}
.main > .main-left > .nav-toggle a {
  display: inline-block;
  white-space: nowrap;

  text-align: left;
  width: 100%;
  font-family: inherit;
  font-size: 1.2rem;
  padding: 0.5rem 1rem;
  color: #dbe6f4;
  background: transparent;
  border-bottom: 1px solid #0a3b6f;
}
/* nav-toggle reduce
 * ---------------------------------------------------------------------------- */
.reduce .main-left {
  clear: both;
  position: fixed;
  /*width: 2.5%;*/
  width: calc(0% + 3rem);
}
.reduce .main-right {
  clear: both;
  position: fixed;
  margin-left: 3em;
  margin-left: 3rem;
  /*
    width: 100%;*/
  width: 97.5%;
  width: calc(100% - 3rem);
}
.reduce > .main-left > .nav > .slide > a {
  /*pointer-events: none; */
}
/*
 * ---------------------------------------------------------------------------- */
.main > .main-left > .nav > li a {
  color: #dbe6f4; /*#404040;*/
  display: block;
}

.main > .main-left > .nav > li:nth-last-child(1) {
  /*margin-top: 2rem;
    font-size: 1.2rem;*/
}

.main > .main-left > .nav > li {
  /*padding: 0.5rem 1rem;*/
  /*cursor: pointer;*/
}

.main > .main-left > .nav > .slide {
  padding: 0;
}

.main > .main-left > .nav > .slide > ul {
  display: none;
  position: relative;
}
/*竖线*/
.main > .main-left > .nav > .slide > ul:before {
  content: "";
  display: block;
  position: absolute;
  z-index: 1;
  left: 1.5em;
  left: 1.5rem;
  top: 0px;
  bottom: 17px;
  bottom: 15px\9;
  border-left: 1px solid #7a7a7a;
}
/*横线*/
.main > .main-left > .nav > .slide > ul > li:before {
  content: "";
  display: block;
  position: relative;
  width: 1em;
  width: 1rem;
  right: 2em;
  right: 2rem;
  top: 11px;
  border-top: 1px solid #7a7a7a;
  z-index: 1;
}
.main > .main-left > .nav > .slide > .menu {
  cursor: default;
}
.main > .main-left > .nav > .slide > .menu,
.main > .main-left > .nav > li > a {
  display: block;
  padding: 0.5rem 1rem;
  /*text-decoration: none;*/
  font-size: 1.2rem;
}
/*加号前宽度定位*/
.main > .main-left > .nav > .slide > .menu span {
  display: inline-block;
  width: 140px;
}
/*小加号*/
.main > .main-left > .nav > .slide > .menu small {
  display: inline-block;
  position: relative;
  left: 38px;
  top: -2px;
  font-size: 0.7rem;
}
/*:root .main >.main-left > .nav > .slide > .menu small {font-size: 0.6em;}*/
@-moz-document url-prefix() {
  .main > .main-left > .nav > .slide > .menu small {
    font-size: 0.8rem;
  }
}
.main > .main-left > .nav > li:hover,
.main > .main-left > .nav > .slide > .menu:hover {
  /*background: #D4D4D4;*/
  color: #fff;
}

.main > .main-left > .nav > .slide:hover {
  background: none;
}

.slide-menu {
  background-color: rgba(17, 17, 18, 0.2);
}

.main > .main-left > .nav > .slide > .slide-menu > li {
  padding: 0.4rem 3.5rem;
}

.main > .main-left > .nav > .slide > .slide-menu > .active {
  /*background-color: #0099CC;*/
 /* background-color: #105DAD;*/
  background: linear-gradient(to right, #2d88e5 93%, #103469 100%)
}
.main > .main-left > .nav > .systemMenu.active {
  /*background-color: #0099CC;*/
 /* background-color: #105DAD;*/
  background: linear-gradient(to right, #2d88e5 93%, #103469 100%)
}
.main > .main-left > .nav > .slide > .slide-menu > li > a {
  /*text-decoration: none;*/
  white-space: nowrap;
}

.main > .main-left > .nav > .slide > .slide-menu > .active > a {
  color: white;
}

.main > .main-left > .nav > .slide > .slide-menu > li:hover {
  /*background: #D4D4D4;*/
}
.main > .main-left > .nav > .slide > .slide-menu > li > a:hover {
  color: #fff;
}
.main > .main-left > .nav > .slide > .slide-menu > .active:hover {
  /*background-color: #0099CC;*/
  /*background-color: #105DAD;*/
  cursor: hand;
}

li {
  list-style-type: none;
}

#maincontent > .container {
  /*margin: 3rem 2rem 1rem 2rem;*/
  /*width: 100%;*/
  /*max-width: 940px;*/
  margin: 3.5em 2em 2em;
  margin: 3.5rem 2rem 2rem;
}

h1 {
  font-size: 4em;
  font-size: 4rem;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

h2 {
  margin-top: 2em;
  margin: 2rem 0 0 0;
  font-size: 2.5em;
  font-size: 2.5rem;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

h3 {
  margin-top: 2em;
  margin: 2rem 0 0 0;
  font-size: 2em;
  font-size: 2rem;
  padding-bottom: 10px;
}
.h3 {
  margin-top: 2em;
  margin: 2rem 0 0 0;
  font-size: 1.3em;
  font-size: 1.3rem;
  color: #0069d6;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}
fieldset {
  margin-top: 1em;
  margin: 2rem 0 0 0;
  padding: 1em;
  padding: 2rem;
  border: 0;
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  font-family: inherit;

  min-width: inherit;
  overflow: auto;

  border-radius: 0;
  background-color: #fff;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.16), 0 0 2px 0 rgba(0, 0, 0, 0.12);
}

fieldset > legend {
  display: none !important;
}

fieldset > fieldset {
  margin: 0;
  padding: 0;
  border: none;
  box-shadow: none;
}

.panel-title {
  width: 100%;
  display: block;
  line-height: 1;
  color: #404040;
  font-size: 1.5em;
  font-size: 1.5rem;
  padding-bottom: 1em;
  padding-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

table {
  border-spacing: 0;
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #eee;
}

table > tbody > tr > td,
table > tbody > tr > th,
table > tfoot > tr > td,
table > tfoot > tr > th,
table > thead > tr > td,
table > thead > tr > th {
  padding: 5px;
  line-height: 1.42857143;
  border-top: 1px solid #ddd;

  white-space: nowrap;
  text-align: center;
  vertical-align: middle;
}

.cbi-section-table-cell {
  text-align: center;
}

.cbi-section-table-row {
  text-align: center;
}

fieldset > table > tbody > tr:nth-of-type(odd) {
  background-color: #f9f9f9;
}

/* fix progress bar */
#memfree > div,
#membuff > div,
#conns > div,
#memtotal > div {
  width: 100% !important;
  height: 1.4rem !important;
}

#memfree > div > div,
#membuff > div > div,
#conns > div > div,
#memtotal > div > div {
  height: 1.4rem !important;
  /*background-color: #0099CC !important;*/
  background-color: #105dad !important;
}

/* fix multiple table */

table table {
  border: none;
}

.cbi-value-field table {
  border: none;
}

td > table > tbody > tr > td {
  border: none;
}

.cbi-value-field > table > tbody > tr > td {
  border: none;
}

/* button style */

.cbi-button {
  display: inline-block;
  padding: 0.3em 1em;
  padding: 0.3rem 1rem;
  /* margin: 0 0.5rem;
    margin: 0 0.5em;*/
  font-size: 1em;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;

  /*font: inherit;*/
  overflow: visible;
  text-transform: none;
  /*color: #333;*/
  /*background-color: #fff;*/
  /*border-color: #ccc;*/
  min-width: 6rem;
}

.cbi-button-reset,
.cbi-input-remove {
  color: #fff;
  background-color: #f0ad4e;
  border-color: #eea236;
}

.cbi-input-save,
.cbi-button-add,
.cbi-button-save,
.cbi-button-find,
.cbi-input-reload,
.cbi-button-reload {
  color: #fff;
  background-color: #337ab7 !important;
  border-color: #2e6da4 !important;
}

.cbi-input-apply,
.cbi-button-apply,
.cbi-button-edit {
  color: #fff;
  background-color: #5bc0de !important;
  border-color: #46b8da !important;
}

.cbi-input-reset,
.cbi-button-remove {
  color: #fff;
  background-color: #d9534f !important;
  border-color: #d43f3a !important;
}

/* table

.tabs {
    margin: 0 -2rem;
    padding-left: 0.5rem;
    background-color: #FFFFFF;
}*/

.cbi-tabmenu > li/*,
.tabs > li*/ {
  display: inline-block;
  padding: 0.9em 0em;
  padding: 0.9rem 0rem;
}

.cbi-tabmenu > li > a/*,
.tabs > li > a*/ {
  /*text-decoration: none;*/
  color: #404040;
  padding: 0.9em 1.5em;
  padding: 0.9rem 1.5rem;
}

/*.tabs > li[class~="active"],
.tabs > li:hover {
    cursor: pointer;
    background-color: white;
    border-bottom: 0.2rem solid #0099CC;
    /*color: #0099CC;*/
/*color: #105DAD;*/
/*}*/

/*.tabs > li[class~="active"] > a {*/
/*color: #0099cc;*/
/*color: #105DAD;*/
/*}

.tabs > li:hover {
    border-bottom: 0.2rem solid #C9C9C9;
}*/

/*启用原tab样式
********************************************************************************************/
.tabs {
  clear: both;
  margin: 0 0 18px;
  padding: 0;
  list-style: none;
  zoom: 1;
}

.tabs:before,
.tabs:after {
  display: table;
  content: "";
  zoom: 1;
}

.tabs:after {
  clear: both;
}

.tabs > li {
  float: left;
}

.tabs > li > a {
  display: block;
}

.tabs {
  border-color: #ddd;
  border-style: solid;
  border-width: 0 0 1px;
}

.tabs > li {
  /*position: relative;*/
  margin-bottom: -1px;
}

.tabs > li > a {
  padding: 0 15px;
  margin-right: 2px;
  line-height: 34px;
  border: 1px solid transparent;
  -webkit-border-radius: 4px 4px 0 0;
  -moz-border-radius: 4px 4px 0 0;
  border-radius: 4px 4px 0 0;
}

.tabs > li > a:hover {
  text-decoration: none;
  background-color: #eee;
  border-color: #eee #eee #ddd;
}

.tabs .active > a,
.tabs .active > a:hover {
  color: #808080;
  background-color: #ffffff;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
  cursor: default;
}
/*
********************************************************************************************/

.cbi-tabmenu {
  border-top: 1px solid #d4d4d4;
  border-left: 1px solid #d4d4d4;
  border-right: 1px solid #d4d4d4;
}

.cbi-tabmenu > li:hover {
  background-color: #f1f1f1;
}

.cbi-tabmenu > li[class~="cbi-tab"] {
  background-color: white;
}

.cbi-tabmenu {
  background-color: #d4d4d4;
}

.cbi-section-node-tabbed {
  padding: 0;
  margin-top: 0;
  border-bottom: 1px solid #d4d4d4;
  border-left: 1px solid #d4d4d4;
  border-right: 1px solid #d4d4d4;
}

.cbi-tabcontainer > .cbi-value:nth-of-type(2n) {
  background-color: #f9f9f9;
}

.cbi-value-field,
.cbi-value-description {
  display: table-cell;
}

.cbi-value-helpicon > img {
  display: none;
}

.cbi-value-helpicon:before {
  content: "\f059";
}

.cbi-value-description {
  font-size: small;
  opacity: 0.5;
  padding-top: 0.5em;
  padding: 0.5rem 0 0 0;
}

.cbi-value-title {
  display: table-cell;
  word-wrap: break-word;
  padding-top: 0.6em;
  padding-top: 0.6rem;
  width: 23rem;
  float: left;
  text-align: right;
  padding-right: 2em;
  padding-right: 2rem;
}

.cbi-value {
  padding: 1rem;
}

.cbi-section-table-descr > .cbi-section-table-cell,
.cbi-section-table-titles > .cbi-section-table-cell {
  border: none;
}
.cbi-rowstyle-2 {
  background-color: #eee;
}

.cbi-section-table .cbi-section-table-titles .cbi-section-table-cell {
  width: auto !important;
}

/* desc */
.cbi-section-descr,
.cbi-map-descr {
  padding: 0.5em;
  padding: 0.5rem;
  color: #999;
}

/* luci */

.hidden {
  display: none;
}

.left {
  text-align: left !important;
}

.right {
  text-align: right !important;
}

.inline {
  display: inline-block;
}

.cbi-page-actions {
  border-top: 1px solid #eee;
  padding-top: 2em;
  padding-top: 2rem;
  text-align: right;
}

/* input */
.cbi-value input[type="password"],
.cbi-value input[type="text"] {
  padding: 0.36em 1em;
  padding: 0.36rem 1rem;
  color: #555;
  min-width: 15em;
  min-width: 15rem;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  margin-bottom: 0.3em;
  margin-bottom: 0.3rem;
}

.cbi-value-field input[type="password"],
.cbi-value-field input[type="text"] {
  padding: 0.36em 1em;
  padding: 0.36rem 1rem;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
}

/* select */
.cbi-value-field .cbi-input-select {
  width: 95%;
  min-width: 15em;
  min-width: 15rem;
  padding: 0.36em 0.8em;
  padding: 0.36rem 0.8rem;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
}

.ifacebadge {
  display: inline-block;
  min-width: 8em;
  min-width: 8rem;
  border: 1px solid #cccccc;
  padding: 0.5em 1em;
  padding: 0.5rem 1rem;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.05);
  -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.05);
}

.ifacebadge > img {
  float: right;
  margin-left: 0.3em;
  margin-left: 0.3rem;
}

/*textarea*/

.cbi-input-textarea {
  width: 100%;
  min-height: 16em;
  min-height: 16rem;
  padding: 1em;
  padding: 1rem;
  font-size: 0.9em;
  font-size: 0.9rem;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: black;
}

#syslog {
  width: 100%;
  min-height: 15em;
  min-height: 15rem;
  padding: 1em;
  padding: 1rem;
  font-size: smaller;
  color: #5f5f5f;

  margin-bottom: 20px;
  border-radius: 0;
  background-color: #fff;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.16), 0 0 2px 0 rgba(0, 0, 0, 0.12);
  border: none;
}

/* change */

.uci-change-list {
  font-family: monospace;
}

.uci-change-list ins,
.uci-change-legend-label ins {
  /*text-decoration: none;*/
  border: 1px solid #00ff00;
  background-color: #ccffcc;
  display: block;
  padding: 2px;
}

.uci-change-list del,
.uci-change-legend-label del {
  /*text-decoration: none;*/
  border: 1px solid #ff0000;
  background-color: #ffcccc;
  display: block;
  font-style: normal;
  padding: 2px;
}

.uci-change-list var,
.uci-change-legend-label var {
  /*text-decoration: none*/
  border: 1px solid #cccccc;
  background-color: #eeeeee;
  display: block;
  font-style: normal;
  padding: 2px;
}

.uci-change-list var ins,
.uci-change-list var del {
  border: none;
  white-space: pre;
  font-style: normal;
  padding: 0px;
}

.uci-change-legend {
  padding: 5px;
}

.uci-change-legend-label {
  width: 150px;
  float: left;
}

.uci-change-legend-label > ins,
.uci-change-legend-label > del,
.uci-change-legend-label > var {
  float: left;
  margin-right: 4px;
  width: 10px;
  height: 10px;
  display: block;
}

.uci-change-legend-label var ins,
.uci-change-legend-label var del {
  line-height: 6px;
  border: none;
}

.uci-change-list var,
.uci-change-list del,
.uci-change-list ins {
  padding: 0.5em;
  padding: 0.5rem;
}

/* other fix */
#iwsvg,
#iwsvg2,
#bwsvg {
  border: 1px solid #d4d4d4 !important;
  border-top: none !important;
}

.ifacebox {
  border: 1px solid #999;
  background-color: #f9f9f9;
}

.cbi-image-button {
  margin-left: 0.5em;
  margin-left: 0.5rem;
}

.zonebadge {
  padding: 0.2em 0.5em;
  padding: 0.2rem 0.5rem;
  display: inline-block;
  cursor: pointer;
}

.zonebadge > .ifacebadge {
  padding: 0.2em 1em;
  padding: 0.2rem 1rem;
  margin: 0.3em;
  margin: 0.3rem;
  border: 1px solid #6c6c6c;
}

.zonebadge > input[type="text"] {
  padding: 0.16em 1em;
  padding: 0.16rem 1rem;
  min-width: 10em;
  min-width: 10rem;
  margin-top: 0.3rem;
}

.cbi-value-field .cbi-input-checkbox,
.cbi-value-field .cbi-input-radio {
  margin-top: 1em;
  margin-top: 1rem;
}

.cbi-section-table-row > .cbi-value-field .cbi-input-select {
  min-width: 7em;
  min-width: 7rem;
}

.cbi-section-create > .cbi-button-add {
  margin: 0.5em;
  margin: 0.5rem;
}

div.cbi-value var,
td.cbi-value-field var {
  font-style: italic;
  color: #0069d6;
}

small {
  display: inline-block;
  width: 70px;
  font-size: smaller;
  white-space: normal;
}

.cbi-button-up,
.cbi-button-down {
  display: inline-block;
  min-width: 0;
  padding: 0.2em 0.3em;
  padding: 0.2rem 0.3rem;
  font-size: 1.3rem;
}

#diag-rc-output > pre {
  background-color: #f5f5f5;
  display: block;
  padding: 8.5px;
  margin: 0 0 18px;
  line-height: 1.5rem;
  -moz-border-radius: 3px;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 1.5rem;
  color: #404040;
}

input[name="ping"],
input[name="traceroute"],
input[name="nslookup"] {
  width: 80%;
}

header > .container > .pull-right > * {
  position: relative;
  top: 0.45rem;
  cursor: pointer;
}

#xhr_poll_status > .label.success {
  background-color: #14ce14;
}

.label {
  padding: 0.3em 0.8em;
  padding: 0.3rem 0.8rem;
  /*font-size: 1rem;*/
  font-size: 0.8rem;
  font-weight: bold;
  color: #ffffff !important;
  text-transform: uppercase;
  white-space: nowrap;
  background-color: #bfbfbf;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  text-shadow: none;
  /*text-decoration: none;*/
}

.notice {
  background-color: #5bc0de;
}

.showSide {
  display: none;
}

.darkMask {
  width: 100%;
  height: 100%;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.56);
  content: "";
  z-index: 99;
  display: none;
}

/* fix Main Login*/
/*.node-main-login fieldset{
    overflow: hidden;
}*/
.node-main-login > .main {
  overflow-x: hidden;
  background: url(../images/bg.png) no-repeat fixed top;
}
.node-main-login > .main > .main-left,
.node-main-login > .main > .main-right .iBread {
  display: none;
}

.node-main-login > .main > .main-right {
  width: 100%;
  margin-left: 0;
  display: table;
  background: none;
  transition: none;
}
.node-main-login .main .loginBox-header {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(64, 64, 64, 0.44);
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  padding: 1em 0em;
  padding: 1rem 0rem;
  font-size: 2em;
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
}
/*.node-main-login .loginBox .lab {
    color: #404040;
    text-align: left;
    padding: 0;
    font-size: 1rem;
    font-weight: bold;
    display: block;
    margin-bottom: 0.5rem;
    line-height: 1.5rem;
}*/
.node-main-login .loginBox .input {
  position: relative;
  display: block;
}
.node-main-login .loginBox .input input {
  display: block;
  width: 100%;
  height: 2.5em;
  height: 2.5rem;
  border-radius: 4px;
}
.node-main-login > .main .loginBox fieldset {
  padding: 0rem 3rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
  display: block;
  background: none;
  border: none;
  box-shadow: none;
}
.node-main-login .loginBox fieldset > .section {
  margin-bottom: 1.5em;
  margin-bottom: 1.5rem;
}
.node-main-login > .main #maincontent {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
}
.node-main-login > .main .container {
  display: inline-block;
  text-align: left;
  min-width: 26rem;
  margin-top: 0rem !important;
  margin-bottom: 8rem !important; /*登录框提上去*/
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.16), 0 0 2px 0 rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(64, 64, 64, 0.44);
  border-radius: 5px;
}
.node-main-login .loginBox .input .icon-append {
  right: 5px;
  padding-left: 9px;
  border-left: 1px solid #ccc;

  position: absolute;
  top: 5px;
  width: 29px;
  height: 29px;
  font-size: 15px;
  color: #ccc;
  line-height: 29px;
  text-align: center;
}
.node-main-login .loginBox-footer:after {
  content: "";
  display: table;
  clear: both;
}
.node-main-login .loginBox-footer {
  margin: 0 3rem 2rem 3rem;
  border-top: rgba(0, 0, 0, 0.03) 1px solid;
}
.node-main-login .loginBox-footer .cbi-button {
  margin: 1rem 0 0 2rem;
  width: 100%;
  height: 2.5rem;
}
.alert-danger {
  font-size: 0.8rem;
  color: #a94442;
  background-color: #f2dede;
  /* border-color: #ebccd1; */

  padding: 0.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid transparent;
  border-radius: 4px;
}
/*.node-main-login #maincontent {
    background: url(../BG.jpg) no-repeat fixed top;
} */

.node-main-login footer {
  bottom: 0;
  padding: 0.5rem; /*降低高度*/
  position: absolute;
  width: 100%;
  background-color: #113560;
  text-align: center;
}

/* fix status overview */

.node-status-overview > .main fieldset:nth-child(4) td:nth-child(2) {
  white-space: normal;
}

/* fix status processes */

.node-status-processes > .main table tr td:nth-child(3) {
  white-space: normal;
}

.node-status-firewall > .main fieldset li {
  display: inline-block;
}

.node-status-firewall > .main fieldset li > a {
  font: inherit;
  overflow: visible;
  text-transform: none;
  margin-bottom: 0;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
  color: #333;
  min-width: 6rem;
  padding: 0.5rem 1rem;
  font-size: 1.1rem;
  line-height: 1.42857143;
  background-color: #f0ad4e;
  border-color: #eea236;

  color: #fff;
  /*text-decoration: none;*/
}

/* fix system reboot

.node-system-reboot > .main > .main-right p,
.node-system-reboot > .main > .main-right h3 {
    padding-left: 2rem;
}

.node-system-reboot > .main > .main-right p > a {
    margin-top: 2rem;
    font: inherit;
    overflow: visible;
    text-transform: none;
    display: inline-block;
    margin-bottom: 0;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    min-width: 6rem;
    padding: 0.5rem 1rem;
    font-size: 1.1rem;
    line-height: 1.42857143;
    color: #fff;
    background-color: #d9534f !important;
    border-color: #d43f3a !important;
}*/

/* fix Services  Network Shares
.node-services-network_shares > .main .cbi-tabcontainer:nth-child(3) .cbi-value-title {
    margin-bottom: 1rem;
}

.node-services-network_shares > .main .cbi-tabcontainer:nth-child(3) .cbi-value-field {
    display: list-item;
}

.node-services-network_shares > .main .cbi-tabcontainer:nth-child(3) .cbi-value-description {
    padding-top: 1rem;
}

/* fix System Software*/
/*.node-system-software > .main table tr td:nth-child(4) {
    white-space: normal;
    font-size: small;
    color: #404040;
}

.node-system-software > .main .cbi-tabmenu > li > a, .tabs > li > a {
    padding: 0.5rem 1rem;
}

.node-system-software > .main .cbi-value > pre {
    background-color: #eee;
    padding: 0.5rem;
    overflow: auto;
}

.cbi-tabmenu + .cbi-section {
    margin-top: 0;
}

.node-status-firewall fieldset,
.node-system-software fieldset,
.node-system-backup_flash_firmware fieldset {
    margin-top: 0;
}

.node-status-firewall .cbi-tabmenu,
.node-system-software .cbi-tabmenu,
.node-system-backup_flash_firmware .cbi-tabmenu {
    border: none;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .16), 0 0 2px 0 rgba(0, 0, 0, .12);
}*/

/* language fix */
body.lang_pl.node-main-login .cbi-value-title {
  width: 12rem;
}
/* 自定义
*---------------------------------------------------------------------*/
/*面包屑导航*/
#iBread ul {
  background: #10498b;
  overflow: hidden;
  width: 100%;
}

#iBread ul li {
  float: left;
}

#iBread ul a {
  cursor: default;
  padding: 1em 2em 1em 3em;
  float: left;
  font-size: 0.8em;
  color: #dbe6f4;
  position: relative;
  background-color: #163c76;
}

#iBread ul li:first-child a {
  padding-left: 4em;
}

#iBread ul a:after,
#iBread ul a:before {
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -1.8em;
  border-top: 1.9em solid transparent;
  border-bottom: 1.9em solid transparent;
  border-left: 1.5em solid;
  right: -1em;
}

#iBread ul a:after {
  z-index: 2;
  border-left-color: #163c76;
}

#iBread ul a:before {
  border-left-color: #dbe6f4;
  right: -1.2em;
  z-index: 1;
}
#iBread ul .current {
  color: #fff;
  background: none;
}

#iBread ul .current:after,
#iBread ul .current:before {
  content: normal;
}

/*table 2cell*/
.info-section-table tbody th {
  text-align: right;
  width: 42%;
}
.info-section-table tbody td {
  text-align: left;
  padding-left: 2em;
}
.tableThree td {
  width: 33%;
}
.title-center {
  display: inline-block;
  width: 6rem;
  text-align: right;
}

.cbi-button-grow {
  text-align: center;
  margin: 0.5em 0em;
  margin: 0.5rem 0rem;
}

/*端口安全提示框*/
.selfocus {
  outline: 0;
  border-color: rgba(82, 168, 236, 0.8);
  -webkit-box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1),
    0 0 8px rgba(82, 168, 236, 0.6);
  -moz-box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1),
    0 0 8px rgba(82, 168, 236, 0.6);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1),
    0 0 8px rgba(82, 168, 236, 0.6);
}

.wrapper {
  position: relative;
}
.tooltip {
  background: #151515;
  color: #fff;
  display: block;
  opacity: 0;
  padding: 5px 10px;
  pointer-events: none;
  position: absolute;
  bottom: -70%;
  left: 10%;
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -ms-transform: translateY(0px);
  -o-transform: translateY(0px);
  transform: translateY(0px);
  -webkit-transition: all 0.2s ease-out;
  -moz-transition: all 0.2s ease-out;
  -ms-transition: all 0.2s ease-out;
  -o-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
  -webkit-box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.28);
  -moz-box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.28);
  -ms-box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.28);
  -o-box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.28);
  box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.28);
}

.wrapper .tooltip:after {
  border-left: solid transparent 10px;
  border-right: solid transparent 10px;
  border-bottom: solid #151515 10px;
  bottom: 30px;
  content: " ";
  left: 50%;
  position: absolute;
}
.tooltipShow {
  opacity: 1;
  -webkit-transform: translateY(10px);
  -moz-transform: translateY(10px);
  -ms-transform: translateY(10px);
  -o-transform: translateY(10px);
  transform: translateY(10px);
}
.lte8 .tooltip {
  display: none;
  bottom: -40px;
}
.lte8 .tooltipShow {
  display: block;
}

/*miniTab*/
.miniTab {
  width: 60%;
}
.miniTab tbody td {
  width: 60%;
}
@media screen and (max-width: 1600px) {
  .main-left {
    width: calc(0% + 15rem);
  }
  .main > .main-left > .nav > .slide > .menu small {
    left: 6px;
  }
  .main-right {
    width: calc(100% - 15rem);
  }

  .cbi-button {
    padding: 0.3rem 0.6rem;
    font-size: 1rem;
    line-height: 1.5;
  }

  header > .container > .pull-right > * {
    /*top: 0.35rem;*/
  }

  .label {
    /*padding: 0.2rem 0.6rem;*/
  }

  .cbi-value-title {
    width: 15rem;
  }

  fieldset {
    padding: 1rem;
  }

  .cbi-input-textarea {
    font-size: smaller;
  }
  .node-status-firewall > .main fieldset li > a {
    padding: 0.3rem 0.6rem;
  }
}

@media screen and (max-width: 1280px) {
  html {
    font-size: 14px;
  }
  header {
    /*height: 3.5rem;*/
    height: 2.5rem;
  }

  header > .container {
    /*margin-top: 0.25rem;*/
  }
  header > .container img {
    height: 26px;
  }
  .main {
    /*top: 3.5rem;*/
    top: 2.5rem;
  }

  .main-left {
    width: calc(0% + 14rem);
  }

  .main-right {
    width: calc(100% - 14rem);
  }
  .main-right .iBread {
    height: 2.5rem;
  }
  .main-right #iBread ul a {
    padding: 0.85em 1em 0.85em 2em;
  }
  .cbi-tabmenu > li > a,
  .tabs > li > a {
    padding: 0.2rem 0.5rem;
  }

  /* .panel-title {
        font-size: 1.3rem;
        padding-bottom: 1rem;
    }*/

  table {
    /* font-size: 0.8rem !important;*/
  }

  .main > .main-left > .nav-toggle a,
  .main > .main-left > .nav > li,
  .main > .main-left > .nav > li a,
  .main > .main-left > .nav > .slide > .menu {
    font-size: 1.1rem;
  }
  .main > .main-left > .nav > .slide > .menu small {
    left: -22px;
  }
  .main > .main-left > .nav > .slide > ul:before {
    bottom: 13px;
  }
  .main > .main-left > .nav > .slide > ul > li:before {
    left: -1.8em;
    top: 10px;
  }
  .main > .main-left > .nav > .slide > .slide-menu > li > a {
    font-size: 0.9rem;
  }
}
@media screen and (max-width: 1024px) {
  .main > .main-left > .nav > .slide > .menu small {
    left: -40px;
  }
  table {
    font-size: 0.8rem !important;
  }
}
@media screen and (max-width: 992px) {
  header > .container img {
    height: 23px;
  }
  .main-left {
    width: 0;
    position: fixed;
    z-index: 100;
  }
  .reduce .main-left {
    width: 0;
  }
  .main > .main-left > .nav-toggle {
    display: none;
  }
  .main > .main-left > .nav > .slide > .menu small {
    left: -15px;
    top: 0;
  }

  .reduce .main-right,
  .main-right {
    width: 100%;
    margin-left: 0em;
  }
  .showSide {
    color: #274c73;
    /*padding: 0.1rem;*/
    margin-right: 0.5rem;
    display: inline-block;
    cursor: pointer;
  }

  .showSide:before {
    content: "\e236";
    font-size: 1.5rem;
  }

  .node-main-login .showSide {
    display: none !important;
  }

  .cbi-value-title {
    width: 9rem;
    padding-right: 1rem;
  }
  /*---------------------网络网络诊断页面--------------------------------------------------------*/
  /*.node-network-diagnostics > .main .cbi-map fieldset > div * {
        width: 100% !important;
    }

    .node-network-diagnostics > .main .cbi-map fieldset > div input[type="text"] {
        margin: 3rem 0 0 0 !important;
    }

    .node-network-diagnostics > .main .cbi-map fieldset > div:nth-child(4) input[type="text"] {
        margin: 0 !important;
    }

    .node-network-diagnostics > .main .cbi-map fieldset > div select,
    .node-network-diagnostics > .main .cbi-map fieldset > div input[type="button"] {
        margin: 1rem 0 0 0;
    }

    .node-network-diagnostics > .main .cbi-map fieldset > div {
        width: 100% !important;
    }*/

  #diag-rc-output > pre {
    font-size: 1.2rem;
  }

  .node-main-login > .main .cbi-value-title {
    text-align: left;
  }
}
@media screen and (max-width: 660px) {
  .miniTab {
    width: 100%;
  }
}
@media screen and (max-width: 480px) {
  small {
    display: block;
    text-align: center;
  }
  fieldset {
    padding: 1rem;
    margin: 1rem 0 0 0;
  }

  #maincontent > .container {
    margin: 3.5rem 1rem 1.5rem 1rem;
  }

  .main > .main-left > .nav > .slide > .menu,
  .main > .main-left > .nav > li a {
    font-size: 1.3rem;
  }

  .main > .main-left > .nav > .slide > .slide-menu > li > a {
    font-size: 1.1rem;
  }
  .main > .main-left > .nav > .slide > ul:before {
    bottom: 16px;
  }
  .cbi-value-title {
    width: 100%;
    min-width: 0rem !important;
    display: block;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    text-align: left;
  }

  .cbi-value-field,
  .cbi-value-description {
    width: 100%;
  }

  .cbi-value > .cbi-value-field {
    display: inline-block;
  }

  .cbi-tabmenu > li {
    padding: 0.6rem 0rem;
  }

  .cbi-tabmenu > li > a,
  .tabs > li > a {
    padding: 0.2rem 0.3rem;
    font-size: 0.9rem;
  }

  .cbi-page-actions > div > input {
    display: none;
  }
  .node-main-login > .main .container {
    min-width: 20rem;
  }
  .node-main-login > .main fieldset {
    margin: 0;
    padding: 0.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  .tabs > li > a {
    font-size: 0.9rem;
  }

  select,
  input {
    font-size: 0.9rem;
  }

  .mobile-hide {
    display: none;
  }

  .panel-title {
    font-size: 1.4rem;
    padding-bottom: 1rem;
  }

  .node-system-software > .main .cbi-value.cbi-value-last > div {
    width: 100% !important;
  }

  .node-system-software > .main .cbi-value .cbi-value-field input {
    width: 100%;
  }
}

/*分页样式*/
.pagination {
  display: inline-block;
  padding-left: 0;
  margin: 20px 0;
  border-radius: 4px;
}
.pagination > li {
  display: inline;
}
.pagination .current {
  color: #fff;
  background-color: #428bca;
}
.pagination-sm > li > a,
.pagination-sm > li > span {
  padding: 5px 10px;
  font-size: 12px;
}
.pagination > li > a,
.pagination > li > span {
  position: relative;
  float: left;
  padding: 5px 10px;
  margin-left: -1px;
  line-height: 1.42857143;
  color: #428bca;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid #ddd;
}
.pagination-sm > li:first-child > a,
.pagination-sm > li:first-child > span {
  margin-left: 0;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}
         .custom-prompt-mask {
            display: block;
            position: fixed;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;

            z-index: 1000;
        }

        .custom-prompt-box {
            background: white;
            padding: 20px;
            padding-top: 45px;
            width: 450px;
            height: 200px;
            box-sizing: border-box;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translate(-50%, 0);
            box-shadow: 0px 0px 8px 0px #8a8989;
            border-radius: 10px;
        }

        .label1 {
            font-size: 14px;
        }

        .custom-prompt-box input {
            margin-top: 10px;
            width: 400px;
            padding: 7px 10px;
            font-size: 16px;
            border-radius: 10px;
            outline: none;
            border: 2px solid #0B57D0;


        }

        .custom-prompt-box input:focus {
            outline: none;
            border: 2px solid #0B57D0;
        }

        .custom-prompt-box .button {
            margin-top: 12px;
            display: flex;
            justify-content: flex-end;
        }

        .ok,
        .cancel {
            width: 55px;
            height: 35px;
            cursor: pointer;
            border-radius: 20px;
            border: 1px solid #0B57D0;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            margin-top: 10px;
        }

        .ok {
            color: #fff;
            background-color: #0B57D0;
            margin-right: 5px;
        }

        .cancel {
            border: 1px solid #0B57D0;
            background-color: #fff;
            color: #0B57D0;
        }