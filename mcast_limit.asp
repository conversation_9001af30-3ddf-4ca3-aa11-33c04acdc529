<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<%  var authmode; checkCurMode(); %>

<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"MCAST");</script></title>
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript">

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function display(){

}

function del_sv(portId,appid)
{
				
		var hid = document.vlan_port;
		
		hid.port_value.value=portId;
		hid.appid_value.value=appid;
		
		hid.action="/goform/delMcastLimit"
		hid.submit();
		return 0;
}


function selectToAll() 
{  
		var cf = document.forms[0];
		var objs = document.getElementsByName("checkbox_index"); 
		var i;
		if(cf.check_all.checked == true)
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
		       if(objs[i].disabled==false && objs[i].checked==false){
		         objs[i].checked = true;  
			 		addToPortRange(objs[i]);
			 }
		    }
		}
		else
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
					if(objs[i].checked==true){
					objs[i].checked = false;  
					 addToPortRange(objs[i]);
			}
		         
		    }
		} 
    
}


function isMulticastMac(mac)
{
	var head = mac.substring(0,2);
	if(parseInt(head,10)%2 ==1)
		return true;
	return false;
}

function checkup()
{
	var tf = document.vlan_port;
	var bbb = document.vlan_port.appid.value;
	var ddd = document.vlan_port.limit_value.value;

	var myRE =/^[0-9a-fA-F]{1,4}\.[a-fA-F0-9]{1,4}\.[a-fA-F0-9]{1,4}$/;

		
	if (isNaN(parseInt(ddd)) || (parseInt(ddd) < 16 )  || (parseInt(ddd) > 1000000 ))
	{
		alert("Invaild input, effective range: 16~1000000!");
		return false;
	}	
	
 	if (myRE.test(bbb))
	{
		if (!isMulticastMac(bbb))
		{
			alert(putmsg(<% write(lang); %>,"输入MAC地址非法  格式为16进制：HHHH.HHHH.HHHH"));
			return false;
		}
		else
		{
			tf.action = "/goform/selMcastLimit";
			tf.submit();
		}
	}	
	else
	{
		alert(putmsg(<% write(lang); %>,"输入MAC地址非法  格式为16进制：HHHH.HHHH.HHHH"));
		return false;
	}
}

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var target = document.getElementById("port_range");
	var avid = document.getElementById("avid");
	var dvid = document.getElementById("dvid");
	var porttype = document.getElementById("porttype");
	var filter = document.getElementById("filter");
	var type_value = document.getElementById("type_value");
	var filter_value = document.getElementById("filter_value");
	var vlan_port_id = document.getElementById("vlan_port_id");
	var p = obj.value;

	if(obj.checked){
		target.value =target.value + p + " ";
		vlan_port_id.value =vlan_port_id.value + p + " , ";
		
	

	}
	else
	{
		target.value = target.value.replace(p + " ","");
		vlan_port_id.value = vlan_port_id.value.replace(p + " , ","");
	}

}

function P(index,portId,appid,avg)
{
    var narr=4;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+portId);	


    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		
	    tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
	tbtr.cells[0].innerHTML = index;
	tbtr.cells[1].innerHTML = appid;
    tbtr.cells[2].innerHTML = avg;
    

	tbtr.cells[3].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick=del_sv('"+portId+"','"+appid+"')>";
}


function getPage(page)
{
   location.href="mcast_limit.asp?page="+page+"&ltime="+<% write(lltime); %>;
}

function jumptoPage()
{
	var topage = document.getElementById("jump2page");
   location.href="mcast_limit.asp?page="+topage.value+"&ltime="+<% write(lltime); %>;
}

function dofirst(){location.href="mcast_limit.asp?page=1"+"&ltime="+<% write(lltime); %>;}


function refreshpage()
{
  location.href='mcast_limit.asp?page=1&ltime='+<% write(lltime); %>;
}


function AddOption(portname){

	var selectObject = document.getElementById("rang_monitor");
	var y=document.createElement('option');
  	y.text=portname;
	y.value = portname;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  	catch(ex)
    {
   	selectObject.add(y); // IE only
  	}
}


</script>
</head>

<body onload="display()" ><br>
					  
<form name="vlan_port" method="POST" action="">
	<% var  qoscfg; getQoSCfg(); %>
	<input type="hidden" name="qos_cfg"  id="qos_cfg"   value="<% write(qoscfg); %>">


<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr>
	<td>
		<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  		<tr>
    		<td valign="top" >
     			<table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
				<tr>
					<td>
					
 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>组播流量设置</b></font></td></tr>
 </table>
 
<!--
	    	  			<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
		     			<tr height="25">
		       				<td align="left" colspan="4"  class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"组播流量限速");</script></td>
		     			</tr>
	        			</table>
-->
	        			
       				</td>
       			</tr>
				<tr>
					<td>  	
						<table  border="0" cellspacing="0" cellpadding="0" width="100%" id="table1" class="tablebord">
						<tr height="25">
			     			<td width="18%" class="crons">&nbsp;组播MAC地址</td>
			     			<td width="32%" class="crons" >&nbsp;
			     	    		<input name="appid" type="text" class="input_x" >(HHHH.HHHH.HHHH,16进制)	     	    
			     	    	</td>
			     	  		<td width="18%" class="crons">&nbsp;&nbsp;阀值(16的倍数: 16~1000000)</td>
			     	  		<td width="32%" class="crons" >&nbsp;
			     	    		<input name="limit_value" type="text" class="input_x" >(Kbps)	     	    
			     	    	</td>
			 	    	</tr>		     	 
			    		<tr height="25">
							<td colspan="4" align="middle" class="crons">
								<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify_bu","checkup()");</script>&nbsp;&nbsp;&nbsp;
							</td>
			     		</tr>	  
						<tr>
			    			<td colspan="4">
			    				<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
			    		 		<tr align="center" height="25" class="crons">

									<th class="td2" width="20%">序号</th>
				    		 		<th class="td2" width="20%"><font color="#333333"><b>&nbsp;MAC</b></font></th>
				    		 		<th class="td2" width="20%">阀值</th>						
				    		 		<th class="td2" width="30%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"删除");</script></b></font></th>						
			    		 		</tr>
								<script>  <%  var errorcode; showMcastLimitPage("list"); %></script>
			    				</table>	  
			    			</td>
		   				</tr>
						</table>
					</td>
				</tr>
				<tr>
			  		<td colspan="2" align="center" height="35">
<!--	  	   
	  	   <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
-->	  	   
	  	   <script>writebutton(1,<% write(lang); %>,"首  页","button","button","firstpage","dofirst()");</script>
				<%showMcastLimitPage("pagebutton");%>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<%showMcastLimitPage("pagenum");%>
				<%showMcastLimitPage("allpage");%> 
			  		</td>
				</tr>
				</table>
			</td>
		</tr> 
		</table>
	</td>
</tr>
</table>


	<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

	<input type="hidden" name="port_value"  value="" >
	<input type="hidden" name="appid_value"  value="" >

</form>

<script>
changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</body>
</html>


