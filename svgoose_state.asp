<HTML>
<HEAD>
<% var lltime,lang; getltime_lanflag(); %>
<TITLE>page</TITLE>
<META http-equiv=Content-Type content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />

<% var svgooseState; getsvgooseStateInfo(); %>
<script>
function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function messageCheck()
{
	var hid = document.webForm;

	if(document.getElementById("d1").checked == true)
		document.forms[0].para1.value = "enable";
	else
		document.forms[0].para1.value = "disable";

	hid.submit();
	return true;
}


function display()
{
	tmp = "<% write(svgooseState); %>";
	array_cfg = tmp.split(",");

	if (array_cfg[0] == "enable")
		document.getElementById("d1").checked = true;
	else
		document.getElementById("d2").checked = true;
}


</script>
</HEAD>


<body  onload="display();"><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>

<form  name="webForm" method="get" action="/goform/setsvgooseState">

<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="para1">


 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>电网报文设置/传输业务报文设置</b></font></td></tr>
 </table>




<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
<tr height="25">
	<td width="15%" align="left" class="crons">&nbsp;转发GOOSE/SV报文模式</td>
	<td width="25%" colspan="3"align="left" class="crons">&nbsp;
		<input type="radio" name="rw" value="rw"  id="d1">Enable
		<input type="radio" name="rw" value="ro"  id="d2">Disable
	</td>
</tr>
</TABLE>

<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
<tr>
    <td   class="tablenew" id=tabs name=tabs><div align="center">
	<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","buttons_apply","button","button","messageCheck()");</script>
    </div></td>

  </tr>
</table>
</FORM>
<script>
changebgcolor();

<% if (errorcode!="") { write_errorcode(errorcode); } %>

</script>
</BODY></HTML>


