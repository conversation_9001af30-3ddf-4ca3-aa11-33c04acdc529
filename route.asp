<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_3.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"静态ARP设置");</script></title>


<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 

<script language="JavaScript" type="text/JavaScript">

function getPage(page)
{
   location.href="route.asp?page="+page+"&ltime="+<% write(lltime); %>;
}
var num = 0; 
function dofirst(){location.href="route.asp?page=1";}

function check()
{
	var tf=document.route;
	var dstip=document.getElementById("dstip").value;
    var nexthop=document.getElementById("nexthop").value;

    if(IpCheckAndMask(dstip) || dstip == "0.0.0.0/0")
   	{	
	    if(IpCheck(nexthop))
		{
			tf.submit();
		}
		else
		{
			alert(putmsg(<% write(lang); %>,"IP地址输入非法！格式为 A.B.C.D"));
		}
	}
	else
	{
		alert(putmsg(<% write(lang); %>,"IP地址输入非法！格式为 A.B.C.D/M 缺省路由格式为 0.0.0.0/0"));
	}
}

function checkdel()
{
	var tf=document.route;
	var del = document.getElementById("del");
	var dstip = document.getElementById("dstip").value;
	var nexthop=document.getElementById("nexthop").value;
    var route_tbl = document.getElementById("table_route");
    var i,j=0;
	
	if(dstip=="" || nexthop=="")
	{
		alert(putmsg(<% write(lang); %>,"请选择IP!"));
		return 0;
	}

	var checkboxes = document.getElementsByName("checkbox_index");
	for(i=0;i<checkboxes.length;i++){
		if(checkboxes[i].checked)
		{
			if(route_tbl.rows[i+1].cells[2].innerHTML=="connected" && dstip==route_tbl.rows[i+1].cells[1].innerHTML)
			{
				alert(putmsg(<% write(lang); %>,"不能删除直连路由!"));
				return;
			}
			if(route_tbl.rows[i+1].cells[2].innerHTML==nexthop && dstip==route_tbl.rows[i+1].cells[1].innerHTML)
			{
				del.value = "1";
    			tf.submit();
				return;
			}
		}
	}
}  

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var dstip = document.getElementById("dstip");
	var nexthop=document.getElementById("nexthop");

	if(obj.checked)
	{
		dstip.value=trobj.cells[1].innerHTML;
		nexthop.value=trobj.cells[2].innerHTML;		
	}
}

function P(portId,name,enable,instance,flag)
{
    var narr=6;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_route").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    num = num + 1;
	
    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+num);	

    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
    tbtr.cells[1].abbr = portId;
	
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+num+"\" onclick=\"addToPortRange(this)\"/>";
	tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = name;
    tbtr.cells[3].innerHTML = enable;	
    tbtr.cells[4].innerHTML = instance;
	tbtr.cells[5].innerHTML = flag;
}


function checkData()
{
	var tf=document.route;
	tf.action = "/goform/saveComm?name=route";
	tf.submit();
}

function refreshpage()
{
  location.href='route.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('route',<% write(lang); %>);
}
</script>
</head>

<body  >
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="route" method="POST" action="/goform/routeConfig">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<input type="hidden" name="pvid_config"  value="@pvid_config#">

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">

<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="90%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
         <tr>
           <td colspan="2" align="left" class="cword09">&nbsp;<script>writemsg(<% write(lang); %>,"三层转发管理");</script> <b><font color="#FF7F00">&gt;&gt;</font></b> &nbsp;<script>writemsg(<% write(lang); %>,"路由管理");</script></td>
         </tr>
        </table>
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%">
	     <tr height="25">
	       <th align="left" colspan="2">&nbsp;<script>writemsg(<% write(lang); %>,"静态路由设置");</script></td>
	     </tr>
		 <tr height="25">
              <td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"目的IP地址");</script></td>
              <td width="81%" align="left" class="crons">&nbsp;
                <input name="dstip" type="text"  id="dstip"/>&nbsp;<script>writemsg(<% write(lang); %>,"(格式为 A.B.C.D/M)");</script></td>
         </tr> 

	     <tr height="25">
	     	  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"下一跳IP地址");</script></td>
	     	  <td class="crons">
&nbsp;
<input name="nexthop" type="text"  id="nexthop"/>&nbsp;<script>writemsg(<% write(lang); %>,"(格式为 A.B.C.D)");</script></td>
	     </tr>		 		 
		 
	     <tr height="25">

	     	  <td colspan="2" align="middle" class="crons">
                      <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","add","check()");</script>	     	  
                      &nbsp;
                      <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","delete","checkdel()");</script>	     
	     	  </td>
	     </tr>
	   
<tr><td colspan="2" height="8"></td></tr>
   <tr>
	    	<td colspan="2">
	    		<table id="table_route" name="table_route" border="0" cellspacing="0" cellpadding="0" width="100%" class="table2">
	    		 	<tr align="center" height="25" class="crons">
	    		 		<th class="td2" width="5%"></th>
	    		 		<th class="td2" width="20%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"目的IP地址");</script></b></font></th>
	    		 		<th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"下一跳IP地址");</script></b></font></th>						
	    		 		<th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"接口");</script></b></font></th>
	    		 		<th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"类型");</script></b></font></th>
	    		 		<th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"标志");</script></b></font></th>
	    		 	</tr>
					<script>  <%  var errorcode; showRoute("route"); %></script>
	    		</table>
	    	</td>
   </tr>
     </table>
   </td></tr>
	<tr>
	  	   <td colspan="2" align="center" height="35">
	  	   <script>writebutton(1,<% write(lang); %>,"首  页","button","button","firstpage","getPage(1)");</script>
				<%showRoute("pagebutton");%>
	  		    &nbsp;
	  		    <%showRoute("pagenum");%>
			    <%showRoute("allpage");%>
	  		</td>
   </tr>
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>
<INPUT type="hidden" name="del" id= "del" value="0">
</form>    
<script>
changebgcolor();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</html>
