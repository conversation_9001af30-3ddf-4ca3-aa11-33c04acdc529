<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<HTML>
<HEAD>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<%  var authmode; checkCurMode(); %>

<TITLE>page</TITLE>
<SCRIPT language=javascript>var logout_alter="您确定要退出?"</script>

<meta http-equiv="Content-Type" content="text/html; charset=utf8" />
<metah ttp-equiv="X-UA-Compatible" content="IE=9; IE=8; IE=7; IE=EDGE">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1">
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<script language="javascript">



var statusStr;
var packetsStr;
var packetsStrOld = "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0";
var refeshTime=5001;
var portStatus=new Array(33);
var portPackets = new Array(33);
var portPacketsOld = new Array(33);
var alarm = 0;

var power1_alarm = "off";
var power2_alarm = "off";

var timer = null;

function changePort()
{
    timer=setTimeout("changePort()",refeshTime);


	if(window.parent.document.getElementById("hidden1").contentWindow.document.form1)
	{
		//window.parent.document.getElementsByTagName("frame")["hidden1"].contentWindow.document.form1.submit();
		//statusStr = window.parent.document.getElementsByTagName("frame")["hidden1"].contentWindow.statusStr;
		window.parent.document.getElementById("hidden1").contentWindow.document.form1.submit();
		statusStr = window.parent.document.getElementById("hidden1").contentWindow.statusStr;
		//alarm = window.parent.document.getElementsByTagName("frame")["hidden1"].contentWindow.alarm;
		//power1_alarm = window.parent.document.getElementsByTagName("frame")["hidden1"].contentWindow.power1_alarm;
		//power2_alarm = window.parent.document.getElementsByTagName("frame")["hidden1"].contentWindow.power2_alarm;
	    loadPort();
	}
}
function changeCursor()
{
	var imageID;

	for(var i = 1; i < 20; i++){
		imageID = 'Image' + i;

	  document.getElementById(imageID).style.cursor = "pointer";
	  document.getElementById(imageID).onclick = function() {
    top.mainctrl.location.href = "portstatus.asp?portNum=" + i;//this.id.substring(5,this.id.length);
    };
	}
}

function display_prologo(logo_pic, width, height)
{
	var info=new Array(logo_pic, width, height);
	var myImage = document.getElementById("imglogo");
	myImage.src = info[0];
	myImage.width = info[1];
	myImage.height = info[2];

	///myImage.src = logo_pic;
	//myImage.width = width;
	//myImage.height = height;
}
function isIE() {
    if(!!window.ActiveXObject || "ActiveXObject" in window){
      return true;
    }else{
      return false;
　　 }
}

function loadPort(){
	var imageID,picSrc,nPackets,pLevel;
	var srcEnd;
	var tdid,tdcid;
	var k=0;



	if(statusStr != null)
		portStatus = ToNumArray(statusStr.split(','));
	else
		return;

	for(var i = 0; i < portStatus.length; i++){

		imageID = 'Image' + (i + 1);
		tdid = 'tdImage' + (i + 1);
		tdcid = 'tdcImage' + (i + 1);

		if(portStatus[i]=='1')//copper down
		{
			if(i%2==0)
			picSrc = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXBAMAAAHvLGBEAAAAB3RJTUUH1AIKDDogR9tASAAAAAlwSFlzAAAOwwAADsMBx2+oZAAAAARnQU1BAACxjwv8YQUAAAAhUExURePj46mpqXx8fAEBAREREWliXU5NS9LS0re3t5mZmcLCwl6/DqgAAACXSURBVHjatY5BCgIxDEWDLgZm6REKeoGfjd1m530GwRN4gTmAVBfmlP4mODgguPK1tC+fpq2IyBZ9ElTRKqO7M2AxNrgEjG7SgGMud+XiimccJhQEX8WbATyzKQVayl4WLtn++JBosrfghxhHFzdojXuQsuNbKOUgw3nFJIOvmBlAsZAB/hBYOjfTCNxbRqf8x7XT65D5BRFBeUiNNVMyAAAAAElFTkSuQmCC';
			else
			picSrc = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXBAMAAACYK1DSAAAAIVBMVEXj4+Opqal8fHwBAQERERFpYl1OTUvS0tK3t7eZmZnCwsJevw6oAAAAAWJLR0QAiAUdSAAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB9cEGgk7C616mjMAAABcSURBVHjaY2AAA2MggLAY2DtAACQAZkxlYF+1atViY2NnYxNjKyATLgACXnCBJUAFSCqcIfJAytkELACkjZ2BGAQgAkhgkAqYQAVM0FSgAKIEylHAVAZGJRQQCgCsfXte0Tk7dwAAAABJRU5ErkJggg==';
		}
		else if(portStatus[i]=='2')//copper up
		{
			if(i%2==0)
			picSrc = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXCAYAAABqBU3hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAOwQAADsEBuJFr7QAAAAd0SU1FB9QCCg0eFnhLXkAAAAEKSURBVEhLY3z8+PF/hgEC9+/fZwA74P3791Ah+oEPHz4wLF8wG+EAkAA9wfXr1xkunDzCwATlDxgYcAdgRIG9vT2YBgEBAQGqR83BgwfBNCwKsDpAcYM3mE1NUP7ClyEjIwPDAXSLAk1NTSgLFYwmwlEHjDpg1AFYS8IZM2bgLDgoASCzCRbF/v7+ZJX/uOoNZHEQe+PGjWA2TgdgA8iOwldBocvBfIsNkFQXIBuKz6HIciDHEAPIToTYLEAWw+dQZACOAlDj8OvXr1Ah/MDLywvKwg62bdsGZeEHjx49gqSBQ4cO/X/+/DnD5cuXoVL4QWtrK5SFCmDxX11dDRXBD94+e8jw9PU7BgBu9sLStNVQzwAAAABJRU5ErkJggg==';
			else
			picSrc = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXCAYAAABqBU3hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAGYktHRAD/AP8A/6C9p5MAAAAHdElNRQfXBBoKAjVg2bR7AAAA9klEQVRIS+2UMQ6CMBSGn54AL0DiyCnYcehtPAujR5CFncRA3EwYGEngALAwa1pf5QmlFiUs9pv+vobmo695m7qu7zAD13UxveM4DrRtC1VVYcUMIVCWJXRdhyU9QRBgUhPHMSY9XPR2vTwFmqYR9lP4vo+pR/4xZVhLkgTTmKIohMAW17NRCdMalzHBSIAepjuY7ululDJqAWPM+GPK8PoltM5zFEUiyxaMBHi/wzAEz/PEekn42fJdaAX254PIS3LaHZUCXz/CpbACVsAK/I8AHzwqlJNQMjXff+HjKF4LO4olrxasTZZl/RtI0xTyPMetNQF4AFE1ti2vqz4vAAAAAElFTkSuQmCC';
		}
		else if(portStatus[i]=='3')//fiber down
		{
	     	 picSrc = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXBAMAAACYK1DSAAAAB3RJTUUH2AYKCB4IBs3HcwAAAAlwSFlzAAALEgAACxIB0t1+/AAAAARnQU1BAACxjwv8YQUAAAAwUExURePj46mpqXx8fNLS0mliXU5NS7e3t5mZmcLCwgAAAAAAAAAAAAAAAAAAAAAAAAAAAGq05IQAAAAtSURBVHjaY2BAB8zGKMCEgbkDBZRgE5iJBIa/QEcbofBAF0hDASUMjEoowBUARgqsenjRgOAAAAAASUVORK5CYII=';
		}
		else if(portStatus[i]=='4')//fiber up
		{
	      	picSrc = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXBAMAAACYK1DSAAAAB3RJTUUH2AYKCCIH5YGjHQAAAAlwSFlzAAAK8AAACvABQqw0mAAAAARnQU1BAACxjwv8YQUAAAAwUExURQAAAE5NS2liXXx8fJmZmampqbe3t8LCwtLS0uf8A+Pj4wAAAAAAAAAAAAAAAAAAAAh+Of8AAAA7SURBVHjaY1iFBhhWdKCAJoYV5SjACZsAAxIAC3DOhIMJQ1BgAgMDJ1wAw3OEwwNdIA0FODEsNUYBigCg/alOPkwAGwAAAABJRU5ErkJggg==';
		}
		else// if(portStatus[i]=='0')
		{
			continue;
		}
		if(k==0)
		{

			document.getElementById(tdid).style="text-align:center";
			document.getElementById(tdcid).style="text-align:center";
			if(isIE())
			{
				document.getElementById(tdid).style.display="";
				document.getElementById(tdcid).style.display="";
				document.getElementById(tdid).style.textAlign="center"
				document.getElementById(tdcid).style.textAlign="center"
			}
		}
		document.getElementById(imageID).src = picSrc;

	}



  if(alarm == 0){
    document.getElementById("alarm1").src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAABKSURBVDhP1ZBBCgAgCAT7/w98rVFhqAwSdWpgTu5cbAqISJAIYQ6ynh3SkDRmSIPKt5AOJ/4UXj/nKRzQgDR2OKCh1xNCowoWqh1SRVwneKa47QAAAABJRU5ErkJggg==";
  }else{
    document.getElementById("alarm1").src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAOxAAADsQBlSsOGwAAAE1JREFUOE/VkDEKACAMxHyIo///mW+oqFRaCUV0MpCpl6VJgJqLk3DhHuxaVkhDUhkhDSLfQjqc+FN4/ZynsEMDUllhh4ZWiwuVKJiINDJTAM8xEDqcAAAAAElFTkSuQmCC";
  }

  if(power1_alarm == "on"){
    document.getElementById("pwr1").src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAABMSURBVDhP1ZDBCQAgDMScxXlc1gkrKpVWQhF9Gcirl0+TALkWJ+HCPdi1rJCGpDJCGkS+hXQ48afw+jlPYYcGpLLCDg2tFhcqUTARaQOg+Kix1rkcAAAAAElFTkSuQmCC";
  }else{
    document.getElementById("pwr1").src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAABKSURBVDhP1ZBBCgAgCAT7/w98rVFhqAwSdWpgTu5cbAqISJAIYQ6ynh3SkDRmSIPKt5AOJ/4UXj/nKRzQgDR2OKCh1xNCowoWqh1SRVwneKa47QAAAABJRU5ErkJggg==";
  }


  if(power2_alarm == "on"){
    document.getElementById("pwr2").src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAABMSURBVDhP1ZDBCQAgDMScxXlc1gkrKpVWQhF9Gcirl0+TALkWJ+HCPdi1rJCGpDJCGkS+hXQ48afw+jlPYYcGpLLCDg2tFhcqUTARaQOg+Kix1rkcAAAAAElFTkSuQmCC";
  }else{
    document.getElementById("pwr2").src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAABKSURBVDhP1ZBBCgAgCAT7/w98rVFhqAwSdWpgTu5cbAqISJAIYQ6ynh3SkDRmSIPKt5AOJ/4UXj/nKRzQgDR2OKCh1xNCowoWqh1SRVwneKa47QAAAABJRU5ErkJggg==";
  }

}

function ToNumArray(anStrArray)
{   var len, i;
    len = anStrArray.length;
    for (i=0; i<len; i++)
        anStrArray[i] = parseInt(anStrArray[i]);
    return anStrArray;
}
function gotoSta(portNum)
{
  top.mainctrl.location.href = "portstatus.asp?portNum=" + portNum;
}

window.onload = function() {
    changePort();
    //changeCursor(); //delet by Ray, 20171229
}


var _msgTable_en={
"是否保存当前配置:":"Save current changes before reboot"
};


var _msgTable_ch={
"ZZZ":"ZZZ"
};
function putmsg(lang,key)
{
try {
      eval("var m=_msgTable_"+lang+"[key];");
      if (!m)
      {
       return key;
      }
       return m;
	}
	catch (e) {
	   return key;
	}
}

function messageCheck()
{
	var hid = document.form1;

	hid.submit();
	return true;
}

function messageCheck2()
{
    var hid = document.form2;

	if (<% write(authmode); %> == 1)
	{
		if(confirm(putmsg(<% write(lang); %>,"确认要保存所有配置吗？")))
		{
			hid.submit();
			return true;
		}
	}
	else
	{
		alert('不具备执行该操作的权限!');
		return false;
	}
}

</script>
<style type="text/css">
body {margin:0px; font-family : Verdana, Arial, Helvetica; background-color:#ffffff}
td {font-family : Verdana, Arial, Helvetica;font-size : 12px;color:000000}
.link {font-family : Verdana, Arial, Helvetica;font-size : 12px;font-weight: bold; }
.logo{ border:0; position:absolute;top:8px;left:15px}
</style>
</head>



<BODY   bgcolor="D8D6D0" height="70"   >



<form id="form1" name="form1" method="get" action="/goform/setLogoutCfg">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
</form>
<form id="form2" name="form2" method="get" action="/goform/saveconfig2">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
</form>

<form name="forma"   >

<input name="system_user_can_reboot"  id="system_user_can_reboot" value="$%system_user_can_reboot#$" type="hidden">

<table width="100%" border="0" cellspacing="0" cellpadding="0" height="80" style="padding-top:1px; padding-bottom:1px">
<tr>

	<td width="25%">
	<table>


		<tr>
		<td><img id="imglogo" src="" width="210" height="79"  /></td>
		<!--
		<td style="font-size:50px;font-weight:bold;FONT-FAMILY:Arial;color:#3cc4c4">Switch</td>
		-->
		</tr>
		<TBODY>
		<script>
		<%  var errorcode; showlogobyvendorInfo(); %>
		</script>

	</table>
	</td>


	<td align="center" width="50%">
	<table width="522" height="" border="0" cellspacing="0" cellpadding="0" >
		<tr style="display:none">
			<td  width="16" rowspan="5">&nbsp;</td>
			<td  width="54">&nbsp;</td>

			<td  width="10" rowspan="5">&nbsp;</td>
			<td  width="110" colspan="4" style="text-align:center"><font color="#1874CD" size="1">┌────M1────┐</font></td>

			<td  width="10" rowspan="5">&nbsp;</td>
			<td  width="110" colspan="4" style="text-align:center"><font color="#1874CD" size="1">┌────M2────┐</font></td>

      		<td  width="10" rowspan="5">&nbsp;</td>
			<td  width="110" colspan="4" style="text-align:center"><font color="#1874CD" size="1">┌────M3────┐</font></td>

			<td  width="10" rowspan="5">&nbsp;</td>
			<td  width="56" colspan="2" style="text-align:center"><font color="#1874CD" size="1">┌─M0─┐</font></td>

			<td  width="26" rowspan="5">&nbsp;</td>
		</tr>
		<tr style="text-align:center">

			<td style="display:none" width="54" height="" rowspan="4"  align="right" style="padding:0px;border:0px;margin:0px;font-size:1px" >
				<div><span style="margin:0px;"><font color="#1874CD" size="1">PWR1</font>
				<img id="pwr1" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAABKSURBVDhP1ZBBCgAgCAT7/w98rVFhqAwSdWpgTu5cbAqISJAIYQ6ynh3SkDRmSIPKt5AOJ/4UXj/nKRzQgDR2OKCh1xNCowoWqh1SRVwneKa47QAAAABJRU5ErkJggg==" width="12" height="12"/></span></div>
				<div><span style="margin:0px;"><font color="#1874CD" size="1">PWR2</font>
				<img id="pwr2" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAABKSURBVDhP1ZBBCgAgCAT7/w98rVFhqAwSdWpgTu5cbAqISJAIYQ6ynh3SkDRmSIPKt5AOJ/4UXj/nKRzQgDR2OKCh1xNCowoWqh1SRVwneKa47QAAAABJRU5ErkJggg==" width="12" height="12"/></span></div>
				<div><span style="margin:0px;"><font color="#1874CD" size="1">ALM</font>
				<img id="alarm1" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAABKSURBVDhP1ZBBCgAgCAT7/w98rVFhqAwSdWpgTu5cbAqISJAIYQ6ynh3SkDRmSIPKt5AOJ/4UXj/nKRzQgDR2OKCh1xNCowoWqh1SRVwneKa47QAAAABJRU5ErkJggg==" width="12" height="12"/></span></div>
			</td>
			<td  style="display:none" width="26" id="tdcImage30"><font color="#1874CD" size="1">G2</font></td>
			<td  style="display:none" width="26" id="tdcImage32"><font color="#1874CD" size="1">G4</font></td>

			<td  style="display:none" width="26" id="tdcImage2"><font color="#1874CD" size="1">2</font></td>
			<td  style="display:none" width="26" id="tdcImage4"><font color="#1874CD" size="1">4</font></td>
			<td  style="display:none" width="26" id="tdcImage6"><font color="#1874CD" size="1">6</font></td>
			<td  style="display:none" width="26" id="tdcImage8"><font color="#1874CD" size="1">8</font></td>
			<td  style="display:none" width="26" id="tdcImage10"><font color="#1874CD" size="1">10</font></td>
			<td  style="display:none" width="26" id="tdcImage12"><font color="#1874CD" size="1">12</font></td>
			<td  style="display:none" width="26" id="tdcImage14"><font color="#1874CD" size="1">14</font></td>
			<td  style="display:none" width="26" id="tdcImage16"><font color="#1874CD" size="1">16</font></td>
			<td  style="display:none" width="26" id="tdcImage18"><font color="#1874CD" size="1">18</font></td>
			<td  style="display:none" width="26" id="tdcImage20"><font color="#1874CD" size="1">20</font></td>
			<td  style="display:none" width="26" id="tdcImage22"><font color="#1874CD" size="1">22</font></td>
			<td  style="display:none" width="26" id="tdcImage24"><font color="#1874CD" size="1">24</font></td>
			<td  style="display:none" width="26" id="tdcImage26"><font color="#1874CD" size="1">26</font></td>
			<!--<td  style="display:none" width="26" id="tdcImage28"><font color="#1874CD" size="1">28</font></td>-->

    	</tr>

    	<tr style="text-align:center">
    		<td  style="display:none" width="26" id="tdImage30"><img id="Image30" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage32"><img id="Image32" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage2"><img id="Image2" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage4"><img id="Image4" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage6"><img id="Image6" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage8"><img id="Image8" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage10"><img id="Image10" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage12"><img id="Image12" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage14"><img id="Image14" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage16"><img id="Image16" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage18"><img id="Image18" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage20"><img id="Image20" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage22"><img id="Image22" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage24"><img id="Image24" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage26"><img id="Image26" src="" width="26" height="18"/></td>
			<!--<td  style="display:none" width="26" id="tdImage28"><img id="Image28" src="" width="26" height="18"/></td>-->

    	</tr>
    	<tr style="text-align:center">
    		<td  style="display:none" width="26" id="tdImage29"><img id="Image29" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage31"><img id="Image31" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage1"><img id="Image1" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage3"><img id="Image3" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage5"><img id="Image5" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage7"><img id="Image7" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage9"><img id="Image9" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage11"><img id="Image11" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage13"><img id="Image13" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage15"><img id="Image15" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage17"><img id="Image17" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage19"><img id="Image19" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage21"><img id="Image21" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage23"><img id="Image23" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage25"><img id="Image25" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage27"><img id="Image27" src="" width="26" height="18"/></td>
			<td  style="display:none" width="26" id="tdImage28"><img id="Image28" src="" width="26" height="18"/></td>

    	</tr>
    	<tr style="text-align:center">
 			<td  style="display:none" width="26" id="tdcImage29"><font color="#1874CD" size="1">G1</font></td>
			<td  style="display:none" width="26" id="tdcImage31"><font color="#1874CD" size="1">G2</font></td>
			<td  style="display:none" width="26" id="tdcImage1"><font color="#1874CD" size="1">1</font></td>
			<td  style="display:none" width="26" id="tdcImage3"><font color="#1874CD" size="1">3</font></td>
			<td  style="display:none" width="26" id="tdcImage5"><font color="#1874CD" size="1">5</font></td>
			<td  style="display:none" width="26" id="tdcImage7"><font color="#1874CD" size="1">7</font></td>
			<td  style="display:none" width="26" id="tdcImage9"><font color="#1874CD" size="1">9</font></td>
			<td  style="display:none" width="26" id="tdcImage11"><font color="#1874CD" size="1">11</font></td>
			<td  style="display:none" width="26" id="tdcImage13"><font color="#1874CD" size="1">13</font></td>
			<td  style="display:none" width="26" id="tdcImage15"><font color="#1874CD" size="1">15</font></td>
			<td  style="display:none" width="26" id="tdcImage17"><font color="#1874CD" size="1">17</font></td>
			<td  style="display:none" width="26" id="tdcImage19"><font color="#1874CD" size="1">19</font></td>
			<td  style="display:none" width="26" id="tdcImage21"><font color="#1874CD" size="1">21</font></td>
			<td  style="display:none" width="26" id="tdcImage23"><font color="#1874CD" size="1">23</font></td>
			<td  style="display:none" width="26" id="tdcImage25"><font color="#1874CD" size="1">25</font></td>
			<td  style="display:none" width="26" id="tdcImage27"><font color="#1874CD" size="1">27</font></td>
			<td  style="display:none" width="26" id="tdcImage28"><font color="#1874CD" size="1">28</font></td>
    	</tr>
	</table>
	</td>

	<td>
		<table align="right">
		<tr height="58" valign="top">
			<td colspan="2" style="display:none"><div id="showtimes"></div></td>
		</tr>
		<tr>
			<td style="display:none">
 	 		<a onClick="javascript:messageCheck2();" style="font-size:15px;font-weight:bold;text-decoration:none;color:#3cc4c4;cursor:pointer">保存</a>&nbsp;|
 	 		<a onClick="javascript:messageCheck();" style="font-size:15px;font-weight:bold;text-decoration:none;color:#3cc4c4;cursor:pointer">退出</a>&nbsp;
 	 		</td>
		</tr>
		</table>
	</td>

</tr>

<tr>
	<td colspan="3" height="15px" bgcolor="#3cc4c4">&nbsp;</td></tr>
<tr>

</table>

</form>
<script>

function show_cur_times(){
 var date_time = new Date();
 var week;
 switch (date_time.getDay()){
	 case 1: week="星期一"; break;
	 case 2: week="星期二"; break;
	 case 3: week="星期三"; break;
	 case 4: week="星期四"; break;
	 case 5: week="星期五"; break;
	 case 6: week="星期六"; break;
	 default:week="星期天"; break;
 }
 var year = date_time.getFullYear();
   if(year<10){
 	year="0"+year;
 }
 var month = date_time.getMonth()+1;
  if(month<10){
	 month="0"+month;
 }
 var day = date_time.getDate();
   if(day<10){
 	day="0"+day;
 }
 var hours =date_time.getHours();
    if(hours<10){
 	hours="0"+hours;
 }
 var minutes =date_time.getMinutes();
    if(minutes<10){
 	minutes="0"+minutes;
 }
 var seconds=date_time.getSeconds();
    if(seconds<10){
 	seconds="0"+seconds;
 }
 var date_str = year+"-"+month+"-"+day+"&nbsp;&nbsp;"+hours+":"+minutes+":"+seconds+"&nbsp;&nbsp;"+week;
 document.getElementById("showtimes").innerHTML= date_str;

}
setInterval("show_cur_times()",100);

</script>


</body>


</html>
