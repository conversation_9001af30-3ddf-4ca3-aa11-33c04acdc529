<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_rtl.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"VLAN规则设置");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 

<script language="JavaScript" type="text/JavaScript">

function check()
{
	var tf=document.vlan_classport;
	var protocol_vlan = document.getElementById("protocol_vlan");
	var mac_vlan = document.getElementById("mac_vlan");
	var subnet_vlan = document.getElementById("subnet_vlan");
	var check_both=document.getElementsByName("check_both");
	var port = document.getElementById("port").value;
	if(port=="")
	{
	    alert(putmsg(<% write(lang); %>,"请选择端口!"));	
		return 0;
	}

	if(check_both[0].checked)
		mac_vlan.value=1;
	if(check_both[1].checked)
		subnet_vlan.value=1;
	if(check_both[2].checked)
		{
		  if(document.vlan_classport.rule_id.value=="")
		  	{
		  	 alert(putmsg(<% write(lang); %>,"Rule ID不能为空!"));	
		     return 0;
		  	}
		   protocol_vlan.value=1;
		}
	
	tf.submit();
}

function checkdel()
{
	var tf=document.vlan_classport;	
	var port = document.getElementById("port").value;
	var del = document.getElementById("del");
	var protocol_vlan = document.getElementById("protocol_vlan");
	var mac_vlan = document.getElementById("mac_vlan");
    var subnet_vlan = document.getElementById("subnet_vlan");
	var check_both=document.getElementsByName("check_both");
	if(port=="")
	{
		alert(putmsg(<% write(lang); %>,"请选择端口!"));
		return 0;
	}
	if(check_both[0].checked)
		mac_vlan.value=2;
	if(check_both[1].checked)
		subnet_vlan.value=2;
	if(check_both[2].checked)
		protocol_vlan.value=2;

    del.value = "1";
 	tf.submit();
}  

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var vid = document.getElementById("port");
	var check_both=document.getElementsByName("check_both");
	var p = obj.value;

	if(obj.checked)
	{
		vid.value=trobj.cells[1].innerHTML;
		if(check_both[0].value == trobj.cells[2].innerHTML)
			check_both[0].checked = true;
		else
			check_both[0].checked = false;
		
		if(check_both[1].value == trobj.cells[2].innerHTML)
			check_both[1].checked = true;
		else
			check_both[1].checked = false;

        if(trobj.cells[2].innerHTML.indexOf(check_both[2].value)!=-1)
    	{
		    check_both[2].checked = true;
			document.vlan_classport.rule_id.disabled=false;
			document.vlan_classport.rule_id.value=trobj.cells[2].innerHTML.substring(14);
    	}
		else
		{
			check_both[2].checked = false;
			document.vlan_classport.rule_id.disabled=true;
			document.vlan_classport.rule_id.value="";
		}
	}
}

function p(vid,vlantype)
{
    var narr=3;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_claport_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+vid+vlantype);	

    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	            
  	
    
    tbtr.cells[0].abbr = 0;
    tbtr.cells[1].abbr = vid;
	
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+vid+vlantype+"\" onclick=\"addToPortRange(this)\"/>";
    tbtr.cells[1].innerHTML = vid;
    tbtr.cells[2].innerHTML = vlantype;	
}

function protocol_msg(obj)
{
  if(!obj.checked)
  	document.vlan_classport.rule_id.disabled=true;
  else
  	document.vlan_classport.rule_id.disabled=false;
  return;
}

function checkData()
{
	var tf=document.vlan_classport;
	tf.action = "/goform/saveComm?name=vlan_classport";
	tf.submit();
}

function refreshpage()
{
  location.href='vlan_classport.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('vlan_classport',<% write(lang); %>);
}

</script></head>

<body  >
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="vlan_classport" method="POST" action="/goform/vlanClaportConfig">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="90%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
         <tr>
           <td colspan="2" align="left" class="cword09">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN设置");</script><b><font color="#FF7F00">&gt;&gt;</font></b> &nbsp;<script>writemsg(<% write(lang); %>,"VLAN规则配置");</script></td>
         </tr>
        </table>
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%">
	     <tr height="25">
	       <th align="left" colspan="2">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN规则");</script></td>
	     </tr>
	      <tr height="25">
	     	  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></td>
	     	  <td class="crons">
	     	    &nbsp;<input type="text" id="port" name="port" class="input_board5" maxlength="4">&nbsp;<script>writemsg(<% write(lang); %>,"(例如:fe1,ge1,xe1....)");</script>
	     	  </td>
	     </tr>		 
	     <tr height="25">
	     	  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN分类");</script></td>
	     	  <td class="crons">
	     	    <input type="checkbox" name="check_both"  value="mac-vlan"/> &nbsp;MAC-Based VLAN 
	     	    <br><input type="checkbox" name="check_both" value="subnet-vlan"/>&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"子网VLAN");</script>
	     	    <br><input type="checkbox" name="check_both" value="protocol-vlan:" onclick="protocol_msg(this)"/>&nbsp;&nbsp;<script>writemsg(<% write(lang); %>,"协议VLAN");</script> 
	     	        <input type="text" name="rule_id" disabled="true"/>&nbsp;&nbsp;(&nbsp;<script>writemsg(<% write(lang); %>,"格式");</script>:2000,2001,2002......)Rule ID 
	     	  </td>
	     </tr>
	     <tr height="25">

	     	  <td colspan="2" align="middle" class="crons">
	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","add","check()");</script>
    		    				&nbsp;
			  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","delete","checkdel()");</script>
	     	  </td>
	     </tr>	   
<tr><td colspan="2" height="8"></td></tr>
   <tr>
	    	<td colspan="2">
	    		<table id="table_claport_vlan" border="0" cellspacing="0" cellpadding="0" width="100%" class="table2">
	    		 	<tr align="center" height="25" class="crons">
	    		 		<th class="td2" width="15%"></th>
	    		 		<th width="15%" class="td2"><strong><font color="#333333">&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></font></strong></th>
	    		 		<th class="td2" width="25%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"VLAN分类");</script></b></font></th>
	    		 	</tr>
	    		 	<script><%  var errorcode; showVlanClaport(); %></script>
	    		</table>
	    	</td>
   </tr>
   <tr>
	  	   <td colspan="2" align="center" height="35">
	  		    <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
	  		</td>
   </tr>
	    
     </table>
   </td></tr>
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>
<INPUT type="hidden" name="del" id= "del" value="0">
<INPUT type="hidden" name="protocol_vlan" id= "protocol_vlan" value="0">
<INPUT type="hidden" name="mac_vlan" id= "mac_vlan" value="0">
<INPUT type="hidden" name="subnet_vlan" id= "subnet_vlan" value="0">
</form>    
<script>
<% if (errorcode=="1") { write("alert(putmsg("); write(lang); write(",'Rule ID不能为空!')); location.href = 'vlan_classport.asp?ltime="); write(lltime); write("';");} %>
<% if (errorcode=="2") { write("alert(putmsg("); write(lang); write(",'Rule ID的范围应该在2000-2099之间!')); location.href = 'vlan_classport.asp?ltime="); write(lltime); write("';");} %>
<% if (errorcode!="1" && errorcode!="2"&& errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</html>
