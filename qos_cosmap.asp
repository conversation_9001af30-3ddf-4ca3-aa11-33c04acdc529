<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"QoS");</script></title>

<script language="JavaScript">
</script>

<script language="JavaScript" type="text/JavaScript">

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function display()
{
	changebgcolor();
	
	var wValue = document.getElementById("qos_cfg").value.split(',');

	if(wValue[0] == "disable")
	{
		document.getElementById("modify2").disabled = true;
		alert("QoS未开启!");
	}
	else
	{
		document.forms[0].QosCosMap1.value = wValue[0];
		document.forms[0].QosCosMap2.value = wValue[1];
		document.forms[0].QosCosMap3.value = wValue[2];
		document.forms[0].QosCosMap4.value = wValue[3];
		document.forms[0].QosCosMap5.value = wValue[4];
		document.forms[0].QosCosMap6.value = wValue[5];
		document.forms[0].QosCosMap7.value = wValue[6];
		document.forms[0].QosCosMap8.value = wValue[7];
	}
}


function checkSub()
{
	var idName= "";
	var quee;
	var cmd= "";
	var wValue ="";
	var tf=document.form1;
	
	for(var j=1;j<9;j++)
	{
		idName = "QosCosMap"+j;
		wValue = document.getElementById(idName).value;
		if(wValue == 0)
		cmd += " 0 ";
		else
		cmd += " " + wValue;
	}

	cmd = "mls qos cos-map " + cmd;

	document.forms[0].qos_cmd.value = cmd;
	//alert(cmd);
	tf.submit();
}

</script>

</HEAD>

<BODY  onload=display() >
<br>

<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<% var errorcode, qoscfg; getQoSCosMap(); %>


<form name="form1" method="post" action="/goform/setQoSCosMap" >
<input type="hidden" name="qos_cfg"  id="qos_cfg"   value="<% write(qoscfg); %>">
<input type="hidden" name="qos_cmd"  id="qos_cmd"   value="">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>


 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>QOS设置/COS映射</b></font></td></tr>
 </table>
 
<!--

<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
<tr>
    <td  class=Tablelist id=tabs name=tabs><nobr>802.1P优先级队列</nobr></td>
 
    
  </tr>
</table>
-->

<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
  <TBODY>
    <TR height=22>
      <TD width="25%" valign="top"><div align="left">&nbsp;&nbsp;<span class="td25">COS优先级0的<span class="partition">队列值</span></span></div></TD>
      <TD width="25%" ><span class="crons">
        <select name="QosCosMap1" id="QosCosMap1">
          <option value="0" >0</option>
          <option value="1" >1</option>
          <option value="2" >2</option>
          <option value="3" >3</option>
          <option value="4" >4</option>
          <option value="5" >5</option>
          <option value="6" >6</option>
          <option value="7" >7</option>
        </select>
      </span></TD>
      <TD width="25%" >&nbsp;&nbsp;<span class="td25">COS优先级1的<span class="partition">队列值</span></span></TD>
      <TD width="25%" ><span class="crons">
        <select name="QosCosMap2" id="QosCosMap2">
            <option value="0" >0</option>
          <option value="1" >1</option>
          <option value="2" >2</option>
          <option value="3" >3</option>
          <option value="4" >4</option>
          <option value="5" >5</option>
          <option value="6" >6</option>
          <option value="7" >7</option>
       </select>
      </span></TD>
    </TR>
    <TR height=22>
      <TD valign="top">&nbsp;&nbsp;<span class="td25">COS优先级2的<span class="partition">队列值</span></span></TD>
      <TD ><span class="crons">
        <select name="QosCosMap3" id="QosCosMap3">
          <option value="0" >0</option>
          <option value="1" >1</option>
          <option value="2" >2</option>
          <option value="3" >3</option>
          <option value="4" >4</option>
          <option value="5" >5</option>
          <option value="6" >6</option>
          <option value="7" >7</option>
         </select>
      </span></TD>
      <TD >&nbsp;&nbsp;<span class="td25">COS优先级3的<span class="partition">队列值</span></span></TD>
      <TD ><span class="crons">
        <select name="QosCosMap4" id="QosCosMap4">
          <option value="0" >0</option>
          <option value="1" >1</option>
          <option value="2" >2</option>
          <option value="3" >3</option>
          <option value="4" >4</option>
          <option value="5" >5</option>
          <option value="6" >6</option>
          <option value="7" >7</option>
         </select>
      </span></TD>
    </TR>
    <TR height=22>
      <TD valign="top">&nbsp;&nbsp;<span class="td25">COS优先级4的<span class="partition">队列值</span></span></TD>
      <TD ><span class="crons">
        <select name="QosCosMap5" id="QosCosMap5">
          <option value="0" >0</option>
          <option value="1" >1</option>
          <option value="2" >2</option>
          <option value="3" >3</option>
          <option value="4" >4</option>
          <option value="5" >5</option>
          <option value="6" >6</option>
          <option value="7" >7</option>
         </select>
      </span></TD>
      <TD >&nbsp;&nbsp;<span class="td25">COS优先级5的<span class="partition">队列值</span></span></TD>
      <TD ><span class="crons">
        <select name="QosCosMap6" id="QosCosMap6">
           <option value="0" >0</option>
          <option value="1" >1</option>
          <option value="2" >2</option>
          <option value="3" >3</option>
          <option value="4" >4</option>
          <option value="5" >5</option>
          <option value="6" >6</option>
          <option value="7" >7</option>
         </select>
      </span></TD>
    </TR>
    <TR height=22>
      <TD valign="top">&nbsp;&nbsp;<span class="td25">COS优先级6的<span class="partition">队列值</span></span></TD>
      <TD ><span class="crons">
        <select name="QosCosMap7" id="QosCosMap7">
          <option value="0" >0</option>
          <option value="1" >1</option>
          <option value="2" >2</option>
          <option value="3" >3</option>
          <option value="4" >4</option>
          <option value="5" >5</option>
          <option value="6" >6</option>
          <option value="7" >7</option>
         </select>
      </span></TD>
      <TD >&nbsp;&nbsp;<span class="td25">COS优先级7的<span class="partition">队列值</span></span></TD>
      <TD ><span class="crons">
        <select name="QosCosMap8" id="QosCosMap8">
          <option value="0" >0</option>
          <option value="1" >1</option>
          <option value="2" >2</option>
          <option value="3" >3</option>
          <option value="4" >4</option>
          <option value="5" >5</option>
          <option value="6" >6</option>
          <option value="7" >7</option>
         </select>
      </span></TD>
    </TR>
    <TR height=22>
      <TD colspan="4" valign="top">
             <div align="center">
                 <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify2","checkSub()");</script>    
                 </div></TD>
      </TR>
</TABLE>


</form>
<script>
<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</BODY></HTML>
