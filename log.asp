<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"日志管理");</script></title>
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript">
function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}
/*
function checkMessage()
{
	var dplen;
	var dpvalue;
	var reauthn;
	var reauthpd;

	
	var name = prompt("操作认证-用户名", "");
	document.getElementById("reauthn").value = name;
	var pwd = prompt("操作认证-密码", "");
	document.getElementById("reauthpd").value = pwd;


	reauthn = document.getElementById("reauthn").value;
	reauthpd = document.getElementById("reauthpd").value;

	dplen = parseInt(reauthn.length/2);
	dpvalue = reauthn;
	reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen) 
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(reauthpd.length/2);
	dpvalue = reauthpd;
	document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen) 
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
	document.getElementById("reauthn").value = "XD!Zb3BjRCCoPExe";

  document.sys_log.submit();
	return true;
}
*/
function getPage(v)
{
	var v1=parseInt(v);
	var value=parseInt(document.getElementById("pageNum").value);
	var page;
	var maxcount=parseInt(document.getElementById("maxcount").value);
	if(v1==0)
		{
			document.location.href="log.asp?page=1&ltime="+<% write(lltime); %>;
			return true;
		}
	if(v1==-1)
		{
			page=value-1;
			if(page<1)
				{
					alert(putmsg(<% write(lang); %>,"当前页面已经是第一页!"));
					return true;
				}
		}
	if(v1==1)
		{
			page=value+1;
			if(page>((maxcount/40)+1))
			{
				alert(putmsg(<% write(lang); %>,"已经到达尾页!"));
				return true;
			}
			if((page==((maxcount/40)+1))&&((maxcount%40)==0))
			{
				alert(putmsg(<% write(lang); %>,"已经到达尾页!"));
				return true;
			}
		}
	if(v1==2)
		{
			if((parseInt(maxcount)%40)!=0)
			{
				page=parseInt((parseInt(parseInt(maxcount)/40))+1);
			}
			if((parseInt(maxcount)%40)==0)
			{
				page=parseInt(maxcount)/40;
			}
		}
	if(v1==3)
		{
			page=value;
		}
	document.location.href="log.asp?page="+page+"&ltime="+<% write(lltime); %>;
	return true;
}

function showHelpinfo()
{
   showHelp('log',<% write(lang); %>);
}



</SCRIPT>
</head>

<body  onload=""><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>

<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
	<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
		<tr><td class="tit" height="30px" ><font color="#0069d6"><div class="bot">系统日志</div></font></td></tr>
	 </table>
	 
<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" class="formContain">
  <tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td>

 
<!--
        <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
            <tr height="25">
              	<td colspan="1" align="left" class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"系统日志");</script></td>
		
				<form  name="getlogForm" method="post" action="">
  			 <td align="right"  style='display:none'>
  			 <input type="hidden" name="ltime" value=<% write(lltime); %>>
  			 <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"下  载","button","submit","apply4","getlogCheck()");</script>
  			 </td>
  		     </form>
			 
			 
            </tr>

        </table>
-->
        </td>
        
      </tr>
 <form name="sys_log" method="POST" action="">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="maxpage" id="maxpage">     <tr>
<input type="hidden" id="reauthn" name="reauthn" value="">
<input type="hidden" id="reauthpd" name="reauthpd" value="">


        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
            <tr>
		        <td><table id="table1"  border="0" cellspacing="0" cellpadding="0" width="100%"  class="tablebord2">
              		<% var pageNum,maxcount,errorcode;showSysLog(); %>
              		<input type="hidden" name="pageNum" id="pageNum" value=<% write(pageNum); %>>
              		<input type="hidden" name="maxcount" id="maxcount" value=<% write(maxcount); %>>
		        </table></td>
      		</tr>
        </table></td>
      </tr>
      	<tr>
        <td align="center" height="35">
          <script>writebutton(1,<% write(lang); %>,"首  页","button","button","firstpage","getPage(0)");</script>
          &nbsp;
          <script>writebutton(1,<% write(lang); %>,"上一页","button","button","prepage","getPage(-1)");</script>
          &nbsp;
          <script>writebutton(1,<% write(lang); %>,"下一页","button","button","nextpage","getPage(1)");</script>
          &nbsp;
          <script>writebutton(1,<% write(lang); %>,"尾  页","button","button","lastpage","getPage(2)");</script>
          &nbsp;
          <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","getPage(3)");</script>
           &nbsp;
          &nbsp;&nbsp;
          <script>
          	if((parseInt(document.getElementById("maxcount").value)%40)==0)
          		document.getElementById("maxpage").value=parseInt(document.getElementById("maxcount").value)/40;
          	else
          		document.getElementById("maxpage").value=parseInt(parseInt(document.getElementById("maxcount").value)/40+1);

			var page=parseInt(document.getElementById("pageNum").value);
			writemsg(<% write(lang); %>,"当前第&nbsp;"+page+"&nbsp;页");
		  </script>
		  &nbsp;
		  <script>
		    var maxcount=parseInt(document.getElementById("maxpage").value);
			writemsg(<% write(lang); %>,"总共&nbsp;"+maxcount+"&nbsp;页");
		  </script>
          </td>
      </tr>
	 
    </table></td>
  </tr> 
</table>
</td></tr>

</table>
<script>
changebgcolor();

<% if (errorcode=="3") { write("alert(putmsg(");write(lang);write(",'不具备执行该操作的权限!'));");  } %>
<% if (errorcode!="") { if (errorcode!="3") { write_errorcode(errorcode); }} %>
</script>
<input type="hidden" name="todo" value="save">
<INPUT type="hidden" name="this_file" value="port.asp">
<INPUT type="hidden" name="next_file" value="port.asp">
<input type="hidden" name="message" value="@msg_text#">
</form>  

</body>

</html>


