<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_rtl.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"MAC地址绑定");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script language="JavaScript" type="text/JavaScript">

function getPage(page)
{
   location.href="mac.asp?page="+page+"&ltime="+<% write(lltime); %>;
}

function dofirst(){
   location.href="mac.asp?page=1&ltime="+<% write(lltime); %>;
}

function addToPortRangearp(obj){
	var tdobj = obj.parentNode;
	var trobj = tdobj.parentNode;
	var arp_port = document.getElementById("arp_port");
	var arp_mac = document.getElementById("arp_mac");
	var arp_vlan = document.getElementById("arp_vlan");
	var p = obj.value;
	var i;
	if(obj.checked){
		arp_mac.value = trobj.cells[2].innerHTML;
		arp_vlan.value = trobj.cells[3].innerHTML;
		var val = trobj.cells[1].innerHTML;
		for(i=0;i<arp_port.options.length;i++){	
			if(arp_port.options[i].text==trobj.cells[1].innerHTML)
			{
				arp_port.options[i].selected=true;
			}
			else
			{
				arp_port.options[i].selected=false;
			}
		}
		 
	}

}

function p(port,mac_addr,vlan_value)
{
	
	var narr=4;
    var tbtd;
    var i;
    var tbtr = document.getElementById("arp_tbl").insertRow(-1);
	
	tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");
	tbtr.setAttribute("id", "tr_"+port);
	
	for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" onclick=\"addToPortRangearp(this)\"/>";
	tbtr.cells[1].innerHTML = port;
    tbtr.cells[2].innerHTML = mac_addr;
    tbtr.cells[3].innerHTML = vlan_value;
}

function checkData()
{
	var tf=document.macsetting;
	tf.action = "/goform/saveComm?name=mac";
	tf.submit();
}

function AddOption(port_name){
	var selectObject = document.getElementById("arp_port");
	var y=document.createElement('option');
  	y.text=port_name;
	y.value=port_name;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  catch(ex)
    {
    selectObject.add(y); // IE only
    }
}

function MacDelete()
{
	var tf=document.macsetting;
	var mac_port = document.getElementById("arp_port");
	var mac_addr = document.getElementById("arp_mac").value;
	var mac_vlan = document.getElementById("arp_vlan").value;
	var values = "";
	var j = 0;
	for(i=0;i<mac_port.options.length;i++)
	{
		if(mac_port.options[i].selected==true)
		{
			values=mac_port.options[i].text;
		}
	}
	var arp_tbl = document.getElementById("arp_tbl");
	for(i=1;i<arp_tbl.rows.length;i++)
	{
		if(arp_tbl.rows[i].cells[1].innerHTML==values && arp_tbl.rows[i].cells[2].innerHTML==mac_addr && arp_tbl.rows[i].cells[3].innerHTML==mac_vlan)
		{
			j++;
			tf.action = "/goform/MACBoundDel";
			tf.submit();
		}
	}
	if(j==0)
	{
		alert(putmsg(<% write(lang); %>,"没有你要删除的对象!"));
	}
}

function MaChange(macselect)
{
	var tf=document.macsetting;
	var arp_mac = document.getElementById("arp_mac").value;
	var arp_vlan = document.getElementById("arp_vlan").value;
	if(MacCheck(arp_mac))
	{
		if(DataScope(arp_vlan,4094,1))
		{

			tf.action = "/goform/MACBound";
			tf.submit();
		}
		else{
			alert(putmsg(<% write(lang); %>,"vlan 的范围在1-4094"));
		}
	}
	else
	{
		alert(putmsg(<% write(lang); %>,"输入MAC地址非法  格式为16进制：HHHH.HHHH.HHHH"));
	}
}

function refreshpage()
{
  location.href='mac.asp?page=1&ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('mac',<% write(lang); %>);
}

</script>
</head>

<body  >
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
	<form name="macsetting" method="POST" action="mac.asp">
    <input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
	<input type="hidden" name="left_menu_id" value="">
  
<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="90%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     <tr><td>
  		<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
       <tr>
       <td colspan="2" align="left" class="cword09">&nbsp;<script>writemsg(<% write(lang); %>,"二层转发");</script> <b><font color="#FF7F00">&gt;&gt; </font></b>&nbsp;<script>writemsg(<% write(lang); %>,"MAC绑定");</script></td>
       </tr>
       </table>
       </td></tr>
       <tr><td> 
     <table  width="100%" border="0" align="center" cellpadding="0" cellspacing="0" >
      <tr>
       <th height="30px" colspan="6" align="left" class="td2">&nbsp;<script>writemsg(<% write(lang); %>,"MAC绑定");</script>       </th>
       </tr>
	   
	   <tr>
         <td height="30px" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"网络端口");</script></td>
		 
         <td colspan="5" align="left" class="crons">&nbsp;<select id="arp_port" name="arp_port">
				 <script>
				 	<% var errorcode;AppendOptionMacbind(); %>
				</script>
			</select>
		 </td>
       </tr>
	   
       <tr>
       		<td width="15%" height="30px" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"MAC地址");</script></td>
       		<td colspan="5" align="left" class="crons">
        		&nbsp;<input type="text" name="arp_mac" id="arp_mac" >&nbsp;<script>writemsg(<% write(lang); %>,"(格式为16进制：HHHH.HHHH.HHHH)");</script>  		</td>
       </tr>
	   
       <tr>
       		<td height="30px" align="left" class="crons">&nbsp;VLAN</td>
        	<td colspan="5" align="left" class="crons">
         	&nbsp;<input type="text" name="arp_vlan" id="arp_vlan" >&nbsp;(1-4094)        	</td>
       </tr>
         
       <tr>
		 	<td colspan="6" align="center" class="crons">
		 	    <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","add_arp","MaChange(1)");</script>
<!--			<input id="change_arp" name="change_arp" type="button" class="button" value="修 改"  onClick="MaChange(2)">     -->
		 	    <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","det_arp","MacDelete()");</script>
			</td>
		</tr>
             </table>
             </td>
       </tr>
       <tr>
         <td height="8"></td>
      </tr>
	   <tr>
	   <td>
	   <table id="arp_tbl" name="arp_tbl"	 border="0" cellspacing="0" cellpadding="0" width="100%"  class="table2">
	      <tr>
		      <td width="15%"  height="25" align="center"  class="td6"></td>
			  <th width="17%"  height="25" align="center" class="td6">&nbsp;<script>writemsg(<% write(lang); %>,"端口号");</script></td>
			  <th width="17%" height="25" align="center" class="td6"> &nbsp;<script>writemsg(<% write(lang); %>,"MAC地址");</script></td>
			  <th width="17%"  height="25" align="center" class="td6">VLAN</th>
		  </tr>
		  <script>
		  		<% MACBindShow("mac"); %>
		  		
		  </script>
	   </table>
	   </td>
	   </tr>
    <tr>
	  	   <td colspan="6" align="center" height="23">
	  	   <script>writebutton(1,<% write(lang); %>,"首  页","button","button","firstpage","dofirst()");</script>
			<%MACBindShow("pagebutton"); %>
	  	   &nbsp;
	  		    <%MACBindShow("pagenum"); %>
			    <%MACBindShow("allpage"); %>
    </tr>  
    </table>
 </td></tr> 
 
</table>
</td></tr>


<tr><td>
</td></tr></table>
<input type="hidden" name="h_arp_enable" value="@h_arp_enable#">
<input type="hidden" name="todo" value="save">
<INPUT type="hidden" name="this_file" value="port.html">
<INPUT type="hidden" name="next_file" value="port.html">
<input type="hidden" name="message" value="@msg_text#">
</form>
<script>
changebgcolor();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</html>

