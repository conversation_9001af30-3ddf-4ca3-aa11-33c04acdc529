<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
  <% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
    <meta http-equiv="Content-Type" content="text/html; charset=utf8">
    <script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
    <script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
    <link rel="stylesheet" href="css/display_web.css">
    <title>&nbsp;
      <script>writemsg(<% write(lang); %>, "文件管理");</script>
    </title>
    <style type="text/css">
      .mydiv {
        background-color: #FF7F00;
        border: 1px solid #f00;
        text-align: center;
        line-height: 40px;
        font-size: 12px;
        font-weight: bold;
        z-index: 999;
        width: 300px;
        height: 120px;
        left: 50%;
        top: 30%;
        margin-left: -150px !important;
        /*FF IE7 该值为本身宽的一半 */
        margin-top: -60px !important;
        /*FF IE7 该值为本身高的一半*/
        margin-top: 0px;
        position: fixed !important;
        /* FF IE7*/
        position: absolute;
        /*IE6*/
        _top: expression(eval(document.compatMode && document.compatMode=='CSS1Compat') ? documentElement.scrollTop + (document.documentElement.clientHeight-this.offsetHeight)/2 :
            /*IE6*/
            document.body.scrollTop + (document.body.clientHeight - this.clientHeight)/2);
        /*IE5 IE5.5*/
      }

      .bg,
      .popIframe {
        background-color: #666;
        display: none;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        /*FF IE7*/
        filter: alpha(opacity=50);
        /*IE*/
        opacity: 0.5;
        /*FF*/
        z-index: 1;
        position: fixed !important;
        /*FF IE7*/
        position: absolute;
        /*IE6*/
        _top: expression(eval(document.compatMode && document.compatMode=='CSS1Compat') ? documentElement.scrollTop + (document.documentElement.clientHeight-this.offsetHeight)/2 :
            /*IE6*/
            document.body.scrollTop + (document.body.clientHeight - this.clientHeight)/2);
      }

      .popIframe {
        filter: alpha(opacity=0);
        /*IE*/
        opacity: 0;
        /*FF*/
      }

      -->
    </style>

    <% getErrorcodeInfo(); %>

      <script language="JavaScript">

        function showHelp(helpname, lang) {
          var tmp = lang + "_help.html#" + helpname;
          window.open(tmp);
        }
        function showHelpinfo() {
          showHelp('upgrade',<% write(lang); %>);
        }
        function messageCheck1() {
          var hid = document.webForm1;
          hid.action = "/goform/loadImage";
          return true;
        }

    async    function messageCheck5() {
          var hid = document.webForm6;
          var dplen;
          var dpvalue;
          var reauthn;
          var reauthpd;


          if (confirm(putmsg(<% write(lang); %>, "确认要删除所有配置，恢复出厂默认值吗？")))
          {
            hid.action = "/goform/clearconfig";

           // var name = prompt("操作认证-用户名", "");
            document.getElementById("reauthn").value = await userNamePrompt();
            /* var pwd = prompt("操作认证-密码", "");
            document.getElementById("reauthpd").value = pwd; */
            document.getElementById("reauthpd").value = await testThePrompt();
            reauthn = document.getElementById("reauthn").value;
            reauthpd = document.getElementById("reauthpd").value;

            dplen = parseInt(reauthn.length / 2);
            dpvalue = reauthn;
            reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
              + reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

            dplen = parseInt(reauthpd.length / 2);
            dpvalue = reauthpd;
            document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
              + reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
            document.getElementById("reauthn").value = "YKY5IJwmo@lqt2HQ";
            hid.submit();
          }

          return true;
        }

        function messageCheck7() {
          var hid = document.webForm7;
          hid.action = "/goform/loadRunning_stateFile";
          return true;
        }

      </SCRIPT>
</head>

<body onload=""><br>
  <% show_div("lang"); %>
    <div id="bg" class="bg" <!-- style="display:none;" -->></div>
    <iframe id='popIframe' class='popIframe' frameborder='0'></iframe>

    <% var authmode; checkCurMode(); %>
      <script>
        checktop(<% write(lang); %>);
      </script>
      <% var version,memleft; getImageVersion(); %>
        <input type="hidden" name="memleft" value=<% write(memleft); %>>
        <input type="hidden" name="ltime" value=<% write(lltime); %>>
        <input type="hidden" name="lastts" value=<% write(serverts); %>>
        <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
        <div class="formContain">
          <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
            <tr>
              <td height="30px">
                <font size="5" color="#404040">
                  <div class="bot">恢复出厂设置</div>
                </font>
              </td>
            </tr>
          </table>
          <table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" id="table1">


            <tr height="25" style='display:none'>
              <td align="left" class="crons">&nbsp;
                <script>writemsg(<% write(lang); %>, "当前版本");</script>
              </td>
              <td align="left" class="crons">&nbsp;<% write(version); %>&nbsp</td>
              <form name="webForm1" method="post" action="">
                <td align="left" class="crons">
                  <script>writebutton(<% write(authmode); %>,<% write(lang); %>, "下  载", "button", "submit", "apply1", "messageCheck1()");</script>
                  &nbsp;&nbsp;
                </td>
              </form>
            </tr>


            <tr height="25" style='display:none'>
              <td width="20%" align="left" class="crons" colspan="2">&nbsp;
                <script>writemsg(<% write(lang); %>, "下载运行状态文件");</script>
              </td>
              <form name="webForm7" method="post" action="">
                <td align="left" class="crons">
                  <input type="hidden" name="ltime" value=<% write(lltime); %>>
                  <input type="hidden" name="lastts" value=<% write(serverts); %>>
                  <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
                  <script>writebutton(<% write(authmode); %>,<% write(lang); %>, "下  载", "button", "submit", "apply4", "messageCheck7()");</script>
                </td>
              </form>
            </tr>

            <tr height="25">
              <form name="webForm6" method="post" action="">
                <td align="center" class="crons" style="padding: 5px; border-top: 1px solid #ddd;">
                  <input type="hidden" name="ltime" value=<% write(lltime); %>>
                  <input type="hidden" name="lastts" value=<% write(serverts); %>>
                  <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
                  <input type="hidden" id="reauthn" name="reauthn" value="">
                  <input type="hidden" id="reauthpd" name="reauthpd" value="">

                  <script>writebutton(<% write(authmode); %>,<% write(lang); %>, "恢  复", "button", "button", "apply5", "messageCheck5()");</script>
                </td>
              </form>

            </tr>

          </table>
        </div>


        <script>
          //获取传参
          let urlSearch = window.location.search
          const search = new URLSearchParams(urlSearch)
          const params = Object.fromEntries(search.entries())
          if (params.error) {
            if (params.error == "3") {
              alert("不具备执行该操作的权限!")
            } else {
              alert("操作失败!")
            }
          }


        </script>

        <script>
          /*	            <% var errorcode; %>
          <% if (errorcode=="1") { write("closeDiv();"); write("alert('版本升级成功,重启交换机后新版本生效!'); location.href = 'upgrade.asp';"); } %>
          <% if (errorcode=="2") { write("closeDiv();"); write("alert('升级版本失败!');");  } %>
          <% if (errorcode=="3") { write("closeDiv();"); write("alert('上传配置文件成功!');");  } %>
          <% if (errorcode=="4") { write("closeDiv();"); write("alert('上传配置文件失败!');");  } %>
          <% if (errorcode=="5") { write("closeDiv();"); write("alert('恢复出厂配置失败!');");  } %>
          */
        </script>
</body>

</html>