
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<%  var authmode; checkCurMode(); %>

<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"GMRP");</script></title>

<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript">

function writebutton2(priv,lang,key,bclass,btype,bname,benable,bfunc)
{
	var outputstr ="";
	if((priv == 1)||(priv == 5))
	  outputstr="<input class="+bclass+" id='"+bname+"' "+benable+" name='"+bname+"' type='"+btype+"' value='"+putmsg(lang,key)+"' onClick='return "+bfunc+";'>";
	else
		outputstr="&nbsp;";
	document.write(outputstr);

}

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

 
var portStaList=[<%snmp_groupSetShow();%>];

function delmac(i)
{

	var hid = document.formaa;
	
	hid.snmpgroupName.value = portStaList[5*i];
	hid.snmpsecLevel.value = portStaList[5*i+4];


	hid.action="/goform/snmp_groupSet";
	hid.submit();
	return 0;

}

function writeLines()
{
var j = 0;
for(var i=0;i<portStaList.length/5;i++)
{
document.write(" <tr  class='tables_all'>");



document.write("    <td class='inputsyslog1'>"+portStaList[j]+"</td>");

j++
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;

			document.write("    <td  class='inputsyslog1'>");

	if (<% write(authmode); %> == 1)
		document.write("      <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick='delmac("+i+")'  />");

		document.write("      </td>");




document.write("  </tr>");

}
}
 
 
 
/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true;  
			 addToPortRange(i);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRange(i);
			}
             
        }
    } 
    
}


function checkup()
{
	//var tf = document.vlan_port;
	//tf.action = "/goform/selportsecurity";
	//tf.submit();
}


function checking2()
{
 	//var port_range = document.getElementById("port_range").value;
 
  	//var checkbox_index = document.getElementsByName("checkbox_index");
	//var table_port = document.getElementById("table_port");
	var tf=document.vlan_port;
	//var i,j;


	 
			tf.submit();
 

	//if(port_txrate.length==0)port_txrate="-";
	////if(port_txburst.length==0)port_txburst="-";
	//if(port_rxrate.length==0)port_rxrate="-";	    
	//if(port_rxburst.length==0)port_rxburst="-";	
	 

}


function AddOption(portname){

	var selectObject = document.getElementById("port_range");
	var y=document.createElement('option');
  	y.text=portname;
	y.value = portname;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  	catch(ex)
    {
   	selectObject.add(y); // IE only
  	}
}

function checkname(field,err_blank,err_space,err_invalid,flag)
{
	if ((field.value.indexOf(" ") >=0)&&(flag == 1))
   {
		alert(err_space);
		field.focus();
		field.select();
		return false;
	}

	left_trim(field);
	right_trim(field);

	if ((field.value =="" | field.value == null)&&(flag == 1))
	{
		
		alert(err_blank);
		field.focus();
		field.select();
		return false;
	}


	if (checkOtherChar(field.value, err_invalid)==false)
	{
		field.focus();
		field.select();
		return false;
	}
	
/*	
	if (isChinese(field.value)==true)
	{
		field.focus();
		field.select();
		return false;
	}
*/

	return true;
}

function checkSub()
{
	if(!tdOIDCheck(document.getElementById("snmpsecLevel").value))
	{
		alert("OID 错误");
		return;
	}
	if(document.getElementById("snmpsecLevel").value.length > 128 ||document.getElementById("snmpsecLevel").value.length == 0)
	{
		alert("OID 不能为空，长度也不能超过128");
		return;
	}
	if(!checkname(document.getElementById("snmpgroupName"),"视图名为空","视图名错误","视图名错误",1))
	//if(!tdCheckASCIICode(document.getElementById("snmpgroupName").value))
	{
		//alert("group Name 错误");
		return;
	}
	
	if(document.getElementById("snmpgroupName").value.length > 32 || document.getElementById("snmpgroupName").value.length == 0)
	{
		alert("视图名不能为空，长度也不能超过32");
		return;
	}

	document.vlan_port.submit();
}

function refreshpage()
{
  location.href='snmp_group.asp?ltime='+<% write(lltime); %>;
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function showHelpinfo()
{
   showHelp('vlan_port',<% write(lang); %>);
}

</script>
</head>

<body  ><br>

<script>
checktop(<% write(lang); %>);
</script>

<form  name="vlan_port" method="post" action="/goform/snmp_groupSet">
<input name="del_flag" type="hidden" class="input_x"  value="0">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<input type="hidden" name="snmpDelgroupID" id="snmpDelgroupID"/>
<input type="hidden" name="snmpDelgroupOid" id="snmpDelgroupOid">


 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>群组</b></font></td></tr>
 </table>


<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
  <TBODY>
    <TR height=22>
      <TD width="15%" valign="top"><div align="left">&nbsp;&nbsp;<span class="td25">群组名</span></div></TD>
      <TD width="35%" ><span class="crons">
        <input type="text" name="snmpgroupName" id="snmpgroupName" size="20" maxlength="32" />
        (范围：1~32个字符) </span></TD>
      <TD colspan="2" >&nbsp;</TD>
    </TR>
    <TR height=22>
      <TD width="15%" valign="top">&nbsp;&nbsp;<span class="td25">只读视图</span></TD>
      <TD width="35%" ><span class="crons">
        <input type="text" name="snmpreadView" id="snmpreadView" size="20" maxlength="32" />
        (范围：1~32个字符) </span></TD>
      <TD width="15%" >&nbsp;&nbsp;<span class="td25">读写视图</span></TD>
      <TD width="35%" ><span class="crons">
        <input type="text" name="snmpwriteView" id="snmpwriteView" size="20" maxlength="32" />
        (范围：1~32个字符) </span></TD>
    </TR>
    <TR height=22>
      <TD width="15%" valign="top">&nbsp;&nbsp;<span class="td25">告警视图</span></TD>
      <TD width="35%" ><span class="crons">
        <input type="text" name="snmpnotifyView" id="snmpnotifyView" size="20" maxlength="32" />
        (范围：1~32个字符) </span></TD>
      <TD width="15%" >&nbsp;&nbsp;<span class="td25">安全等级</span></TD>
      <TD width="35%" ><span class="crons">
        <select name="snmpsecLevel" id="snmpsecLevel" size="1">
          <option value="auth"  >Auth</option>
          <option value="noauth"  >Noauth</option>
          <option value="priv"  >Priv</option>
        </select>
      </span></TD>
    </TR>
    
    <tr height="25">
      <td  colspan="4" align="center" class="crons">&nbsp;
   	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"提  交","button","button","modify","checking2()");</script>
      </td>
  </tr> 
</TABLE>


<br>
 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#ABD8EF" height="20px" ><font size="2" color="#FFFFFF"><b>SNMP群组表</b></font></td></tr>
 </table>


<table width="98%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table_port_vlan"  >
    <TR align="center" height=22>
      <TD   nowrap class="all_tables_list"><span class="partition">群组名</span></TD>
      <TD   nowrap class="all_tables_list"><span class="partition">只读视图</span></TD>
      <TD   nowrap class="all_tables_list"><span class="td25">读写视图</span></TD>
      <TD   nowrap class="all_tables_list"><span class="td25">告警视图</span></TD>
      <TD   nowrap class="all_tables_list"><span class="td25">安全等级</span></TD>
      <TD   nowrap class="all_tables_list"><span class="partition">删除</span></TD>
    </TR>
  <script language="javascript">
  writeLines();
  </script>
  </table>
</form>
<form name="formaa" method="POST" action="">
<input name="snmpsecLevel" type="hidden" class="input_x"  >
<input name="snmpgroupType" type="hidden" class="input_x"   >
<input name="snmpgroupName" type="hidden" class="input_x"  >
<input name="del_flag" type="hidden" class="input_x"  value="1">
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

</form> 
<script>
changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
 
</body>
</html>
