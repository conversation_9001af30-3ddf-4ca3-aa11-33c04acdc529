<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"AlarmPortrate");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<% var errorcode, alarmportratecfg; getAlarmPortrate(); %>
 
<script language="JavaScript" type="text/JavaScript">

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor_port(){
 var tab = document.all.table_port;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function alarmportrateJudgment()
{
	if(document.getElementById("pm1").checked == true)
		document.forms[0].para1.value = "enable";
	else
		document.forms[0].para1.value = "disable";

	document.forms[0].submit();
	return true;   
}


function refreshpage()
{
  location.href='alarm_portrate.asp?ltime='+<% write(lltime); %>;
}

function checking2()
{
	var port_range = document.getElementById("port_range").value;
	var rate_threshold = document.getElementById("rate_threshold");
 	var hid=document.webForm;
	var i,j;

	
	if(rate_threshold < 0 || rate_threshold > 100)
	{
        alert(putmsg(<% write(lang); %>,"Valid percentage(%) 0-100!"));
		return;
	}

    hid.action="/goform/setAlarmPortrateThreshold";
	hid.submit();
}


/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true;  
			 addToPortRange(objs[i]);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRange(objs[i]);
			}
             
        }
    } 
    
}


function addToPortRange(obj)
{
	var trid="tr_"+obj.value;
	var nodeArray;
	var trobj = document.getElementById(trid);
	var target = document.getElementById("port_range");
	var rate_threshold = document.getElementById("rate_threshold");

	var p = obj.value;
	var i;

	if (obj.checked)
	{
		target.value = target.value  + p + " ";

		for (i=0; i<rate_threshold.options.length; i++)
		{
			if (rate_threshold.options[i].text==trobj.cells[2].innerHTML)
			{
				rate_threshold.options[i].selected=true;
				break;
			}
		}			
	}
	else
	{

		target.value = target.value.replace(p+" ","");
	}

}

/*display function*/
function P(portId, threshold)
{

    var narr=3;
	var arr="";
	var speed="";
	var Desc="";
    var tbtd;
    var i;
    var opt;
    var tbtr = document.getElementById("table_port").insertRow(-1);

	
    tbtr.classname = "td7";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "td7");
    tbtr.setAttribute("className", "td7");
	tbtr.setAttribute("id", "tr_"+portId);

	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	

        tbtr.appendChild(tbtd);
    }

	/*display*/
	tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"checkbox_index\" value=\""+portId+"\" onchange=\"addToPortRange(this)\"/>";
	tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = threshold;

}

function display()
{
	tmp = "<% write(alarmportratecfg); %>";

	if(tmp == "enable")
	  document.getElementById("pm1").checked = true;
	else
		document.getElementById("pm2").checked = true;

}


</script>
</head>
<body  onload="display();"><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setAlarmPortrate">

<input type="hidden" name="para1">


<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>端口流量越限</b></font></td></tr>
 </table>


<table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
<tr>
	<td>
		<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">
		<tr height="25">
			<td width="15%" align="left" class="crons">&nbsp;端口流量越限 &nbsp:</td>
			<td width="25%" colspan="2"align="left" class="crons">&nbsp;
				<input type="radio" name="portrate_mode" value="enable" checked  id="pm1">Enable
				<input type="radio" name="portrate_mode" value="disable"  id="pm2">Disable
			</td>
		</tr>
		
		<tr height="25">
			<td colspan="3" align="center" class="crons">
				<div align="center">
				<script>writebutton(<% write(authmode); %>,"ch","修  改","button","button","","alarmportrateJudgment()");</script>
					&nbsp;&nbsp;&nbsp;
				</div>
			</td>
		</tr>
		</table>		
	</td>
</tr>



<tr>
<td>
	<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table2" class="tablebord">
		<tr height="30">
			<td align="left" class="td7">&nbsp;<script>writemsg(<% write(lang); %>,"端口选择");</script></td>
			<td width="81%" align="left" class="td7">&nbsp;
				<input name="port_range" type="text" class="input_x" id="port_range" readonly="true"/>
			</td>
		</tr>		

		<tr height="25"  >
			<td align="left" class="crons">&nbsp;流量阈值:</td>
			<td colspan="1" align="left" class="crons">&nbsp;
				<input type="text" id="rate_threshold" name="rate_threshold" class="input_board3">&nbsp;(%, 0-100)
			</td>
		</tr>

		<tr>
			<td colspan="2" align="center" class="td7">
				<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify","checking2()");</script>
			</td>
		</tr>
	</table>
	
</td>
</tr>

<tr>
	<td height="8"></td>
</tr>

<tr>
<td>
	<table width="100%" height="35"  border="0" cellpadding="0" cellspacing="0"  class="tablebord" id="table_port">
		<tr height="30" align="center" class="td7">
			<th align="center" class="td2" width="20%" ></th>
			<th align="center" class="td2" width="40%" ><script>writemsg(<% write(lang); %>,"端口");</script></th>
			<th align="center" class="td2" width="40%" ><script>writemsg(<% write(lang); %>,"流量阈值(%)");</script></th>
		</tr>
        <script>
        	<% var errorcode;  alarmPortrateShow();%>
        </script>
	</table>
</td>
</tr>

<tr>
<td align="center" height="35">
	<table> 
		<tr>
			<script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
			&nbsp;
			&nbsp;
		</tr>
	</table>
</td>
</tr> 
</table>

</form>

<script>
changebgcolor();
changebgcolor2();
changebgcolor_port();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
<br>
<br>

</body>
</html>

