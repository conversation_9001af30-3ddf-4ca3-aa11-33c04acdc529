<HTML>

<HEAD>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<% var authmode; checkCurMode(); %>

			<TITLE>page</TITLE>
			<META http-equiv=Content-Type content="text/html; charset=utf8">
			<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
			<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
			<link href="css/display_web.css" rel="stylesheet" type="text/css" />

			<script>

				function changebgcolor() {
					var tab = document.all.table1;
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								tab.rows[i].cells[j].className = "all_tables";
							}
							else {
								tab.rows[i].cells[j].className = "all_tables";
							}

						}

					}
				}


				function changebgcolor2() {
					var tab = document.all.table2;
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								tab.rows[i].cells[j].className = "all_tables all_tables2";
							}
							else {
								tab.rows[i].cells[j].className = "all_tables all_tables1";
							}

						}

					}
				}



				async function messageCheck() {
					var dplen;
					var dpvalue;
					var reauthn;
					var reauthpd;
					var hid = document.webForm;


					if (confirm(putmsg(<% write(lang); %>, "确定要重启吗?")))
					{
						//var name = prompt("操作认证-用户名", "");
						document.getElementById("reauthn").value = await userNamePrompt();

						document.getElementById("reauthpd").value = await testThePrompt();

						reauthn = document.getElementById("reauthn").value;
						reauthpd = document.getElementById("reauthpd").value;

						dplen = parseInt(reauthn.length / 2);
						dpvalue = reauthn;
						reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

						dplen = parseInt(reauthpd.length / 2);
						dpvalue = reauthpd;
						document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
						document.getElementById("reauthn").value = "wn!vTU1EQAs8IUqb";


						hid.submit();
						return true;
					}
					return false;
				}
			
				async function messageCheck2() {
					var hid = document.webForm;
					var dplen;
					var dpvalue;
					var reauthn;
					var reauthpd;

					if (<% write(authmode); %> == 1)
					{
						if (confirm(putmsg(<% write(lang); %>, "确认要保存所有配置吗？")))
						{
							// var name = prompt("操作认证-用户名", "");
							 document.getElementById("reauthn").value = await userNamePrompt()
							
							document.getElementById("reauthpd").value = await testThePrompt();
							reauthn = document.getElementById("reauthn").value;
							reauthpd = document.getElementById("reauthpd").value;

							dplen = parseInt(reauthn.length / 2);
							dpvalue = reauthn;
							reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
								+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

							dplen = parseInt(reauthpd.length / 2);
							dpvalue = reauthpd;
							document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
								+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
							document.getElementById("reauthn").value = "dr4tpCZQmp@s6ph6";

							hid.action = "/goform/saveconfig3";
							hid.submit();
							return true;
						}
					}
	else
					{
						alert('不具备执行该操作的权限!');
						return false;
					}
				}


				function showHelpinfo() {
					showHelp('reset',<% write(lang); %>);
				}
			</script>
</HEAD>

<BODY>
	<br>
	<% web_get_stat(); %>
		<script>
			checktop(<% write(lang); %>);
		</script>

		<div id="view_help" style="display:none">

			<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader">
				<tr>
					<td class="Tablelist">
						<nobr>帮助</nobr>
					</td>
					<td class="tablenew"></td>
				</tr>
			</table>
			<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 class="tablebord" bgcolor="efefef">

				<SCRIPT language=javascript>
					//document.writeln(top.leftframe.helptext('ipaddress'));
				</SCRIPT>

			</TABLE>
		</div>
		<% var errorcode;resetError(); %>
			<form name="webForm" method="get" action="/goform/fcReset">
				<input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
				<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
				<input type="hidden" id="reauthn" name="reauthn" value="">
				<input type="hidden" id="reauthpd" name="reauthpd" value="">

				<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td class="tit" height="30px">
							<font color="#0069d6">
								<div>保存重启</div>
							</font>
						</td>
					</tr>
				</table>

				<br />
				<div class="formContain">
					<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="20px">
								<font size="5" color="#404040">
									<div class="bot">保存当前配置</div>
								</font>
							</td>
						</tr>
					</table>

					<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
						<TBODY>
							<TR height=22>
								<TD colspan="2" valign="top">
									<div align="center">
										<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "保  存", "buttons_apply", "button", "aaa", "messageCheck2()");</script>
									</div>
								</TD>
							</TR>
					</TABLE>


					<br />

					<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="20px">
								<font size="5" color="#404040">
									<div class="bot">重启交换机</div>
								</font>
							</td>
						</tr>
					</table>

					<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table2" class="tablebord">
						<TBODY>

							<TR height=22>
								<TD width="42%" height="24" valign="top">&nbsp;&nbsp;
									<script>writemsg(<% write(lang); %>, "是否保存当前配置:");</script>
								</TD>
								<td><span class="crons">
										<input type="checkbox" id=checkcfg name="checkcfg" checked></span> (注意:
									恢复出厂设置需取消勾选，再重启机器)
								</td>
							</TR>
							<tr height=22>
								<td colspan="2" class="tablenew" id=tabs name=tabs>
									<div align="center">
										<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "重  启", "buttons_apply", "button", "button", "messageCheck()");</script>
									</div>
								</td>

							</tr>
					</table>
				</div>
			</FORM>
			<script>
				changebgcolor();
				changebgcolor2();

<% if (errorcode == "1") { write("alert(putmsg("); write(lang); write(",'保存配置失败!'));"); } %>
<% if (errorcode == "2") { write("alert(putmsg("); write(lang); write(",'保存配置成功!'));"); } %>
<% if (errorcode == "3") { write("alert(putmsg("); write(lang); write(",'不具备执行该操作的权限!'));"); } %>
<% if (errorcode != "") { if (errorcode != "1" && errorcode != "2" && errorcode != "3") { write_errorcode(errorcode); } } %>

			</script>

</BODY>

</HTML>