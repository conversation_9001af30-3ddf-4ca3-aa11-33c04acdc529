<!DOCTYPE html>
<html>

<head>
  <% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
    <meta http-equiv="Content-Type" content="text/html; charset=utf8" />
    <script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
    <script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
    <script src="js/alpinejs.min.js" defer></script>
    <link href="css/display_web.css" rel="stylesheet" type="text/css" />
    <title>
      &nbsp;
      <script>writemsg(<% write(lang); %>, "802.1Q VLAN设置");</script>
    </title>
    <script language="JavaScript">
      var boardType = <% getSysCfg(); %>;
    </script>

    <script>
      <% var responseJsonStr; jw_get_vlanConfig("{\"pageNum\":0}"); %>
        vlanConfig = <% write(responseJsonStr); %>;
      <% var responseVlanAttr; jw_get_vlanAttrConfig(); %>
        vlanAttrConfig = <% write(responseVlanAttr); %>;

      const pagePath = 'vlan_fwd.asp?ltime=' +<% write(lltime); %>;
      function addVlan() {
        if (!checkVlan(vid.value)) {
          return
        }
        const param = {
          operate: "create",
          vlanId: vid.value,
          pagePath: pagePath + '&tab=tab1'
        }
        const form = vlan_fwd_setting
        form.param1.value = JSON.stringify(param)
        form.action = "/goform/jw_set_vlanConfig";
        form.submit();
      }
      function removeVlan() {
        if (!checkVlan(vid.value)) {
          return
        }
        const param = {
          operate: "del",
          vlanId: vid.value,
          pagePath: pagePath + '&tab=tab1'
        }
        const form = vlan_fwd_setting
        form.param1.value = JSON.stringify(param)
        form.action = "/goform/jw_set_vlanConfig";
        form.submit();
      }
      const porttypeArr = ["Access", "Trunk", "Hybrid"]
      function selectPort(index) {
        let data = vlanAttrConfig.PortAttrInfo[index]
  
        const form = document.vlan_port
        if(data.modeName === 'trunk' || data.modeName === 'Hybrid') {
          form.avid.value = data.pVid
        }else{
          form.avid.value = ''
        }
        
        form.portName.value = data.portName
        form.modeName.value = data.modeName
        // form.avid.disabled = data.modeName === 'access'
        form.pvid.value = data.pVid
        return data.modeName
      }
      function addVlanAttr() {
        const form = document.vlan_port
        if (!form.portName.value) {
          alert('请输入端口名称或选择端口')
          return
        }
        const parma = {
          pagePath: pagePath + '&tab=tab2',
          portName: form.portName.value,
          modeName: form.modeName.value,
          allowVlanOpe: 'add',
          "Egress-tagged": Number(form.tagged.checked)
        }
        if (form.pvid.value) {
          if (!checkVlan(form.pvid.value)) {
            return
          }
          parma.pVid = form.pvid.value
        }
        if (form.avid.value) {
          if (!checkVlan(form.avid.value)) {
            return
          }
          parma.aVid = form.avid.value
        }
        form.param1.value = JSON.stringify(parma)
        form.action = "/goform/jw_set_vlanAttrConfig"
        form.submit()
      }
      function delVlanAttr() {
        const form = document.vlan_port
        if (!form.portName.value) {
          alert('请输入端口名称或选择端口')
          return
        }
        const parma = {
          pagePath: pagePath + '&tab=tab2',
          portName: form.portName.value,
          modeName: form.modeName.value,
          allowVlanOpe: 'remove',
          "Egress-tagged": Number(form.tagged.checked)
        }
        if (form.pvid.value) {
          if (!checkVlan(form.pvid.value)) {
            return
          }
          parma.pVid = form.pvid.value
        }
        if (form.avid.value) {
          if (!checkVlan(form.avid.value)) {
            return
          }
          parma.aVid = form.avid.value
        }
        form.param1.value = JSON.stringify(parma)
        form.action = "/goform/jw_set_vlanAttrConfig"
        form.submit()
      }
      function getUrlParamTab() {
        let urlSearch = window.location.search
        const search = new URLSearchParams(urlSearch)
        const params = Object.fromEntries(search.entries())
        try {
          if (params.tab) return params.tab
        } catch { }
        return 'tab1'
      }
      function checkVlan(str, min = 1, max = 4094) {
        //vlan必须大于1小于4094
        const vlanArr = getVlan(str)
        try {
          vlanArr.forEach(e => {
            const vlan = Number(e)
            if (vlan < min || vlan > max) {
              throw new Error(`输入的vlan必须大于等于${min}，小于等于${max}`)
            }
          })
        } catch (err) {
          alert(err)
          return false
        }
        return true
      }
      function getVlan(str) {
        //用于处理逗号和横杠同时存在的情况,比如:"1,2,3,4-8,9"
        if (!str) return []
        let arr1 = str.split(',')
        let arr2 = arr1.map(el => el.split('-'))
        return arr2.flat()
      }
    </script>
</head>

<body x-data="{active:'tab1'}" x-init="active=getUrlParamTab()">
  <div>
    <ul class="tabmenu">
      <li id="tab1" :class="active==='tab1'?'tab':'tab-disable'">
        <a herf="#" x-on:click="active='tab1'">vlan 创建</a>
      </li>
      <li id="tab2" :class="active==='tab2'?'tab':'tab-disable'">
        <a herf="#" x-on:click="active='tab2'">vlan 设置</a>
      </li>
    </ul>
  </div>
  <form x-show="active==='tab1'" name="vlan_fwd_setting" method="POST" action="/goform/jw_set_vlanConfig"
    style="min-height: 655px;">
    <input type="hidden" name="param1" id="param1" />
    <input type="hidden" name="ltime" value=<% write(lltime); %>>
    <input type="hidden" name="lastts" id="lastts" value=<% write(serverts); %>>
    <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

    <div class="formContain" style="min-height: 240px;">
      <table x-ref="table1" id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0"
        cellspacing="0">
        <tr>
          <td>
            <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
              <tr>
                <td valign="top">
                  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
                    <tr>
                      <td>
                        <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
                          <tr>
                            <td height="30px">
                              <font size="5" color="#404040">
                                <div class="bot">vlan 创建</div>
                              </font>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <table border="0" cellspacing="0" cellpadding="0" width="100%" id="table1" class="tablebord1"
                          x-data="{ data: {vlanInfo:[]} }" x-init="()=>{data=vlanConfig}">
                          <tr height="30">
                            <td width="42%" class="all_tables all_tables1">
                              &nbsp;VLAN ID
                            </td>
                            <td width="58%" class="all_tables all_tables2">
                              &nbsp;
                              <input type="text" name="vid" id="vid" class="input_board3" maxlength="12"
                                autocomplete="off" />
                              &nbsp;(2-4094)
                            </td>
                          </tr>
                          <!-- <tr height="30">
                          <td width="42%" class="all_tables all_tables1">
                            &nbsp;
                            名称
                          </td>
                          <td width="58%" class="all_tables all_tables2">
                            &nbsp;
                            <input type="text" name="vlan_name" id="vlan_name" class="input_board3" maxlength="16"
                              autocomplete="off" />
                          </td>
                        </tr> -->
                          <tr height="30">
                            <td colspan="2" valign="top" class="all_tables all_tables1">
                              <div align="center" class="btn">
                                <input class="button" id="formadd" name="formadd" type="button" value="添  加"
                                  @click="addVlan" />
                                <input class="button" id="formdel" name="formdel" type="button" value="删  除"
                                  @click="removeVlan" />
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td colspan="2" height="30">
                              &nbsp; "备注：创建或删除多个连续的Vlan时，Vlan
                              ID可用\“-\”连接，如：4-6。"
                            </td>
                            <td colspan="2" height="8"></td>
                          </tr>
                          <tr>
                            <td colspan="2">

                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </div>
    <div class="formContain" style="margin-top: 15px;">
      <div class="">
        <font size="5" color="#404040">
          <div class="bot">vlan列表</div>
        </font>
      </div>
      <table id="table_vlan_fwd" border="0" cellspacing="0" cellpadding="0" width="100%" class="tablebord"
        bgColor="#fff" x-data="{ data: {vlanInfo:[]} }" x-init="()=>{data=vlanConfig}">
        <tr align="center" height="30" class="crons">
          <th class="td2" width="5%">
            <font color="#333333">序号</font>
          </th>
          <th class="td2" width="10%">
            <font color="#333333">vlan id</font>
          </th>
          <th class="td2" width="15%">
            <font color="#333333">vlan 名称</font>
          </th>
          <th class="td2" width="40%">
            <font color="#333333">端口列表</font>
          </th>
        </tr>
        <template x-for="(vlan,index) in data.vlanInfo" :key="vlan.vlanId">
          <tr height="30" class="crons" classname="crons">
            <td align="center" class="all_tables" classname="td2">
              <span x-text="index+1"></span>
            </td>
            <td align="center" class="all_tables" classname="td2">
              <span x-text="vlan.vlanId"></span>
            </td>
            <td align="center" class="all_tables" classname="td2">
              <span x-text="vlan.vlanName"></span>
            </td>
            <td align="center" class="all_tables" classname="td2">
              <span x-text="vlan.portList"></span>
            </td>
          </tr>
        </template>
      </table>
    </div>
  </form>
  <form x-show="active==='tab2'" name="vlan_port" method="POST" action="/goform/jw_set_vlanAttrConfig"
    x-data="{modeName:'access'}">
    <input type="hidden" name="param1" id="param1" />
    <input type="hidden" name="ltime" value=<% write(lltime); %>>
    <input type="hidden" name="lastts" id="lastts" value=<% write(serverts); %>>
    <input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

    <div class="formContain">
      <table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
        <tr>
          <td>
            <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
              <tr>
                <td valign="top">
                  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
                    <tr>
                      <td>
                        <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
                          <tr>
                            <td height="30px">
                              <font size="5" color="#404040">
                                <div class="bot">端口配置</div>
                              </font>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <table x-ref="table2" border="0" cellspacing="0" cellpadding="0" width="100%" id="table1"
                          class="tablebord1">

                          <tr>
                            <td colspan="4">

                            </td>
                          </tr>
                          <tr height="30">
                            <td width="42%" class="all_tables all_tables1">
                              端口
                            </td>
                            <td width="58%" class="all_tables all_tables2">
                              <input name="portName" type="text" class="input_x" id="port_range" readonly="true" />
                            </td>
                          </tr>
                          <tr height="30">
                            <td width="42%" class="all_tables all_tables1">
                              模式
                            </td>
                            <td width="58%" class="all_tables all_tables2">
                              <select name="modeName" id="modeName" class="select1" @change="modeName=$el.value">
                                <template x-for="(port,index) in porttypeArr" :key="index">
                                  <option :value="port.toLowerCase()" x-text="port"></option>
                                </template>
                              </select>
                            </td>
                          </tr>
                          <tr height="30">
                            <td width="42%" class="all_tables all_tables1">
                              Allowed VLAN ID
                            </td>
                            <td width="58%" class="all_tables all_tables2">
                              <input type="text" name="avid" id="avid" class="input_board3" maxlength="25"
                                autocomplete="off" :disabled="modeName==='access'" />
                              (1-4094)
                              <span x-show="modeName==='hybrid'" id="taggedId">
                                <input name="tagged" id="tagged" type="checkbox" name="checkbox" id="c1" />
                                Egress-tagged
                              </span>
                            </td>
                          </tr>
                          <tr height="30">
                            <td width="42%" class="all_tables all_tables1">
                              缺省VLAN ID
                            </td>
                            <td width="58%" class="all_tables all_tables2">
                              <input type="text" name="pvid" id="pvid" class="input_board3" maxlength="4"
                                autocomplete="off" />
                              (1-4094)
                            </td>
                          </tr>
                          <tr height="30">
                            <td align="middle" colspan="3" class="all_tables all_tables1">
                              <div align="center" class="btn">
                                <input class="button" id="form2add" name="form2add" type="button" value="添  加"
                                  @click="addVlanAttr" />
                                <input class="button" id="form2del" name="form2del" type="button" value="删  除"
                                  @click="delVlanAttr" />
                              </div>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </div>
    <div class="formContain" style="margin-top: 15px;">
      <div class="">
        <font size="5" color="#404040">
          <div class="bot">端口显示</div>
        </font>
      </div>
      <table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%" class="tablebord"
        x-data="{ data: {PortAttrInfo:[]} }" x-init="()=>{data=vlanAttrConfig}">
        <tr align="center" height="30" class="crons">
          <th class="td2" width="10%"></th>
          <th class="td2" width="10%">
            <font color="#333333"><b>端口</b></font>
          </th>
          <th class="td2" width="15%">
            <font color="#333333"><b>模式</b></font>
          </th>

          <th class="td2" width="15%">
            <font color="#333333"><b>缺省Vid</b></font>
          </th>
          <th class="td2" width="50%">
            <font color="#333333"><b>Vlan ID</b></font>
          </th>
        </tr>
        <template x-for="(port,index) in data.PortAttrInfo" :key="port.portName">
          <tr height="30" class="crons" classname="crons">
            <td align="center" class="all_tables" classname="td2">
              <input type="radio" name="checkbox_index" :value="port.portName" @change="modeName = selectPort(index)" />
            </td>
            <td align="center" class="all_tables" classname="td2">
              <span x-text="port.portName"></span>
            </td>
            <td align="center" class="all_tables" classname="td2">
              <span x-text="port.modeName"></span>
            </td>
            <td align="center" class="all_tables" classname="td2">
              <span x-text="port.pVid"></span>
            </td>
            <td align="center" class="all_tables" classname="td2">
              <span x-text="port.vlanList"></span>
            </td>
          </tr>
        </template>
      </table>
    </div>
  </form>
</body>
<script>
  function getUrlParamErr() {
    let urlSearch = window.location.search
    const search = new URLSearchParams(urlSearch)
    const params = Object.fromEntries(search.entries())
    if(params.error) {
      alert(params.error)
    }
 
  }
  getUrlParamErr()
</script>

</html>