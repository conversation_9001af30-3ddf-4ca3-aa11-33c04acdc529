<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<% var errorcode, lldptraffic; getLldpTraffic(); %>
			<meta http-equiv="Content-Type" content="text/html; charset=utf8">
			<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
			<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
			<script
			language="javascript"
			type="text/javascript"
			src="js/alpinejs.min.js"
		  ></script>
			<link href="css/display_web.css" rel="stylesheet" type="text/css" />
			<title>&nbsp;
				<script>writemsg(<% write(lang); %>, "lldp管理");</script>
			</title>
			<script language="javascript">
				const pagePath = 'lldpconfig.asp?ltime=' +<% write(lltime); %>;
				function changebgcolor() {
					var tab = document.all.table1;
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables ";
							}
							else {
								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables all_table3-right";
							}

						}

					}
				}
				function checkvalue() {
					var hid = document.webForm;

					var lldptimer = document.webForm.lldptimer.vlaue;
					var lldptxhold = document.webForm.lldptxhold.vlaue;
					var lldpreint = document.webForm.lldpreint.vlaue;
					var lldptxdelay = document.webForm.lldptxdelay.vlaue;


					if ((lldptimer < 5) || (lldptimer > 300)) {
						alert(putmsg(<% write(lang); %>, "Invalid LLDP Timer value!"));
						return 0;
					}

					if ((lldptxhold < 1) || (lldptxhold > 10)) {
						alert(putmsg(<% write(lang); %>, "Invalid Tx Hold value!"));
						return 0;
					}

					if ((lldpreint < 1) || (lldpreint > 10)) {
						alert(putmsg(<% write(lang); %>, "Invalid Reint Delay value!"));
						return 0;
					}

					if ((lldptxdelay < 1) || (lldptxdelay > 3600)) {
						alert(putmsg(<% write(lang); %>, "Invalid Tx Delay value!"));
						return 0;
					}



					hid.submit();
					return true;
				}

				var lldpInfoList = [<% lldpInfoShow();%>];

				//var lldpInfoList = ["Port 1","00-E0-0F-C4-7E-AB","1","Port 1PSW618","aaaaaaaaaaaaaaaaaa"," Image ********  Oct 19 2011 15:35:46","Bridge(+)","************* (IPv4)"];
				function writeLines() {
					var j = 0;
					for (var i = 0; i < lldpInfoList.length / 8; i++) {
						document.write("  <tr  class='tables_all'>");
						document.write("    <td class='inputsyslog1'>" + lldpInfoList[j] + "</td>");
						j++;
						document.write("    <td class='inputsyslog1'>" + lldpInfoList[j] + "</td>");
						j++;
						document.write("    <td class='inputsyslog1'>" + lldpInfoList[j] + "</td>");
						j++;
						document.write("    <td class='inputsyslog1'>" + lldpInfoList[j] + "</td>");
						j++;
						document.write("    <td class='inputsyslog1'>" + lldpInfoList[j] + "</td>");
						j++;
						document.write("    <td class='inputsyslog1'>" + lldpInfoList[j] + "</td>");
						j++;
						document.write("    <td class='inputsyslog1'>" + lldpInfoList[j] + "</td>");
						j++;
						//document.write("    <td >"+lldpInfoList[j]+"</td>");
						//document.write("    <td class='inputsyslog1'><a  target='_blank'  href='http://"+lldpInfoList[j].split("(",1)+"'>"+lldpInfoList[j]+"</a></td>");
						document.write("    <td class='inputsyslog1'>" + lldpInfoList[j] + "</td>");
						j++;

						document.write("  </tr>");
					}
				}


				function refreshpage() {
					location.href = 'lldpconfig.asp?ltime=' +<% write(lltime); %>;
				}

				function changebgcolor_port() {
					var tab = document.all.table_port;
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables all_tables3";
							}
							else {
								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables all_tables3";
							}

						}

					}
				}


				function changebgcolor_name(value) {

					var tab = document.getElementById(value);
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								//    tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables";
							}
							else {
								//    tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables";
							}

						}

					}
				}

				function changebgcolor11() {
					var tab = document.all.table11;
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								//    tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables";
							}
							else {
								//    tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables all_tables3";
							}

						}

					}
				}
			

				function writeLinesPort() {
					
					var data=portStaList.PortSafe
					var j = 0;
					for (var i = 0; i < data.length ; i++)
						if (i % 2 == 0) {
							{
								document.write(" <tr  class='tables_all'>");
								document.write("    <td class='inputsyslog1'><input type='checkbox' name='checkbox_index' onchange=addToPortRange('" + i + "') ></td>");
								document.write("    <td class='inputsyslog1'>" + data[i]['portName'] + "</td>");
								j++
								document.write("    <td  class='inputsyslog1'>" +"<select  value="+data[i]['sofeMode']+" onchange=changeSelect("+data[i]+")><option value='tx' >tx</option><option value='rx' >rx</option> <option value='txrx' >txrx</option><option value='disable' >disable</option></select> </td>");
								j++;
								document.write("  </tr>");
							}
						} else {
							{
								document.write(" <tr  class='tables_allLLDP'>");
								document.write("    <td class='inputsyslog1'><input type='checkbox' name='checkbox_index' onchange=addToPortRange('" + i + "') ></td>");
								document.write("    <td class='inputsyslog1'>" + data[i]['portName'] + "</td>");
								j++
								document.write("    <td  class='inputsyslog1'>" + data[i]['sofeMode'] + "</td>");
								j++;
								document.write("  </tr>");
							}

						}
				}

				/*select ALL*/
				function selectToAll() {
					var cf = document.forms[2];
					var objs = document.getElementsByName("checkbox_index");
					
					var i;
					if (cf.check_all.checked == true) {
						for (i = 0; i < objs.length; i++) {
							if (objs[i].disabled == false && objs[i].checked == false) {
								objs[i].checked = true;
								addToPortRange(i);
							}
						}
					}
					else {
						for (i = 0; i < objs.length; i++) {
							if (objs[i].checked == true) {
								objs[i].checked = false;
								addToPortRange(i);
							}
						}
					}
				}
				function changeSelect(data){
					
					let obj={
						pageName:'lldpconfig.asp',
						pagePath: pagePath + '&tab=tab2',
						lldpPort:[{
							portName:data.portName,
						portStatus:data.portStatus,
						}

						]
					}
					var tf = document.vlan_port;
					
					tf.param1.value = JSON.stringify(obj)
					console.log(JSON.stringify(obj))
					tf.action = "/goform/jw_set_LLDP_workModeConfig"
					tf.submit();
				}
				function addToPortRange(index) {
					var target = document.getElementById("port_range");
					var objs = document.getElementsByName("checkbox_index");
					
					if (objs[index].checked) {
						target.value = target.value + portStaList['PortSafe'][index]['portName'] + " ";
					} else {
						target.value = target.value.replace(portStaList['PortSafe'][index]['portName'] + " ", "");
					}
				}

				function changebgcolor21() {
					var tab = document.all.table21;
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables all_tables3";
							}
							else {
								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables all_tables3";
							}

						}
						// if(i%2==0){
						// 	tab.rows[i].bgColor='#fff'
						// }else{
						// 	tab.rows[i].bgColor='#f9f9f9'
						// }


					}
				}

				var lldpStaList = [<% write(lldptraffic); %>];

				function writeLinesStat() {
					var j = 0;
					for (var i = 0; i < lldpStaList.length / 8; i++)
						if (i % 2 == 0) {
							{
								document.write(" <tr  class='tables_all'>");
								document.write("    <td class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("  </tr>");
							}
						} else {
							{
								document.write(" <tr  class='tables_allLLDP'>");
								document.write("    <td class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("    <td  class='inputsyslog1'>" + lldpStaList[j] + "</td>");
								j++;
								document.write("  </tr>");
							}
						}

				}
				function getUrlParamTab() {
        let urlSearch = window.location.search
        const search = new URLSearchParams(urlSearch)
        const params = Object.fromEntries(search.entries())
        try {
          if (params.tab) return params.tab
        } catch { }
        return 'tab1'
      }
			</script>
</head>

<body onload="" x-data="{active:'tab1'}" x-init="active=getUrlParamTab()"><br>
	<% web_get_stat(); %>
		<% var authmode; checkCurMode(); %>
			<script>
				checktop(<% write(lang); %>);
					retValue = <% var responseJsonStr; jw_get_LLDP_workModeConfig(); %>
				  portStaList = <% write(responseJsonStr); %>;
				  console.log(portStaList,0000000)
				// var portStaList = [<% LldpPortShow();%>];
				const appData = {
      
					lldpPort: [
{
	portName: '',
	portStatus: '',
	
},
],

};
appData.lldpPort=[...portStaList.lldpPort]
  // 初始化 Alpine.js
  Alpine.data('myApp', () => ({
      	...appData,
      
      }));
			</script>
			  <div>
				<ul class="tabmenu">
				  <li id="tab1" :class="active==='tab1'?'tab':'tab-disable'">
					<a herf="#" x-on:click="active='tab1'">LLDP 设置</a>
				  </li>
				  <li  id="tab2" :class="active==='tab2'?'tab':'tab-disable'">
					<a herf="#" x-on:click="active='tab2'">LLDP工作模式</a>
				  </li>
				  <li  id="tab3" :class="active==='tab3'?'tab':'tab-disable'">
					<a herf="#" x-on:click="active='tab3'">LLDP统计</a>
				  </li>
				</ul>
			  </div>
			<form x-show="active==='tab1'" name="webForm" method="post" action="/goform/setLldpConfig" class="formContain">
				<input type="hidden" name="flag">
				<input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
				<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
				<% var errorcode, lldpstatus, l2pro, lldptimer, lldptxhold, lldpreint, lldptxdelay; getLldpCfg(); %>
					<table id="table111" width="98%" height="100%" border="0" align="center" cellpadding="0"
						cellspacing="0">
						<tr>
							<td>
								<table width="100%"  border="0" align="left" cellpadding="0"
									cellspacing="0">
									<tr>
										<td valign="top">
											<table width="100%" border="0" align="center" cellpadding="0"
												cellspacing="0" class="cword09">
												<tr>
													<td>

														<table width="100%" align="center" border="0" cellspacing="0"
															cellpadding="0">
															<tr>
																<td height="30px">
																	<font size="5" color="#404040">
																		<div class="bot">LLDP设置</div>
																	</font>
																</td>
															</tr>
														</table>

													</td>
												</tr>

												<tr>

													<td>
														<table width="100%" border="0" align="center" cellpadding="0"
															cellspacing="0" id="table1" class="tablebord">
															<!--
            <tr height="25">
              <td width="15%" align="left" class="crons">&nbsp;
                <script>writemsg(<% write(lang); %>,"L2 Protocol");</script>
              </td>
              	<td width="74%" align="left" class="crons">
					<input type="radio" id="lp1" name="l2pro" value="pass" <% if (l2pro=="pass") write("checked"); %>>Enable										
          			<input type="radio" id="lp2" name="l2pro" value="block" <% if (l2pro=="block") write("checked"); %>>Disable
                </td>
            </tr>
-->

															<tr height="25">
																<td  align="right" width="50%" class="crons" style="text-align: right;">&nbsp;
																	<script>writemsg(<% write(lang); %>, "LLDP使能:");</script>
																</td>
																<td  align="left" class="crons">
																	<input type="radio" name="lldpstatus" id="ls1"
																		value="enable" <% if (lldpstatus=="enable" )
																		write("checked"); %>>
																	开启
																	<input type="radio" name="lldpstatus" id="ls2"
																		value="disable" <% if (lldpstatus!="enable" )
																		write("checked"); %>>
																	关闭
																</td>
															</tr>

															<tr height="25">
																<td  align="right" class="crons" style="text-align: right;"  width="50%">&nbsp;
																	<script>writemsg(<% write(lang); %>, "报文发送时间间隔:");</script>
																</td>
																<td align="left" class="crons"><input type="text"
																		name="lldptimer" class="input_board3" value=<%
																		write(lldptimer); %>>
																	&nbsp;&nbsp; (5-300)</td>
															</tr>


															<tr height="25">
																<td  align="right" class="crons" style="text-align: right;"  width="50%">&nbsp;
																	<script>writemsg(<% write(lang); %>, "报文发送参数倍率:");</script>
																</td>
																<td align="left" class="crons">
																	<input type="text" name="lldptxhold"
																		class="input_board3" value=<% write(lldptxhold);
																		%>>
																	&nbsp;&nbsp;(1-10)
																</td>
															</tr>
															<tr height="25">
																<td  align="right" class="crons" style="text-align: right; "  width="50%">&nbsp;
																	<script>writemsg(<% write(lang); %>, "报文重新初始化延时时间:");</script>
																</td>
																<td align="left" class="crons">
																	<input type="text" name="lldpreint"
																		class="input_board3" value=<% write(lldpreint);
																		%>>
																	&nbsp;&nbsp;(1-10)
																</td>
															</tr>
															<tr height="25">
																<td  align="right" class="crons" style="text-align: right;"  width="50%">&nbsp;
																	<script>writemsg(<% write(lang); %>, "报文快速发送时间间隔:");</script>
																</td>
																<td align="left" class="crons">
																	<input type="text" name="lldptxdelay"
																		class="input_board3" value=<%
																		write(lldptxdelay); %>>
																	&nbsp;&nbsp;(1-3600)
																</td>
															</tr>


															<tr height="25">
																<td colspan="2" align="left" class="crons">
																	<div align="center">
																		<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用","buttons_apply","button","modify2", "checkvalue()");</script>
																	</div>
																</td>
															</tr>



														</table>
													</td>
												</tr>
											</table>
										</td>
									</tr>

								</table>
							</td>
						</tr>
						<tr>
							<td>
							</td>
						</tr>
					</table>
			</form>
			<br>
			<form x-show="active==='tab1'" name="port_setting" method="POST" action="/goform/PortLimitChange" class="formContain">
				<input type="hidden" name="left_menu_id" value="@left_menu_id#">
				<input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
				<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>


				<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
					<tr>
						<td>
							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
								<tr>
									<td valign="top">
										<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"
											class="cword09">
											<tr>
												<td>

													<table width="100%" align="center" border="0" cellspacing="0"
														cellpadding="0">
														<tr>
															<td height="30px">
																<font size="5" color="#404040">
																	<div class="bot">LLDP信息</div>
																</font>
															</td>
														</tr>
													</table>
												</td>
											</tr>



											<tr>
												<td>
													<table width="100%" height="35" border="0" cellpadding="0"
														cellspacing="0" class="tablebord" id="table_port">
														<tr height="30" align="center" class="td7">
															<td width="12%" nowrap  class="all_tables_list">本地端口</td>
															<td width="12%" nowrap  class="all_tables_list">系统ID</td>
															<td width="12%" nowrap  class="all_tables_list">远端端口</td>
															<td width="12%" nowrap  class="all_tables_list">端口描述</td>
															<td width="13%" nowrap  class="all_tables_list">系统名</td>
															<td width="13%" nowrap  class="all_tables_list">系统描述</td>
															<td width="13%" nowrap  class="all_tables_list">系统类型</td>
															<td width="13%" nowrap  class="all_tables_list">管理地址</td>
														</tr>


														<script language="javascript">
															writeLines();

														</script>
													</table>
												</td>
											</tr>
											<tr>
												<td align="center" height="35">
													<br />
													<script>writebutton(1,<% write(lang); %>, "刷  新","button", "button", "Refresh", "refreshpage()");</script>
													&nbsp;
											</tr>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>


				</table>

				<input type="hidden" name="todo" value="save">
				<INPUT type="hidden" name="this_file" value="port.asp">
				<INPUT type="hidden" name="next_file" value="port.asp">
				<input type="hidden" name="message" value="@msg_text#">
			</form>
			
			<form x-show="active==='tab2'" name="vlan_port" method="POST" action="/goform/PortLldpChange" class="formContain">
				<input type="hidden" name="left_menu_id" value="@left_menu_id#">
				<input type="hidden" name="trunk_config" value="@trunk_config#">
				<!-- <input type="hidden" name="vlan_port_id" id="vlan_port_id"> -->
				<!-- <input type="hidden" name="flag" id="flag"> -->
				<input type="hidden" name="type_value" id="type_value" value="@trunk_config#">
				<input type="hidden" name="filter_value" id="filter_value" value="@trunk_config#">
				<input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
				<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
				<input type="hidden" name="pvid_config" value="@pvid_config#">
				<input type="hidden" name="egress_tagged" id="egress_tagged" value="@egress_tagged#">
				<input type="hidden" name="param1" id="param1" >
				
	
				<table id="mainTblPort" width="100%" height="100%" border="0" align="center" cellpadding="0"
					cellspacing="0">

					<tr>
						<td>
							<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0">
								<tr>
									<td valign="top">
										<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"
											class="cword09">
											<tr>
												<td>

													<table width="100%" align="center" border="0" cellspacing="0"
														cellpadding="0">
														<tr>
															<td height="30px">
																<font size="5" color="#404040">
																	<div class="bot">LLDP工作模式</div>
																</font>
															</td>
														</tr>
													</table>
												</td>
											</tr>

											<tr>
												<td>
													
														
																<div x-data="myApp">
																<table id="table_port_vlan" border="0" cellspacing="0"
																	cellpadding="0" width="100%" class="tablebord">
																	<tr align="center" height="25" class="crons">
																		
																		<th class="td2" width="30%">
																			<font color="#333333"><b>&nbsp;
																					<script>writemsg(<% write(lang); %>, "端口");</script>
																				</b></font>
																		</th>
																		<th class="td2" width="60%">
																			<font color="#333333"><b>&nbsp;
																					<script>writemsg(<% write(lang); %>, "端口状态");</script>
																				</b></font>
																		</th>
																	</tr>
																	<template  x-for="(row,index) in appData.lldpPort" :key="index">
																		 <tr  class='tables' align="center">";
																			   
																			   <td class='inputsyslog1' style="font-weight: normal;"  x-text="row.portName"></td>
																			
																			    <td  class='inputsyslog1'>
																				<select  x-model="row.portStatus" @change="changeSelect(row)">
																					<option value='tx' >tx</option>
																					<option value='rx' >rx</option> 
																					<option value='txrx' >txrx</option>
																					<option value='disable' >disable</option>
																					<option value='all' >all</option>
																				</select>
																					 </td>
																			
																			</tr>

																	</template>

																	

																</table>
															</div>
															
														
													
												</td>
											</tr>
											<tr>
												<td colspan="2" align="center" height="35">
													<br>
													<script>writebutton(1,<% write(lang); %>, "刷  新","button","button", "Refresh", "refreshpage()");</script>
												</td>
											</tr>
										</table>
									</td>
								</tr>

							</table>
						</td>
					</tr>
				</table>

			</form>
			
			<form x-show="active==='tab3'" id="formStat" name="formStat" method="post" action="" onSubmit="" class="formContain">
				<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td height="30px">
							<font size="5" color="#404040">
								<div class="bot">LLDP统计</div>
							</font>
						</td>
					</tr>
				</table>
				<table width="98%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table21">
					<tr class="table_maintable">
						<td width="9%" align="center" class="all_tables_list">端口</td>
						<td width="13%" align="center" class="all_tables_list">Frames out</td>
						<td width="13%" align="center" class="all_tables_list">Ages out</td>
						<td width="13%" align="center" class="all_tables_list">Frames discarded</td>
						<td width="13%" align="center" class="all_tables_list">Frames received in error</td>
						<td width="13%" align="center" class="all_tables_list">Frames received in</td>
						<td width="13%" align="center" class="all_tables_list">Frames TLVs discarded</td>
						<td width="13%" align="center" class="all_tables_list">Frames TLVs unrecognized</td>
					</tr>
					<script language="javascript">
						writeLinesStat();
					</script>
				</table>
				<span class="table_main21"></span>
			</form>

			<script>
				changebgcolor();
				changebgcolor_port();
				// changebgcolor11();
				changebgcolor_name("table_port_vlan");
				changebgcolor21()
					<% if (errorcode != "") { write_errorcode(errorcode); } %>
			</script>
</body>

</html>