<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"MAC学习限制")</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>

<script language="JavaScript" type="text/JavaScript">

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor3(){
 var tab = document.all.table3;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function isINT(str){
var re = /^[0-9]*[0-9][0-9]*$/ ;

var result = re.test(str);   //测试 返回true或false

return result;

}

function delmac_limit()
{
	var hid = document.maclimitedsetting;
	hid.action="/goform/MACLimitedDel"
	hid.submit();
	return 0;
}

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var arp_port = document.getElementById("arp_port");
	var p = obj.value;
	var i;
	if(obj.checked){
		for(i=0;i<arp_port.options.length;i++){	
			if(arp_port.options[i].text==p)
			{
				arp_port.options[i].selected=true;
				document.getElementById("arp_limited_vlan").value=trobj.cells[2].innerHTML;
			}
			else
			{
				arp_port.options[i].selected=false;
			}
		}
		 
	}

}

function P(port,mac_maximum,currmum,status)
{
	var narr=5;
	var tbtd;
	var i;
	var tbtr = document.getElementById("arp_limited_tbl").insertRow(-1);
	tbtr.classname = "crons";
	tbtr.height = "30";
	tbtr.setAttribute("height", "30");
	tbtr.setAttribute("class", "crons");
	tbtr.setAttribute("className", "crons");
	tbtr.setAttribute("id", "tr_"+port);
	for(i=0;i<narr;i++)
	{
		tbtd = document.createElement("td");
		tbtd.align = "center";
		tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		tbtr.appendChild(tbtd);
	}
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+port+"\" onclick=\"addToPortRange(this)\"/>";
	tbtr.cells[1].innerHTML = port;
  	tbtr.cells[2].innerHTML = mac_maximum;
	tbtr.cells[3].innerHTML = currmum;
  	tbtr.cells[4].innerHTML = status;
  //	tbtr.cells[4].innerHTML = "<a style='cursor:hand' onClick='return delmac_limit(\""+port+"\");'>删除</a>";
}

function checkData()
{
	var tf=document.maclimitedsetting;
	tf.action = "/goform/saveComm?name=mac_limited";
	tf.submit();
}

function AddOption(port_name)
{
	var selectObject = document.getElementById("arp_port");
	var y=document.createElement('option');
	y.text=port_name;
	y.value=port_name;
	try
	{
    	selectObject.add(y,null); // standards compliant
  	}
  	catch(ex)
  	{
    	selectObject.add(y); // IE only
  	}
}
/*isNaN判断是否是数字，包括整数与浮点数*/
function MacLimitedAdd()
{
	var tf=document.maclimitedsetting;
	var arp_limited_vlan = document.getElementById("arp_limited_vlan").value;
	if(isINT(arp_limited_vlan))
	{
    	if(arp_limited_vlan>1024||arp_limited_vlan<1)
			alert(putmsg(<% write(lang); %>,"输入的数据超出限制范围！"));	
    	else
    	{
    		tf.action="/goform/MACLimitedAdd";
    		tf.submit();
    	}
	}
	else
	{
	  	alert(putmsg(<% write(lang); %>,"输入值必须为整数!"));	
	}
}

function refreshpage()
{
  location.href='mac_limited.asp?ltime='+<% write(lltime); %>;
}

<% var errorcode, maclimitedcfg; getMaclimitedCfg(); %>

var macLimitedList = [<% write(maclimitedcfg); %>];

function writeMacLimitedLine()
{
	for(var i=0;i<macLimitedList.length;i++)
	{
		document.write("<tr  class='tables_all'>");
		document.write("    <td height='32' class='inputsyslog1'>"+macLimitedList[i].split(",")[0]+"</td>");
		document.write("    <td height='32' class='inputsyslog1'>"+macLimitedList[i].split(",")[1]+"</td>");
		document.write("    <td height='32' class='inputsyslog1'>"+macLimitedList[i].split(",")[2]+"</td>");
		document.write("    <td height='32' class='inputsyslog1'>"+macLimitedList[i].split(",")[3]+"</td>");

		document.write("  </tr>");
	}
}


</script>
</head>
<body  >
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
	
<form name="maclimitedsetting" method="POST" action="">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="left_menu_id" value="">

<table id="table111" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
	<tr>
  	<td>
  		<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" >
  			<tr>
  			<td valign="top" >
  				<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
  					<tr>
  					<td>
  						<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
  							<tr>
              				<td colspan="7" align="left" class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"MAC学习限制");</script></td>
       						</tr>
       					</table>
       				</td>
       				</tr>
       		
       				<tr>
       				<td> 
     					<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table1" class="tablebord">      						
			       			<tr height="25">
			       				<td width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></td>
			       				<td colspan="5" align="left" class="crons">&nbsp;<select id="arp_port" name="arp_port"><script><% var errorcode;AppendOption(); %></script></select></td>
			       			</tr>
			       			
			       			<tr height="25">
			       				<td width="20%" align="left" class="crons">&nbsp;Max Secure Addr</td>
			        			<td colspan="5" align="left" class="crons">&nbsp;<input type="text" name="arp_limited_vlan" id="arp_limited_vlan" >&nbsp;(1-1024)</td>
							</tr>
							
							<tr height="25">
								<td colspan="6" align="center" class="crons">
									<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","add_limited_arp","MacLimitedAdd()");</script>
					 	            <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","del_limited_arp","delmac_limit()");</script>
								</td>
							</tr>
						</table>
					</td>
					</tr>
			
					<tr>
						<td height="8"></td>
					</tr>
			
					<tr>
					<td>
						<table width="100%" border="0" align="center" cellspacing="0" cellpadding="0" id="table2" class="tablebord">
							<TR align="center" height=22>
								<TD width="15%"  nowrap class="all_tables_list">Secure Port</TD>
								<TD width="17%"  nowrap class="all_tables_list">Max Secure Addr</TD>
								<TD width="17%"  nowrap class="all_tables_list">Current Addr</TD>
								<TD width="17%"  nowrap class="all_tables_list">Security Action</TD>
							</TR>

							<script language="javascript">
								  writeMacLimitedLine();
							</script>


<!--		                
					<tr>
						<td colspan="6" align="center" height="23">
		                <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
		                &nbsp;
						<script><% var errorcode; MACLimitedShow(); %></script>
						<script><% var errorcode; %></script>
					</tr>  
-->						
						</table> 
					</td>
					</tr>
				</table>
			</td>
			</tr>
		</table>
	</td>
	</tr>
</table>


<input type="hidden" name="h_arp_enable" value="@h_arp_enable#">
<input type="hidden" name="todo" value="save">
<INPUT type="hidden" name="this_file" value="port.html">
<INPUT type="hidden" name="next_file" value="port.html">
<input type="hidden" name="message" value="@msg_text#">

</form>


<script>
changebgcolor();
changebgcolor2();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</body>
</html>

