<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<% var authmode; checkCurMode(); %>

			<meta http-equiv="Content-Type" content="text/html; charset=utf8">
			<script src="js/alpinejs.min.js"></script>
			<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
			<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
			<link href="css/display_web.css" rel="stylesheet" type="text/css" />
			<title>&nbsp;
				<script>writemsg(<% write(lang); %>, "MAC地址绑定");</script>
			</title>

			<script language="JavaScript">
				var boardType = <% getSysCfg(); %>;
			</script>
			<script language="JavaScript" type="text/JavaScript">
function isMulticastMac(mac)
{
	var head = mac.substring(0,2);
	if(parseInt(head,16)%2 ==1)
		return true;
	return false;
}

/*by LuoM  11-5-18*/
function DataScope(value,max,min)
{
	if(!isNaN(value) && value>=min && value<=max)
		return true;
	else
		return false;
}

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}

function getPage(page)
{
   location.href="mmac.asp?page="+page+"&ltime="+<% write(lltime); %>;
}

function jumptoPage()
{
	var topage = document.getElementById("jump2page");
   location.href="mmac.asp?page="+topage.value+"&ltime="+<% write(lltime); %>;
}
function dofirst(){

location.href="mmac.asp?page=1&ltime="+<% write(lltime); %>;

}

function del_mac(mac, vlan, port)
{
	var hid = document.macdel;
	var topage = document.getElementById("jump2page");
	
	hid.mac_value.value = mac;
	hid.vlan_value.value = vlan;
	hid.port_value.value = port;
	
	// hid.stayonthispage.value=topage.value;
	
	hid.action="/goform/Mmacdelsingle"
	hid.submit();
	return 0;
}

function checkdelportlist()
{
	var checkbox_index = document.getElementsByName("checkbox_index");
	var fordel_port = document.getElementById("fordel_port");
	var i;
	var port_id;

	fordel_port.value = "";

	for (i = 0; i < checkbox_index.length; i++)
	{
		if (false == checkbox_index[i].checked)
		{
			port_id = checkbox_index[i].id;
			fordel_port.value = fordel_port.value + port_id + ",";
		}
	}

	fordel_port.value = fordel_port.value.substring(0, fordel_port.value.length-1);

}

function addToModify(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var vlan = document.getElementById("forfil_vlan");
	var mac = document.getElementById("forfil_mac");
	var checkbox_index = document.getElementsByName("checkbox_index");
	var forfil_port = document.getElementById("forfil_port");
	var portlist=trobj.cells[5].innerHTML;
	var i, j;
	var port_id;
	var array = new Array();
	

	if(obj.checked)
	{
		vlan.value=trobj.cells[2].innerHTML;
		mac.value=trobj.cells[3].innerHTML;
		forfil_port.value = portlist;
		if (forfil_port.value.length != 0)
			forfil_port.value += ",";
		
		for (i = 0; i < checkbox_index.length; i++)
		{
			checkbox_index[i].checked = false;
			port_id = checkbox_index[i].id;
			array = portlist.split(",");

			for(j = 0; j < array.length; j++)
			{
				if (array[j] == port_id)
					checkbox_index[i].checked = true;
			}
		}
	}
}

function p(index, mmac_vlan, mmac_mac, mmac_type, mmac_port)
{
    var narr=7;
    var tbtd;
    var i;
    var tbtr = document.getElementById("mmac_tbl").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");
	tbtr.setAttribute("id", "tr_"+index);	

	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
		
	/*display*/
	tbtr.cells[0].abbr = 0;

	if ((<% write(authmode); %> == 1) && (mmac_type != "gmrp") && (mmac_type != "igmp")&& (mmac_type != "csd"))
		tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index2\" value=\""+index+"\" onclick=\"addToModify(this)\"/>";
	else
		tbtr.cells[0].innerHTML = " ";
	
	tbtr.cells[1].innerHTML = index;
	tbtr.cells[2].innerHTML = mmac_vlan;
	tbtr.cells[3].innerHTML = mmac_mac;
    tbtr.cells[4].innerHTML = mmac_type;
	tbtr.cells[5].innerHTML = mmac_port;
	
	if ((<% write(authmode); %> == 1) && (mmac_type != "gmrp") && (mmac_type != "igmp")&& (mmac_type != "csd"))
		tbtr.cells[6].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick=del_mac('"+mmac_mac+"','"+mmac_vlan+"','"+mmac_port+"')>";
	else
		tbtr.cells[6].innerHTML = " ";
	
 //tbtr.cells[5].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+mmac_mac+"\" onclick=\"addToVLanRange(this)\"/>";
// tbtr.cells[5].innerHTML = "<a style='cursor:hand' onClick=return del_mac('"+mmac_mac+"','"+"+mmac_vlan+"');>删除</a>";

}


function AddOption(port_name){
	var selectObject = document.getElementById("forfil_port");
	var y=document.createElement('option');
  	y.text=port_name;
	y.value=port_name;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  catch(ex)
    {
    selectObject.add(y); // IE only
    }
}


function selectForfil()
{
	var tf = document.mmacsetting;
	var forfil_mac = document.getElementById("forfil_mac").value;
	var forfil_vlan = document.getElementById("forfil_vlan").value;
	
		var forfil_port = document.getElementById("forfil_port");
		
	var myRE =/^[0-9a-fA-F]{1,4}\.[a-fA-F0-9]{1,4}\.[a-fA-F0-9]{1,4}$/;

	if (forfil_port.value.length == 0)
	{
		alert(putmsg(<% write(lang); %>,"请选择端口,目前没有选择任何端口!"));
		return ;
	}


	if ((myRE.test(forfil_mac)) && (isMulticastMac(forfil_mac)))
	{
		if(DataScope(forfil_vlan,4094,1))
		{
			
			 forfil_port.value = forfil_port.value.substring(0,forfil_port.value.length-1);
			
			//alert(forfil_port.value);
			
			checkdelportlist();
			
			tf.action = "/goform/AddMmac";
			tf.submit();
		}
		else{
			alert(putmsg(<% write(lang); %>,"vlan 的范围在1-4094"));
		}
	}
	else
	{
		alert(putmsg(<% write(lang); %>,"输入MAC地址非法"));
	}
}

function selectFordel()
{
	var tf = document.mmacsetting;
	var forfil_mac = document.getElementById("forfil_mac").value;
	var forfil_vlan = document.getElementById("forfil_vlan").value;
	var forfil_port = document.getElementById("forfil_port");
	var myRE =/^[0-9a-fA-F]{1,4}\.[a-fA-F0-9]{1,4}\.[a-fA-F0-9]{1,4}$/;
	var hid = document.macdel;
	var topage = document.getElementById("jump2page");

	if ((myRE.test(forfil_mac)) && (isMulticastMac(forfil_mac)))
	{
		if(DataScope(forfil_vlan,4094,1))
		{
			
			
			forfil_port.value = forfil_port.value.substring(0,forfil_port.value.length-1);
			
	
			hid.mac_value.value = forfil_mac;
			hid.vlan_value.value = forfil_vlan;
			hid.port_value.value = forfil_port.value;

			hid.stayonthispage.value=topage.value;

			hid.action="/goform/Mmacdelsingle"
			hid.submit();
		}
		else{
			alert(putmsg(<% write(lang); %>,"vlan 的范围在1-4094"));
		}
	}
	else
	{
		alert(putmsg(<% write(lang); %>,"输入MAC地址非法"));
	}
	
}
function delmmac(x)
{
	var tf = document.mmacsetting;
	var mmac_tbl = document.getElementById("mmac_tbl");
	var selid = document.getElementById("selid");
	var i=1,j=0;
	switch(x)
	{
		case 1:
			var vlan_del = document.getElementById("vlan_del");
			selid.value=1;
			for(;i<mmac_tbl.rows.length;i++){
				if(mmac_tbl.rows[i].cells[0].innerHTML==vlan_del.value){	
					j=0;
					break;
				}
				j=1;
			}
/*by FC-fcy 2012-4-23  根据VLAN批量删除*/		
			if(j==1)
			{
				alert(putmsg(<% write(lang); %>,"你输入的vlan不存在!"));
				return;
			}
			/*by FC-LuoM 2012-5-14 start*/
			for(;i<mmac_tbl.rows.length;i++){
				if(mmac_tbl.rows[i].cells[2].innerHTML=="dynamic"){	
					j=0;
					break;
				}
			}
            /*by FC-LuoM 2012-5-14 end*/
			if(j==1)
			{
				alert(putmsg(<% write(lang); %>,"不能删除static类型项!"));
				return;
			}
			
			tf.action = "/goform/Delmmac";
			tf.submit();
/*by FC-fcy 2012-4-23 end*/

			break;
		case 2:
			var port_del = document.getElementById("port_del");
			selid.value=2;
			for(i;i<mmac_tbl.rows.length;i++){
				if(mmac_tbl.rows[i].cells[3].innerHTML==port_del.value){	
					j=0;
					break;
				}
				j=1;
			}
/*by FC-fcy 2012-4-23  根据端口批量删除*/		
			if(j==1)
			{
				alert(putmsg(<% write(lang); %>,"你输入的端口不存在!"));
				return;
			}
			/*by FC-LuoM 2012-5-14 start*/
			for(;i<mmac_tbl.rows.length;i++){
				if(mmac_tbl.rows[i].cells[2].innerHTML=="dynamic"){	
					j=0;
					break;
				}
			}
            /*by FC-LuoM 2012-5-14 end*/
			if(j==1)
			{
				alert(putmsg(<% write(lang); %>,"不能删除static类型项!"));
				return;
			}

  			tf.action = "/goform/Delmmac";
			tf.submit();
/*by FC-fcy 2012-4-23 end*/			
			break;
		case 3:
			var mac_del = document.getElementById("mac_del");
			selid.value=3;
			for(i;i<mmac_tbl.rows.length;i++){
				if(mmac_tbl.rows[i].cells[1].innerHTML==mac_del.value){	
					j=0
					break;
				}
				j=1;
			}
/*by FC-fcy 2012-4-23  根据MAC地址批量删除*/		
			if(j==1)
			{
				alert(putmsg(<% write(lang); %>,"你输入的MAC地址不存在!"));
				return;
			}
			/*by FC-LuoM 2012-5-14 start*/
			for(;i<mmac_tbl.rows.length;i++){
				if(mmac_tbl.rows[i].cells[2].innerHTML=="dynamic"){	
					j=0;
					break;
				}
			}
            /*by FC-LuoM 2012-5-14 end*/
			if(j==1)
			{
				alert(putmsg(<% write(lang); %>,"不能删除static类型项!"));
				return;
			}
			
			tf.action = "/goform/Delmmac";
			tf.submit();
/*by FC-fcy 2012-4-23 end*/	
			break;
		case 4:
			selid.value=4;
			tf.action = "/goform/Delmmac";
			tf.submit();
			break;
	}
}
function checkData(x)
{
	var tf=document.mmacsetting;
	tf.action = "/goform/saveComm?name=mmac";
	tf.submit();	
}
function selectToAll() 
{  
	var cf = document.forms[0];
	var objs = document.getElementsByName("checkbox_index"); 
	var i;
	if(cf.check_all.checked == true)
	{
	    for(i = 0; i < objs.length; i++) 
	    {    
	       if(objs[i].disabled==false && objs[i].checked==false){
	         objs[i].checked = true;  
		 		addToPortRange(objs[i]);
		 }
	    }
	}
	else
	{
	    for(i = 0; i < objs.length; i++) 
	    {    
				if(objs[i].checked==true){
				objs[i].checked = false;  
				 addToPortRange(objs[i]);
		}
	    }
	} 
}
function addToPortRange(obj){
	var forfil_port = document.getElementById("forfil_port");
	var p = obj.value;

	if(obj.checked){
		//target.value =target.value + p + " ";
		forfil_port.value =forfil_port.value + p + ",";
	}
	else
	{
		//target.value = target.value.replace(p + " ","");
		forfil_port.value = forfil_port.value.replace(p + ",","");
	}
}
</script>
</head>

<body><br>

	<% web_get_stat(); %>
		<script>
			checktop(<% write(lang); %>);
		</script>

		<form name="mmacsetting" method="POST" action="mmac.asp" style="min-height: 600px;">
			<input type="hidden" name="left_menu_id" value="">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input type="hidden" name="selid" id="selid" value="">
			<input type="hidden" name="forfil_port" id="forfil_port">
			<input type="hidden" name="fordel_port" id="fordel_port">
			<div class="formContain">
				<table id="mainTb2" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
					<tr>
						<td>
							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
								<tr>
									<td valign="top">
										<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"
											class="cword09">
											<tr>
												<td height="8"></td>
											</tr>

											<table width="100%" align="center" border="0" cellspacing="0"
												x-data="{portList:[]}"
												x-init="portList = JSON.parse(localStorage.getItem('portList')).map(item => item.portName)"
												cellpadding="0">
												<tr>
													<td height="30px" colspan="11">
														<font size="5" color="#404040">
															<div class="bot">静态组播MAC地址表</div>
														</font>
													</td>
												</tr>
												<tr>
													<td height="30px" align="left" class="all_tables all_tables3 "
														style="border: none;text-align: left;">&nbsp;VLAN:</td>
													<td colspan="10" align="left" class="crons"
														style="font-size: 16px;">
														&nbsp;<input type="text" name="forfil_vlan"
															id="forfil_vlan">&nbsp;(1-4094) </td>
												</tr>

												<tr>
													<td width="15%" height="30px" align="left"
														class="all_tables all_tables3"
														style="border: none;text-align: left;">&nbsp;
														<script>writemsg(<% write(lang); %>, "组播MAC地址:");</script>
													</td>
													<td colspan="10" align="left" class="crons"
														style="font-size: 16px;">
														&nbsp;<input type="text" name="forfil_mac"
															id="forfil_mac">&nbsp;
														<script>writemsg(<% write(lang); %>, "(HHHH.HHHH.HHHH，为16进制格式)");</script>
													</td>
												</tr>

												<tr>
													<td width="15%" height="30" class="all_tables all_table3-right">
														&nbsp;
														<script>writemsg(<% write(lang); %>, "端口:");</script>
													</td>
													<template x-for="(item,idx) in portList" :key="idx">
														<td class="all_tables all_table3-right" x-text="item"></td>
													</template>
												</tr>
												<tr>
													<td class="all_tables all_table3-right" style="font-size: 16px;">
														&nbsp;
														选择:
													</td>
													<template x-for="(item,idx) in portList" :key="idx">
														<td align="center" class="all_tables all_table3-right">
															<input type="checkbox" name="checkbox_index" :value="item"
																:id="item" : onchange="addToPortRange(this)">

														</td>
													</template>
												</tr>
												<td :colspan="portList.length+1" align="center" class="crons"
													style="padding:5px;border-top:  1px solid #ddd;">
													<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "添  加", "buttons_add", "button", "add_arp", "selectForfil()");</script>
												</td>
											</table>

											<tr>
											</tr>

											<tr>


										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>

				</table>
			</div>
			<input type="hidden" name="sed_mac" id="sed_mac" value=0>
			<input type="hidden" name="h_arp_enable" value="@h_arp_enable#">
			<input type="hidden" name="todo" value="save">
			<INPUT type="hidden" name="this_file" value="port.html">
			<INPUT type="hidden" name="next_file" value="port.html">
			<input type="hidden" name="message" value="@msg_text#">

			<div class="formContain" style="margin-top: 10px;">
				<div style="padding: 10px 5px;">
					<font size="5" color="#404040">
						<div class="bot">信息展示</div>
					</font>
				</div>
				<div style="padding: 10px 5px;">
					<table id="mmac_tbl" name="mmac_tbl" border="0" cellspacing="0" cellpadding="0" width="100%"
						class="tablebord">
						<thead>
							<th width="10%" height="25" align="center" class="td6">&nbsp;&nbsp;</th>
							<th width="10%" height="25" align="center" class="td6">序号</th>
							<th width="12%" height="25" align="center" class="td6">VLAN</th>
							<th width="15%" height="25" align="center" class="td6"> &nbsp;
								<script>writemsg(<% write(lang); %>, "MAC地址");</script>
								</td>
							<th width="12%" height="25" align="center" class="td6">&nbsp;
								<script>writemsg(<% write(lang); %>, "类型");</script>
								</td>
							<th width="31%" height="25" align="center" class="td6">&nbsp;
								<script>writemsg(<% write(lang); %>, "端口");</script>
								</td>
							<th width="10%" height="25" align="center" class="td6">&nbsp;
								<script>writemsg(<% write(lang); %>, "删除");</script>
								</td>
						</thead>
						<script>
				<%  var errorcode; MmacShow("mac");%>
						</script>
					</table>
				</div>


			</div>
		</form>
		<script>
				//changebgcolor();
				// changebgcolor2();
				changebgcolor_name("mmac_tbl");

<% if (errorcode == "1") { write("alert(putmsg("); write(lang); write(",'正在删除，可能需要一些时间，稍后请手动刷新页面!'));"); } %>
<% if (errorcode != "" && errorcode != "1") { write_errorcode(errorcode); } %>
		</script>

		<br><br><br>

		<form name="macdel" method="POST" action="">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input type="hidden" name="mac_value" value="">
			<input type="hidden" name="vlan_value" value="">
			<input type="hidden" name="port_value" value="">
			<input type="hidden" name="stayonthispage" value="">
		</form>
</body>

</html>
