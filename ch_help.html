<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<title>帮助</title>

<link href="css/global.css" rel="stylesheet" type="text/css">
<script language="JavaScript">
</script>
<script language="JavaScript" type="text/JavaScript">
</script>
<style type="text/css">
<!--
.STYLE1 {font-size: 14px}
.STYLE2 {font-size: 12px}
.STYLE3 {color: #FF0000}
-->
</style>
</head>

<body class="main">
    <table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
        <tr><td>
            <table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" class="leftnav table1">
            <tr>
				<td valign="top" >

				    <!-- 具体内容 begin-->
				    <table width="90%"   border="0" align="center" cellpadding="4" cellspacing="1" class="cword09">    
							    <tr>
								   	<td> 		   	
							   	      <table width="100%" height="77" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09" id="sys_info">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="sysinfo">基本信息</a></th>
								        </tr>
									    <tr>
									      <td height="50" valign="middle"><span class="STYLE1">&nbsp;显示当前设备产品名称、版本信息、OID、波特率、厂商信息、版权、MAC地址 、当前设备运行时间、CPU利用率、内存利用率等信息。</span></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="port">端口管理</a></th>
								        </tr>
									    <tr>
									      <td height="258" valign="middle"><span class="STYLE1">&nbsp;交换机端口速率、流量控制、MTU等二层属性的设置。<br>
										  	  <br>
											  &nbsp;&nbsp; <strong>端口范围</strong>  页面复选框选择需要修改属性的二层端口<br>
											  <br>
											  &nbsp;&nbsp; <strong>端口启用</strong>  开启/关闭端口状态，相当于no shutdown/shutdown。<br>
											  <br>
											  &nbsp;&nbsp; <strong>端口速率</strong>  设置端口双工状态及端口速率。<br>
											  <br>
											  &nbsp;&nbsp; <strong>流量控制</strong>  启用/禁用端口流量拥塞控制，注意：接受和发送方向可单独设置。默认禁用。<br>
											  <br>
											 
											  
										      &nbsp;&nbsp; <strong>接口描述</strong>  设置二层端口的端口描述。	<br>
										      <br>
										      &nbsp;<span class="STYLE3">注意：</span>不能在文本框手动输入端口名。聚合口修改端口速率、出口线速，出口burst、入口线速、入口burst无效。</span>				  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="storm">风暴抑制</a></th>
								        </tr>
									    <tr>
									      <td height="234"><span class="STYLE1">&nbsp;风暴抑制主要防止交换机的端口被局域网中的广播、组播或者未知单播风暴所破坏，占用相当可观的网络带宽，造成整个网络无法正常工作。<br>
										  <br>
											  &nbsp;&nbsp; <strong>端口范围</strong>   页面复选框选择需要修改属性的二层端口，注意：不能在文本框手动输入端口名。<br>
											  <br>
											  &nbsp;&nbsp; <strong>广播数据包</strong>  设置抑制广播包报文个数，单位帧/每秒，默认值200。<br>
											  <br>
											  &nbsp;&nbsp; <strong>多播数据包</strong>  设置抑制组播包报文个数，单位帧/每秒，默认不开启。<br>
											  <br>
											  &nbsp;&nbsp; <strong>未知单播包</strong>  设置抑制未知单播包报文个数，单位帧/每秒，默认值200。<br>
											  <br>
											  &nbsp;<span class="STYLE3">注意：</span>删除是恢复端口默认风暴抑制百分比。</span>									  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mirror">端口镜像</a></th>
								        </tr>
									    <tr>
									      <td height="187"><span class="STYLE1">&nbsp;端口镜像是将指定端口的报文复制到镜像目的端口，镜像目的端口会接入流量分析设备，用户利用这些设备分析目的端口接收到的流量，进行网络监控和故障排除。<br>
										  <br>
										  &nbsp;&nbsp; <strong>镜像端口</strong>   设置镜像端口，即流量的目的端口，一般接流量分析监控设备。<br>
										  <br>
										  &nbsp;&nbsp; <strong>监控端口</strong>   设置需要监控的端口，即流量的源端口。<br>
										  <br>
										  &nbsp;&nbsp; <strong>方向</strong>   设置被监控端口的数据采集方向。<br>
										  <br>
										  &nbsp;<span class="STYLE3">注意：</span>修改只能修改采集数据方向的值，即方向。</span>								  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="sedtab">转发表</a></th>
								        </tr>
									    <tr>
										  <td height="180"><span class="STYLE1">&nbsp;为了快速转发报文，交换机需要维护mac地址转发表。mac地址转发表是一张基于端口的二层转发表，是交换机实现二层报文快速转发的基础。mac地址转发表的表项包括：目的mac地址、端口所属的vlan id、转发端口号等。<br>
										  <br>
										  &nbsp;&nbsp; <strong>老化时间</strong>          修改转发表的老化时间，可以设置为0，0代表不老化。默认300s。<br>
										  <br>
										  &nbsp;&nbsp; <strong>二层表项的删除</strong>    可以按照VLAN、端口ID、MAC地址不同的条件删除表项，也可以用"清空" 按钮全部删除。<br>
										  <br>
										  &nbsp;<span class="STYLE3">注意：</span>删除和清空操作只能删除dynamic类型的mac地址表项。</span>									  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mac">MAC绑定</a></th>
								        </tr>
									    <tr>
									      <td height="148"><span class="STYLE1">&nbsp;&nbsp; <strong>网络端口</strong>    设置mac地址绑定的物理端口。<br>
										  	  <br>	
											  &nbsp;&nbsp;<strong> MAC地址</strong>   设置绑定的目的mac地址。<br>
											  <br>
											  &nbsp;&nbsp; <strong>Vlan</strong>        设置绑定对应的VLAN ID。<br>
											  <br>
											  &nbsp;<span class="STYLE3">注意：</span>MAC绑定会关闭端口的mac学习功能，只有绑定的mac地址可以转发。</span>									  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							  
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mac_limit">MAC学习限制</a></th>
								        </tr>
									    <tr>
									      <td height="148"><span class="STYLE1">&nbsp;&nbsp; <strong>网络端口</strong>    设置mac地址学习限制的物理端口。<br>
										  	  <br>	
											  &nbsp;&nbsp;<strong> Max Secure Addr</strong>   设置网络端口的学习上限。<br>
											  <br>
											  &nbsp;&nbsp; <strong>Current Addr</strong>        当前学习数目。<br>
										  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="vlan_fwd">VLAN配置</a></th>
								        </tr>
									    <tr>
										  <td height="196"><span class="STYLE1">&nbsp;VLAN把一个物理上的LAN划分成多个逻辑上的LAN，每个VLAN是一个广播域。VLAN内的主机间通过传统的以太网通信方式即可进行报文的交互，而处在不同VLAN内的主机之间如果需要通信，则必须通过路由器或三层交换机等网络层设备才能够实现。<br>
										  <br>
										  &nbsp;&nbsp; <strong>VLAN ID</strong>     设置VLAN  ID。批量创建或删除多个连续的VLAN时，中间用短横线连接，如：3-10。<br>
										 
										 
										  <br>
										  &nbsp;<span class="STYLE3">注意：</span>一次最多可创建或删除100个连续的VLAN。 </span>										  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
							   	 <td><table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
                                   <tr height="30">
                                     <th align="left" colspan="2">&nbsp;<a name="vlan_port">端口VLAN</a></th>
                                   </tr>
                                   <tr>
                                     <td height="218"><span class="STYLE1">&nbsp;&nbsp; <strong>端口范围</strong> 页面复选框选择需要修改VLAN属性的二层端口，注意：不能在文本框里手动输入端口名。<br>
                                           <br>
                                       &nbsp;&nbsp; <strong>链路类型</strong> 设置端口的链路类型，默认所有端口都为access。<br>
                                       <br>
                                       &nbsp;&nbsp; <strong>Ingress Filter</strong> 设置是否开启入口过滤。默认关闭。<br>
                                       <br>
                                       &nbsp;&nbsp; <strong>Allowed VLAN ID</strong> 设置端口为trunk类型时允许通过的VLAN ID。<br>
                                       <br>
                                       &nbsp;&nbsp; <strong>Default VLAN ID</strong> 设置端口的默认VLAN ID ，trunk类型端口默认VLAN ID只能为1。<br>
                                       <br>
                                       &nbsp;<span class="STYLE3">注意：</span>需先修改端口的链路类型，才能设置Allowed VLAN、Default VLAN等值。</span> </td>
                                   </tr>
                                 </table></td>
							   </tr>
							  
							  
							   
							   
							   
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="lacp_static">静态汇聚</a></th>
								        </tr>
									    <tr>
									      <td height="159"><span class="STYLE1">&nbsp;静态汇聚将多个物理端口手工捆绑到一个逻辑端口从而增加在交换机和网络节点之间的带宽，静态汇聚不允许系统自动添加或删除汇聚组中的端口，汇聚组中至少包含一个端口。<br>
										  <br>&nbsp;&nbsp; <strong>端口列表</strong>     选择需要进行静态汇聚的端口，可以多选。<br>
										  <br>&nbsp;&nbsp; <strong>创建汇聚组</strong>   设置创建静态汇聚的ID。<br>
										  <br>&nbsp;<span class="STYLE3">注意：</span>静态汇聚组列表可以查看和删除相关静态组。</span>								  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   
							   
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mstpbasic">MSTP基本信息</a></th>
								        </tr>
									    <tr>
										  <td height="118"><span class="STYLE1">&nbsp;生成树协议是一种二层管理协议，它通过选择性地阻塞网络中的冗余链路来消除二层环路，同时还具备链路备份的功能。MSTP（Multiple Spanning Tree Protocol，多生成树协议）可以弥补STP和RSTP的缺陷，它既可以快速收敛，也能使不同VLAN的流量沿各自的路径转发，从而为冗余链路提供了更好的负载分担机制。<br>
										  <br>&nbsp;&nbsp; 显示MSTP域的配置信息、实例VLAN的对应关系及端口在实例里角色状态等信息。</span>								  </td>
									    </tr>
								      </table>							       </td>
							   </tr><tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mstpinfor">生成树信息</a></th>
								        </tr>
									    <tr>
									      <td height="50"><span class="STYLE1">&nbsp;显示生成树桥的基本信息及端口的生成树相关的属性信息。</span><br></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mstpinstance">实例信息</a></th>
								        </tr>
									    <tr>
									      <td height="57"><span class="STYLE1">&nbsp;显示实例桥的信息及端口与实例映射信息。</span><br></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mstpcfg">生成树配置</a></th>
								        </tr>
									    <tr>
									      <td height="158"><span class="STYLE1">&nbsp;&nbsp; <strong>MSTP开关</strong>      使能/去使能MSTP协议状态。默认开启。<br>
										  <br>&nbsp;&nbsp; <strong>实例配置</strong>        设置VLAN和实例的映射关系。<br>
										  <br>&nbsp;&nbsp; <strong>MST配置</strong>       MSTP相关全局参数的配置。<br>
										  <br>&nbsp;&nbsp; <strong>实例VLAN关系</strong>  显示实例和VLAN的映射关系。</span>									  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mstpportcfg">端口配置</a></th>
								        </tr>
									    <tr>
									      <td height="50"><span class="STYLE1">&nbsp;配置端口MSTP相关属性，包括路径开销、优先级、portfast、根保护等属性。</span><br></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="eaps">eaps管理</a></th>
								        </tr>
									    <tr>
									      <td height="111"><span class="STYLE1">&nbsp;EAPS是一个用于以太网环路保护的链路层协议。它在以太网环完整时能够防止数据环路引起的广播风暴，而当以太网环上一条链路断开时能迅速恢复环网上各个节点之间的通信通路。
配置EAPS协议有关参数，包括环ID、模式、extreme兼容性、主端口、第二端口、控制VLAN、保护VLAN、失效时间和hell 时间。<br>
                                           
                                          <br>&nbsp;<span class="STYLE3">注意：</span>需正确配置完EAPS所有属性才能使能该环，修改环的所有属性必须关闭该环。</span>										  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   
							   
							  
							  
							  
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="ip">IP地址</a></th>
								        </tr>
									    <tr>
									      <td height="87"><span class="STYLE1">&nbsp;设置管理VLAN的IP地址。<br>
										  <br>&nbsp;<span class="STYLE3">注意：</span>修改IP地址会导致当前连接断掉。</span>								  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="user">用户管理</a></th>
								        </tr>
									    <tr>
									      <td height="86"><span class="STYLE1">&nbsp;修改当前用户的密码<br>
										  <br>&nbsp;<span class="STYLE3">注意：</span>只能修改当前用户对应的密码。</span>									  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="reset">重启交换机</a></th>
								        </tr>
									    <tr>
									      <td height="93"><span class="STYLE1">&nbsp;重启交换机<br>
										  <br>&nbsp;&nbsp; 重启当前设备，可以保存当前配置或不保存配置重启。</span>								  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="snmp">SNMP配置</a></th>
								        </tr>
									    <tr>
										  <td height="237"><span class="STYLE1">&nbsp;SNMP（Simple Network Management Protocol，简单网络管理协议），用于保证管理信息在网络中任意两点间传送，便于网络管理员在网络上的任何节点检索信息、修改信息、定位故障、完成故障诊断、进行容量规划和生成报告。<br>
										  <br>&nbsp;&nbsp; <strong>团体名</strong>         设置SNMP的community团体名及读写权限。<br>
										  <br>&nbsp;&nbsp; <strong>管理员标识</strong>     系统管理维护联系信息。<br>
										  <br>&nbsp;&nbsp; <strong>SNMP traps</strong>    使能/不使能SNMP traps功能。<br>
										  <br>&nbsp;&nbsp; <strong>Traps主机</strong>      trap主机IP地址配置及对应团体名设置。<br>
										  <br>&nbsp;&nbsp; <strong>设备位置</strong>        设备物理位置信息。</span><br>
										  <br>&nbsp;<span class="STYLE1"><span class="STYLE3">注意：</span>团体名列表和traps列表可以查看和删除相关信息。 </span></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="sntp">系统时间</a></th>
								        </tr>
									    <tr>
									      <td height="73"><span class="STYLE1">&nbsp;设置设备系统时间、时区、SNTP主服务器和SNTP从服务器等信息。<br>
									      &nbsp;<span class="STYLE3">注意：</span>系统不支持RTC时不能保存系统时间。</span>									  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="log">日志输出</a></th>
								        </tr>
									    <tr>
									      <td height="71"><span class="STYLE1">&nbsp;查看当前设备日志输出信息。</span><br></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="ping">ping检测</a></th>
								        </tr>
									    <tr>
									      <td height="77"><span class="STYLE1">&nbsp;Ping命令检测目的IP地址的可达性。</span><br></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="tracert">traceroute</a></th>
								        </tr>
									    <tr>
									      <td height="72"><span class="STYLE1">&nbsp;Traceroute命令检测达到目的IP地址所经过的路径信息。</span><br></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="upgrade">文件管理</a></th>
								        </tr>
									    <tr>
									      <td height="159"><span class="STYLE1">
										 &nbsp;&nbsp; <strong>上传配置文件</strong>       通过TFTP将交换机的配置文件上传到pc机。<br> 
										  <br>&nbsp;&nbsp; <strong>下载当前配置文件</strong>  通过TFTP将配置文件从pc机导到交换机上。<br>
										  <br>&nbsp;&nbsp; <strong>恢复出厂配置</strong>       所有配置信息将恢复到出厂时的状况，交换机将自动重新启动。<br>
										  <br>&nbsp;&nbsp; <strong>保存所有配置</strong>       将当前的配置信息保存到配置文件中。</span>								  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
				    </table>
					<!-- 具体内容 end -->

				</td>
            </tr>
        </td></tr>
        
    </table>
</body>
</html>
