<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<%  var authmode; checkCurMode(); %>

<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<title>&nbsp;<script>writemsg(<% write(lang); %>,"端口镜像");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript" type="text/JavaScript">

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function del_igs(ports,vlanid)
{
		
		//alert(ports);
//	alert(vlanid);

		
		var hid = document.macdel;
		
		hid.port_value.value=ports;
		hid.vlan_value.value=vlanid;
 
		
		
		hid.action="/goform/delIgsRouter"
		hid.submit();
		return 0;
}


function IgsDisplay(ports, vlanid)
{	
    var narr=3;
    var tbtd;
    var i;
    var tbtr = document.getElementById("mirror_table3").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");
	tbtr.setAttribute("id", "tr_"+vlanid);

	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
		
	/*display*/
	tbtr.cells[0].innerHTML = ports;
	tbtr.cells[1].innerHTML = vlanid;

	if (<% write(authmode); %> == 1)
		tbtr.cells[2].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick=del_igs('"+ports+"','"+vlanid+"')>";	
	else
		tbtr.cells[2].innerHTML = " ";
}


function selectToAll() 
{  
		var cf = document.forms[0];
		var objs = document.getElementsByName("checkbox_index"); 
		var i;
		if(cf.check_all.checked == true)
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
		       if(objs[i].disabled==false && objs[i].checked==false){
		         objs[i].checked = true;  
			 		addToPortRange(objs[i]);
			 }
		    }
		}
		else
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
					if(objs[i].checked==true){
					objs[i].checked = false;  
					 addToPortRange(objs[i]);
			}
		         
		    }
		} 
    
}


function checkup()
{
	var tf = document.vlan_port;
	tf.action = "/goform/AddIgmpRouter";
	tf.submit();
}

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var target = document.getElementById("portNum");
	var avid = document.getElementById("avid");
	var dvid = document.getElementById("dvid");
	var porttype = document.getElementById("porttype");
	var filter = document.getElementById("filter");
	var type_value = document.getElementById("type_value");
	var filter_value = document.getElementById("filter_value");
	//var vlan_port_id = document.getElementById("vlan_port_id");
	var p = obj.value;

	if(obj.checked){
		target.value =target.value + p + " ";
		//vlan_port_id.value =vlan_port_id.value + p + " , ";
		
	

	}
	else
	{
		target.value = target.value.replace(p + " ","");
		//vlan_port_id.value = vlan_port_id.value.replace(p + " , ","");
	}

}


function changebgcolor_mirror_table3(){
 var tab = document.all.mirror_table3;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {

	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
function showHelpinfo()
{
   showHelp('mirror',<% write(lang); %>);
}

function refreshpage()
{
  location.href='igmprouter.asp?ltime='+<% write(lltime); %>;
}
</script>
</head>

<body  onLoad=" " ><br>

<% web_get_stat(); %>
<script>
checktop(<% write(lang); %>);
</script>
	<form name="vlan_port" method="POST" action="/goform/AddIgmpRouter">
	<input type="hidden" name="left_menu_id" value="@left_menu_id#">
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
    <input type="hidden" name="portNum" id="portNum">
<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>


 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>IGMP Snooping设置/IGMP路由端口</b></font></td></tr>
 </table>
 
<!--    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
                    <tr>
                <td height="30px" align="left" colspan="2"   class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"IGMP路由端口");</script></td>
              </tr>
        </table>
-->
        
       </td></tr>
       <tr><td height="130"> 
         <table   width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord" >
  
              
             <tr>
               <td width="15%" height="30"  rowspan="3" class="crons" >&nbsp;<script>writemsg(<% write(lang); %>,"路由端口");</script></td>
               <td align="left" class="crons">&nbsp;<% AppendOptionMirrored(); %></td>
             </tr>
		
             
             <tr>
               <td width="15%" height="30" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN");</script></td>
               <td align="left" class="crons">     <input type="text" name="rang_vlan" id="rang_vlan">
  </td>
             </tr>
			

           
             <tr>
			 	<td colspan="2" align="center" class="crons">
			 	<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","modify","checkup()");</script>
<!--
			 	<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify","alterMirror(1)");</script>
			 	<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","modify","alterMirror(2)");</script>				
-->
			 	</td>			 	
			 </tr>
    </table></td></tr>
		<tr>
         <td height="8"></td>
      </tr>
     <tr> 
         <td colspan="6">
             <table id="mirror_table3" width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="tablebord">
                <tr>
                   <td align="center" height="30px" class="td6" width="30%"><b>&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></b></td>
                   <th align="center" height="30px" class="td6" width="40%"><b>&nbsp;<script>writemsg(<% write(lang); %>,"VLAN");</script></b></th>
                   <td align="center" height="30px" class="td6" width="30%"><b>&nbsp;<script>writemsg(<% write(lang); %>,"删除");</script></b></td>
                </tr>
                <script>
					
					<% var errorcode; IgmpRouterShows(); %>
					
				</script>
             </table>
         </td>
     </tr>
     <td colspan="6" align="center" height="35">
		<script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
    </table>
    
 </td></tr> 
 
</table>
</td></tr>


</table>

<input type="hidden" name="switch_port" value="26">
<input type="hidden" name="h_mirror_enable" value="@h_mirror_enable#" >
<input name="s" value="@s#" type="hidden">
<input type="hidden" name="h_capture_port" value="@h_capture_port#" > 
<input type="hidden" name="h_mirror_capture_type" value="@h_mirror_capture_type#" > 
<input type="hidden" name="loader" value="@loader#" > 
<input type="hidden" name="todo" value="save">
<input type="hidden" name="this_file" value="mirror.html">
<input type="hidden" name="next_file" value="mirror.html">
<input type="hidden" name="message" value="@msg_text#">
<input type="hidden" name="mirror_table" value="@mirror_table#">
</form>   
<script>
changebgcolor();
changebgcolor_mirror_table3();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>


<form name="macdel" method="POST" action="">
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
	<input type="hidden" name="port_value"  value="" >
	<input type="hidden" name="vlan_value"  value="" >
	



</form>
</body>
</html>
