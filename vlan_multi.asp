<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<%  var authmode,errorcode,portStaList; checkCurMode();multiSetShow(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"GMRP");</script></title>
 
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript">

function display()
{
	tmp = <% write(portStaList); %>;
	if(tmp == "disable")
	{
		document.vlan_port.optaction.value = 1;
	}else
	{
		document.vlan_port.optaction.value = 0;
	}
}
 
function checking2()
{
 	//var port_range = document.getElementById("port_range").value;
 
  	//var checkbox_index = document.getElementsByName("checkbox_index");
	//var table_port = document.getElementById("table_port");
	var tf=document.vlan_port;
	//var i,j;


	 
			tf.submit();
 

	//if(port_txrate.length==0)port_txrate="-";
	////if(port_txburst.length==0)port_txburst="-";
	//if(port_rxrate.length==0)port_rxrate="-";	    
	//if(port_rxburst.length==0)port_rxburst="-";	
	 

}




function addToPortRange(index)
{
	//alert(index);

	
	var target = document.getElementById("port_range");
	var mac_value = document.getElementById("mac_range");
	var vlan_speed = document.getElementById("vlan_range");
	
//	var port_flow_t = document.getElementById("port_flow_t");
//	var port_flow_r = document.getElementById("port_flow_r");
//	var port_mtu = document.getElementById("port_mtu");
//	var port_description=document.getElementById("port_description");

     var objs = document.getElementsByName("checkbox_index"); 

	if(objs[index].checked){

		target.value = portStaList[3*index];
		mac_value.value = portStaList[3*index+1];
		vlan_speed.value = portStaList[3*index+2];
 
	}else{

		target.value = "";
		mac_value.value ="";
		vlan_speed.value ="";
 	}

}

function P(portId,enable)
{
    var narr=2;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+portId);	


    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		
	        tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
    tbtr.cells[0].abbr = portId;
    
   
	

		//tbtr.cells[0].innerHTML = "<input type=\"checkbox\" id=\"input_"+portId+"\" name=\"checkbox_index\" value=\""+portId+"\" onclick=\"addToPortRange(this)\"/>";
	
		tbtr.cells[0].innerHTML = portId;
    tbtr.cells[1].innerHTML = enable;


}


function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function refreshpage()
{
  location.href='vlan_multi.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('vlan_port',<% write(lang); %>);
}

</script>
</head>

<body  onload="display()"><br>


<script>
checktop(<% write(lang); %>);
</script>
<form name="vlan_port" method="POST" action="/goform/multiSet">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">

<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
     	 
 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>组播选项</b></font></td></tr>
 </table>
      
    </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%" id="table1" class="tablebord">


		<tr height="25">
			<td width="18%" class="crons">&nbsp;未知组播丢弃</td>
			<td width="7%" class="crons" >&nbsp;
			<select name="optaction" id="optaction">
			<option value="0" >Enable</option>
			<option value="1" >Disable</option>
			</select></td>
			<td class="crons">&nbsp;<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify","checking2()");</script>
			</td>
		</tr>
	          
	  </table></td>
   </tr>
     </table>
   </td></tr>
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>

<script>
changebgcolor();
changebgcolor_name("vlan_port");
</script>
</body> 
<script>
<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</html>

