<!DOCTYPE html>
<html>

<head>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<% var authmode; checkCurMode(); %>

			<meta http-equiv="Content-Type" content="text/html; charset=utf8">
			<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
			<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
			<link href="css/display_web.css" rel="stylesheet" type="text/css" />
			<title>&nbsp;
				<script>writemsg(<% write(lang); %>, "VLan Security");</script>
			</title>

			<script>

				function writebutton2(priv, lang, key, bclass, btype, bname, benable, bfunc) {
					var outputstr = "";
					if ((priv == 1) || (priv == 5))
						outputstr = "<input class=" + bclass + " id='" + bname + "' " + benable + " name='" + bname + "' type='" + btype + "' value='" + putmsg(lang, key) + "' onClick='return " + bfunc + ";'>";
					else
						outputstr = "&nbsp;";
					document.write(outputstr);

				}

				function showHelp(helpname, lang) {
					var tmp = lang + "_help.html#" + helpname;
					window.open(tmp);
				}


				var portStaList = [<% vlansecurityMaxShow();%>];

				function delmac(i) {
					var hid = document.vlan_port;

					hid.port_range.value = portStaList[3 * i + 2];
					hid.mac_max.value = portStaList[3 * i + 1];
					hid.vlan_id.value = portStaList[3 * i];
					hid.del_flag.value = 1;
					hid.action = "/goform/vlanSecurityMax";
					hid.submit();
					return 0;

				}
				function writeLines() {
					var j = 0;
					for (var i = 0; i < portStaList.length / 3; i++) {
						document.write(" <tr  class='tables_all'>");


						document.write("    <td class='inputsyslog1'>" + portStaList[j] + "</td>");

						j++
						document.write("    <td  class='inputsyslog1'>" + portStaList[j] + "</td>");
						j++;
						document.write("    <td  class='inputsyslog1'>" + portStaList[j] + "</td>");
						j++;

						document.write("    <td  class='inputsyslog1'>");

						if (<% write(authmode); %> == 1)
						document.write("      <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick='delmac(" + i + ")'  />");

						document.write("      </td>");

						document.write("  </tr>");

					}
				}

				function AddOption(portname) {

					var selectObject = document.getElementById("port_range");
					var y = document.createElement('option');
					y.text = portname;
					y.value = portname;
					try {
						selectObject.add(y, null); // standards compliant
					}
					catch (ex) {
						selectObject.add(y); // IE only
					}
				}


				function checking2() {
					var tf = document.vlan_port;


					var vlan_id = tf.vlan_id.value;

					if (vlan_id < 1 || vlan_id > 4094) {
						alert('Invaid value, vlan id effective range: 1~4094！');
						tf.vlan_id.focus();
						return false;
					}

					var mac_max = tf.mac_max.value;

					if (mac_max < 0 || mac_max > 1024) {
						alert('Invaid value, mac limit effective range: 0~1024');
						tf.mac_max.focus();
						return false;
					}

					tf.submit();
				}

				function changebgcolor_name(value) {

					var tab = document.getElementById(value);
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								// tab.rows[i].cells[j].className = "all_tables" ;
							}
							else {
								// tab.rows[i].cells[j].className = "all_tables" ;
							}

						}

					}
				}


				function changebgcolor_port() {
					var tab = document.all.table_port;
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								tab.rows[i].cells[j].className = "all_tables";
							}
							else {
								tab.rows[i].cells[j].className = "all_tables";
							}

						}

					}
				}

				function changebgcolor() {
					var tab = document.all.table1;
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								tab.rows[i].cells[j].className = "all_tables all_tables2";
							}
							else {
								tab.rows[i].cells[j].className = "all_tables all_tables1";
							}

						}

					}
				}
				function refreshpage() {
					location.href = 'vlansecuritymax.asp?ltime=' +<% write(lltime); %>;
				}

				function showHelpinfo() {
					showHelp('vlan_port',<% write(lang); %>);
				}

			</script>
</head>


<body>
	<br />

	<script>
		checktop(<% write(lang); %>);
	</script>
	<form name="vlan_port" method="POST" action="/goform/vlanSecurityMax" >
		<input name="del_flag" id="del_flag" type="hidden" class="input_x" value="0">
		<input type="hidden" name="ltime" value=<% write(lltime); %>>
		<input type="hidden" name="lastts" value=<% write(serverts); %>>
		<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
		<div class="formContain">
		<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">

			<tr>
				<td>
					<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0">
						<tr>
							<td valign="top">
								<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"
									class="cword09">
									<tr>
										<td>


											<table width="100%" align="center" border="0" cellspacing="0"
												cellpadding="0">
												<tr>
													<td height="30px">
														<font size="5" color="#404040">
															<div class="bot">VLAN MAC地址学习限制</div>
														</font>
													</td>
												</tr>
											</table>
										</td>
									</tr>

									<tr>
										<td>
											<table border="0" cellspacing="0" cellpadding="0" width="100%" id="table1"
												class="tablebord">

												<tr height="30">
													<td width="42%" align="left" class="td7">&nbsp;
														<script>writemsg(<% write(lang); %>, "VLAN ID:");</script>
													</td>
													<td width="58%" align="left" class="td7">&nbsp;
														<input name="vlan_id" type="text" class="input_x"
															id="vlan_id">(1-4094)
													</td>
												</tr>
												<tr height="30">
													<td class="crons">&nbsp;
														<script>writemsg(<% write(lang); %>, "MAC地址限制最大值:");</script>
													</td>
													<td class="crons">&nbsp;
														<input name="mac_max" type="text" class="input_x"
															id="mac_max">(0-1024, 0为取消限制)
													</td>
												</tr>
												<tr height="30">
													<td class="crons">&nbsp;
														<script>writemsg(<% write(lang); %>, "端口选择:");</script>
													</td>
													<td class="crons">&nbsp;
														<select id="port_range" name="port_range"></select>
														<script><% AppendOption(); %> </script>
													</td>
												</tr>
												<tr height="25">

													<td colspan="2" align="middle" style="text-align: center;"
														class="crons">
														<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "设  置", "button", "button", "modify", "checking2()");</script>
													</td>
												</tr>

												
											</table>
										</td>
									</tr>
									

								</table>
							</td>
						</tr>

					</table>
				</td>
			</tr>
		</table>
	</div>
		<div class="formContain" style="margin-top: 15px;">
			<div class="">
				<font size="5" color="#404040">
					<div class="bot">MAC地址学习限制信息</div>
				</font>
			</div>
			<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%" class="tablebord">
				<tr align="center" height="25" class="crons">
					<th class="td2" width="30%">
						<font color="#333333"><b>&nbsp;
								<script>writemsg(<% write(lang); %>, "VLAN ID");</script>
							</b></font>
					</th>
					<th class="td2" width="30%">
						<font color="#333333"><b>&nbsp;
								<script>writemsg(<% write(lang); %>, "MAC地址限制最大值");</script>
							</b></font>
					</th>
					<th class="td2" width="30%">
						<font color="#333333"><b>&nbsp;
								<script>writemsg(<% write(lang); %>, "端口");</script>
							</b></font>
					</th>
					<th class="td2" width="10%">&nbsp;
						<script>writemsg(<% write(lang); %>, "删除");</script>
					</th>
				</tr>

				<script language="javascript">
					writeLines();

				</script>
			</table>
			<div class=""  align="center">
				<script>writebutton(1,<% write(lang); %>, "刷  新", "button", "button", "Refresh", "refreshpage()");</script>
			</div>
		</div>
	</form>

	<script>
		changebgcolor();
		changebgcolor_name("table_port_vlan");

<% if (errorcode != "") { write_errorcode(errorcode); } %>
	</script>
</body>

</html>