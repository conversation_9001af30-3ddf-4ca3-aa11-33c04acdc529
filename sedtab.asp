<!DOCTYPE html>
<html>

<head>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<% var authmode; checkCurMode(); %>

			<meta http-equiv="Content-Type" content="text/html; charset=utf8">
			<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
			<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
			<script src="js/alpinejs.min.js" defer></script>
			<link href="css/display_web.css" rel="stylesheet" type="text/css" />
			<title>&nbsp;
				<script>writemsg(<% write(lang); %>, "MAC地址绑定");</script>
			</title>

			<script language="JavaScript">
				var boardType = <% getSysCfg(); %>;
			</script>
			<script>

				function showHelp(helpname, lang) {
					var tmp = lang + "_help.html#" + helpname;
					window.open(tmp);
				}

				/*by LuoM  11-5-18*/
				function MacCheck(mac_addr) {
					var myRE = /^[0-9a-fA-F]{1,4}\.[a-fA-F0-9]{1,4}\.[a-fA-F0-9]{1,4}$/;
					if (myRE.test(mac_addr)) {
						var val = mac_addr.split(".", 1);
						var vals = val[0].split("");
						if (val[0].length == 3) {
							if (vals[0] < 10 && vals[0] >= 0) {
								if (vals[0] % 2 == 1) {
									return false;
								}
							}
							else {
								if ((vals[0].charCodeAt() + 1) % 2 == 1) {
									return false;
								}
							}
						}
						if (val[0].length == 4) {
							if (vals[1] < 10 && vals[1] >= 0) {
								if (vals[1] % 2 == 1) {
									return false
								}
							}
							else {
								if ((vals[1].charCodeAt() + 1) % 2 == 1) {
									return false
								}
							}
						}
						return true;
					}
					else {
						return false;
					}

				}

				function SelectIsExitItem(objSelect, objItemValue) {
					var isExit = false;
					for (var i = 0; i < objSelect.options.length; i++) {
						if (objSelect.options[i].value == objItemValue) {
							isExit = true;
							break;
						}
					}
					return isExit;
				}

				/*by LuoM  11-5-18*/
				function DataScope(value, max, min) {
					if (!isNaN(value) && value >= min && value <= max)
						return true;
					else
						return false;
				}

				function changebgcolor_name(value) {

					var tab = document.getElementById(value);
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables";
							}
							else {
								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables";
							}

						}

					}
				}


				function changebgcolor() {
					var tab = document.all.table1;
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables";
							}
							else {
								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables all_tables3";
							}

						}

					}
				}
				function changebgcolor2() {
					var tab = document.all.table2;
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables";
							}
							else {
								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables all_tables3";
							}

						}

					}
				}
				function changebgcolor22() {
					var tab = document.all.table22;
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (j % 2 == 1) {

								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables";
							}
							else {
								// tab.rows[i].cells[j].bgColor = "efefef";
								tab.rows[i].cells[j].className = "all_tables all_tables3";
							}

						}

					}
				}
				function del_mac(mac, vlan) {
					var hid = document.macdel;

					hid.mac_value.value = mac;
					hid.vlan_value.value = vlan;


					hid.action = "/goform/Umacdelsingle";
					hid.submit();
					return 0;
				}
				function add_mac(mac, vlan, port) {
					var hid = document.SedTabsetting;

					document.getElementById("forfil_mac").value = mac;
					document.getElementById("forfil_vlan").value = vlan;
					document.getElementById("forfil_port").value = port;

					hid.action = "/goform/AddForfil";
					hid.submit();
					return 0;
				}


				function getPage(page) {
					location.href = "sedtab.asp?page=" + page + "&ltime=" +<% write(lltime); %>;
				}

				function dofirst() {

					location.href = "sedtab.asp?page=1&ltime=" +<% write(lltime); %>;

				}

				function p(index, SedTab_vlan, SedTab_mac, SedTab_type, SedTab_port) {
					var narr = 7;
					var tbtd;
					var i;
					var tbtr = document.getElementById("SedTab_tbl").insertRow(-1);

					tbtr.classname = "crons";
					tbtr.height = "30";

					tbtr.setAttribute("height", "30");
					tbtr.setAttribute("class", "crons");
					tbtr.setAttribute("className", "crons");

					/*Cycle respectively and setting attributes*/
					for (i = 0; i < narr; i++) {
						tbtd = document.createElement("td");

						tbtd.align = "center";
						tbtd.setAttribute("class", "td2");
						tbtd.setAttribute("className", "td2");
						tbtr.appendChild(tbtd);
					}

					/*display*/
					tbtr.cells[0].innerHTML = index;
					tbtr.cells[1].innerHTML = SedTab_vlan;
					tbtr.cells[2].innerHTML = SedTab_mac;
					tbtr.cells[3].innerHTML = SedTab_type;
					tbtr.cells[4].innerHTML = SedTab_port;

					if ((SedTab_port != "CPU") && (SedTab_type != "bind") && (<% write(authmode); %> == 1) && (SedTab_type != "dynamic"))
					{
						tbtr.cells[5].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick=del_mac('" + SedTab_mac + "','" + SedTab_vlan + "')>";
						tbtr.cells[6].innerHTML = " &nbsp";
					}
	else if ((SedTab_type == "dynamic") && (<% write(authmode); %> == 1))
					{
						tbtr.cells[5].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick=del_mac('" + SedTab_mac + "','" + SedTab_vlan + "')>";
						tbtr.cells[6].innerHTML = " <input type='button' name='button4' id='button4' class='botton_under_line' value='添加静态' onclick=add_mac('" + SedTab_mac + "','" + SedTab_vlan + "','" + SedTab_port + "')>";
					}
	else
					{
						tbtr.cells[5].innerHTML = " &nbsp";
						tbtr.cells[6].innerHTML = " &nbsp";
					}


				}



				function AddOptionForfilPort(port_name) {
					var selectObject = document.getElementById("forfil_port");
					if (SelectIsExitItem(selectObject, port_name) == false) {
						var y = document.createElement('option');
						y.text = port_name;
						y.value = port_name;
						try {
							selectObject.add(y, null); // standards compliant
						}
						catch (ex) {
							selectObject.add(y); // IE only
						}
					}
				}


				function selectForfil() {
					var tf = document.SedTabsetting;
					var forfil_mac = document.getElementById("forfil_mac").value;
					var forfil_vlan = document.getElementById("forfil_vlan").value;
					if (forfil_mac == '0000.0000.0000') {
						alert(putmsg(<% write(lang); %>, "输入MAC地址错误"));
						return
					}
					if (MacCheck(forfil_mac)) {
						if (DataScope(forfil_vlan, 4094, 1)) {
							tf.action = "/goform/AddForfil";
							tf.submit();
						}
						else {
							alert(putmsg(<% write(lang); %>, "vlan 的范围在1-4094"));
						}
					}
					else {
						alert(putmsg(<% write(lang); %>, "输入MAC地址非法  格式为16进制：HHHH.HHHH.HHHH"));
					}

				}



				function updataTime() {
					var tf = document.SedTabsetting;
					var agetime = document.getElementById("age_time");
					if (agetime.value == "") {
						alert(putmsg(<% write(lang); %>, "没有输入老化时间，请输入数字!"));
						return;
					}
					if (!isNaN(agetime.value)) {
						if ((agetime.value >= 10 && agetime.value <= 1000000) || agetime.value == 0) {
							tf.action = "/goform/UpTime";
							tf.submit();
						}
						else {
							alert(putmsg(<% write(lang); %>, "输入的数必须在10-1000000之间,或为 0 "));
						}
					}
					else {
						alert(putmsg(<% write(lang); %>, "输入必须为数字!"));
					}
				}
				function delSedTab(x) {
					var tf = document.SedTabsetting;
					var SedTab_tbl = document.getElementById("SedTab_tbl");
					var selid = document.getElementById("selid");
					var i = 1, j = 0;
					switch (x) {
						case 1:
							var vlan_del = document.getElementById("vlan_del");
							selid.value = 1;
							for (; i < SedTab_tbl.rows.length; i++) {
								if (SedTab_tbl.rows[i].cells[0].innerHTML == vlan_del.value) {
									j = 0;
									break;
								}
								j = 1;
							}
							/*by FC-fcy 2012-4-23  根据VLAN批量删除*/
							if (j == 1) {
								alert(putmsg(<% write(lang); %>, "你输入的vlan不存在!"));
								return;
							}
							/*by FC-LuoM 2012-5-14 start*/
							for (; i < SedTab_tbl.rows.length; i++) {
								if (SedTab_tbl.rows[i].cells[2].innerHTML == "dynamic") {
									j = 0;
									break;
								}
							}
							/*by FC-LuoM 2012-5-14 end*/
							if (j == 1) {
								alert(putmsg(<% write(lang); %>, "不能删除static类型项!"));
								return;
							}

							tf.action = "/goform/DelSedTab";
							tf.submit();
							/*by FC-fcy 2012-4-23 end*/

							break;
						case 2:
							var port_del = document.getElementById("port_del");
							selid.value = 2;
							for (i; i < SedTab_tbl.rows.length; i++) {
								if (SedTab_tbl.rows[i].cells[3].innerHTML == port_del.value) {
									j = 0;
									break;
								}
								j = 1;
							}
							/*by FC-fcy 2012-4-23  根据端口批量删除*/
							if (j == 1) {
								alert(putmsg(<% write(lang); %>, "你输入的端口不存在!"));
								return;
							}
							/*by FC-LuoM 2012-5-14 start*/
							for (; i < SedTab_tbl.rows.length; i++) {
								if (SedTab_tbl.rows[i].cells[2].innerHTML == "dynamic") {
									j = 0;
									break;
								}
							}
							/*by FC-LuoM 2012-5-14 end*/
							if (j == 1) {
								alert(putmsg(<% write(lang); %>, "不能删除static类型项!"));
								return;
							}

							tf.action = "/goform/DelSedTab";
							tf.submit();
							/*by FC-fcy 2012-4-23 end*/
							break;
						case 3:
							var mac_del = document.getElementById("mac_del");
							selid.value = 3;
							for (i; i < SedTab_tbl.rows.length; i++) {
								if (SedTab_tbl.rows[i].cells[1].innerHTML == mac_del.value) {
									j = 0
									break;
								}
								j = 1;
							}
							/*by FC-fcy 2012-4-23  根据MAC地址批量删除*/
							if (j == 1) {
								alert(putmsg(<% write(lang); %>, "你输入的MAC地址不存在!"));
								return;
							}
							/*by FC-LuoM 2012-5-14 start*/
							for (; i < SedTab_tbl.rows.length; i++) {
								if (SedTab_tbl.rows[i].cells[2].innerHTML == "dynamic") {
									j = 0;
									break;
								}
							}
							/*by FC-LuoM 2012-5-14 end*/
							if (j == 1) {
								alert(putmsg(<% write(lang); %>, "不能删除static类型项!"));
								return;
							}

							tf.action = "/goform/DelSedTab";
							tf.submit();
							/*by FC-fcy 2012-4-23 end*/
							break;
						case 4:
							selid.value = 4;
							tf.action = "/goform/DelSedTab";
							tf.submit();
							break;

					}


				}

				function checkData(x) {
					var tf = document.SedTabsetting;
					tf.action = "/goform/saveComm?name=sedtab";
					tf.submit();
				}


				var portStaList = [<% portsecurityMaxShow();%>];

				function delmac(i) {
					var hid = document.formaaa;

					hid.port_range.value = portStaList[2 * i];
					hid.action = "/goform/PortSecurityMax";
					hid.tabName.value = 'tab2'
					hid.submit();
					return 0;
				}

				function addToVLanRange(obj) {
					var port_range = document.getElementById("port_range");
					var mac_range = document.getElementById("mac_range");

					var p = obj.value;

					if (obj.checked) {
						port_range.value = portStaList[2 * p];
						mac_range.value = portStaList[2 * p + 1];
					}
				}



				function writeLines() {
					var j = 0;
					for (var i = 0; i < portStaList.length / 2; i++) {
						document.write(" <tr  class='tables_all'>");

						document.write("    <td  class='inputsyslog1'>");

						document.write("      <input type='radio' name='checkbox_index'  class='botton_under_line' value=\"" + i + "\" onclick='addToVLanRange(this)'  />");

						document.write("      </td>");

						document.write("    <td class='inputsyslog1'>" + portStaList[j] + "</td>");

						j++
						document.write("    <td  class='inputsyslog1'>" + portStaList[j] + "</td>");
						j++;

						document.write("    <td  class='inputsyslog1'>");

						if (<% write(authmode); %> == 1)
						document.write("      <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick='delmac(" + i + ")'  />");

						document.write("      </td>");

						document.write("  </tr>");

					}
				}

				function checking2() {
					var tf = document.vlan_port;
					var mac_range = tf.mac_range.value;

					if (mac_range < 0 || mac_range > 32766) {
						alert('门限值错误，有效范围是 0-32766！');
						tf.mac_range.focus();
						return false;
					}
					tf.tabName.value = 'tab2'
					tf.submit();
				}


				function AddOptionPortRange(portname) {

					var selectObject = document.getElementById("port_range");
					if (SelectIsExitItem(selectObject, portname) == false) {
						var y = document.createElement('option');
						y.text = portname;
						y.value = portname;
						try {
							selectObject.add(y, null); // standards compliant
						}
						catch (ex) {
							selectObject.add(y); // IE only
						}
					}
				}

				function addToPortRange(index) {
					var target = document.getElementById("port_range");
					var mac_value = document.getElementById("mac_range");
					var vlan_speed = document.getElementById("vlan_range");
					var objs = document.getElementsByName("checkbox_index");

					if (objs[index].checked) {

						target.value = portStaList[3 * index];
						mac_value.value = portStaList[3 * index + 1];
						vlan_speed.value = portStaList[3 * index + 2];

					} else {

						target.value = "";
						mac_value.value = "";
						vlan_speed.value = "";
					}

				}

				function showHelp(helpname, lang) {
					var tmp = lang + "_help.html#" + helpname;
					window.open(tmp);
				}



				var portStaMacList = [<% portsecurityMacShow();%>];

				function delmacsecurity(i) {

					var hid = document.formaa;

					hid.port_security_range.value = portStaMacList[3 * i];
					hid.mac_security_range.value = portStaMacList[3 * i + 1];
					hid.vlan_security_range.value = portStaMacList[3 * i + 2];
					hid.tabName.value = 'tab3'

					hid.action = "/goform/PortSecurityMac";
					hid.submit();
					return 0;

				}

				function writeLinessecurity() {
					var j = 0;
					for (var i = 0; i < portStaMacList.length / 3; i++) {
						document.write(" <tr  class='tables_all'>");
						document.write("    <td class='inputsyslog1'>" + portStaMacList[j] + "</td>");
						j++
						document.write("    <td  class='inputsyslog1'>" + portStaMacList[j] + "</td>");
						j++;
						document.write("    <td  class='inputsyslog1'>" + portStaMacList[j] + "</td>");
						j++;
						document.write("    <td  class='inputsyslog1'>");
						if (<% write(authmode); %> == 1)
						document.write("      <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick='delmacsecurity(" + i + ")'  />");
						document.write("      </td>");
						document.write("  </tr>");
					}
				}



				/*select ALL*/
				function selectToAll() {
					var cf = document.forms[0];
					var objs = document.getElementsByName("checkbox_security_index");
					var i;
					if (cf.check_all.checked == true) {
						for (i = 0; i < objs.length; i++) {
							if (objs[i].disabled == false && objs[i].checked == false) {
								objs[i].checked = true;
								addToPortRangeSecurity(i);
							}
						}
					}
					else {
						for (i = 0; i < objs.length; i++) {
							if (objs[i].checked == true) {
								objs[i].checked = false;
								addToPortRangeSecurity(i);
							}

						}
					}

				}

				function checking3() {
					var tf = document.vlan_port_Security;
					var mac_addr = tf.mac_security_range.value;
					var vlan_id = tf.vlan_security_range.value;

					if (mac_addr == '0000.0000.0000') {
						alert(putmsg(<% write(lang); %>, "输入MAC地址错误"));
						return
					}
					if (MacCheck(mac_addr)) {
						if (DataScope(vlan_id, 4094, 1)) {
							tf.tabName.value = 'tab3'
							tf.submit();
						}
						else {
							alert(putmsg(<% write(lang); %>, "vlan 的范围在1-4094"));
						}
					}
					else {
						alert(putmsg(<% write(lang); %>, "输入MAC地址非法  格式为16进制：HHHH.HHHH.HHHH"));
					}
				}

				function AddOptionPortSecurityRange(portname) {

					var selectObject = document.getElementById("port_security_range");
					if (SelectIsExitItem(selectObject, portname) == false) {
						var y = document.createElement('option');
						y.text = portname;
						y.value = portname;
						try {
							selectObject.add(y, null); // standards compliant
						}
						catch (ex) {
							selectObject.add(y); // IE only
						}
					}
				}



				function addToPortRangeSecurity(index) {
					//alert(index);
					var target = document.getElementById("port_security_range");
					var mac_value = document.getElementById("mac_security_range");
					var vlan_speed = document.getElementById("vlan_security_range");

					//	var port_flow_t = document.getElementById("port_flow_t");
					//	var port_flow_r = document.getElementById("port_flow_r");
					//	var port_mtu = document.getElementById("port_mtu");
					//	var port_description=document.getElementById("port_description");
					var objs = document.getElementsByName("checkbox_security_index");

					if (objs[index].checked) {
						target.value = portStaMacList[3 * index];
						mac_value.value = portStaMacList[3 * index + 1];
						vlan_speed.value = portStaMacList[3 * index + 2];
					} else {
						target.value = "";
						mac_value.value = "";
						vlan_speed.value = "";
					}
				}

				function P(portId, enable) {
					var narr = 2;
					var tbtd;
					var eport = "";
					var gport = "";
					var i;
					var opt;
					var gtrunk = 0;
					var tbtr = document.getElementById("table_port_security").insertRow(-1);

					tbtr.classname = "crons";
					tbtr.height = "30";
					tbtr.setAttribute("height", "30");
					tbtr.setAttribute("class", "crons");
					tbtr.setAttribute("className", "crons");
					tbtr.setAttribute("id", "tr_" + portId);

					for (i = 0; i < narr; i++) {
						tbtd = document.createElement("td");
						tbtd.align = "center";
						tbtd.setAttribute("class", "td2");
						tbtd.setAttribute("className", "td2");
						tbtr.appendChild(tbtd);
					}
					tbtr.cells[0].abbr = 0;
					tbtr.cells[0].abbr = portId;
					//tbtr.cells[0].innerHTML = "<input type=\"checkbox\" id=\"input_"+portId+"\" name=\"checkbox_security_index\" value=\""+portId+"\" onclick=\"addToPortRangeSecurity(this)\"/>";
					tbtr.cells[0].innerHTML = portId;
					tbtr.cells[1].innerHTML = enable;
				}


				function refreshpage(tab) {
					location.href = 'sedtab.asp?tab=' + tab + '&ltime=' +<% write(lltime); %>;
				}

				function AddOption(port_name) {
					AddOptionForfilPort(port_name);
					AddOptionPortRange(port_name);
					AddOptionPortSecurityRange(port_name);
				}
				function getUrlParamTab() {
					let urlSearch = window.location.search
					const search = new URLSearchParams(urlSearch)
					const params = Object.fromEntries(search.entries())
					try {
						if (params.tab) return params.tab
					} catch { }
					return 'tab1'
				}
				var portStaList1 = [<% vlansecurityMaxShow();%>];
				function writeLines6() {
					var j = 0;
					for (var i = 0; i < portStaList1.length / 3; i++) {
						document.write(" <tr  class='tables_all'>");


						document.write("    <td class='inputsyslog1'>" + portStaList1[j] + "</td>");

						j++
						document.write("    <td  class='inputsyslog1'>" + portStaList1[j] + "</td>");
						j++;
						document.write("    <td  class='inputsyslog1'>" + portStaList1[j] + "</td>");
						j++;

						document.write("    <td  class='inputsyslog1'>");

						if (<% write(authmode); %> == 1)
						document.write("      <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick='delmac1(" + i + ")'  />");

						document.write("      </td>");

						document.write("  </tr>");

					}
				}
			</script>
</head>

<body x-data="{active:'tab1'}" x-init="active=getUrlParamTab()"><br>

	<% web_get_stat(); %>
		<script>
			checktop(<% write(lang); %>);
		</script>
		<div>
			<ul class="tabmenu">
				<li id="tab1" :class="active==='tab1'?'tab':'tab-disable'">
					<a herf="#" x-on:click="active='tab1'">静态单播MAC地址表</a>
				</li>
				<li id="tab2" :class="active==='tab2'?'tab':'tab-disable'">
					<a herf="#" x-on:click="active='tab2'">端口MAC地址学习限制</a>
				</li>
				<li id="tab3" :class="active==='tab3'?'tab':'tab-disable'">
					<a herf="#" x-on:click="active='tab3'">端口MAC地址绑定</a>
				</li>
				<li id="tab4" :class="active==='tab4'?'tab':'tab-disable'">
					<a herf="#" x-on:click="active='tab4'">VLAN MAC限制</a>
				</li>
			</ul>
		</div>
		<form x-show="active==='tab1'" name="SedTabsetting" method="POST" action="sedtab.asp">
			<input type="hidden" name="left_menu_id" value="">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input type="hidden" name="selid" id="selid" value="">
			<div class="formContain">
				<table id="mainTb2" width="100%" height="100%" border="0" align="center" cellpadding="0"
					cellspacing="0">
					<tr>
						<td>
							<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td height="30px">
										<font size="5" color="#404040">
											<div class="bot">静态单播MAC地址表设置</div>
										</font>
									</td>
								</tr>
							</table>
							<br>
						</td>
					</tr>
					<tr>
						<td>
							<table width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
								<tr>
									<td valign="top">

										<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"
											id="table2" class="tablebord1">
											<tr>
												<td width="42%" class="all_tables all_tables1">
													&nbsp;VLAN
												</td>
												<td width="58%" class="all_tables all_tables2">
													<input type="number" name="forfil_vlan" id="forfil_vlan"
														class="input_board3" maxlength="12" autocomplete="off" />
													&nbsp;(1-4094)
												</td>
											</tr>
											<tr>
												<td width="42%" class="all_tables all_tables1">&nbsp;
													<script>writemsg(<% write(lang); %>, "MAC地址");</script>
												</td>
												<td width="58%" class="all_tables all_tables2">
													<input type="text" name="forfil_mac" id="forfil_mac">&nbsp;
													<script>writemsg(<% write(lang); %>, "(HHHH.HHHH.HHHH, 为16进制格式)");</script>
												</td>
											</tr>
											<tr>
												<td width="42%" class="all_tables all_tables1">&nbsp;
													<script>writemsg(<% write(lang); %>, "转发端口");</script>
												</td>
												<td width="58%" class="all_tables all_tables2">
													<input type="radio" name="mac_forfil" id="mac_forfil2"
														value="interface" onClick="forfilChange()" style='display:none'>

													<select id="forfil_port" name="forfil_port"></select>
													<script>
						<% AppendOptionForfil(); %>
													</script>
													</label>
													<div style='display:none'>
														<label>
															<input type="radio" name="mac_forfil" id="mac_forfil1"
																value="drop" onClick="forfilChange()">
														</label>
														<font id="all_data">&nbsp;
															<script>writemsg(<% write(lang); %>, "MAC过滤");</script>
														</font>
													</div>
												</td>
											</tr>

											<tr height="30">
												<td colspan="6" align="center" style="padding:5px">
													<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "添  加", "button", "button", "add_arp", "selectForfil()");</script>
												</td>
											</tr>
										</table>



									</td>
								</tr>

							</table>
						</td>
					</tr>

				</table>
			</div>
			<input type="hidden" name="sed_mac" id="sed_mac" value=0>
			<input type="hidden" name="h_arp_enable" value="@h_arp_enable#">
			<input type="hidden" name="todo" value="save">
			<INPUT type="hidden" name="this_file" value="port.html">
			<INPUT type="hidden" name="next_file" value="port.html">
			<input type="hidden" name="message" value="@msg_text#">
			<div class="formContain" style="margin-top: 15px;">
				<div class="">
					<font size="5" color="#404040">
						<div class="bot">静态单播MAC地址表展示</div>
					</font>
				</div>
				<table id="SedTab_tbl" name="SedTab_tbl" border="0" cellspacing="0" cellpadding="0" width="100%"
					class="tablebord">
					<tr>
						<th width="10%" height="25" align="center" class="td6">序号</th>
						<th width="15%" height="25" align="center" class="td6">VLAN</th>
						<th width="15%" height="25" align="center" class="td6"> &nbsp;
							<script>writemsg(<% write(lang); %>, "MAC地址");</script>
							</td>
						<th width="15%" height="25" align="center" class="td6">&nbsp;
							<script>writemsg(<% write(lang); %>, "类型");</script>
							</td>
						<th width="15%" height="25" align="center" class="td6">&nbsp;
							<script>writemsg(<% write(lang); %>, "端口");</script>
							</td>
						<th width="15%" height="25" align="center" class="td6">&nbsp;
							<script>writemsg(<% write(lang); %>, "删除");</script>
							</td>
						<th width="15%" height="25" align="center" class="td6">&nbsp;
							<script>writemsg(<% write(lang); %>, "添加静态");</script>
							</td>
					</tr>
					<script>
<%  var errorcode; SedTabShow("mac");%>
					</script>
				</table>
				<div align="center">
					<script>writebutton(1,<% write(lang); %>, "首  页", "button", "button", "firstpage", "getPage(1)");</script>
					<%SedTabShow("pagebutton");%>
						<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "清  空", "button", "button", "delete", "delSedTab(4)");</script>
						&nbsp;
						<%SedTabShow("pagenum");%>
							<%SedTabShow("allpage");%>
				</div>

			</div>
		</form>
		<br>
		<br>
		<form x-show="active==='tab2'" name="vlan_port" method="POST" action="/goform/PortSecurityMax">
			<input type="hidden" name="left_menu_id" value="@left_menu_id#">
			<input type="hidden" name="trunk_config" value="@trunk_config#">
			<input type="hidden" name="vlan_port_id" id="vlan_port_id">
			<input type="hidden" name="flag" id="flag">
			<input type="hidden" name="type_value" id="type_value" value="@trunk_config#">
			<input type="hidden" name="filter_value" id="filter_value" value="@trunk_config#">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input type="hidden" name="pvid_config" value="@pvid_config#">
			<input type="hidden" name="egress_tagged" id="egress_tagged" value="@egress_tagged#">
			<input name="del_flag" type="hidden" class="input_x" value="0">
			<input name="tabName" type="hidden" id="tabName">
			<div class="formContain">
				<table id="mainTbPort" width="100%" height="100%" border="0" align="center" cellpadding="0"
					cellspacing="0">

					<tr>
						<td>

							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"
								class="cword09">
								<tr>
									<td>
										<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
											<tr>
												<td height="30px">
													<font size="5" color="#404040">
														<div class="bot">端口MAC地址学习限制</div>
													</font>
												</td>
											</tr>
										</table>
									</td>
								</tr>

								<tr>
									<td>
										<table border="0" cellspacing="0" cellpadding="0" width="100%" id="table1"
											class="tablebord">

											<tr height="30">
												<td width="42%" class="all_tables all_tables1">
													<script>writemsg(<% write(lang); %>, "端口选择");</script>
												</td>
												<td width="58%" class="all_tables all_tables2">
													<select id="port_range" name="port_range"></select>
													<script><% AppendOption(); %> </script>
												</td>
											</tr>
											<tr height="25">
												<td width="42%" class="all_tables all_tables1">
													<script>writemsg(<% write(lang); %>, "MAC地址限制最大值");</script>
												</td>
												<td width="58%" class="all_tables all_tables2"><input name="mac_range"
														type="text" class="input_x" id="mac_range">
													[0-32766]</td>
											</tr>
											<tr height="30">
												<td colspan="2" align="middle" style="padding:5px">
													<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "设  置", "button", "button", "modify", "checking2()");</script>
												</td>
											</tr>
											<tr>
												<td colspan="2">

												</td>
											</tr>

										</table>
									</td>
								</tr>



							</table>

						</td>
					</tr>
				</table>
			</div>

			<div class="formContain" style="margin-top: 15px;">
				<div class="">
					<font size="5" color="#404040">
						<div class="bot">端口MAC地址学习限制列表</div>
					</font>
				</div>
				<table id="table_port" border="0" cellspacing="0" cellpadding="0" width="100%" class="tablebord">
					<tr align="center" height="25" class="crons">
						<th class="td2" width="17%">&nbsp;</th>
						<th class="td2" width="15%">
							<font color="#333333"><b>&nbsp;
									<script>writemsg(<% write(lang); %>, "端口");</script>
								</b></font>
						</th>
						<th class="td2" width="21%">
							<font color="#333333"><b>&nbsp;
									<script>writemsg(<% write(lang); %>, "MAC地址限制最大值");</script>
								</b></font>
						</th>
						<th class="td2" width="47%">&nbsp;
							<script>writemsg(<% write(lang); %>, "删除");</script>
						</th>
						<script language="javascript">writeLines();</script>
				</table>
				<div align="center">
					<script>writebutton(1,<% write(lang); %>, "刷  新", "button", "button", "Refresh", "refreshpage(`tab2`)");</script>
				</div>
			</div>
		</form>
		<form name="formaaa" method="POST" action="">
			<input name="port_range" type="hidden" class="input_x">

			<input name="del_flag" type="hidden" class="input_x" value="1">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input type="hidden" name="tabName" id="tabName">
		</form>
		<form x-show="active==='tab3'" name="vlan_port_Security" method="POST" action="/goform/PortSecurityMac">
			<input type="hidden" name="left_menu_id" value="@left_menu_id#">
			<input type="hidden" name="trunk_config" value="@trunk_config#">
			<input type="hidden" name="vlan_port_id" id="vlan_port_id">
			<input type="hidden" name="flag" id="flag">
			<input type="hidden" name="type_value" id="type_value" value="@trunk_config#">
			<input type="hidden" name="filter_value" id="filter_value" value="@trunk_config#">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input type="hidden" name="pvid_config" value="@pvid_config#">
			<input type="hidden" name="egress_tagged" id="egress_tagged" value="@egress_tagged#">
			<input name="del_flag" type="hidden" class="input_x" value="0">
			<input name="tabName" type="hidden" id="tabName">
			<div class="formContain">
				<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0"
					cellspacing="0">

					<tr>
						<td>

							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"
								class="cword09">
								<tr>
									<td>


										<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
											<tr>
												<td height="30px">
													<font size="5" color="#404040">
														<div class="bot">端口MAC地址绑定</div>
													</font>
												</td>
											</tr>
										</table>


									</td>
								</tr>

								<tr>
									<td>
										<table border="0" cellspacing="0" cellpadding="0" width="100%" id="table22"
											class="tablebord">

											<tr height="30">
												<td width="42%" class="all_tables all_tables1">
													<script>writemsg(<% write(lang); %>, "端口选择");</script>
												</td>
												<td width="58%" class="all_tables all_tables2">
													<select id="port_security_range"
														name="port_security_range"></select>
													<script><% AppendOption(); %> </script>
												</td>
											</tr>
											<tr height="25">
												<td width="42%" class="all_tables all_tables1">
													<script>writemsg(<% write(lang); %>, "MAC地址");</script>
													(HHHH.HHHH.HHHH, 为16进制格式)
												</td>
												<td width="58%" class="all_tables all_tables2"><input
														name="mac_security_range" type="text" class="input_x"
														id="mac_security_range"></td>
											</tr>
											<tr>
												<td width="42%" class="all_tables all_tables1">
													<script>writemsg(<% write(lang); %>, "VLAN");</script>
												</td>
												<td width="58%" class="all_tables all_tables2"><input
														name="vlan_security_range" type="number" class="input_x"
														id="vlan_security_range"></td>
											</tr>
											<tr height="30">
												<td colspan="4" align="middle" style="padding:5px">
													<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "创  建", "button", "button", "modify", "checking3()");</script>
												</td>
											</tr>

										</table>
									</td>
								</tr>


							</table>

						</td>
					</tr>
				</table>
			</div>

			<div class="formContain" style="margin-top: 15px;">
				<div class="">
					<font size="5" color="#404040">
						<div class="bot">
							端口MAC地址绑定列表</div>
					</font>
				</div>
				<table id="table_port_security" border="0" cellspacing="0" cellpadding="0" width="100%"
					class="tablebord">
					<tr align="center" height="25" class="crons">
						<th class="td2" width="19%">
							<font color="#333333"><b>&nbsp;
									<script>writemsg(<% write(lang); %>, "端口");</script>
								</b></font>
						</th>
						<th class="td2" width="34%">
							<font color="#333333"><b>&nbsp;
									<script>writemsg(<% write(lang); %>, "MAC地址");</script>
								</b></font>
						</th>
						<th class="td2" width="35%">&nbsp;
							<script>writemsg(<% write(lang); %>, "VLAN");</script>
						</th>
						<th class="td2" width="35%">&nbsp;
							<script>writemsg(<% write(lang); %>, "删除");</script>
						</th>
					</tr>

					<script language="javascript">
						writeLinessecurity();
					</script>
				</table>
				<div align="center">
					<script>writebutton(1,<% write(lang); %>, "刷  新", "button", "button", "Refresh", "refreshpage(`tab3`)");</script>
				</div>
			</div>
		</form>
		<form name="formaa" method="POST" action="">
			<input name="port_security_range" type="hidden" class="input_x">
			<input name="mac_security_range" type="hidden" class="input_x">
			<input name="vlan_security_range" type="hidden" class="input_x">
			<input name="del_flag" type="hidden" class="input_x" value="1">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input type="hidden" name="tabName" id="tabName">
		</form>
		<form name="vlan_port1" x-show="active=='tab4'" method="POST" action="/goform/vlanSecurityMax">
			<input name="del_flag" id="del_flag" type="hidden" class="input_x" value="0">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input name="tabName" type="hidden" id="tabName">
			<div class="formContain">
				<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0"
					cellspacing="0">

					<tr>
						<td>
							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
								<tr>
									<td valign="top">
										<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"
											class="cword09">
											<tr>
												<td>


													<table width="100%" align="center" border="0" cellspacing="0"
														cellpadding="0">
														<tr>
															<td height="30px">
																<font size="5" color="#404040">
																	<div class="bot">VLAN MAC地址学习限制</div>
																</font>
															</td>
														</tr>
													</table>
												</td>
											</tr>

											<tr>
												<td>
													<table border="0" cellspacing="0" cellpadding="0" width="100%"
														x-data="{portList:[]}"
														x-init="portList = JSON.parse(localStorage.getItem('portList')).map(item => item.portName)"
														id="table11" class="tablebord">

														<tr height="30">
															<td width="42%" align="left" class="td7">&nbsp;
																<script>writemsg(<% write(lang); %>, "VLAN ID:");</script>
															</td>
															<td width="58%" align="left" class="td7">&nbsp;
																<input name="vlan_id" type="number" class="input_x"
																	id="vlan_id">(1-4094)
															</td>
														</tr>
														<tr height="30">
															<td class="crons">&nbsp;
																<script>writemsg(<% write(lang); %>, "MAC地址限制最大值:");</script>
															</td>
															<td class="crons">&nbsp;
																<input name="mac_max" type="text" class="input_x"
																	id="mac_max">(0-1024, 0为取消限制)
															</td>
														</tr>
														<tr height="30">
															<td class="crons">&nbsp;
																<script>writemsg(<% write(lang); %>, "端口选择:");</script>
															</td>
															<td class="crons">&nbsp;

																<select id="port_range" name="port_range">
																	<template x-for="(port,index) in portList" :key="index">
																		<option :value="port" x-text="port">
																		</option>
																	</template>
																</select>

															</td>
														</tr>
														<tr height="25">

															<td colspan="2" align="middle" style="text-align: center;"
																class="crons">
																<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "设  置", "button", "button", "modify", "checking4()");</script>
															</td>
														</tr>


													</table>
												</td>
											</tr>


										</table>
									</td>
								</tr>

							</table>
						</td>
					</tr>
				</table>
			</div>
			<div class="formContain" style="margin-top: 15px;">
				<div class="">
					<font size="5" color="#404040">
						<div class="bot">MAC地址学习限制信息</div>
					</font>
				</div>
				<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%" class="tablebord">
					<tr align="center" height="25" class="crons">
						<th class="td2" width="30%">
							<font color="#333333"><b>&nbsp;
									<script>writemsg(<% write(lang); %>, "VLAN ID");</script>
								</b></font>
						</th>
						<th class="td2" width="30%">
							<font color="#333333"><b>&nbsp;
									<script>writemsg(<% write(lang); %>, "MAC地址限制最大值");</script>
								</b></font>
						</th>
						<th class="td2" width="30%">
							<font color="#333333"><b>&nbsp;
									<script>writemsg(<% write(lang); %>, "端口");</script>
								</b></font>
						</th>
						<th class="td2" width="10%">&nbsp;
							<script>writemsg(<% write(lang); %>, "删除");</script>
						</th>
					</tr>

					<script language="javascript">
						writeLines6();

					</script>
				</table>
				<div class="" align="center">
					<script>writebutton(1,<% write(lang); %>, "刷  新", "button", "button", "Refresh", "refreshpage(`tab4`)");</script>
				</div>
			</div>
		</form>
		<script>
			// changebgcolor();
			// changebgcolor2();
			// changebgcolor22();
			changebgcolor_name("SedTab_tbl");
			changebgcolor_name("table_port");
			changebgcolor_name("table_port_vlan");
			changebgcolor_name("table_port_security");

<% if (errorcode == "1") { write("alert(putmsg("); write(lang); write(",'正在删除，可能需要一些时间，稍后请手动刷新页面!'));"); } %>
<% if (errorcode != "") { if (errorcode != "1") { write_errorcode(errorcode); } } %>
		</script>

		<form name="macdel" method="POST" action="">
			<input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

			<input type="hidden" name="mac_value" value="">
			<input type="hidden" name="vlan_value" value="">

		</form>
		<br><br><br>

</body>
<script>
		function checking4() {
			var tf = document.vlan_port1;


			var vlan_id = tf.vlan_id.value;

			if (vlan_id < 1 || vlan_id > 4094) {
				alert('Invaid value, vlan id effective range: 1~4094！');
				tf.vlan_id.focus();
				return false;
			}

			var mac_max = tf.mac_max.value;

			if (mac_max < 0 || mac_max > 1024) {
				alert('Invaid value, mac limit effective range: 0~1024');
				tf.mac_max.focus();
				return false;
			}
			tf.tabName.value = 'tab4'
			tf.submit();
		}

	function changebgcolor() {
		var tab = document.all.table11;
		var len = tab.rows.length;
		for (var i = 0; i < len; i++) {
			var lencol = tab.rows[i].cells.length
			for (var j = 0; j < lencol; j++) {
				if (j % 2 == 1) {

					tab.rows[i].cells[j].className = "all_tables all_tables2";
				}
				else {
					tab.rows[i].cells[j].className = "all_tables all_tables1";
				}

			}

		}
	}


	function delmac1(i) {
		var hid = document.vlan_port1;

		hid.port_range.value = portStaList1[3 * i + 2];
		hid.mac_max.value = portStaList1[3 * i + 1];
		hid.vlan_id.value = portStaList1[3 * i];
		hid.del_flag.value = 1;
		hid.action = "/goform/vlanSecurityMax";
		hid.tabName.value = 'tab4'
		hid.submit();
		return 0;

	}
	changebgcolor();
</script>

</html>