<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title><script>putmsg(<% write(lang); %>,"退出系统");</script></title>
<script language="JavaScript">

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function messageCheck()
{
	var hid = document.webForm;

	hid.submit();
	return true;
}
</SCRIPT>
</head>
<body  onload=""><br>
<% var errorcode;getexitstate(); %>
<script>
<% if (errorcode=="1") { write("top.location.href = 'login_ch.asp';"); } %>
<% if (errorcode=="2") { write("top.location.href = 'login_en.asp';"); } %>
</script>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="get" action="/goform/setLogoutCfg" class="formContain">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td>

 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="30px" ><font size="5" color="#404040"><div class="bot">退出</div></font></td></tr>
 </table>
 
<!--
        <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
            <tr height="25">
              <td colspan="3" align="left" class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"退出系统");</script></td>
            </tr>
        </table>
-->

        </td>
      </tr>
      
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1">
            <tr height="25">
            <!--
              <td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"退出系统");</script></td>
              -->
              <td align="center" class="crons" style="border-top: 1px solid #ddd; padding: 5px;">
              <script>writebutton(1,<% write(lang); %>,"返回登录","button","button","apply1","messageCheck()");</script>
              </td>
            </tr>
        </table></td>
      </tr>
      <tr>
        <td height="8"></td>
      </tr>

    </table></td>
  </tr> 
</table>
</td></tr>
<tr><td>
</td></tr></table>

<script>
changebgcolor();


</script>

</body>

</form>

<script>
<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</html>


