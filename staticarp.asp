<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_3.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"静态ARP设置");</script></title>

<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 

<script language="JavaScript" type="text/JavaScript">

var num = 0; 
function getPage(page)
{
   location.href="staticarp.asp?page="+page+"&ltime="+<% write(lltime); %>;
}
function check()
{
	var tf=document.arp;
	var mac=document.getElementById("macaddr").value;
    var ip=document.getElementById("ipaddr").value;
	
    if(MacCheck(mac))
   	{	
	    if(IpCheck(ip))
		{
			tf.submit();
		}
		else
		{
			alert(putmsg(<% write(lang); %>,"IP地址输入非法！格式为 A.B.C.D"));
		}
	}
	else
	{
		alert(putmsg(<% write(lang); %>,"MAC地址输入非法！格式为 HHHH.HHHH.HHHH"));
	}
}

function checkdelall()
{
	var tf=document.arp;
	var delall = document.getElementById("delall");
	
	delall.value = "1";
	tf.submit();
}  

function checkdel()
{
	var tf=document.arp;
	var del = document.getElementById("del");
	var macaddr = document.getElementById("macaddr").value;
	var ipaddr=document.getElementById("ipaddr").value;

	if(macaddr=="" || ipaddr=="")
	{
		alert(putmsg(<% write(lang); %>,"请选择地址!"));
		return 0;
	}

	del.value = "1";
    tf.submit();
}  

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var macaddr = document.getElementById("macaddr");
	var ipaddr=document.getElementById("ipaddr");

	if(obj.checked)
	{
		ipaddr.value=trobj.cells[1].innerHTML;
		macaddr.value=trobj.cells[2].innerHTML;		
	}
}

function P(portId,name,enable,instance)
{
    var narr=5;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_arp").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    num = num + 1;

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+num);	

    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
    tbtr.cells[1].abbr = portId;
	
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+num+"\" onclick=\"addToPortRange(this)\"/>";
	tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = name;
    tbtr.cells[3].innerHTML = enable;	
    tbtr.cells[4].innerHTML = instance;
}

function checkData()
{
	var tf=document.arp;
	tf.action = "/goform/saveComm?name=staticarp";
	tf.submit();
}
function refreshpage()
{
  location.href='staticarp.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('staticarp',<% write(lang); %>);
}

</script>
</head>

<body  >
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="arp" method="POST" action="/goform/arpConfig">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<input type="hidden" name="pvid_config"  value="@pvid_config#">

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">

<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="90%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
         <tr>
           <td colspan="2" align="left" class="cword09">&nbsp;<script>writemsg(<% write(lang); %>,"三层转发管理");</script> <b><font color="#FF7F00">&gt;&gt;</font></b> &nbsp;<script>writemsg(<% write(lang); %>,"静态ARP设置");</script></td>
         </tr>
        </table>
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%">
	     <tr height="25">
	       <th align="left" colspan="2">&nbsp;<script>writemsg(<% write(lang); %>,"静态ARP设置");</script></td>
	     </tr>
		 <tr height="25">
              <td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"IP 地址");</script></td>
              <td width="81%" align="left" class="crons">&nbsp;
                <input name="ipaddr" type="text"  id="ipaddr"/>&nbsp;<script>writemsg(<% write(lang); %>,"(格式为 A.B.C.D)");</script></td>
         </tr>
	     <tr height="25">
	     	  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"MAC 地址");</script></td>
	     	  <td class="crons">&nbsp;
                <input name="macaddr" type="text"  id="macaddr"/>&nbsp;<script>writemsg(<% write(lang); %>,"(格式为 HHHH.HHHH.HHHH)");</script>
	     	  </td>
	     </tr>			 	 		 		 
		 
	     <tr height="25">

	     	  <td colspan="2" align="middle" class="crons">
	     	  	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","add","check()");</script>
	     	  	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","delete","checkdel()");</script>
	     	  </td>
	     </tr>
	   
<tr><td colspan="2" height="8"></td></tr>
   <tr>
	    	<td colspan="2">
	    		<table id="table_arp" border="0" cellspacing="0" cellpadding="0" width="100%" class="table2">
	    		 	<tr align="center" height="25" class="crons">
					    <th class="td2" width="5%">
	    		 		<th class="td2" width="5%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"IP 地址");</script></b></font></th>
	    		 		<th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"MAC 地址");</script></b></font></th>		
						<th class="td2" width="5%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"接口");</script></b></font></th>
	    		 		<th class="td2" width="15%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"类型");</script></b></font></th>																	
	    		 	</tr>
					<script>  <%  var errorcode; showArp("arp"); %></script>
	    		</table>
	    	</td>
   </tr>
     </table>
   </td></tr>
   <tr>
	  	   <td colspan="2" align="center" height="35">
	  	   <script>writebutton(1,<% write(lang); %>,"首  页","button","button","firstpage","getPage(1)");</script>
				<%showArp("pagebutton");%>
	  	    <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"清  空","button","button","delete","checkdelall()");</script>
	  		    &nbsp;
	  		    <%showArp("pagenum");%>
			    <%showArp("allpage");%>
	  		</td>
   </tr>
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>
<INPUT type="hidden" name="del" id= "del" value="0">
<INPUT type="hidden" name="delall" id= "delall" value="0">
</form>
<script>
changebgcolor();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>
</html>
