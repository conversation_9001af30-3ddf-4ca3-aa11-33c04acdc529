<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"GMRP");</script></title>
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript">


function left_trim(field)
{
 var tmp_string = field.value;
 while (''+tmp_string.charAt(0) == ' ')
 tmp_string = tmp_string.substring(1,tmp_string.length);

 field.value = tmp_string;
}

function right_trim(field)
{
 var tmp_string = field.value;

 while (''+tmp_string.charAt(tmp_string.length-1) == ' ')
 tmp_string = tmp_string.substring(0,tmp_string.length-1);

 field.value = tmp_string;
}


function  checkOtherChar(str,errmsg) {
       for(var loop_index=0; loop_index<str.length; loop_index++)
       {
         if(str.charAt(loop_index) == '~'
           ||str.charAt(loop_index) == '!'
           ||str.charAt(loop_index) == '@'
           ||str.charAt(loop_index) == '#'
           ||str.charAt(loop_index) == '$'
           ||str.charAt(loop_index) == '%'
           ||str.charAt(loop_index) == '^'
           ||str.charAt(loop_index) == '&'
           ||str.charAt(loop_index) == '*'
           ||str.charAt(loop_index) == '('
           ||str.charAt(loop_index) == ')'
           ||str.charAt(loop_index) == '+'
           ||str.charAt(loop_index) == '{'
           ||str.charAt(loop_index) == '}'
           ||str.charAt(loop_index) == '|'
           ||str.charAt(loop_index) == ':'
           ||str.charAt(loop_index) == '"'
           ||str.charAt(loop_index) == '<'
           ||str.charAt(loop_index) == '>'
           ||str.charAt(loop_index) == '?'
           ||str.charAt(loop_index) == '`'
           ||str.charAt(loop_index) == '='
           ||str.charAt(loop_index) == '['
           ||str.charAt(loop_index) == ']'
           ||str.charAt(loop_index) == '\\'
           ||str.charAt(loop_index) == ';'
           ||str.charAt(loop_index) == '\''
           ||str.charAt(loop_index) == '/')
          {            
            alert(errmsg);
            return false;
   	   }
         }
      return true;
   }

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function checkname(field,err_blank,err_space,err_invalid,flag)
{
	if ((field.value.indexOf(" ") >=0)&&(flag == 1))
   {
		alert(err_space);
		field.focus();
		field.select();
		return false;
	}

	left_trim(field);
	right_trim(field);

	if ((field.value =="" | field.value == null)&&(flag == 1))
	{
		
		alert(err_blank);
		field.focus();
		field.select();
		return false;
	}


	if (checkOtherChar(field.value, err_invalid)==false)
	{
		field.focus();
		field.select();
		return false;
	}
	
/*	
	if (isChinese(field.value)==true)
	{
		field.focus();
		field.select();
		return false;
	}
*/

	return true;
}

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}


function getPage(page)
{
   location.href="qos_dscpcos.asp?page="+page+"&ltime="+<% write(lltime); %>;
}
function dofirst(){location.href="qos_dscpcos.asp?page=1"+"&ltime="+<% write(lltime); %>;}



function changestat(str,id)
{
/*
var port = 64 ;
	var checked = 0;
	var valid_port = 0;
		
	for(i=1;i<=port;i++)
	{		
			if(document.getElementById("LacpTypePort"+i).checked)
			{
				checked = checked + 1;
			}
			valid_port = valid_port + 1;
	}
	
	if(checked == valid_port)
	{
		document.getElementById(id).checked = true ;
	}
	else
	{
		document.getElementById(id).checked = false ; 
	}
	
	*/
}


function selectToAll() 
{  
		var cf = document.forms[0];
		var objs = document.getElementsByName("LacpTypePort"); 
		var i;
		if(cf.check_all.checked == true)
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
		       if(objs[i].disabled==false && objs[i].checked==false){
		         objs[i].checked = true;  
			 		addToPortRange(objs[i]);
			 }
		    }
		}
		else
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
					if(objs[i].checked==true){
					objs[i].checked = false;  
			}
		         
		    }
		} 
    
}



function check(){
  var doc = document.form1;
  var lsplit = " ";
	var tmp = 1, aPort = "";
	var j=0;
	var qosDscpCosMapName = doc.qosDscpCosMapName.value;


	if(checkname(document.form1.qosDscpCosMapName,"名称不能为空","名称中不能有空格","名称中必须是合法字符",0) == false)
	{
		return false;	
	}
	
	
	for(var i = 1; i <= 64; i++){
	  if(document.getElementById("LacpTypePort" + i).checked)
	  {
	  		j++;
		   aPort = aPort+(i-1)+lsplit;
	  }
	}
	if(j>8 )
	{
		alert("最多选择8个值！");
		return false;
	}
	if(aPort == "")
	{
		alert("请选择值！");
		return false;
	}
	doc.qosDscpCosMapMask.value = aPort;

	for(var i = 1; i <= 64; i++)
	{
	  document.getElementById("LacpTypePort" + i).disabled = true;
	}
		
	//alert(aPort);	
	
 	 document.form1.submit();
	
	//return true;
}
function addTodscpRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var dscp_name=document.getElementById("qosDscpCosMapName");

	if(obj.checked)
		dscp_name.value=trobj.cells[1].innerHTML;
}

function P(mapName,dscpvalue,cosvalue)
{
    var narr=4;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+mapName);	


    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		
	        tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
    tbtr.cells[0].abbr = mapName;
    
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+mapName+"\" onclick=\"addTodscpRange(this)\"/>";
	
		tbtr.cells[1].innerHTML = mapName;
    tbtr.cells[2].innerHTML = dscpvalue;
    tbtr.cells[3].innerHTML = cosvalue;


}

function checkdel()
{		
	var del = document.getElementById("del");
	var tf=document.form1;

		   	del.value = "1";
	 		tf.submit();
}

function refreshpage()
{
  location.href='qos_dscpcos.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('form1',<% write(lang); %>);
}

</script>
</head>

<body  ><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="form1" method="POST" action="/goform/setDscpCos">
   <input type="hidden" name="qosDscpCosMapMask" id="qosDscpCosMapMask"  value=""/>
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="form1_id" id="form1_id" >
<input type="hidden" name="flag" id="flag" >
<input type="hidden" name="type_value" id = "type_value"  value="@trunk_config#">
<input type="hidden" name="filter_value" id = "filter_value"  value="@trunk_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="pvid_config"  value="@pvid_config#">
<input type="hidden" name="egress_tagged" id="egress_tagged" value="@egress_tagged#">


<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">

<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
     	 
 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>QOS设置/DSCP映射</b></font></td></tr>
 </table>
 
<!--

    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
	     <tr height="25">
	       <td align="left" colspan="4"  class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"Dscp to Cos");</script></td>
	     </tr>
        </table>
-->
        
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%" id="table1" class="tablebord">

		<tr height="25">
		  <td align="left" class="crons">&nbsp;&nbsp;名称</td>
		  <td width="30%" align="left" class="crons"><input type="text" name="qosDscpCosMapName" id="qosDscpCosMapName"></td>
		  <td width="10%" align="left" class="crons">&nbsp;&nbsp;映射COS值</td>
		  <td width="42%" align="left" class="crons"><select name="qosDscpCosMapValue" id="qosDscpCosMapValue">
            <option value="0" >0</option>
            <option value="1" >1</option>
            <option value="2" >2</option>
            <option value="3" >3</option>
            <option value="4" >4</option>
            <option value="5" >5</option>
            <option value="6" >6</option>
            <option value="7" >7</option>
          </select></td>
		</tr>
		<tr height="25">
              <td width="18%" rowspan="8" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"DSCP值");</script>
                ：&nbsp; </td>
              <td align="left" class="crons" colspan="3">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <input type="checkbox" name="LacpTypePort1" id="LacpTypePort1" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
0&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort2" id="LacpTypePort2" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort3" id="LacpTypePort3" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
2&nbsp;&nbsp;&nbsp;&nbsp; 
<input type="checkbox" name="LacpTypePort4" id="LacpTypePort4" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort5" id="LacpTypePort5" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
4&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort6" id="LacpTypePort6" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
5&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort7" id="LacpTypePort7" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
6&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort8" id="LacpTypePort8" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
7&nbsp;&nbsp;&nbsp;&nbsp;</td>
         </tr>
		<tr height="25">
		  <td align="left" class="crons" colspan="3">&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort9" id="LacpTypePort9" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
8&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort10" id="LacpTypePort10" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
9&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort11" id="LacpTypePort11" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
10&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort12" id="LacpTypePort12" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
11&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort13" id="LacpTypePort13" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
12&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort14" id="LacpTypePort14" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
13&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort15" id="LacpTypePort15" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
14&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort16" id="LacpTypePort16" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
15</td>
		  </tr>
		<tr height="25">
		  <td align="left" class="crons" colspan="3">&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort17" id="LacpTypePort17" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
16&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort18" id="LacpTypePort18" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
17&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort19" id="LacpTypePort19" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
18&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort20" id="LacpTypePort20" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
19&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort21" id="LacpTypePort21" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
20&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort22" id="LacpTypePort22" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
21&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort23" id="LacpTypePort23" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
22&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort24" id="LacpTypePort24" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
23</td>
		  </tr>
		<tr height="25">
		  <td align="left" class="crons" colspan="3">&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort25" id="LacpTypePort25" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
24&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort26" id="LacpTypePort26" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
25&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort27" id="LacpTypePort27" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
26&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort28" id="LacpTypePort28" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
27&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort29" id="LacpTypePort29" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
28&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort30" id="LacpTypePort30" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
29&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort31" id="LacpTypePort31" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
30&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort32" id="LacpTypePort32" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
31</td>
		  </tr>
		<tr height="25">
		  <td align="left" class="crons" colspan="3">&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort33" id="LacpTypePort33" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
32&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort34" id="LacpTypePort34" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
33&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort35" id="LacpTypePort35" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
34&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort36" id="LacpTypePort36" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
35&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort37" id="LacpTypePort37" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
36&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort38" id="LacpTypePort38" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
37&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort39" id="LacpTypePort39" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
38&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort40" id="LacpTypePort40" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
39&nbsp;</td>
		  </tr>
		<tr height="25">
		  <td align="left" class="crons" colspan="3">&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort41" id="LacpTypePort41" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
40&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort42" id="LacpTypePort42" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
41&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort43" id="LacpTypePort43" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
42&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort44" id="LacpTypePort44" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
43&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort45" id="LacpTypePort45" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
44&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort46" id="LacpTypePort46" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
45&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort47" id="LacpTypePort47" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
46&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort48" id="LacpTypePort48" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
47</td>
		  </tr>
		<tr height="25">
		  <td align="left" class="crons" colspan="3">&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort49" id="LacpTypePort49" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
48&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort50" id="LacpTypePort50" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
49&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort51" id="LacpTypePort51" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
50&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort52" id="LacpTypePort52" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
51&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort53" id="LacpTypePort53" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
52&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort54" id="LacpTypePort54" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
53&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort55" id="LacpTypePort55" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
54&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort56" id="LacpTypePort56" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
55</td>
		  </tr>
		<tr height="25">
		  <td align="left" class="crons" colspan="3">&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort57" id="LacpTypePort57" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
56&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort58" id="LacpTypePort58" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
57&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort59" id="LacpTypePort59" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
58&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort60" id="LacpTypePort60" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
59&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort61" id="LacpTypePort61" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
60&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort62" id="LacpTypePort62" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
61&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort63" id="LacpTypePort63" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
62&nbsp;&nbsp;&nbsp;
<input type="checkbox" name="LacpTypePort64" id="LacpTypePort64" value="ON"   onClick="changestat('LacpTypePort','select_ss');"/>
63&nbsp;&nbsp; </td>
		  </tr>
	     		 
	    <tr height="25">

	     	  <td colspan="4" align="middle" class="crons">
	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"提  交","button","button","modify","check()");</script>
	     	  
	     	  	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","delete","checkdel()");</script>

&nbsp;&nbsp;&nbsp;</td>
	     </tr>	  
			 
   <tr>
	    	<td colspan="4">
	    		<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
	    		 	<tr align="center" height="25" class="crons">
	    		 		<th class="td2" width="10%"><font color="#333333"><b>&nbsp;</b></font></th>
	    		 		<th class="td2" width="20%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"名称");</script></b></font></th>
	    		 		<th class="td2" width="30%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"DSCP");</script></b></font></th>						
	    		 		<th class="td2" width="30%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"COS");</script></b></font></th>						
	    		 	</tr>


		  <script>
		  		<%  var errorcode; QoSDscpCosShow("mac");%>
		  </script>
		  	    		</table>	    	</td>
   </tr>
     </table>
   </td></tr>
    <tr>
	  	   <td colspan="6" align="center" height="23">
	  	    <script>writebutton(1,<% write(lang); %>,"首  页","button","button","firstpage","getPage(1)");</script>
				<%QoSDscpCosShow("pagebutton");%>

            	  		    &nbsp;
	  		    <%QoSDscpCosShow("pagenum");%>
			    <%QoSDscpCosShow("allpage");%>
    </tr>  
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>
<INPUT type="hidden" name="del" id= "del" value="0">

</form>  
<script>
changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
<br><br><br>

</body>
</html>
