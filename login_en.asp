<html ><head>
<title>Login</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<SCRIPT language=javascript>

if (top.location != document.location) top.location.href = document.location.href;
 </SCRIPT>
<script language="javascript">
if (window.focus) self.focus();



function login()
{

 /*
  if(document.getElementById("validateValue").value!= document.tF0.yzm_txt.value )
  {
  	alert("Verification Code Error!");
	document.tF0.yzm_txt.select();
	return false;
  }
 */ 
  
	if(""==document.tF0.username.value)
	{
		alert("Please input username!");
		return false;
	}
	if(""==document.tF0.passwd.value)
	{
		alert("Please input password!");
		return false;
	}
	if(document.tF0.passwd.value.indexOf(" ")>-1)
	{
		alert("Password should not contain space!");
		return false;
	}
	return true;
	
	//document.tF0.username.disabled = true;
	//document.tF0.passwd.disabled = true;
	document.tF0.yzm_txt.disabled = true;
	
	return true;
}

function messageCheck(language_type)
{
  switch(language_type)
  {
	case 1:
	  location.href="login_ch.asp";
	  break;
	case 2:
	  location.href="login_en.asp";
	  break;
	default:
      break;
  }
  return true;
}



function validteCode()
{
	var validateCode="";
	var codes = new Array(4);       // 用于存储随机Verification Code
	var colors = new Array("Red","Green","Gray","Blue","Maroon","Aqua","Fuchsia","Lime","Olive","Silver");
	for(var i=0;i < codes.length;i++)
	{
		//获取随机Verification Code
		codes[i] = Math.floor(Math.random()*10);
	}
	//var spans = document.getElementById("divCode").all;
	document.getElementById("divCode1").value = codes[0];
	document.getElementById("divCode2").value = codes[1];
	document.getElementById("divCode3").value = codes[2];
	document.getElementById("divCode4").value = codes[3];
	

	for(var i=0;i<4;i++)
	{
		validateCode+=codes[i];
	}
	
	//alert(validateCode);
	
/*
	for(var i=0;i<spans.length;i++)
	{
		spans[i].innerHTML=codes[i];
		spans[i].style.color = colors[Math.floor(Math.random()*10)];    // 随机设置Verification Code颜色
		validateCode+=codes[i];
	}
*/	
	//将Verification Code的值保存到一个隐藏控件
	document.getElementById("validateValue").value=validateCode;
	
document.forms[0].username.focus(); 	
	
	
	}


</script>

<style type="text/css">
<!--
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
}
.strone-field {font-weight: bold;}
.login_page_top{ height:5; background-color:#336799}
.login_page_top_xia{ height:14; background-color:#4693CB}
td.login_page_middle{ vertical-align:bottom;background-color:#4693CB; height:85}
td.login_page_middle_left{ vertical-align:bottom;background-color:#4693CB; height:85; text-align:left}
td.login_page_middle_right{ vertical-align:center;background-color:#4693CB; height:85; text-align:center;font-family : Verdana, Arial, Helvetica;font-size : 11px;color:ffffff;font-weight: bold;}
.larges_title {font-size: 14px;font-weight: bold;	color: #ffffff}
.mainHeader {
	MARGIN-TOP: 0px; PADDING-LEFT: 3px; FONT-WEIGHT: bold; FONT-SIZE: 16px; VERTICAL-ALIGN: bottom; COLOR: #be6301; BORDER-TOP-STYLE: none; BORDER-BOTTOM: #4891C6 1px solid; FONT-FAMILY: Geneva, Arial, Helvetica, sans-serif; BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; HEIGHT: 22px; TEXT-ALIGN: left
}
.bottom {
	FONT-WEIGHT: normal; FONT-SIZE: 11px; COLOR: #ffffff; FONT-FAMILY: Arial, Helvetica, sans-serif; HEIGHT: 20px; BACKGROUND-COLOR: #336799
}
.login_name{ FONT-FAMILY: Geneva, Arial, Helvetica, sans-serif;font-size: 14px;font-weight: bold; }
.crons {vertical-align:middle }

-->
</style>

</head>
<body  onload="validteCode();">

<table width="100%" border="0" cellspacing="0" cellpadding="0">
  <tr>
 <td class="login_page_top">&nbsp;</td>
  </tr>
  <tr>
 <td class="login_page_top_xia">&nbsp;</td>
  </tr>
</table>
<table width="100%" border="0" cellspacing="0" cellpadding="0">
  <tr>
 <td width="74%" class="login_page_middle_left"><img src="logo_web.gif" width="181" height="68" /><span class="larges_title" style="padding-left:10px"> Industrial Ethernet Switch
  </span><br />
<br /></td>
 <% TopLanguageShow(); %>
  </tr>
</table>
 <table width="100%" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td  ></td>
  </tr>
</table>
  <table width="100%" border="0" cellspacing="0" cellpadding="0">
  <tr align="center">  
 <td height="470" colspan="2" align="center">
 
<form name="tF0" method="post" action="/goform/setLoginCfg">
<% var errorcode;getLoginCfg(); %>
<% web_get_stat(); %>
<input type="hidden" name="lang" value="en">

		    <table width="60%"  border="0" cellpadding="0" cellspacing="0">
              <tr>
                <td height="12" colspan="2" bgcolor="#f2f2f2"></td>
              </tr>
            </table>
		    <table width="60%" border="0" align="center" cellpadding="0" cellspacing="0" bgcolor="f2f2f2" >
  <tbody>
 <tr>
<td width="35%" rowspan="6" align="right" ><img src="login_logo.gif" width="244" height="102">&nbsp;&nbsp;</td>
<td height="32" colspan="3" align="center" bgcolor="#fafafa" class="mainHeader" >&nbsp;Welcome</span>&nbsp;</td>
</tr>
 <tr>
<td height="6" colspan="3" align="right" bgcolor="#fafafa" >&nbsp;</td>
</tr>
 <tr height="25">
<td width="23%"   align="right" bgcolor="#fafafa"  class="login_name">UserName：</td>
<td colspan="2"  align="left" bgcolor="#fafafa" ><input  name="username" type="text"size="20" maxlength="16"  style="width:150px; height:20px">  </td>
 </tr>
 <tr height="25">
<td height="25" align="right" bgcolor="#fafafa" class="login_name">Password：</td>
<td colspan="2" align="left" bgcolor="#fafafa" ><input name="passwd" type="password" AUTOCOMPLETE="off" size="20" maxlength="16"  style="width:150px; height:20px" > </td>
 </tr>
 

 <tr align="center" height="25">
   <td height="25" bgcolor="#fafafa"><div align="right" class="login_name">Verification Code：</div></td>
   <td width="23%" height="25" align="left" bgcolor="#fafafa"><input name="yzm_txt" type="text" class="crons" id="yzm_txt"   style="width:150px; height:20px" /></td>
   <td width="19%" height="25" align="left"  bgcolor="#fafafa"><DIV id="divCode"  onclick="JavaScript:validteCode()">
          <input name="divCode1" type="text" id="divCode1" size="1" maxlength="1" style="font-size:22px; border:0;color:#000000;font-weight:bold;background-color:#fafafa; " disabled />
	      <input name="divCode2" type="text" id="divCode2" size="1" maxlength="1"   style="font-size:22px;border:0;color:#000000;font-weight:bold;background-color:#fafafa;" disabled/>
          <input name="divCode3" type="text" id="divCode3" size="1" maxlength="1"  style="font-size:22px;border:0;color:#000000;font-weight:bold;background-color:#fafafa;" disabled/>
          <input name="divCode4" type="text" id="divCode4" size="1" maxlength="1"  style="font-size:22px;border:0;color:#000000;font-weight:bold;background-color:#fafafa;" disabled/>
        </DIV>
   </td>
 </tr>
 <tr align="center" height="25">
<td bgcolor="#fafafa"><INPUT name="hidden" type="hidden" id="validateValue"> &nbsp;</td>
<td colspan="2" align="left" bgcolor="#fafafa"><input name="Submit" type="submit" class="strone-field"  value="Login"/ onClick="return login();">
  <input type="reset" name="button2" id="button2"  class="strone-field" value="重置"  onClick="validteCode();"  style="display:none"  /></td>
 </tr>
  </tbody>
</table>
			
<table width="60%"  border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td height="12" colspan="2" bgcolor="#f2f2f2"></td>
  </tr>
</table>
      </form>
    </td>
  </tr>
</table><table width="100%" border="0" cellspacing="0" cellpadding="0">
<tr>
<td height="20" bgcolor="#336799" align="right" class="bottom">All rights reserved &copy;  2017 SAC Inc.</td>
</tr>
</table>
<script>
<% if (errorcode=="1") { write("alert('Incorrect username or password! Please try again.');"); } %>
<% if (errorcode=="2") { write("alert('Change Password Failed!');"); } %>
<% if (errorcode=="3") { write("alert('The current user has reached the largest!');"); } %>
<% if (errorcode=="5") { write_errorcode("rejected check error"); } %>
</script>
</body>
</html>




