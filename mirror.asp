<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<meta http-equiv="Content-Type" content="text/html; charset=utf8">
		<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
		<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
		<script language="javascript" type="text/javascript" src="js/alpinejs.min.js" defer></script>
		<title>&nbsp;
			<script>writemsg(<% write(lang); %>, "端口镜像");</script>
		</title>
		<link href="css/display_web.css" rel="stylesheet" type="text/css" />
		<script language="JavaScript">
			var boardType = <% getSysCfg(); %>;
		</script>
		<% var mirrorEnable; getmirrorEnable(); %>

			<script language="JavaScript" type="text/JavaScript">

function addToPortRange(obj)
{


}

function addToPortRangeMirror(obj){
	var trid="tr_"+obj.value;
	var nodeArray;
	var trobj = document.getElementById(trid);
	var checkbox_index = document.getElementsByName("checkbox_index");
	var rang_monitor = document.getElementById("rang_monitor");
	var gatherData = document.getElementsByName("mirror_caputure_type");
	var port_id;
	var array=new Array();
	var i,j;
	var val=trobj.cells[1].innerHTML;
	var vals=trobj.cells[2].innerHTML;

	for(i=0;i<3;i++){
		if(gatherData[i].value==trobj.cells[3].innerHTML){
			gatherData[i].checked=true;
			continue;
		}
		gatherData[i].checked=false;
	}


	for(i=0;i<checkbox_index.length;i++)
	{
		checkbox_index[i].checked=false;
		port_id=checkbox_index[i].id
		array=vals.split("|");
		for(j=0;j<array.length;j++)
		{	
			if(array[j]==port_id)
			{
				checkbox_index[i].checked=true;
			}
		}
	}

	for(i=0;i<rang_monitor.options.length;i++)
	{
		if(rang_monitor.options[i].text==val)
		{
			rang_monitor.options[i].selected=true;

		}
		else
		{
			rang_monitor.options[i].selected=false;
		}
	}
	
	document.getElementById("modify2").style.display = "none";
	document.getElementById("modify4").style.display = "inline";
	
}

function MirrorDisplay(rang_mirrors, rang_monitors, gatherDatas)
{	
    var narr=4;
    var tbtd;
    var i;
    var tbtr = document.getElementById("mirror_table3").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");
	tbtr.setAttribute("id", "tr_"+rang_mirrors);

	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
		
	/*display*/
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index2\" value=\""+rang_mirrors+"\" onchange=\"addToPortRangeMirror(this)\"/>";
	tbtr.cells[1].innerHTML = rang_monitors;
	tbtr.cells[2].innerHTML = rang_mirrors;
	tbtr.cells[3].innerHTML = gatherDatas;
}

function AddOption(portname){

	var selectObject = document.getElementById("rang_monitor");
	var y=document.createElement('option');
  	y.text=portname;
	y.value = portname;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  	catch(ex)
    {
   	selectObject.add(y); // IE only
  	}
}


function insertMirror()
{
	var i,j,k,n=0;
	var values2="";
	var hid = document.mirrorsetting;
	var target = document.getElementById("portNum");
	var checkbox_index = document.getElementsByName("checkbox_index");
	var rang_monitor = document.getElementById("rang_monitor");
	var mirror_table3 = document.getElementById("mirror_table3");
	var array=new Array();

    target.value="";
	n=0;
	for(i=0;i<checkbox_index.length;i++)
	{
		if(checkbox_index[i].checked==true)
		{	
			n++;
			
			/*
			for(j=1;j<mirror_table3.rows.length;j++)
			{
				values2 = mirror_table3.rows[j].cells[2].innerHTML;
				array=values2.split("|");
				for(k=0;k<array.length;k++)
				{
					if(array[k]==checkbox_index[i].id)
					{
						alert(putmsg(<% write(lang); %>,"端口")+" "+checkbox_index[i].id+" "+putmsg(<% write(lang); %>,"已经被镜像,请重新选择!"));
						return false;
					}
				}
			}
*/

			for(j=0;j<rang_monitor.options.length;j++)
			{
				values2 = rang_monitor.options[j].text;
				if((rang_monitor.options[j].selected==true) && checkbox_index[i].id==values2)
				{
					alert(putmsg(<% write(lang); %>,"端口")+" "+checkbox_index[i].id+" "+putmsg(<% write(lang); %>,"同时为镜像端口和监控端口，请重新选择!"));
					return false;
				}
			}
			
			if(n==1)
				target.value=target.value+checkbox_index[i].id;
			else
				target.value=target.value+"|"+checkbox_index[i].id;
		}
	}

	if(target.value=="")
	{
		alert(putmsg(<% write(lang); %>,"请选择源端口,目前没有选择任何端口!"));
		return false;
	}
	
	//alert(target.value);

		
	hid.action= "/goform/InsMirror";
	hid.submit();
	
	return true;
}

function messageCheck()
{
	var hid = document.mirrorsetting;
	
	if(document.getElementById("en1").checked == true)
		document.forms[0].para1.value = "enable";
	else
		document.forms[0].para1.value = "disable";

    hid.action="/goform/setmirrorenablecfg";
	hid.submit();
	return true;
}


function alterMirror(a)
{
	var i,j=0;
	var tf=document.mirrorsetting;
	var target = document.getElementById("portNum");
	var checkbox_index = document.getElementsByName("checkbox_index");
	var rang_monitor = document.getElementById("rang_monitor");
	var mirror_table = document.getElementById("mirror_table3");

	target.value="";
	j=0;

	for(i=0;i<checkbox_index.length;i++)
	{
		if(checkbox_index[i].checked==true)
	    {
	    	j++;
	    	if(j==1)
				target.value=target.value+checkbox_index[i].id
			else
				target.value=target.value+"|"+checkbox_index[i].id;
		}
	}

	if(target.value=="")
	{
		alert(putmsg(<% write(lang); %>,"请选择源端口,目前没有选择任何端口!"));
		return false;
	}

	/*
	for(i=0;i<rang_monitor.options.length;i++){
		if(rang_monitor.options[i].selected==true){
			for(j=0;j<checkbox_index.length;j++){
				if(checkbox_index[j].checked==true
				   &&rang_monitor.options[i].text==checkbox_index[j].id){
					alert(putmsg(<% write(lang); %>,"端口")+" "+checkbox_index[j].id+" "+putmsg(<% write(lang); %>,"同时为镜像端口和监控端口，请重新选择!"));
					return false;
				}
			}
		}
	}
	*/

	if(a==1)
	{	
		j=0;
		for(i=1;i<mirror_table.rows.length;i++)
		{
			if(mirror_table.rows[i].cells[1].innerHTML==rang_monitor.value
				&&mirror_table.rows[i].cells[2].innerHTML==target.value)
			{
				j=0;
				break;
			}
			j++;
		}
		if(j==0)
		{
			tf.action= "/goform/ChangeMirror";
			tf.submit();
		}
		else
		{
			alert(putmsg(<% write(lang); %>,"只支持修改方向!"));
			return false;
		}
	}
	alert(target.value);
	alert(rang_monitor.value);
	if(a==2){
		tf.action= "/goform/DelMirror";
		tf.submit();
	}
}
function delMirror()
{
	var i,j=0;
	var tf=document.mirrorsetting;
	var target = document.getElementById("portNum");
	var checkbox_index = document.getElementsByName("checkbox_index");
	var rang_monitor = document.getElementById("rang_monitor");


	target.value="";
	j=0;

	for(i=0;i<checkbox_index.length;i++)
	{
		if(checkbox_index[i].checked==true)
	    {
	    	j++;
	    	if(j==1)
				target.value=target.value+checkbox_index[i].id
			else
				target.value=target.value+"|"+checkbox_index[i].id;
		}
	}

	if(target.value=="")
	{
		alert(putmsg(<% write(lang); %>,"请选择源端口,目前没有选择任何端口!"));
		return false;
	}

	tf.action= "/goform/DelMirror";
	tf.submit();
	
}
function checkData()
{
	var tf=document.mirrorsetting;
	tf.action = "/goform/saveComm?name=mirror";
	tf.submit();	
}

// function init()
// {
// 	var rang_data = document.getElementsByName("mirror_caputure_type");
// 	rang_data[0].checked=true;


// 	tmp = "<% write(mirrorEnable); %>";
// 	array_cfg = tmp.split(",");
	
// 	if (array_cfg[0] == "enable")
// 		document.getElementById("en1").checked = true;
// 	else
// 		document.getElementById("en2").checked = true;
// }

function showHelpinfo()
{
   showHelp('mirror',<% write(lang); %>);
}

function refreshpage()
{
  location.href='mirror.asp?ltime='+<% write(lltime); %>;
}
</script>
</head>

<!-- <body  onLoad="init()" ><br> -->


<% var authmode; checkCurMode(); %>
	<script>

		checktop(<% write(lang); %>);
		// 数据获取
		retValue = <% var responseJsonStr; jw_get_portMirrorConfig(); %>
			responseStr = <% write(responseJsonStr); %>;
		// responseStr.mirrorEnable数据处理
		var startIndex = responseStr.mirrorEnable.indexOf(':') + 1; // 获取冒号后的起始位置
		var endIndex = responseStr.mirrorEnable.length; // 字符串的末尾位置
		var mirrorValue = responseStr.mirrorEnable.slice(startIndex, endIndex).trim();
		console.log(JSON.parse(localStorage.getItem('portList')))
		var appData = {
			portName: JSON.parse(localStorage.getItem('portList')).map(e => e.portName),
			selectOpt: ['禁用'].concat(JSON.parse(localStorage.getItem('portList')).map(e => e.portName)),
		};

		var data11 = []
		var deDa = responseStr.PortMirror.length > 0 ? responseStr.PortMirror[0].portName : '禁用'
		data11.length = appData.portName.length
		appData.portName.forEach((item, index) => {
			data11[index] = {
				mirrorState: 2,
				mirrorName: item,
				mirrorDir: 'both',
			}

		})
		function portInit(portName) {
			const PortMirror = setOpt(portName)
			return data11.map(e => PortMirror.mirrorArray.find(t => e.mirrorName == t.mirrorName) || { mirrorState: 2, mirrorName: e.mirrorName, mirrorDir: 'both' }) || data11;
		}
		// 镜像使能 禁用
		var selectDis = (mirrorValue == 'enable' ? false : true)
		function setMirror(event) {
			var obj = {
				pageName: "mirror.asp",
				mirrorEnable: event.target.value,
			}
			obj.PortMirror = responseStr.PortMirror
			var tf = document.mirrorsetting;
			tf.param1.value = JSON.stringify(obj)
			tf.action = "/goform/jw_set_portMirrorConfig";
			tf.submit()
		}

		function setOpt(value) {
			const ge = responseStr.PortMirror.filter(item => item.portName === value)
			var da = {
				portName: value,
				mirrorArray: []
			}
			ge.map((item, index) => {
				da.mirrorArray = da.mirrorArray.concat(item.mirrorArray)
			})
			return da || { portName: value, mirrorArray: [] }
		}
		function applyData(selt, rowData, cehckedPort, chekSel) {
			if (selt != '禁用' && deDa != '禁用' && mirrorValue !== 'disable') {
				let datas = rowData.filter(item => item.mirrorState === 0 || item.mirrorState === 1)
				var obj = {
					pageName: "mirror.asp",
					mirrorEnable: mirrorValue,
					PortMirror: [
						{
							mirrorArray: datas,
							portName: selt == '' ? deDa : selt
						}
					]
				}
				if (datas.length > 0) {
					var tf = document.mirrorsetting;
					tf.param1.value = JSON.stringify(obj)
					tf.action = "/goform/jw_set_portMirrorConfig";
					tf.submit()
				}


			}

		}
	</script>

	<form name="mirrorsetting" method="post" action="/goform/setAlarmPortrate">
		<input type="hidden" name="param1" id="param1">
		<input type="hidden" name="ltime" value=<% write(lltime); %>>
		<input type="hidden" name="lastts" id="lastts" value=<% write(serverts); %>>
		<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
	</form>

	<table width="100%" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td height="30px">
				<font size="5" color="#0069d6">
					<div style="border-bottom: 1px solid #eee; padding-bottom: 10px;">端口镜像配置</div>
				</font>
			</td>
		</tr>
	</table>
	<p style="font-size: 16px; color: #999; margin: 8px 0 40px;">端口镜像指交换机把一个端口接收或发送的数据帧完全相同的复制给另一个指定端口</p>
	<div style="padding: 0 40px 20px" x-data="{}">
		<b>镜像使能:</b>
		<select x-model="mirrorValue" @change="setMirror">
			<option value="enable">开启</option>
			<option value="disable">关闭</option>

		</select>
	</div>
	<form name="form1" action="" class="formContain">
		<!-- <table width="96%" border="0" cellspacing="0" cellpadding="0" >
		<tr><td height="30px" ><font size="5" color="#404040"><div class="bot">镜像配置</div></font></td></tr>
	</table> -->


		<div x-data="{PortMirror:[],cehckedPort:[],rowData:[],selt:'',disl:''}"
			x-init="PortMirror = setOpt(deDa);cehckedPort=PortMirror.mirrorArray.filter(e=>e.mirrorState===1).map(e=>e.mirrorName);rowData=portInit(deDa);">
			<div style="padding: 0 20px 20px">
				<b>监控端口:</b>
				<select
					@change="PortMirror = setOpt($el.value);selt=$el.value;cehckedPort=PortMirror.mirrorArray.filter(e=>e.mirrorState===1).map(e=>e.mirrorName);rowData=portInit($el.value);deDa=false;">
					<template x-for="(item,index) in appData.selectOpt" :key="index">
						<option :selected="item==deDa" x-text="item" :value="item"></option>
					</template>
				</select>
			</div>
			<table class="tab1 tablebord" width="96%" border="0" align="center" cellpadding="0" cellspacing="0">
				<thead>
					<tr>
						<th class="all_tables">源端口</th>
						<th class="all_tables">状态</th>
						<th class="all_tables">源端口镜像模式</th>
					</tr>
				</thead>
				<tbody>
					<template x-for="(row, rowIndex) in rowData" :key="rowIndex">
						<tr style="text-align: center;">
							<td class="all_tables" x-text="row.mirrorName"></td>
							<td class="all_tables">
								<input type="checkbox"
									:disabled="selectDis || row.mirrorName==selt  || row.mirrorName==deDa || selt=='禁用' || deDa=='禁用'"
									:name="'mir'+rowIndex" :checked="row.mirrorState==1" :value="'ge'+(rowIndex+1)"
									x-model="cehckedPort" @change="$el.checked?row.mirrorState=1:row.mirrorState=0">
							</td>
							<td class="all_tables">
								<label>
									<input x-model="row.mirrorDir"
										:disabled="selectDis || row.mirrorName==selt || row.mirrorName==deDa || selt==='禁用' || deDa=='禁用'"
										type="radio" :name="'option'+rowIndex" value="both">
									双向
								</label>
								<label>
									<input x-model="row.mirrorDir"
										:disabled="selectDis || row.mirrorName==selt || row.mirrorName==deDa || selt==='禁用' || deDa=='禁用'"
										type="radio" :name="'option'+rowIndex" value="receive">
									入方向
								</label>
								<label>
									<input x-model="row.mirrorDir"
										:disabled="selectDis || row.mirrorName==selt || row.mirrorName==deDa || selt==='禁用' || deDa=='禁用'"
										type="radio" :name="'option'+rowIndex" value="transmit">
									出方向
								</label>
							</td>
						</tr>

					</template>
					<tr>

						<th class="all_tables" colspan="3" style="text-align: center;">
							<div style="display: flex; justify-content: center;">
								<div style="line-height: 35px; cursor: pointer; font-weight: normal" class="inpBtn"
									@click="applyData(selt,rowData,cehckedPort,PortMirror)">应用</div>
							</div>
						</th>

					</tr>
				</tbody>
			</table>

		</div>

	</form>

	<script>

<% if (errorcode != "") { write_errorcode(errorcode); } %>
	</script>
	</body>

</html>
