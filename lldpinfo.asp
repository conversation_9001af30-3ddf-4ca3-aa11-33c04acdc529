<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_rtl.js"></script>
<title><script>writemsg(<% write(lang); %>,"端口管理");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script language="JavaScript">

var lldpInfoList=[<%lldpInfoShow();%>];

//var lldpInfoList = ["Port 1","00-E0-0F-C4-7E-AB","1","Port 1PSW618","aaaaaaaaaaaaaaaaaa"," Image ********  Oct 19 2011 15:35:46","Bridge(+)","************* (IPv4)"];
function writeLines()
{
	var j = 0;
	for(var i=0;i<lldpInfoList.length/8;i++)
	{
		document.write("  <tr  class='tables_all'>");
		document.write("    <td class='inputsyslog1'>"+lldpInfoList[j]+"</td>");
		j++;
		document.write("    <td class='inputsyslog1'>"+lldpInfoList[j]+"</td>");
		j++;
		document.write("    <td class='inputsyslog1'>"+lldpInfoList[j]+"</td>");
		j++;
		document.write("    <td class='inputsyslog1'>"+lldpInfoList[j]+"</td>");
		j++;
		document.write("    <td class='inputsyslog1'>"+lldpInfoList[j]+"</td>");
		j++;
		document.write("    <td class='inputsyslog1'>"+lldpInfoList[j]+"</td>");
		j++;
		document.write("    <td class='inputsyslog1'>"+lldpInfoList[j]+"</td>");
		j++;
		//document.write("    <td >"+lldpInfoList[j]+"</td>");
		//document.write("    <td class='inputsyslog1'><a  target='_blank'  href='http://"+lldpInfoList[j].split("(",1)+"'>"+lldpInfoList[j]+"</a></td>");
		document.write("    <td class='inputsyslog1'>"+lldpInfoList[j]+"</td>");
		j++;

		document.write("  </tr>");
	}
}
 
function checkData()
{
	var tf=document.port_setting;
	tf.action = "/goform/saveComm?name=port";
	tf.submit();
}

function refreshpage()
{
  location.href='lldpinfo.asp?ltime='+<% write(lltime); %>;
}

function changebgcolor_port(){
 var tab = document.all.table_port;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function showHelpinfo()
{
   showHelp('port',<% write(lang); %>);
}

</SCRIPT>
</head>

<body  onload=""><br>

<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="port_setting" method="POST" action="/goform/PortLimitChange">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>


<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" ><table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td>

 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>LLDP设置/LLDP信息</b></font></td></tr>
 </table>
 
<!--
        <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
            <tr>
              <td colspan="2" align="left"  class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"LLDP信息");</script></td>
            </tr>
        </table>
-->

        </td>
      </tr>
     
    
    
      <tr>
        <td><table width="100%" height="35"  border="0" cellpadding="0" cellspacing="0"  class="tablebord" id="table_port">
            <tr height="30" align="center" class="td7">
      <td width="12%" nowrap  class="all_tables_list">本地端口</td>
      <td width="12%" nowrap  class="all_tables_list">系统ID</td>
      <td width="12%" nowrap  class="all_tables_list">远端端口</td>
      <td width="12%" nowrap  class="all_tables_list">端口描述</td>
      <td width="13%" nowrap  class="all_tables_list">系统名</td>
      <td width="13%" nowrap  class="all_tables_list">系统描述</td>
      <td width="13%" nowrap  class="all_tables_list">系统类型</td>
      <td width="13%" nowrap  class="all_tables_list">管理地址</td>
      
                 </tr>
			
	
           <script language="javascript">
writeLines();

</script>
        </table></td>
      </tr>
      <tr>
        <td align="center" height="35">
          <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
          &nbsp;
         <!-- <input name="Submit" type="button" class="button" value="保 存" onclick="checkData()">
          &nbsp;
          <script>writebutton(1,<% write(lang); %>,"帮  助","button","button","Help","showHelpinfo()");</script>
          -->
      </tr>
    </table></td>
  </tr> 
</table>
</td></tr>


</table>

<input type="hidden" name="todo" value="save">
<INPUT type="hidden" name="this_file" value="port.asp">
<INPUT type="hidden" name="next_file" value="port.asp">
<input type="hidden" name="message" value="@msg_text#">
</form> 
<script>
//changebgcolor();
changebgcolor_port();
<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</body>

</html>

