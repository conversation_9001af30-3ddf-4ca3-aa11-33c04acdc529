<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"writemsg
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<%  var authmode; checkCurMode(); %>

<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<script language="javascript" type="text/javascript" src="js/alpinejs.min.js" defer></script>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"IPMAC地址绑定");</script></title>

<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript" type="text/JavaScript">

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

/*by LuoM  11-5-18*/
function MacCheck(mac_addr)
{
	var myRE =/^[0-9a-fA-F]{1,4}\.[a-fA-F0-9]{1,4}\.[a-fA-F0-9]{1,4}$/;
	if(myRE.test(mac_addr))
	{
		var val = mac_addr.split(".",1);
		var vals = val[0].split("");
			if(val[0].length==3){
				if(vals[0]<10 && vals[0]>=0){
					if(vals[0]%2==1){
						return false;
					}
				}
				else{
					if((vals[0].charCodeAt()+1)%2==1){
						return false;
					}
				}
			}
			if(val[0].length==4){
				if(vals[1]<10 && vals[1]>=0){
					if(vals[1]%2==1){
						return false
					}
				}
				else{
					if((vals[1].charCodeAt()+1)%2==1){
						return false
					}
				}
			}
			return true;
	}
	else
	{
		return false;
	}
	
}

/*by LuoM  11-5-18*/
function DataScope(value,max,min)
{
	if(!isNaN(value) && value>=min && value<=max)
		return true;
	else
		return false;
}

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables all_tables2" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables all_tables1 btn3" ;
		}

     }

  }
}
function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function getPage(page)
{
   location.href="ipmactab.asp?page="+page+"&ltime="+<% write(lltime); %>;
}

function dofirst(){

location.href="ipmactab.asp?page=1&ltime="+<% write(lltime); %>;

}
function tdIpCheck(textValue)
{
    re1=/(\d+)(\W)(\d+)(\W)(\d+)(\W)(\d+)/
    Check=textValue.search(re1);
    if(Check==-1)
    {
        return false;
    }
    else
    {
        ipSplit=textValue.split('.');
        if(ipSplit.length!=4)
        {
            return false;
        }

        for(i=0; i<ipSplit.length; i++)
        {
            if(isNaN(ipSplit[i]))  return false;
			if(ipSplit[i].length > 1 && ipSplit[i].charAt(0) == "0") return false;
        }
    
        for(i=0; i<ipSplit.length; i++)
        {
            if(ipSplit[i] > 255)
            {
                return false;
            }
            if(ipSplit[i] < 0)
            {
                return false;
            }
        }
        if((ipSplit[0]==255) && (ipSplit[1]==255) && (ipSplit[2]==255) && (ipSplit[3]==255))
        {
            return false;
        }

        if((ipSplit[0]==0) || (ipSplit[0]==127) || (ipSplit[3]==0) || (ipSplit[3]==255))
        {
            return false;
        }

        if(ipSplit[0] >= 224)
        {
            return false;
        }
        return true;
    }
}
function del_mac(mac,ip,name,group)
{
	var tf = document.SedTabsetting;
	var forfil_mac = document.getElementById("forfil_mac");
	var forfil_ip = document.getElementById("forfil_ip");
	var forfil_port = document.getElementById("forfil_port");
	var forfil_type = document.getElementById("forfil_type");
	var forfil_group = document.getElementById("forfil_group");
	
	forfil_type.value="del";
	forfil_mac.value=mac;
	forfil_ip.value=ip;
	forfil_port.value=name;
	forfil_group.value=group;
	
	tf.action="/goform/ipmaccfg";
	tf.submit();
	return 0;
}
function p(index,SedTab_ip, SedTab_mac, SedTab_port,group)
{
    var narr=6;
    var tbtd;
    var i;
    var tbtr = document.getElementById("SedTab_tbl").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");

	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
		
	/*display*/
		tbtr.cells[0].innerHTML = index;
	tbtr.cells[1].innerHTML = SedTab_ip;
	tbtr.cells[2].innerHTML = SedTab_mac;
	tbtr.cells[3].innerHTML = SedTab_port;
	tbtr.cells[4].innerHTML = group;
	tbtr.cells[5].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick=del_mac('"+SedTab_mac+"','"+SedTab_ip+"','"+SedTab_port+"','"+group+"')>";


}


function AddOption(port_name){
	var selectObject = document.getElementById("forfil_port");
	var y=document.createElement('option');
  	y.text=port_name;
	y.value=port_name;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  catch(ex)
    {
    selectObject.add(y); // IE only
    }
}



function selectForfil()
{
	var tf = document.SedTabsetting;
	var forfil_mac = document.getElementById("forfil_mac").value;
	var forfil_ip = document.getElementById("forfil_ip").value;
	var forfil_type = document.getElementById("forfil_type");
	var forfil_group = document.getElementById("forfil_group").value;
	
	forfil_type.value="add";

	if(forfil_mac.length<14)
	{
		forfil_mac="0000.0000.0000";
		document.getElementById("forfil_mac").value=forfil_mac;
	}
	if(forfil_ip.length<7)
	{
		forfil_ip="0.0.0.0";
		document.getElementById("forfil_ip").value=forfil_ip;
	}
	if((forfil_ip!="0.0.0.0")&&tdIpCheck(forfil_ip)==false)
	{
		alert("ip地址格式：A.B.C.D");
		return false;
	}
	if(!MacCheck(forfil_mac))
	{
		alert(putmsg(<% write(lang); %>,"输入MAC地址非法  格式为16进制：HHHH.HHHH.HHHH"));
		return false;
	}
	if(forfil_ip=="0.0.0.0"&&forfil_mac=="0000.0000.0000")
	{
		alert(putmsg(<% write(lang); %>,"输入MAC地址和IP地址不能全为空"));
		return false;
	}

	if((forfil_group<1)||(forfil_group>16)||(forfil_group==""))
	{
		alert(putmsg(<% write(lang); %>,"输入端口组不在范围内"));
		return false;
	}
	tf.action = "/goform/ipmaccfg";
	tf.submit();
	
}
function checkData(x)
{
	var tf=document.SedTabsetting;
	tf.action = "/goform/saveComm?name=ipmactab";
	tf.submit();	
}



</script>
</head>

<body >

<% web_get_stat(); %>
<script>
	checktop(<% write(lang); %>);
	retValue = <% var responseJsonStr; jw_get_ipMacBindConfig("{\"pageNum\":0}"); %>
	responseStr = <% write(responseJsonStr); %>;
</script>

<form name="SedTabsetting" method="POST" action="ipmactab.asp" class="formContain">
	<input type="hidden" name="left_menu_id" value="">
    <input type="hidden" name="ltime" value=<% write(lltime); %>>
	<input type="hidden" name="lastts" value=<% write(serverts); %>>
	<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
	
	<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
		<tr><td height="20px" ><font size="5" color="#404040"><div class="bot">IPMAC列表</div></font></td></tr>
	</table>
	<table id="SedTab_tbl" name="SedTab_tbl"	 border="0" cellspacing="0" cellpadding="0" width="100%"  class="tablebord">
		<tr>
			<th width="10%" height="25" class="all_tables">序号</th>
			<th width="18%" height="25" class="all_tables">IP</th>
			<th width="18%" height="25" class="all_tables">MAC地址</th>
			<th width="12%" height="25" class="all_tables">转发端口</th>
			<th width="12%" height="25" class="all_tables">端口组</th>
			<th width="12%" height="25" class="all_tables">删除</th>
		</tr>
		<template x-for="(row,rowIndex) in responseStr.ipMacBind">
			<tr>
				<td width="10%" height="25" style="font-weight: normal" class="all_tables" x-text="row.index"></td>
				<td width="18%" height="25" style="font-weight: normal" class="all_tables" x-text="row.ipAddr"></td>
				<td width="18%" height="25" style="font-weight: normal" class="all_tables" x-text="row.macAddr"></td>
				<td width="12%" height="25" style="font-weight: normal" class="all_tables" x-text="row.portName"></td>
				<td width="12%" height="25" style="font-weight: normal" class="all_tables" x-text="row.portGroup"></td>
				<td width="12%"  height="25" align="center" class="td6">&nbsp;<script>writemsg(<% write(lang); %>,"删除");</script></td>
			</tr>
		</template>
		<script>
				<%  var errorcode; IpmacTabShow("ipmac");%>
		</script>
	</table>
	
	<br />
	<br />
	<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
		<tr><td height="30px" ><font size="5" color="#404040"><div class="bot">IPMAC绑定</div></font></td></tr>
	</table>
	<table  width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">
		
			
		<tr>
			<td height="30px" align="right" class="crons">&nbsp;IP:</td>
			<td colspan="5" align="left" class="crons">
			&nbsp;<input type="text" name="forfil_ip" id="forfil_ip" >&nbsp;(A.B.C.D)          	</td>
		</tr>

		<tr>
			<td width="42%" height="30px" align="right" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"MAC地址:");</script></td>
			<td colspan="5" align="left" class="crons">
			&nbsp;<input type="text" name="forfil_mac" id="forfil_mac" >&nbsp;<script>writemsg(<% write(lang); %>,"(HHHH.HHHH.HHHH, 为16进制格式)");</script></td>
		</tr>
		<tr>
			<td height="30px" align="right" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"转发端口:");</script></td>
			<td colspan="5" align="left" class="crons">
				<input type="radio" name="mac_forfil"  id="mac_forfil2" value="interface" onClick="forfilChange()"  style='display:none'>
				&nbsp;<select id="forfil_port" name="forfil_port"></select>
					<script>
						<% AppendOptionForfil(); %>	
					</script>
				</label>
				</td>
		</tr> 
		<tr>
			<td width="42%" height="30px" align="right" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"端口组");</script></td>
			<td colspan="5" align="left" class="crons">
			&nbsp;<input type="text" name="forfil_group" id="forfil_group" >&nbsp;<script>writemsg(<% write(lang); %>,"(1-16)");</script>      		</td>
		</tr>
		
		<tr>
			<td colspan="6" class="crons" style="text-align: center">
			<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添加","button","button","add_arp","selectForfil()");</script>
			</td>
		</tr>
	</table>
	<input type="hidden" name="forfil_type" id="forfil_type" value="" >

</form>   
<script>
	changebgcolor();
	changebgcolor_name("SedTab_tbl");
	<% if (errorcode=="1") { write("alert(putmsg("); write(lang); write(",'正在删除，可能需要一些时间，稍后请手动刷新页面!'));"); } %>
	<% if (errorcode != "") { if (errorcode != "1") {write_errorcode(errorcode); }} %>
</script>


</body>
</html>



