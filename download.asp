<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<title>端口管理</title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="js/check.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/msg.js"></script>
<script language="JavaScript">

var portNum = 24;
var trunkNum = 4;
var timeHandle;
var timeout=10*60*1000;
var language="ch";
function checktop(lang)
{
	language=lang;
	if (top.location == location)
	{
		top.location.href = "login_"+lang+".asp";
	}
}
checktop(language);
function Keydown()
{
	localStorage.setItem("lasttimeHandle", 30000);
}
function Mousemove()
{
	localStorage.setItem("lasttimeHandle", 30000);
}
function Check_click_key_mouse()
{
    document.onkeydown=Keydown;
    document.onmousemove=Mousemove;
}

function overtime()
{
	if(language=="en")
		alert("Idle timeout. Please login again");	
	else
		alert("您超时了，请重新登录!");	
	//parent.frames[2].menuForm.submit();
	top.location.href="login_"+language+".asp";
}


function setSysOverTime(obj)
{
	var a = window.top;

    a.timeHandle = setTimeout("overtime()",timeout);
}

Check_click_key_mouse();
//setSysOverTime();


function messageCheck()
{
	var hid = document.webForm;
			hid.submit();//提交页面表单

	return true;
}
</SCRIPT>
</head>

<body  >
<form  name="webForm" method="post" action="/goform/testddd">
	<table>
			<tr>
				<td width=150 height="23" align="center">下载文件：</td>
				<td width=300 height="23" align="left">&nbsp</td>
			</tr>
<tr><td>
	<p><a href="/" target="_blank">下载：WEB-INF.zip</a><a href="/" target="_blank"></p></a>
</td></tr>
</table>
</form>     
</body>

</html>
