<HTML>

<HEAD>
	<% var lltime,lang; getltime_lanflag(); %>



		<TITLE>page</TITLE>
		<META http-equiv=Content-Type content="text/html; charset=utf8">
		<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
		<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
		<script language="javascript" type="text/javascript" src="js/alpinejs.min.js" defer></script>
		<!-- <script language="javascript" type="text/javascript" src="js/func/logintime.js"></script> -->
		<link href="css/display_web.css" rel="stylesheet" type="text/css" />

		<SCRIPT LANGUAGE="javascript">

			function display_version(ProName, CopyInfo, CompanyName, productSeq, deviceName, devicePosition, HwVerInfo, FwVerInfo, VerInfo, VerCRC, VerDate, icdVer, icdCRC, cidVer, cidCRC, PortNumber, BuatRate, MACAddr, MACAddr1, RunTime, CPURatio, MemRatio, brd_temp, cpu_temp, vol, button) {

				var i, j;
				//var buf= new Array(putmsg(<% write(lang); %>,'设备型号'),putmsg(<% write(lang); %>,'唯一编码'),putmsg(<% write(lang); %>,'设备描述'),putmsg(<% write(lang); %>,'设备名称'),putmsg(<% write(lang); %>,'设备位置'),putmsg(<% write(lang); %>,'硬件版本'),putmsg(<% write(lang); %>,'软件版本'),putmsg(<% write(lang); %>,'MAC地址'), putmsg(<% write(lang); %>,'运行时间'), putmsg(<% write(lang); %>,'软件校验码'),putmsg(<% write(lang); %>,'软件生成时间'),putmsg(<% write(lang); %>,'ICD模型版本'),putmsg(<% write(lang); %>,'ICD模型校验码'),putmsg(<% write(lang); %>,'CID模型版本'),putmsg(<% write(lang); %>,'CID模型校验码'),putmsg(<% write(lang); %>,'生产厂商'),putmsg(<% write(lang); %>,'固件版本'),putmsg(<% write(lang); %>,'端口数量'), putmsg(<% write(lang); %>,'CPU利用率'),putmsg(<% write(lang); %>,'内存利用率'),putmsg(<% write(lang); %>,'板子温度'),putmsg(<% write(lang); %>,'CPU温度'),putmsg(<% write(lang); %>,'工作电压'),putmsg(<% write(lang); %>,'拨码开关'));
				//var info=new Array(CopyInfo, productSeq, ProName, deviceName,devicePosition, HwVerInfo, VerInfo, MACAddr, RunTime, VerCRC, VerDate, icdVer, icdCRC, cidVer, cidCRC, CompanyName, FwVerInfo, PortNumber, CPURatio, MemRatio, brd_temp, cpu_temp, vol, button);

				var buf = new Array(putmsg(<% write(lang); %>, '设备型号:'), putmsg(<% write(lang); %>, '唯一编码:'), putmsg(<% write(lang); %>, '设备描述:'), putmsg(<% write(lang); %>, '设备名称:'), putmsg(<% write(lang); %>, '设备位置:'), putmsg(<% write(lang); %>, '硬件版本:'), putmsg(<% write(lang); %>, '软件版本:'), putmsg(<% write(lang); %>, '内部MAC:'), putmsg(<% write(lang); %>, '外部MAC:'), putmsg(<% write(lang); %>, '运行时间:'), putmsg(<% write(lang); %>, '软件校验码:'), putmsg(<% write(lang); %>, '软件生成时间:'), putmsg(<% write(lang); %>, 'ICD模型版本:'), putmsg(<% write(lang); %>, 'ICD模型校验码:'), putmsg(<% write(lang); %>, 'CID模型版本:'), putmsg(<% write(lang); %>, 'CID模型校验码:'), putmsg(<% write(lang); %>, '生产厂商:'), putmsg(<% write(lang); %>, '固件版本:'));
				var info = new Array(CopyInfo, productSeq, ProName, deviceName, devicePosition, HwVerInfo, VerInfo, MACAddr, MACAddr1, RunTime, VerCRC, VerDate, icdVer, icdCRC, cidVer, cidCRC, CompanyName, FwVerInfo);

				var innr = buf.length;
				var tbtr;
				var tbtd;


				for (i = 0; i < innr; i++) {

					if (i == 3 || i == 4) {

					} else {
						if (info[i][0] == "-" || info[i].length < 2) { continue; }
					}



					tbtr = document.getElementById("sys_info").insertRow(-1);

					tbtr.classname = "td25";
					tbtr.height = "22";

					tbtr.setAttribute("height", "30");
					tbtr.setAttribute("class", "td25");
					tbtr.setAttribute("className", "td25");
					for (j = 0; j < 2; j++) {
						tbtd = document.createElement("td");
						tbtd.align = "left";
						if (j == 0)
							tbtd.setAttribute("width", "42%");
						else
							tbtd.setAttribute("width", "58%");
						tbtd.setAttribute("class", "td25");
						tbtd.setAttribute("className", "td25");
						tbtr.appendChild(tbtd);
					}

					tbtr.cells[0].innerHTML = "&nbsp;" + buf[i];
					if (i == 3 || i == 4) {
						tbtr.cells[1].innerHTML = "&nbsp;" + `<input  type='text' value='${info[i]}' id=id${i}>`
					} else {
						tbtr.cells[1].innerHTML = "&nbsp;" + info[i];
					}


				}


			}


			function changebgcolor_sysinfo() {
				var tab = document.all.sys_info;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {

							tab.rows[i].cells[j].className = "all_tables all_tables2";
						}
						else {
							tab.rows[i].cells[j].className = "all_tables all_tables1";
						}

					}

				}
			}

			function display() {
				setTimeout(() => {
					let dom = document.getElementsByClassName('ulList')[0];
					// 国产浏览器无法正确显示宽度，只能在这里设置宽度达到居中效果
					if (hiddenstatusStrArr.length > 20) {
						dom.style.width = '765px'
					} else {
						dom.style.width = '350px'
					}
				}, 100)
				changebgcolor_sysinfo();
			}

			function checkChar(str) {
				for (var loop_index = 0; loop_index < str.length; loop_index++) {
					if (
						(str.charAt(loop_index) == '<')
						|| (str.charAt(loop_index) == '>')
					) {
						alert("<,>是非法字符.请重新输入.");
						return false;
					}
				}
				return true;
			}

			function check() {
				var sysname = document.forms[0].tSysContact.value;
				if (checkChar(sysname) == false) {
					document.forms[0].tSysContact.select();
					return false;
				}

			}


			function checktextarea(src) {
				var Temp = src.value.substring(0, src.value.length - 1);
				if (src.value.length > 254)
					src.value = src.value.substring(0, 253);
			}


			function showHelp(helpname, lang) {
				var tmp = lang + "_help.html#" + helpname;
				window.open(tmp);
			}
			function showHelpinfo() {
				showHelp('sysinfo',<% write(lang); %>);
			}

			//tmp test devicename set 
			function setSystemInfo() {
				var hid = document.webForm;
				//todo input update date
				document.forms[0].para1.value = document.getElementById("id3").value;
				document.forms[0].para2.value = document.getElementById("id4").value;

				hid.action = "/goform/setDeviceInfo";
				hid.submit();
				return true;
			}
			function refresh() {
				location.reload();
			}

		</script>

		<style>
			.ulList {
				padding: 0;
				height: 85px;
				list-style: none;
				width: fit-content;
				margin: 0 auto 50px;
				position: relative;
				display: flex;
			}

			.flex {
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.ulList div {
				text-align: center;
			}

			.ulList img {
				width: 40px;
				height: 30px;
			}

			.ulList .light {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				align-items: center;
				width: 100px;
				height: 110px;
				flex-direction: column-reverse
			}

			.ulList .light2 {
				display: flex;
				flex-wrap: wrap;
				justify-content: flex-end;
				align-items: center;
				width: 100px;
				gap: 10px;
				align-content: flex-end;
				height: 110px;
			}

			.ulList .electricity {
				display: flex;
				justify-content: space-between;
				align-items: center;
				height: 110px;
			}

			.electricity .item {
				margin: 0 3px 0;
			}

			.ulList .ele-left {
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;
				align-items: center;
				width: 170px;
				height: 110px;
				flex-direction: column-reverse
			}

			.ele-left .item {
				height: 50px;
				width: 50px;
			}

			.ulList .ele-right {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				align-items: center;
				width: 100px;
				height: 110px;
				flex-direction: column-reverse
			}

			.refresh {
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				top: 200px;
				cursor: pointer;
			}
		</style>
</HEAD>

<BODY onload=display()>
	<br>
	<% web_get_stat(); %>
		<% var authmode; checkCurMode(); %>
			<% var hiddenstatusStr, packetsStr, alarm, power1_alarm, power2_alarm; vdd_topHidden_get(); %>

				<script>


					retValue = <% var responseJsonStr; jw_get_portConfig(); %>

						responseStr = <% write(responseJsonStr); %>;
					localStorage.setItem('portList', JSON.stringify(responseStr.PortConfig))

					var hiddenstatusStr = "<% write(hiddenstatusStr); %>";
					// var packetsStr = "<% write(packetsStr); %>";
					// var alarm = "<% write(alarm); %>";
					// var power1_alarm = "<% write(power1_alarm); %>";
					// var power2_alarm = "<% write(power2_alarm); %>";
					var hiddenstatusStrArr = hiddenstatusStr.split(',').filter(function (item) {
						return item != 0;
					});
					//var hiddenstatusStrArr = ['3', '3', '1', '2', '1', '1', '1', '2', '1', '1']
					var imgArr = ['data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXBAMAAACYK1DSAAAAIVBMVEXj4+Opqal8fHwBAQERERFpYl1OTUvS0tK3t7eZmZnCwsJevw6oAAAAAWJLR0QAiAUdSAAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB9cEGgk7C616mjMAAABcSURBVHjaY2AAA2MggLAY2DtAACQAZkxlYF+1atViY2NnYxNjKyATLgACXnCBJUAFSCqcIfJAytkELACkjZ2BGAQgAkhgkAqYQAVM0FSgAKIEylHAVAZGJRQQCgCsfXte0Tk7dwAAAABJRU5ErkJggg==', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXBAMAAAHvLGBEAAAAB3RJTUUH1AIKDDogR9tASAAAAAlwSFlzAAAOwwAADsMBx2+oZAAAAARnQU1BAACxjwv8YQUAAAAhUExURePj46mpqXx8fAEBAREREWliXU5NS9LS0re3t5mZmcLCwl6/DqgAAACXSURBVHjatY5BCgIxDEWDLgZm6REKeoGfjd1m530GwRN4gTmAVBfmlP4mODgguPK1tC+fpq2IyBZ9ElTRKqO7M2AxNrgEjG7SgGMud+XiimccJhQEX8WbATyzKQVayl4WLtn++JBosrfghxhHFzdojXuQsuNbKOUgw3nFJIOvmBlAsZAB/hBYOjfTCNxbRqf8x7XT65D5BRFBeUiNNVMyAAAAAElFTkSuQmCC',
					]
					var imgArr2 = ['data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXCAYAAABqBU3hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAGYktHRAD/AP8A/6C9p5MAAAAHdElNRQfXBBoKAjVg2bR7AAAA9klEQVRIS+2UMQ6CMBSGn54AL0DiyCnYcehtPAujR5CFncRA3EwYGEngALAwa1pf5QmlFiUs9pv+vobmo695m7qu7zAD13UxveM4DrRtC1VVYcUMIVCWJXRdhyU9QRBgUhPHMSY9XPR2vTwFmqYR9lP4vo+pR/4xZVhLkgTTmKIohMAW17NRCdMalzHBSIAepjuY7ululDJqAWPM+GPK8PoltM5zFEUiyxaMBHi/wzAEz/PEekn42fJdaAX254PIS3LaHZUCXz/CpbACVsAK/I8AHzwqlJNQMjXff+HjKF4LO4olrxasTZZl/RtI0xTyPMetNQF4AFE1ti2vqz4vAAAAAElFTkSuQmCC', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXCAYAAABqBU3hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAOwQAADsEBuJFr7QAAAAd0SU1FB9QCCg0eFnhLXkAAAAEKSURBVEhLY3z8+PF/hgEC9+/fZwA74P3791Ah+oEPHz4wLF8wG+EAkAA9wfXr1xkunDzCwATlDxgYcAdgRIG9vT2YBgEBAQGqR83BgwfBNCwKsDpAcYM3mE1NUP7ClyEjIwPDAXSLAk1NTSgLFYwmwlEHjDpg1AFYS8IZM2bgLDgoASCzCRbF/v7+ZJX/uOoNZHEQe+PGjWA2TgdgA8iOwldBocvBfIsNkFQXIBuKz6HIciDHEAPIToTYLEAWw+dQZACOAlDj8OvXr1Ah/MDLywvKwg62bdsGZeEHjx49gqSBQ4cO/X/+/DnD5cuXoVL4QWtrK5SFCmDxX11dDRXBD94+e8jw9PU7BgBu9sLStNVQzwAAAABJRU5ErkJggg==',]
				</script>
				<div class="formContain" style="margin-bottom: 15px;">
					<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="30px">
								<font size="5">
									<div class="bot">设备端口信息</div>
								</font>
							</td>
						</tr>
					</table>
					<div class="refresh">
						<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "刷  新", "button", "button", "det_arp", "refresh()");</script>
					</div>

					<div x-data="{arr:hiddenstatusStrArr}" class="ulList">
						<div class="">
							<template x-if="arr.length>10">
								<div class="light">
									<template x-for="(item,index) in arr" :key="index">
										<template x-if="item==3||item==4">
											<div :class="`item${Number(index)+1}`">
												<div x-text="index+1" x-show="(index+1)%2==0"></div>
												<template x-if="item==3">

													<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXBAMAAACYK1DSAAAAB3RJTUUH2AYKCB4IBs3HcwAAAAlwSFlzAAALEgAACxIB0t1+/AAAAARnQU1BAACxjwv8YQUAAAAwUExURePj46mpqXx8fNLS0mliXU5NS7e3t5mZmcLCwgAAAAAAAAAAAAAAAAAAAAAAAAAAAGq05IQAAAAtSURBVHjaY2BAB8zGKMCEgbkDBZRgE5iJBIa/QEcbofBAF0hDASUMjEoowBUARgqsenjRgOAAAAAASUVORK5CYII="
														alt="">
												</template>
												<template x-if="item==4">
													<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXBAMAAACYK1DSAAAAB3RJTUUH2AYKCCIH5YGjHQAAAAlwSFlzAAAK8AAACvABQqw0mAAAAARnQU1BAACxjwv8YQUAAAAwUExURQAAAE5NS2liXXx8fJmZmampqbe3t8LCwtLS0uf8A+Pj4wAAAAAAAAAAAAAAAAAAAAh+Of8AAAA7SURBVHjaY1iFBhhWdKCAJoYV5SjACZsAAxIAC3DOhIMJQ1BgAgMDJ1wAw3OEwwNdIA0FODEsNUYBigCg/alOPkwAGwAAAABJRU5ErkJggg=="
														alt="">
												</template>
												<div x-text="index+1" x-show="(index+1)%2!=0"></div>
											</div>
										</template>
									</template>
								</div>
							</template>
							<template x-if="arr.length<=10">
								<div class="light2">
									<template x-for="(item,index) in arr" :key="index">
										<template x-if="item==3||item==4">
											<div :class="`item${Number(index)+1}`">
												<template x-if="item==3">

													<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXBAMAAACYK1DSAAAAB3RJTUUH2AYKCB4IBs3HcwAAAAlwSFlzAAALEgAACxIB0t1+/AAAAARnQU1BAACxjwv8YQUAAAAwUExURePj46mpqXx8fNLS0mliXU5NS7e3t5mZmcLCwgAAAAAAAAAAAAAAAAAAAAAAAAAAAGq05IQAAAAtSURBVHjaY2BAB8zGKMCEgbkDBZRgE5iJBIa/QEcbofBAF0hDASUMjEoowBUARgqsenjRgOAAAAAASUVORK5CYII="
														alt="">
												</template>
												<template x-if="item==4">
													<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAXBAMAAACYK1DSAAAAB3RJTUUH2AYKCCIH5YGjHQAAAAlwSFlzAAAK8AAACvABQqw0mAAAAARnQU1BAACxjwv8YQUAAAAwUExURQAAAE5NS2liXXx8fJmZmampqbe3t8LCwtLS0uf8A+Pj4wAAAAAAAAAAAAAAAAAAAAh+Of8AAAA7SURBVHjaY1iFBhhWdKCAJoYV5SjACZsAAxIAC3DOhIMJQ1BgAgMDJ1wAw3OEwwNdIA0FODEsNUYBigCg/alOPkwAGwAAAABJRU5ErkJggg=="
														alt="">
												</template>
												<div x-text="index+1"></div>
											</div>
										</template>
									</template>
								</div>
							</template>
						</div>
						<div class="electricity">
							<div class="ele-left">
								<template x-for="(item,index) in arr" :key="index">
									<template x-if="index < arr.length-4">
										<div class="item">
											<template x-if="item==1">
												<div :class="`item${index+1}`">
													<div x-text="index+1" x-show="(index+1)%2==0"></div><img
														:src="imgArr[(index+1)%2]" alt="">
													<div x-text="index+1" x-show="(index+1)%2!=0"></div>
												</div>
											</template>
											<template x-if="item==2">
												<div :class="`item${index+1}`">
													<div x-text="index+1" x-show="(index+1)%2==0"></div><img
														:src="imgArr2[(index+1)%2]" alt="">
													<div x-text="index+1" x-show="(index+1)%2!=0">
													</div>

											</template>
										</div>

									</template>
								</template>

							</div>
							<div class="ele-right">
								<template x-for="(item,index) in arr" :key="index">
									<template x-if="index >= arr.length-4">
										<div class=""> <template x-if="item==1">
												<div :class="`item${index+1}`">
													<div x-text="index+1" x-show="(index+1)%2==0"></div><img
														:src="imgArr[(index+1)%2]" alt="">
													<div x-text="index+1" x-show="(index+1)%2!=0"></div>
												</div>

											</template>
											<template x-if="item==2">
												<div :class="`item${index+1}`">
													<div x-text="index+1" x-show="(index+1)%2==0"></div><img
														:src="imgArr2[(index+1)%2]" alt="">
													<div x-text="index+1" x-show="(index+1)%2!=0">
													</div>

											</template>
										</div>


									</template></template>

							</div>

						</div>
					</div>


				</div>

				<form name="webForm" method="post" action="/goform/setDeviceInfo" class="formContain">
					<div id="view_help" style="display:none">
						<input type="hidden" name="para1">
						<input type="hidden" name="para2">
						<input type="hidden" name="ltime" value=<% write(lltime); %>>
						<input type="hidden" name="lastts" value=<% write(serverts); %>>
						<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
						<input type="hidden" id="reauthn" name="reauthn" value="">
						<input type="hidden" id="reauthpd" name="reauthpd" value="">
						<input type="hidden" id="hiddenstatusStr" value="<% write(hiddenstatusStr); %>">
						<input type="hidden" id="packetsStr" value="<% write(packetsStr); %>">
						<input type="hidden" id="alarm" value="<% write(alarm); %>">
						<input type="hidden" id="power1_alarm" value="<% write(power1_alarm); %>">
						<input type="hidden" id="power2_alarm" value="<% write(power2_alarm); %>">

						<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 class="tablebord"></TABLE>
					</div>

					<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="30px">
								<font size="5" color="#404040">
									<div class="bot">设备基本信息</div>
								</font>
							</td>
						</tr>
					</table>

					<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="sys_info" class="tablebord1">
						<TBODY>
							<script>
									<%  var errorcode; sysInfo(); %>
							</script>
							<tr>
								<td colspan="2" style="text-align: center;">
									<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "button", "button", "det_arp", "setSystemInfo()");</script>
								</td>
							</tr>
						</TBODY>

					</TABLE>
				</form>
				<script>


				</script>
</BODY>

</HTML>