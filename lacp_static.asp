<!DOCTYPE html>
<html>

<head>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<meta http-equiv="Content-Type" content="text/html; charset=utf8">
		<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
		<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
		<script src="js/alpinejs.min.js" defer></script>
		<title>&nbsp;
			<script>writemsg(<% write(lang); %>, "端口管理");</script>
		</title>
		<link href="css/display_web.css" rel="stylesheet" type="text/css" />
		<% var authmode; checkCurMode(); %>
			<script>
				trunkList = null
				ret = <% var responseJsonStr; jw_get_portTrunkConfig(); %>;
				trunkList =<% write(responseJsonStr); %>
					function changebgcolor() {
						var tab = document.all.table1;
						var len = tab.rows.length;
						for (var i = 0; i < len; i++) {
							var lencol = tab.rows[i].cells.length

							for (var j = 0; j < lencol; j++) {
								if (i > 1) {
									if (j % 2 == 1) {
										// tab.rows[i].cells[j].bgColor = "efefef";
										tab.rows[i].cells[j].className = "all_tables";
									}
									else {
										// tab.rows[i].cells[j].bgColor = "efefef";
										tab.rows[i].cells[j].className = "all_tables all_tables3";
									}
								} else {
									tab.rows[i].cells[j].className = "all_tables";
								}

							}



						}
					}
				function changebgcolor2() {
					var tab = document.all.table2;
					var len = tab.rows.length;
					for (var i = 0; i < len; i++) {
						var lencol = tab.rows[i].cells.length
						for (var j = 0; j < lencol; j++) {
							if (i == 0) {
								tab.rows[i].cells[j].className = "all_tables all_tables3";
							} else {
								if (j % 2 == 1) {
									// tab.rows[i].cells[j].bgColor = "efefef";
									tab.rows[i].cells[j].className = "all_tables ";
								}
								else {
									// tab.rows[i].cells[j].bgColor = "efefef";
									tab.rows[i].cells[j].className = "all_tables";
								}
							}
						}
					}
				}
				/*by LuoM  11-5-18*/
				function DataScope(value, max, min) {
					if (!isNaN(value) && value >= min && value <= max)
						return true;
					else
						return false;
				}

				function showTrunkPorts(trunkIdNo, ltime) {
					window.location.href = "lacp_static.asp?trunkIdNoDel=" + trunkIdNo + "&ltime=" + ltime;
				}

				function showTrunkPortsForAdd(trunkIdNo, ltime) {
					window.location.href = "lacp_static.asp?trunkIdNoAdd=" + trunkIdNo + "&ltime=" + ltime;
				}

				function showTrunkcfg(trunkIdNo, ltime) {
					window.location.href = "lacp_static.asp?trunkIdNo=" + trunkIdNo + "&ltime=" + ltime;
				}

				function ModifyChan(v1) {
					var hid = document.webForm;
					var hid1 = document.getElementById("modifyId");
					var hid2 = document.getElementById("modifyport");
					hid.idx.value = v1;
					hid1.value = v1;
					hid2.value = document.getElementById("oldmodifyport").value;
					return 0;
				}

				function delChan(v1) {
					var hid = document.webForm;
					hid.action = "/goform/setLacpStaticCfg"
					hid.idx.value = v1;
					hid.flag.value = 3;
					hid.submit();
					return 0;
				}

				function messageCheckdelPort(data) {
					var hid = document.webForm;
					hid.TrunkIdDel.value = data.portName

					hid.action = "/goform/DeletePortFromChannel"
					hid.submit();
					return 0;
				}

				function ShowStaticChan(v1, v2) {
					var tmp;
					var i = 0;
					var k = 0;
					for (i = 1; i < 129; i++) {
						tmp = "sa" + i;
						if (v1 != tmp)
							continue;
						else
							break;
					}
					if (<% write(authmode); %> == 1) {
						document.write("<tr height='30'><td align='center' width='15%' class='crons'>" + v1 + "</td><td align='center' width='70%' class='crons'>" + v2 + "</td><td align='center' width='15%' class='crons'><input name='Submit' id='isauthA' type='button' class='button'" + "value='" + putmsg(<% write(lang); %>, "删  除") + "' onclick='delChan(" + i + ")'></td></tr>");
					}	else {
						document.write("<tr height='30'><td align='center' width='15%' class='crons'>" + v1 + "</td><td align='center' width='70%' class='crons'>" + v2 + "</td><td align='center' width='15%' class='crons'></td></tr>");
					}
					return 0;
				}

				function messageCheck1() {
					var hid = document.webForm;
					if (hid.creatId.value.length == 0) {
						alert(putmsg(<% write(lang); %>, "请填写需要创建的汇聚组!"));
						return;
					}
					if (hid.portNum.value.length == 0) {
						alert(putmsg(<% write(lang); %>, "请选择端口,目前没有选择任何端口!"));
						return;
					}
					if (!DataScope(hid.creatId.value, 8, 1)) {
						alert(putmsg(<% write(lang); %>, "组ID必须在1到8之间!"));
						return;
					}
					hid.action = "/goform/setLacpStaticCfg"
					hid.flag.value = 1;
					hid.idx.value = hid.creatId.value;
					hid.submit();

					return true;
				}

				function messageCheckAdd(data, ports) {
					if (ports.length === 0) {
						alert(putmsg(<% write(lang); %>, "请选择端口,目前没有选择任何端口!"));
						return;
					}
					const hid = document.webForm;
					// let alr = data.trunkGroup.map(e => e.portName).filter(e => e !== '-')
					const count = [...ports]
					if (count.length >= 8) {
						alert(putmsg(<% write(lang); %>, "同一聚合组内成员端口数不能超过8个!"));
						return;
					}
					hid.portNum.value = count.join('|')
					hid.action = "/goform/setLacpStaticCfg"
					hid.flag.value = 4;
					hid.portNumCount.value = count.length.toString()
					hid.TrunkIdAdd.value = data.portName
					hid.submit();
					return true;
				}

				function messageCheck2() {
					var hid = document.webForm;
					hid.flag.value = 2;
					hid.submit();
					return true;
				}

				function messageCheck() {
					var hid = document.webForm;
					hid.action = "/goform/setStaticChannelLoadBalance"
					hid.submit();
					return true;
				}

				function isChecked(obj) {
					var target = document.getElementById("portNum");
					var counter = document.getElementById("portNumCount");
					var cc = 0;
					var p = obj.name;
					if (obj.checked) {
						cc = parseInt(counter.value);
						if (cc < 8) {
							cc++;
							counter.value = cc.toString();
							target.value = target.value + p + "|";
						}
						else {
							alert(putmsg(<% write(lang); %>, "同一聚合组内成员端口数不能超过8个!"));
							obj.checked = false;
							return;
						}
					}
					else {
						cc = parseInt(counter.value);
						cc--;
						counter.value = cc.toString();
						target.value = target.value.replace(p + "|", "");
					}
					return true;
				}

				function checkData() {
					var tf = document.webForm;
					tf.action = "/goform/saveComm?name=lacp_static";
					tf.submit();
				}

				function refreshpage() {
					location.href = 'lacp_static.asp?ltime=' +<% write(lltime); %>;
				}

				function showHelpinfo() {
					showHelp('lacp_static',<% write(lang); %>);
				}
			</script>
</head>

<body onload="display()" x-data="{showExt:false}" x-init="showExt=ret"><br>
	<% web_get_stat(); %>
		<script>

			function display() {
				if (document.getElementsByName(" isauthA")) {
					if (<% write(authmode); %> != 1)
					{
						allitem = document.getElementsByName("isauthA");
						for (i = 0; i < allitem.length; i++) allitem[i].style.display = "none";
					}
				} var
					counter = document.getElementById("portNumCount"); counter.value = "0";
			} checktop(<% write(lang); %>);
		</script>
		<form name="webForm" method="post" action="">
			<input type="hidden" name="idx">
			<input type="hidden" name="portNum" id="portNum">
			<input type="hidden" name="portNumCount" id="portNumCount">
			<input type="hidden" name="ltime" value=<% write(lltime); %> >
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
			<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
			<input type="hidden" name="TrunkIdAdd" id="TrunkIdAdd">
			<input type="hidden" name="TrunkIdDel" id="TrunkIdDel">
			<input type="hidden" name="flag">
			<div class="formContain">
				<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0"
					cellspacing="0">
					<tr>
						<td>

							<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"
								class="cword09">
								<tr>
									<td>
										<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
											<tr>
												<td height="30px">
													<font size="5" color="#404040">
														<div class="bot">端口汇聚列表</div>
													</font>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td>
										<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"
											class="cword09">
											<tr height="25">
												<td colspan="3" align="left" width="100%">&nbsp;
													<script>writemsg(<% write(lang); %>, "(备注:删除汇聚端口时，请先删除相关配置)");</script>
												</td>
											</tr>
											<tr>
												<td>
													<table width="100%" border="0" align="center" cellpadding="0"
														cellspacing="0">
														<tr height="25">
															<td width="100%">
																<table width="100%" border="0" align="center"
																	cellpadding="0" cellspacing="0" id="table2"
																	class="tablebord">
																	<tr>
																		<td align="center" width="15%" class="crons">
																			&nbsp;
																			<script>writemsg(<% write(lang); %>, "TRUNK ID");</script>
																		</td>
																		<td align="center" width="70%" class="crons">
																			&nbsp;
																			<script>writemsg(<% write(lang); %>, "端口成员");</script>
																		</td>
																		<td align="center" width="15%" class="crons">
																			&nbsp;
																			<script>writemsg(<% write(lang); %>, "操作");</script>
																		</td>
																	</tr>
																	<script>
																			<% showStaticChannelGroup(); %>
																	</script>
																</table>
															</td>
														</tr>
													</table>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td align="center" height="30" class="all_tables all_tables1">
										<div align="center" class="btn">
											<script>writebutton(1,<% write(lang); %>, "刷  新", "button", "button", "Refresh", "refreshpage()");</script>
										</div>
									</td>
								</tr>
								<tr>
									<td>

									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				</td>
				</tr>
				</table>

				</td>
				</tr>


				</table>
			</div>
			<div class="formContain" style="margin-top: 15px;">
				<div>
					<font size="5" color="#404040">
						<div class="bot">端口汇聚配置</div>
					</font>
				</div>

				<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table1"
					x-data="{portList:[]}"
					x-init="portList = JSON.parse(localStorage.getItem('portList')).map(item => item.portName)"
					class="tablebord">
					<!-- <tr height="25">
						<td align="left" class="crons" colspan="5" style="text-align: left;">&nbsp;
							<script>writemsg(<% write(lang); %>, "新建聚合组");</script>
						</td>
					</tr> -->
					<tr height="25">
						<!-- <td align=" left" class="crons" colspan="5">&nbsp;
						<% showPortForStaticLacp(); %>
							</td> -->
						<td align="center" style="font-weight: bold;">
							端口:
						</td>
						<template x-for="(item,idx) in portList" :key="idx">
							<td  class="all_tables all_tables3" x-text="item"></td>
						</template>

					</tr>
					<tr height="25">
						<!-- <td align="left" class="crons" colspan="5">&nbsp;
							<% showPortForStaticLacp(); %>
						</td> -->
						<td align="center" style="font-weight: bold;">
							选择:
						</td>
						<template x-for="(item,idx) in portList" :key="idx">
							<td align="center">
								<input type="checkbox" :name="item" onchange="isChecked(this)">
			
							</td>
						</template>
					</tr>
					<tr height="25">
						<!-- <td   align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"创建汇聚组");</script></td> -->
						<td align="left" class="crons" :colspan="portList.length+1">&nbsp;
							<script>writemsg(<% write(lang); %>, "Trunk ID:");</script>
							<input type="text" name="creatId">(1-8)
						</td>
					</tr>
					<tr height="25">
						<td align="left" class="crons" colspan="11">
							<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "添  加", "button", "button", "creat", "messageCheck1()");</script>
						</td>
					</tr>
					<template x-if="showExt" x-data="{trunkList:null}" x-init="if(ret){trunkList = trunkList;}">
						<tr height="25">
							<td align="left" class="crons" colspan="5" style="text-align: left;">&nbsp;
								<script>writemsg(<% write(lang); %>, "聚合组成员删除");</script>
							</td>
						</tr>
						<tr height="25" x-data="{modelPort:portTrunk[0]}">
							<td align="left" class="all_tables all_tables3">
								静态汇聚组ID:
							</td>
							<td nowrap="" align="left" class="all_tables">
								<select id="TrunkId" @change="modelPort=portTrunk[$el.value]">
									<template x-for="(port,index) in trunkList.portTrunk" :key="port.portName">
										<option :value="index" x-text="port.portName">
										</option>
									</template>
								</select>
							</td>
							<td align="left" class="all_tables all_tables3">
								端口:
							</td>
							<td align="left" class="all_tables">
								<select name="delportname">
									<template x-for="trunkPort in modelPort.trunkGroup" :key="trunkPort.portName">
										<option :value="trunkPort.portName" x-text="trunkPort.portName">
										</option>
									</template>
								</select>&nbsp;&nbsp;(至少保留一个成员口)
							</td>
							<td align="left" class="all_tables all_tables3">
								<input class="button" id="isauthA" name="isauthA" type="button" value="删  除"
									@click="messageCheckdelPort(modelPort)">
							</td>
						</tr>
						<tr height="25">
							<td align="left" class="crons" colspan="5" style="text-align: left;">&nbsp;
								<script>writemsg(<% write(lang); %>, "聚合组成员添加");</script>
							</td>
						</tr>
						<tr height="25" x-data="{ modelPort:portTrunk[0] ,portAvailable:[], portChecked:[] }"
							x-init="portAvailable=trunkList.portAvailable.filter(e=>e.portStat==='0')">
							<td align="left" class="all_tables all_tables3">
								静态汇聚组ID:
							</td>
							<td nowrap="" align="left" class="all_tables">
								<select id="TrunkId" @change="modelPort=portTrunk[$el.value]">
									<template x-for="(port,index) in trunkList.portTrunk" :key="port.portName">
										<option :value="index" x-text="port.portName">
										</option>
									</template>
								</select>
							</td>
							<td align="left" class="all_tables all_tables3">
								端口:
							</td>
							<td align="left" class="all_tables">
								<template x-for="(port,index) in portAvailable" :key="port.portName">
									<span>
										<input type="checkbox" :name="port.portName" :id="port.portName"
											:value="port.portName" x-model="portChecked">
										<label x-text="port.portName" :for="port.portName"></label>
									</span>
								</template>
							</td>
							<td align="left" class="all_tables all_tables3">
								<input class="button" id="isauthB" name="isauthB" type="button" value="添  加"
									@click="messageCheckAdd(modelPort,portChecked)">
							</td>
						</tr>
					</template>
				</table>
			</div>
		</form>
		<script>
			changebgcolor();
			changebgcolor2();
<% if (errorcode != "") { write_errorcode(errorcode); } %>
		</script>
</body>

</html>