<HTML>
<HEAD>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<TITLE>page</TITLE>
<META http-equiv=Content-Type content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />

<% var remoteClientcfg; getRemoteClientCfgInfo(); %>
<script>

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables all_tables2" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables all_tables1" ;
		}

     }

  }
}
 function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables all_tables2" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables all_tables1" ;
		}

     }

  }
}
 function changebgcolor3(){
 var tab = document.all.table3;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables all_tables2" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables all_tables1" ;
		}

     }

  }
}
  function changebgcolor4(){
 var tab = document.all.table4;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables all_tables2" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables all_tables1" ;
		}

     }

  }
}
async function messageCheck()
{
	var hid = document.webForm;
	var dplen;
	var dpvalue;
	var reauthn;
	var reauthpd;


	if(document.getElementById("d1").checked == true)
		document.forms[0].para1.value = "enable";
	else
		document.forms[0].para1.value = "disable";

	if(document.getElementById("s1").checked == true)
		document.forms[0].para2.value = "enable";
	else
		document.forms[0].para2.value = "disable";

	// if(document.getElementById("w1").checked == true)
	// 	document.forms[0].para3.value = "enable";
	// else
	// 	document.forms[0].para3.value = "disable";

	if(document.getElementById("b1").checked == true)
		document.forms[0].para4.value = "enable https";
	else if(document.getElementById("b2").checked == true)
		document.forms[0].para4.value = "enable http";
	else
		document.forms[0].para4.value = "enable";

	
	document.getElementById("reauthn").value =await userNamePrompt();
	// var pwd = prompt("操作认证-密码", "");
	// document.getElementById("reauthpd").value = pwd;
	document.getElementById("reauthpd").value = await testThePrompt();


	reauthn = document.getElementById("reauthn").value;
	reauthpd = document.getElementById("reauthpd").value;


	dplen = parseInt(reauthn.length/2);
	dpvalue = reauthn;
	reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(reauthpd.length/2);
	dpvalue = reauthpd;
	document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
	document.getElementById("reauthn").value = "#CDV2S8tYc72QSvf";


	hid.submit();
	return true;
}

function isIE() {
    if(!!window.ActiveXObject || "ActiveXObject" in window){
      return true;
    }else{
      return false;
　　 }
}
function display()
{
	tmp = "<% write(remoteClientcfg); %>";
	array_cfg = tmp.split(",");

	if (array_cfg[0] == "enable")
		document.getElementById("d1").checked = true;
	else
		document.getElementById("d2").checked = true;

	if (array_cfg[1] == "enable")
		document.getElementById("s1").checked = true;
	else
		document.getElementById("s2").checked = true;

	// if (array_cfg[2] == "enable")
	// 	document.getElementById("w1").checked = true;
	// else
	// 	document.getElementById("w2").checked = true;

	if (array_cfg[3] == "https")
		document.getElementById("b1").checked = true;
	else if (array_cfg[3] == "http")
		document.getElementById("b2").checked = true;
	else
		document.getElementById("b3").checked = true;

	var display="display:inline";

	if(isIE())
	{
		display="";
	}

	if (array_cfg[1] == "0")
		document.getElementById("table2").type = "display:none";
	else
		document.getElementById("table2").type = display;

	if (array_cfg[2] == "0")
		document.getElementById("table3").type = "display:none";
	else
		document.getElementById("table3").type = display;



}


</script>
</HEAD>


<body  onload="display();"><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>

<form  name="webForm" method="get" action="/goform/setRemoteClientConfig" class="formContain">

<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="para1">
<input type="hidden" name="para2">
<input type="hidden" name="para3">
<input type="hidden" name="para4">
<input type="hidden" id="reauthn" name="reauthn" value="">
<input type="hidden" id="reauthpd" name="reauthpd" value="">


 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="30px" ><font size="5" color="#404040"><div class="bot">远程客户端配置</div></font></td></tr>
 </table>




<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord3">
<tr height="25">
	<td width="42%" align="left" class="crons">&nbsp;telnet:</td>
	<td width="58%" colspan="3"align="left" class="crons">&nbsp;
		<input type="radio" name="rw" value="rw"  id="d1">Enable
		<input type="radio" name="rw" value="ro"  id="d2">Disable
	</td>
</tr>
</TABLE>

<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table2" class="tablebord3">
<tr height="25">
	<td width="42%" align="left" class="crons">&nbsp;ssh server:</td>
	<td width="58%" colspan="3"align="left" class="crons">&nbsp;
		<input type="radio" name="rw2" value="rw"  id="s1">Enable
		<input type="radio" name="rw2" value="ro"  id="s2">Disable
	</td>
</tr>
</TABLE>

<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table3" class="tablebord3">
<!-- <tr height="25">
	<td width="42%" align="left" class="crons">&nbsp;网管软件:</td>
	<td width="58%" colspan="3"align="left" class="crons">&nbsp;
		<input type="radio" name="rw3" value="rw"  id="w1">Enable
		<input type="radio" name="rw3" value="ro"  id="w2">Disable
	</td>
</tr> -->
</TABLE>

<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table4" class="tablebord4">
<tr height="25">
	<td width="42%" align="left" class="crons">&nbsp;web:</td>
	<td width="58%" colspan="3"align="left" class="crons">&nbsp;
		<input type="radio" name="rw4" value="rw"  id="b1">https
		<input type="radio" name="rw4" value="rw"  id="b2">http
		<input type="radio" name="rw4" value="rw"  id="b3">enable
	</td>
</tr>
</TABLE>

<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="mainHeader tablebord4">
<tr>
    <td class="tablenew" id=tabs name=tabs><div align="center" style="padding: 5px;">
	<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","buttons_apply","button","button","messageCheck()");</script>
    </div></td>

  </tr>
</table>
</FORM>
<script>
changebgcolor();
changebgcolor2();
changebgcolor3();
changebgcolor4();
<% if (errorcode=="3") { write("alert(putmsg(");write(lang);write(",'不具备执行该操作的权限!'));");  } %>
<% if (errorcode!="") { if (errorcode!="3") { write_errorcode(errorcode); } } %>

</script>

</BODY></HTML>

