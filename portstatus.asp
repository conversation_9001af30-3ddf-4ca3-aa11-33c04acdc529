
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<script language="javascript" type="text/javascript" src="js/alpinejs.min.js" defer></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"SNMP管理");</script></title>

<script language="javascript">

retValue = <% var responseJsonStr; jw_get_portState(); %>
responseStr = <% write(responseJsonStr); %>;

function AddOption(portname){

	var selectObject = document.getElementById("portStaNum");
	var y=document.createElement('option');
  	y.text=portname;
	y.value = portname;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  	catch(ex)
    {
   	selectObject.add(y); // IE only
  	}
}



function changePort()
{
document.form1.submit();
}


function clearSta()
{
 	var hid=document.form1;
	
	if (confirm("您确定要清除所有端口统计数据吗？"))
	{
		hid.action="/goform/clearPortStat";
		hid.submit();
	}
}
function refreshpage()
{
  //location.href='portstatus.asp?ltime='+<% write(lltime); %>;
  document.form1.submit();
}


var canSelect = false;

function selectChange(){
//if(canSelect)
document.form1.submit();
}

window.onload = function(){
canSelect = true;
checkVar();
}

window.onunload = function(){
canSelect = false;
}

function checkVar(){

// document.form1.portStaNum.value = document.form1.portname.value;
//selectChange();
}


</script>
</HEAD>
<BODY  >
<br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>

<form id="form1" name="form1" method="post" action="" onSubmit="" class="formContain">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
<tr><td height="30px" ><font size="5" color="#404040"><div class="bot">流量统计显示信息</div></font></td></tr>
</table>
<div x-data="{ 
	infoPort: [],
	selectAll: false,
	headers:['端口名称','输入字节','输入单播','输入广播','输入多播','输出字节','输出单播','输出广播','输出多播'],
}" x-init="infoPort=responseStr.infoPort">
	<table width="98%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord">
		<thead>
			<tr>
				<template x-for="(item,index) in headers" :key="index">
					<th class="all_tables" x-text="item"></th>
				</template>
			</tr>
		</thead>
		<tbody>
			<template x-for="(row, index) in infoPort" :key="index">
			<tr style="text-align: center;">
				<td class="all_tables" x-text="row.portName"></td>
				<td class="all_tables" x-text="row.inByte"></td>
				<td class="all_tables" x-text="row.inUcast"></td>
				<td class="all_tables" x-text="row.inBcast"></td>
				<td class="all_tables" x-text="row.inMcast"></td>
				<td class="all_tables" x-text="row.outByte"></td>
				<td class="all_tables" x-text="row.outUcast"></td>
				<td class="all_tables" x-text="row.outBcast"></td>
				<td class="all_tables" x-text="row.outMcast"></td>
			</tr>
			</template>
			<tr style="text-align: center;">
				<td class="all_tables" colspan="10">
					<script>writebutton(<% write(authmode); %>, <% write(lang); %>, "清零", "button", "button", "clearBut", "clearSta()");</script>
					&nbsp;&nbsp;
					<input type="submit" class="button" name="button" id="button" value="刷新" />
				</td>
			</tr>
		</tbody>
	</table>
</div>








<script>

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</form>

<br><br><br>
</BODY></HTML>
