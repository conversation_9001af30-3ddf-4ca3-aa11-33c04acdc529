<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_3.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<title>&nbsp;<script>writemsg(<% write(lang); %>,"文件管理");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<style type="text/css">
.mydiv {
background-color: #FF7F00;
border: 1px solid #f00;
text-align: center;
line-height: 40px;
font-size: 12px;
font-weight: bold;
z-index:999;
width: 300px;
height: 120px;
left:50%;
top:30%;
margin-left:-150px!important;/*FF IE7 该值为本身宽的一半 */
margin-top:-60px!important;/*FF IE7 该值为本身高的一半*/
margin-top:0px;
position:fixed!important;/* FF IE7*/
position:absolute;/*IE6*/
_top:       expression(eval(document.compatMode &&
document.compatMode=='CSS1Compat') ?
documentElement.scrollTop + (document.documentElement.clientHeight-this.offsetHeight)/2 :/*IE6*/
document.body.scrollTop + (document.body.clientHeight - this.clientHeight)/2);/*IE5 IE5.5*/
}
.bg,.popIframe {
background-color: #666; display:none;
width: 100%;
height: 100%;
left:0;
top:0;/*FF IE7*/
filter:alpha(opacity=50);/*IE*/
opacity:0.5;/*FF*/
z-index:1;
position:fixed!important;/*FF IE7*/
position:absolute;/*IE6*/
_top:       expression(eval(document.compatMode &&
document.compatMode=='CSS1Compat') ?
documentElement.scrollTop + (document.documentElement.clientHeight-this.offsetHeight)/2 :/*IE6*/
document.body.scrollTop + (document.body.clientHeight - this.clientHeight)/2);
}
.popIframe {
filter:alpha(opacity=0);/*IE*/
opacity:0;/*FF*/
}
-->
</style>
<script language="JavaScript">

function showDiv(){
	parent.frames[1].document.getElementById("bg").style.display='block';
	parent.frames[2].document.getElementById("bg").style.display='block';
	parent.frames[3].document.getElementById("bg").style.display='block';
	parent.frames[4].document.getElementById("bg").style.display='block';
}
function closeDiv(){

	parent.frames[1].document.getElementById("bg").style.display='none';
	parent.frames[2].document.getElementById("bg").style.display='none';
	parent.frames[3].document.getElementById("bg").style.display='none';
	parent.frames[4].document.getElementById("bg").style.display='none';
}
function messageCheck1()
{
    var hid = document.webForm1;
    hid.action="/goform/loadImage";
	return true;
}
function messageCheck2()
{
	var hid = document.webForm2;
	if(hid.webkUpload.value.length == 0)
	{
		alert(putmsg(<% write(lang); %>,"请选择升级映像文件。"));
		return false;
	}
	else
	{
		if(eval(hid.memleft.value)<(10*1024*1024))
			{
				alert(putmsg(<% write(lang); %>,"系统剩余内存小于10M,不能升级!"));
				return false;
			}
    if (confirm(putmsg(<% write(lang); %>,"确定要升级吗?")))
		{
			alert(putmsg(<% write(lang); %>,"整个过程将会持续几分钟，请勿在期间切断电源或者拔掉网线!"));
			showDiv();
			hid.action="/goform/updateImage";
			hid.submit();
		}
	}
	return true;
}
function messageCheck3()
{
	var hid = document.webForm3;
	if(hid.webkUploadcfg.value.length == 0)
	{
		alert(putmsg(<% write(lang); %>,"请选择配置文件。"));
		return false;
	}
	else
	{
    if (confirm(putmsg(<% write(lang); %>,"确定要升级配置文件吗?")))
		{
			alert(putmsg(<% write(lang); %>,"整个过程将会持续几分钟，请勿在期间切断电源或者拔掉网线!"));
			showDiv();
			hid.action="/goform/updateCfgFile";
			hid.submit();
		}
	}
	return true;
}
function messageCheck4()
{
    var hid = document.webForm4;
    hid.action="/goform/loadCfgFile";
	return true;
}

function messageCheck5()
{
    var hid = document.webForm5;
	if(confirm(putmsg(<% write(lang); %>,"确认要删除所有配置，恢复出厂默认值吗？")))
	{
		hid.action="/goform/clearconfig";
		hid.submit();
	}
	
	return true;
}

function messageCheck6()
{
    var hid = document.webForm6;
	if(confirm(putmsg(<% write(lang); %>,"确认要保存所有配置吗？")))
	{
		hid.action="/goform/saveconfig";
		hid.submit();
	}
	
	return true;
}

</SCRIPT>
</head>
<body  onload=""><br>
<div id="bg" class="bg" style="display:none;"></div>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<% var version,memleft,errorcode; getImageVersion(); %>
<input type="hidden" name="memleft" value=<% write(memleft); %>>
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<table id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0"><tr><td>
	<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" ><tr><td valign="top" >
		<table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
            <tr height="25">
                <td colspan="3" align="left" class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"文件管理");</script></td>
            </tr>
        </table></td>
      </tr>
      <tr>
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">

              
            <tr height="25"  style='display:none'>
              <td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"当前版本");</script></td>
              <td align="left" class="crons" >&nbsp;<% write(version); %>&nbsp</td>
              <form  name="webForm1" method="post" action="">
              <td align="left" class="crons" ><script>writebutton(<% write(authmode); %>,<% write(lang); %>,"下  载","button","submit","apply1","messageCheck1()");</script>&nbsp;&nbsp;</td>
              </form>
            </tr>
            <tr height="25" style='display:none'>
              <td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"版本升级");</script></td>
              <form ENCTYPE="multipart/form-data" name="webForm2" method="post" action="">
              <td align="left" class="crons">&nbsp;
			  	<input class=lookup type="file" name="webkUpload">&nbsp
                <input type="hidden" name="memleft" value=<% write(memleft); %>>
                
              </td>
              <td align="left" class="crons">
                <input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
                <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"升  级","button","button","apply2","messageCheck2()");</script>&nbsp;&nbsp;
              </td>
              </form>
            </tr>	

            <tr height="25" style='display:none'>
              <td width="20%" align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"上传配置文件");</script></td>
              <form ENCTYPE="multipart/form-data" name="webForm3" method="post" action="">
              <td align="left" class="crons">&nbsp;
              <input id=uploadcfg class=lookup type="file" name="webkUploadcfg">&nbsp</td>
              <td align="left" class="crons">
               <input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
               <script>writebutton(1,<% write(lang); %>,"上  传","button","button","apply3","messageCheck3()");</script>
              </td> 
              </form>
            </tr>

            <tr height="25" style='display:none'>
              <td width="20%" align="left" class="crons" colspan="2">&nbsp;<script>writemsg(<% write(lang); %>,"下载配置文件");</script></td>
             <form  name="webForm4" method="post" action="">
  			 <td align="left" class="crons" >
  			 <input type="hidden" name="ltime" value=<% write(lltime); %>>
			<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
  			 <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"下  载","button","submit","apply4","messageCheck4()");</script></td>
  		     </form>
            </tr>
       
            
            <tr height="25">
              <td width="20%" align="left" class="crons" colspan="2">&nbsp;<script>writemsg(<% write(lang); %>,"恢复出厂配置");</script></td>
              <form  name="webForm5" method="post" action="">
                <td align="left" class="crons" >
                <input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
                <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"恢  复","button","button","apply5","messageCheck5()");</script></td>
              </form>
            </tr>
            <tr height="25">
              <td width="20%" align="left" class="crons" colspan="2">&nbsp;<script>writemsg(<% write(lang); %>,"保存所有配置");</script></td>
              <form  name="webForm6" method="post" action="">
                <td align="left" class="crons" >
                  <input type="hidden" name="ltime" value=<% write(lltime); %>>
					<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
                  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"保  存","button","button","apply6","messageCheck6()");</script>
                </td>
              </form>
            </tr>
        </table>
      </td>
      </tr>
         <tr>
	  	   <td colspan="2" align="center" height="35">
	  		</td>
   </tr>
    </table></td>
  </tr> 
</table>

</td></tr></table>

<script>

changebgcolor();

 //errorcode= Request.Querystring("error");



<% if (errorcode=="1") { write("closeDiv();"); write("alert(putmsg("); write(lang); write(",'版本升级成功,重启交换机后新版本生效!')); location.href = 'upgrade.asp?ltime="); write(lltime); write("';");} %>
<% if (errorcode=="2") { write("closeDiv();"); write("alert(putmsg("); write(lang); write(",'升级版本失败!')); location.href = 'upgrade.asp?ltime="); write(lltime); write("';");} %>
<% if (errorcode=="3") { write("closeDiv();"); write("alert(putmsg("); write(lang); write(",'上传配置文件成功,重启交换机新配置生效!'));location.href = 'upgrade.asp?ltime="); write(lltime); write("';");} %>
<% if (errorcode=="4") { write("closeDiv();"); write("alert(putmsg("); write(lang); write(",'上传配置文件失败!')); location.href = 'upgrade.asp?ltime="); write(lltime); write("';");} %>
<% if (errorcode=="5") { write("closeDiv();"); write("alert(putmsg("); write(lang); write(",'文件过大!')); location.href = 'upgrade.asp?ltime="); write(lltime); write("';");} %>
<% if (errorcode=="6") { write("closeDiv();"); write("alert(putmsg("); write(lang); write(",'删除所有配置成功，重启交换机后生效')); location.href = 'upgrade.asp?ltime="); write(lltime); write("';");} %>
<% if (errorcode=="7") { write("closeDiv();"); write("alert(putmsg("); write(lang); write(",'删除配置失败!')); location.href = 'upgrade.asp?ltime="); write(lltime); write("';");} %>
<% if (errorcode=="8") { write("closeDiv();"); write("alert(putmsg("); write(lang); write(",'找不到配置文件!')); location.href = 'upgrade.asp?ltime="); write(lltime); write("';");} %>
<% if (errorcode=="9") { write("closeDiv();"); write("alert(putmsg("); write(lang); write(",'保存所有配置失败!')); location.href = 'upgrade.asp?ltime="); write(lltime); write("';");  } %>
<% if (errorcode=="10") { write("closeDiv();"); write("alert(putmsg("); write(lang); write(",'保存所有配置成功!')); location.href = 'upgrade.asp?ltime="); write(lltime); write("';");  } %>
<% if (errorcode=="11") { write("closeDiv();"); write("alert(putmsg("); write(lang); write(",'不是合法的配置文件!')); location.href = 'upgrade.asp?ltime="); write(lltime); write("';");  } %>
</script>

</body>

</html>

