<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<% var authmode,errorcode,portStaList; checkCurMode();multiSetShow(); %>
			<% var errorcode, igmptraffic; getIgmpTraffic(); %>
				<% var errorcode, igmpgroup; getIgmpGroup(); %>
					<meta http-equiv="Content-Type" content="text/html; charset=utf8">
					<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
					<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
					<script src="js/alpinejs.min.js"></script>
					<title>&nbsp;
						<script>writemsg(<% write(lang); %>, "802.1Q VLAN设置");</script>
					</title>
					<link href="css/display_web.css" rel="stylesheet" type="text/css" />

					<script language="JavaScript">
						var boardType = <% getSysCfg(); %>;
					</script>

					<script language="JavaScript" type="text/JavaScript">

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
		if(i==0){
			// tab.rows[i].style.backgroundColor = "#fff";
		}else{
			       if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}
		}
 

     }

  }
}
function getUrlParamTab() {
        let urlSearch = window.location.search
        const search = new URLSearchParams(urlSearch)
		
        const params = Object.fromEntries(search.entries())
		
        try {
          if (params.tab) return params.tab
        } catch { }
        return 'tab1'
      }
function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}

function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}

function checkvalue()
{
    var hid=document.igmp_setting;
	let ms=document.getElementsByName("igmpstatus");

 
	// var radio_ms = document.getElementsByName("igmpstatus");
	// var radio_l2p = document.getElementsByName("l2pro");

	// for(var i=0; i<radio_ms.length; i++)
	// {
	// 	if (radio_ms[i].checked)
	// 	{
	// 		var ms = radio_ms[i].value;
	// 	}
	// }
	// for(var i=0; i<radio_l2p.length; i++)
	// {
	// 	if (radio_l2p[i].checked)
	// 	{
	// 		var l2p = radio_l2p[i].value;
	// 	}
	// }

	/*
	if ( ms == "enable" && l2p != "pass")
	{
		alert("使用IGMP Snooping时请将L2 Protocol设为Enable状态");
		return;	
	}
	*/

   hid.submit();

	return true;
}
function checkvalue1(){
	var hid=document.igmp_setting1;
	let ip=document.getElementsById("querierIp");
	if(validateIP(ip.value)){

		hid.submit();}else{
			alert("请输入正确的IP地址");
		}
}
function validateIP(ip) {
  // IP 地址的正则表达式
  var ipPattern = /^(\d{1,3}\.){3}\d{1,3}$/;

  // 检查 IP 地址是否符合正则表达式的格式要求
  if (ipPattern.test(ip)) {
    // 进一步检查每个 IP 段的取值范围是否有效
    var segments = ip.split('.');
    for (var i = 0; i < segments.length; i++) {
      var segment = parseInt(segments[i]);
      if (segment < 0 || segment > 255) {
        return false;
      }
    }
    return true;
  } else {
    return false;
  }
}
function getPage(page)
{
   location.href="igmp.asp?page="+page+"&ltime="+<% write(lltime); %>;
}
function dofirst(){location.href="igmp.asp?pageName=igmp.asp"+"&ltime="+<% write(lltime); %>;}

function check(b)
{
	var vid=document.getElementById("vid").value;
	var tf=document.igmp_setting;
		var del = document.getElementById("del");
					tf.action = "/goform/setIgmpVlan";

	if(b==1)
	{
		if((vid<=4094) && (vid>=1))
		{
			let querierEnable=document.getElementById("querierEnable");
			let querierIp=document.getElementById("querierIp");
			if(querierEnable.value=='enable'){
				if(validateIP(querierIp.value)){
					
					tf.submit();
				}else{
					alert("请输入正确的IP地址");
				}
			}else{
				tf.submit();
			}
			
		}
		else
		{
			alert(putmsg(<% write(lang); %>,"VID的范围必须在1-4094之间"));
		}
	}
	else
	{
		if((vid<=4094) && (vid>=1))
		{
		del.value = "1";		
			tf.submit();
		}
		else
		{
			alert(putmsg(<% write(lang); %>,"VID的范围必须在1-4094之间"));
		}
	}
}

function V(vid,instance,interfacea,portbmp)
{
    var narr=4;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_igmp").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+vid);	

    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
    
    tbtr.cells[0].abbr = 0;
    tbtr.cells[1].abbr = vid;
	
	tbtr.cells[0].innerHTML = "<input type=\"radio\" name=\"checkbox_index\" value=\""+vid+"\" onclick=\"addToVLanRange(this)\"/>";
    tbtr.cells[1].innerHTML = vid;
    tbtr.cells[2].innerHTML = instance;
    tbtr.cells[3].innerHTML = portbmp;		
}



function checkdel()
{		
	var a = new Array();
	var vid=document.getElementById("vid").value;
	var del = document.getElementById("del");
	var tf=document.igmp_setting;

	if (vid.indexOf("-") >=0)
	{
		a = vid.split("-");
    	if((a[0]<=4094) && (a[0]>1) && (a[1]<=4094) && (a[1]>1))
		{
		   	del.value = "1";
	 		tf.submit();
    	}
		else
		{
	   		alert(putmsg(<% write(lang); %>,"VID的范围必须在2-4094之间"));
		}
	}
	else
	{
    	if((vid<=4094) && (vid>1))
		{
	    	del.value = "1";		
	 		tf.submit();
    	}
		else
		{
	   		alert(putmsg(<% write(lang); %>,"VID的范围必须在2-4094之间"));
		}
   }
}

function addToVLanRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var vid = document.getElementById("vid");
	var vlan_enable = document.getElementById("vlan_enable");
	var vlan_name=document.getElementById("vlan_name");
	var p = obj.value;

	if(obj.checked)
	{
		vid.value=trobj.cells[1].innerHTML;
		//vlan_name.value=trobj.cells[2].innerHTML;
	}
}


function checkData()
{
	var tf=document.igmp_setting;
	
	tf.action = "/goform/saveComm?name=igmp";
	tf.submit();	
}

function refreshpage()
{
  location.href='igmp.asp?page=1&ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('igmp',<% write(lang); %>);
}

function display()
{
	tmp = <% write(portStaList); %>;
	if(tmp == "disable")
	{
		document.vlan_multi.optaction.value = 1;
	}else
	{
		document.vlan_multi.optaction.value = 0;
	}
}
 
function checking2()
{	
	const pagePath = 'igmp.asp?ltime=' +<% write(lltime); %>;
	var tf=document.vlan_multi;
	tf.tabName.value='tab2'
	tf.submit();
}

function changebgcolor11(){
 var tab = document.all.table11;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}


function changebgcolor21(){
 var tab = document.all.table21;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables " ;
		}

     }

  }
}

var igmpStaList=[<% write(igmptraffic); %>];

function writeLines()
{
	var j = 0;
	for(var i=0;i<igmpStaList.length/7;i++)
	{
		document.write(" <tr  style='text-align:center'>");
		document.write("    <td class='inputsyslog1'>"+igmpStaList[j]+"</td>");
		j++
		document.write("    <td  class='inputsyslog1'>"+igmpStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>"+igmpStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>"+igmpStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>"+igmpStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>"+igmpStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>"+igmpStaList[j]+"</td>");
		j++;
		document.write("  </tr>");
	}
}


</script>

					<script language="JavaScript" type="text/JavaScript">

function changebgcolor41(){
	var tab = document.all.table41;
	var len = tab.rows.length ;
	for (var i=0; i<len; i++)
	{
	var lencol = tab.rows[i].cells.length
	for (var j=0; j<lencol; j++)
		{
		if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
			}
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

		}

	}
}

function del_igs(ports,vlanid)
{
	//alert(ports);
	//alert(vlanid);
	var hid = document.macdel;
	hid.port_value.value=ports;
	hid.vlan_value.value=vlanid;

	hid.action="/goform/delIgsRouter"
	hid.tabName.value='tab3'
	hid.submit();
	return 0;
}

function IgsDisplay(ports, vlanid)
{	
	var narr=3;
	var tbtd;
	var i;
	var tbtr = document.getElementById("mirror_table3").insertRow(-1);

	tbtr.classname = "crons";
	tbtr.height = "30";

	tbtr.setAttribute("height", "30");
	tbtr.setAttribute("class", "crons");
	tbtr.setAttribute("className", "crons");
	tbtr.setAttribute("id", "tr_"+vlanid);

	/*Cycle respectively and setting attributes*/
	for(i=0;i<narr;i++)
	{
		tbtd = document.createElement("td");
		
		tbtd.align = "center";
		tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		tbtr.appendChild(tbtd);
	}
		
	/*display*/
	tbtr.cells[0].innerHTML = ports;
	tbtr.cells[1].innerHTML = vlanid;

	if (<% write(authmode); %> == 1)
		tbtr.cells[2].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick=del_igs('"+ports+"','"+vlanid+"')>";	
	else
		tbtr.cells[2].innerHTML = " ";
}


function selectToAll() 
{  
	var cf = document.forms[0];
	var objs = document.getElementsByName("checkbox_index"); 
	var i;
	if(cf.check_all.checked == true)
	{
		for(i = 0; i < objs.length; i++) 
		{    
			if(objs[i].disabled==false && objs[i].checked==false){
				objs[i].checked = true;  
				addToPortRange(objs[i]);
			}
		}
	}
	else
	{
		for(i = 0; i < objs.length; i++) 
		{    
			if(objs[i].checked==true){
				objs[i].checked = false;  
				addToPortRange(objs[i]);
			}
		}
	} 
}

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var target = document.getElementById("portNum");
	var avid = document.getElementById("avid");
	var dvid = document.getElementById("dvid");
	var porttype = document.getElementById("porttype");
	var filter = document.getElementById("filter");
	var type_value = document.getElementById("type_value");
	var filter_value = document.getElementById("filter_value");
	//var vlan_port_id = document.getElementById("vlan_port_id");
	var p = obj.value;

	if(obj.checked){
		target.value =target.value + p + " ";
		//vlan_port_id.value =vlan_port_id.value + p + " , ";
	}
	else
	{
		target.value = target.value.replace(p + " ","");
		//vlan_port_id.value = vlan_port_id.value.replace(p + " , ","");
	}
}

function checkupRouter()
{
	var tf = document.igmp_router;
	tf.action = "/goform/AddIgmpRouter";
	tf.tabName.value='tab3'
	tf.submit();
}

function changebgcolor_mirror_table3()
{
	var tab = document.all.mirror_table3;
	var len = tab.rows.length ;
	for (var i=0; i<len; i++)
	{
	var lencol = tab.rows[i].cells.length
	for (var j=0; j<lencol; j++)
		{
		if (j % 2 == 1){
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}
		}
	}
}

</script>


					<script LANGUAGE="javascript">


						function changebgcolor51() {
							var tab = document.all.table51;
							var len = tab.rows.length;
							for (var i = 0; i < len; i++) {
								var lencol = tab.rows[i].cells.length
								for (var j = 0; j < lencol; j++) {
									if (j % 2 == 1) {
										// tab.rows[i].cells[j].bgColor = "efefef";
										tab.rows[i].cells[j].className = "all_tables ";
									}
									else {
										// tab.rows[i].cells[j].bgColor = "efefef";
										tab.rows[i].cells[j].className = "all_tables ";
									}
								}
							}
						}

						var igmpStaListGroup = [<% write(igmpgroup); %>];

						function writeLinesGroup() {
							var j = 0;
							for (var i = 0; i < igmpStaListGroup.length / 3; i++) {
								document.write(" <tr  style='text-align:center'>");
								document.write("    <td class='inputsyslog1'>" + igmpStaListGroup[j] + "</td>");
								j++
								document.write("    <td  class='inputsyslog1'>" + igmpStaListGroup[j] + "</td>");
								j++;
								document.write("    <td  class='inputsyslog1'>" + igmpStaListGroup[j] + "</td>");
								j++;
								document.write("  </tr>");
							}
						}


					</script>

					<script language="JavaScript">


						function changebgcolor61() {
							var tab = document.all.table61;
							var len = tab.rows.length;
							for (var i = 0; i < len; i++) {
								var lencol = tab.rows[i].cells.length
								for (var j = 0; j < lencol; j++) {
									if (j % 2 == 1) {

										// tab.rows[i].cells[j].bgColor = "efefef";
										tab.rows[i].cells[j].className = "all_tables";
									}
									else {
										// tab.rows[i].cells[j].bgColor = "efefef";
										tab.rows[i].cells[j].className = "all_tables all_tables3";
									}

								}

							}
						}

						function del_sv(portId, appid) {
							var hid = document.form_mcast_limit;

							hid.port_value.value = portId;
							hid.appid_value.value = appid;
							hid.tabName.value = 'tab2'
							hid.action = "/goform/delMcastLimit"
							hid.submit();
							return 0;
						}


						function isMulticastMac(mac) {
							var head = mac.substring(0, 2);
							if (parseInt(head, 10) % 2 == 1)
								return true;
							return false;
						}

						function checkup() {
							var tf = document.form_mcast_limit;
							var bbb = document.form_mcast_limit.appid.value;
							var ddd = document.form_mcast_limit.limit_value.value;
							var myRE = /^[0-9a-fA-F]{1,4}\.[a-fA-F0-9]{1,4}\.[a-fA-F0-9]{1,4}$/;
							if (isNaN(parseInt(ddd)) || (parseInt(ddd) < 16) || (parseInt(ddd) > 1000000)) {
								alert("Invaild input, effective range: 16~1000000!");
								return false;
							}

							if (myRE.test(bbb)) {
								if (!isMulticastMac(bbb)) {
									alert(putmsg(<% write(lang); %>, "输入MAC地址非法  格式为16进制：HHHH.HHHH.HHHH"));
									return false;
								}
								else {

									tf.action = "/goform/selMcastLimit";
									tf.tabName.value = 'tab2'
									tf.submit();

								}
							}
							else {
								alert(putmsg(<% write(lang); %>, "输入MAC地址非法  格式为16进制：HHHH.HHHH.HHHH"));
								return false;
							}
						}

						// function addToPortRange(obj) {
						// 	var trid = "tr_" + obj.value;
						// 	var trobj = document.getElementById(trid);
						// 	var target = document.getElementById("portNum");
						// 	var avid = document.getElementById("avid");
						// 	var dvid = document.getElementById("dvid");
						// 	var porttype = document.getElementById("porttype");
						// 	var filter = document.getElementById("filter");
						// 	var type_value = document.getElementById("type_value");
						// 	var filter_value = document.getElementById("filter_value");
						// 	var vlan_port_id = document.getElementById("vlan_port_id");
						// 	var p = obj.value;

						// 	if (obj.checked) {
						// 		target.value = target.value + p + " ";
						// 		vlan_port_id.value = vlan_port_id.value + p + " , ";
						// 	}
						// 	else {
						// 		target.value = target.value.replace(p + " ", "");
						// 		vlan_port_id.value = vlan_port_id.value.replace(p + " , ", "");
						// 	}
						// }

						function P(index, portId, appid, avg) {
							var narr = 4;
							var tbtd;
							var eport = "";
							var gport = "";
							var i;
							var opt;
							var gtrunk = 0;
							var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

							tbtr.classname = "crons";
							tbtr.height = "30";

							tbtr.setAttribute("height", "30");
							tbtr.setAttribute("class", "crons");
							tbtr.setAttribute("className", "crons");
							tbtr.setAttribute("id", "tr_" + portId);


							for (i = 0; i < narr; i++) {
								tbtd = document.createElement("td");

								tbtd.align = "center";
								tbtd.setAttribute("class", "td2");
								tbtd.setAttribute("className", "td2");

								tbtr.appendChild(tbtd);
							}

							tbtr.cells[0].abbr = 0;
							tbtr.cells[0].innerHTML = index;
							tbtr.cells[1].innerHTML = appid;
							tbtr.cells[2].innerHTML = avg;


							tbtr.cells[3].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick=del_sv('" + portId + "','" + appid + "')>";
						}


						function getPage(page) {
							location.href = "igmp.asp?page=" + page + "&ltime=" +<% write(lltime); %>;
						}

						function jumptoPage() {
							var topage = document.getElementById("jump2page");
							location.href = "igmp.asp?page=" + topage.value + "&ltime=" +<% write(lltime); %>;
						}

						function dofirst() { location.href = "igmp.asp" + "&ltime=" +<% write(lltime); %>; }


						function refreshpage() {
							location.href = 'igmp.asp?page=1&ltime=' +<% write(lltime); %>;
						}


						function AddOption(portname) {

							var selectObject = document.getElementById("rang_monitor");
							var y = document.createElement('option');
							y.text = portname;
							y.value = portname;
							try {
								selectObject.add(y, null); // standards compliant
							}
							catch (ex) {
								selectObject.add(y); // IE only
							}
						}


					</script>

</head>

<body x-data="{active:'tab1'}" x-init="active=getUrlParamTab()"><br>

	<% web_get_stat(); %>
		<%var authmode; checkCurMode(); %>
			<script>
				checktop(<% write(lang); %>);
			</script>
			<div>
				<ul class="tabmenu">
					<li id="tab1" :class="active==='tab1'?'tab':'tab-disable'">
						<a herf="#" x-on:click="active='tab1'">IGMP 设置</a>
					</li>
					<li id="tab2" :class="active==='tab2'?'tab':'tab-disable'">
						<a herf="#" x-on:click="active='tab2'">IGMP 组播选项</a>
					</li>
					<li id="tab3" :class="active==='tab3'?'tab':'tab-disable'" @click="">
						<a herf="#" x-on:click="active='tab3'">IGMP 路由端口</a>
					</li>
					<li id="tab4" :class="active==='tab4'?'tab':'tab-disable'" @click="">
						<a herf="#" x-on:click="active='tab4'">IGMP 统计</a>
					</li>
				</ul>
			</div>

			<form style="margin-top: 20px;" x-show="active==='tab1'" name="igmp_setting" method="POST"
				action="/goform/setIgmpConfig">
				<input type="hidden" name="authmode" class="input_board3" value="<% write(authmode); %>">
				<input type="hidden" name="left_menu_id" value="@left_menu_id#">
				<input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
				<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

				<script><% var errorcode, l2pro, igmpstatus; getIgmpCfg(); %></script>
				<div style="display: flex;margin-bottom: 10px;">
					<div width="15%" align="left" class="all_tables3 all_tables" style="border: none;text-align: left;">
						<script>writemsg(<% write(lang); %>, "IGMP使能:");</script>
					</div>
					<div width="50%" align="left" class="crons" colspan="2">
						<select name="igmpstatus" id="igmpstatus" @change="checkvalue" style="width: 200px;">
							<option value="enable" <% if (igmpstatus=="enable" ) write("selected"); %>>
								开启
							</option>
							<option value="disable" <% if (igmpstatus!="enable" ) write("selected"); %>>
								关闭
							</option>
						</select>

					</div>
				</div>

				<div class="formContain">
					<table id="mainTb" width="100%" height="100%" border="0" align="center" cellpadding="0"
						cellspacing="0">
						<tr>
							<td>
								<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
									<tr>
										<td height="30px" width="15%">
											<font size="5" color="#404040">
												<div class="bot">IGMP设置</div>
											</font>
										</td>

									</tr>

									<tr>
										<td width="50%" class="all_tables all_tables3"
											style="text-align: right;border: none;">

											<script>writemsg(<% write(lang); %>, "VLAN设置:");</script>

										</td>

										<td class="crons" width="50%" style="text-align: left;">
											<input type="text" name="vid" id="vid" class="input_board3"
												placeholder="输入VLAN ID(最多12位)" maxlength="12">&nbsp;(1-4094)
										</td>

									</tr>
									<tr>
										<td width="50%" class="all_tables all_tables3"
											style="text-align: right;border: none;">

											<script>writemsg(<% write(lang); %>, "查询器:");</script>

										</td>
										<!-- <td class="crons" width="20%">&nbsp;VLAN ID</td> -->
										<td class="crons" width="50%" style="text-align: left;">
											<select name="querierEnable" id="querierEnable" @change="checkvalue1"
												style="width: 200px;">
												<option value="enable">
													开启
												</option>
												<option value="disable">
													关闭
												</option>
											</select>
										</td>

									</tr>
									<tr>
										<td width="50%" class="all_tables all_tables3"
											style="text-align: right;border: none;">

											<script>writemsg(<% write(lang); %>, "普遍组查询IP:");</script>

										</td>
										<!-- <td class="crons" width="20%">&nbsp;VLAN ID</td> -->
										<td class="crons" width="50%" style="text-align: left;">
											<input type="text" name="querierIp" id="querierIp" class="input_board3">
										</td>

									</tr>
									<tr>
										<td align="middle" class="crons" width="20%" colspan="3">
											<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "添  加", "buttons_add", "button", "add", "check(1)");</script>

											<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "删  除", "buttons_del", "button", "delete", "check(2)");</script>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>

					<INPUT type="hidden" name="del" id="del" value="0">

			</form>

			</div>
			<div class="formContain" x-show="active==='tab1'" style="margin-top: 10px;min-height: 200px;">
				<div class="" style="padding: 5px;">
					<font size="5" color="#404040">
						<div class="bot">信息展示</div>
					</font>
				</div>
				<div class=""></div>
				<table id="table_igmp" border="0" cellspacing="0" cellpadding="0" width="100%"
					style="table-layout:fixed;word-break:break-all; word-wrap: break-word;margin-top: 10px;"
					class="tablebord">
					<thead>
						<tr align="center" class="crons">
							<th class="tableTd" width="15%"></th>
							<th class="tableTd" width="15%">
								<font color="#333333">VID</font>
							</th>
							<th class="tableTd" width="19%">
								<font color="#333333">&nbsp;
									<script>writemsg(<% write(lang); %>, "查询器");</script>
								</font>
							</th>
							<th class="tableTd" width="51%">
								<font color="#333333">&nbsp;
									<script>writemsg(<% write(lang); %>, "快速离开模式");</script>
								</font>
							</th>
						</tr>
					</thead>

					<script>
									<% var errorcode; showQigmpvlan("igmpvlan");%>  
					</script>
				</table>
			</div>


			<form x-show="active==='tab2'" name="vlan_multi" method="POST" action="/goform/multiSet">
				<input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
				<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
				<input type="hidden" name="tabName" id="tabName">

				<table id="mainTblSta" width="100%" height="100%" border="0" align="center" cellpadding="0"
					cellspacing="0">

					<tr>

						<td>
					<tr>
						<td width="1%" class="all_tables3 all_tables" style="border: none;">未知组播丢弃:</td>
						<td width="7%" class="crons">
							<select name="optaction" id="optaction" @change="checking2">
								<option value="0">开启</option>
								<option value="1">关闭</option>
							</select>
						</td>

					</tr>
					<!-- <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
								<tr>
									<td valign="top">
										<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"
											class="cword09">
											<tr>
												<td>

													<table width="100%" align="center" border="0" cellspacing="0"
														cellpadding="0">
														
													</table>

												</td>
											</tr> -->

					<!-- <tr>
												<td>
													<table border="0" cellspacing="0" cellpadding="0" width="100%"
														id="table11" class="tablebord">


														<tr height="25">
															
															<td class="crons">&nbsp;
																<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "修  改", "buttons_apply", "button", "modify", "checking2()");</script>
															</td>
														</tr>

													</table>
												</td>
											</tr> -->
					<!-- </table>
									</td>
								</tr>

							</table> -->
					</td>
					</tr>

				</table>
				</td>
				</tr>
			</form>
			<br>

			<form x-show="active==='tab2'" name="form_mcast_limit" method="POST" action="">
				<% var qoscfg; getQoSCfg(); %>
					<input type="hidden" name="qos_cfg" id="qos_cfg" value="<% write(qoscfg); %>">
					<input type="hidden" name="tabName" id="tabName">
					<div class="formContain">
						<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0"
							cellspacing="0">
							<tr>
								<td>
									<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">

										<tr>
											<td height="30px">
												<font size="5" color="#404040">
													<div class="bot">组播流量设置</div>
												</font>
											</td>

										</tr>


										<tr>
											<td width="18%" class="tableTd" align="right">&nbsp;组播MAC地址:</td>
											<td width="32%" class="crons">&nbsp;
												<input name="appid" type="text" class="input_x">(HHHH.HHHH.HHHH,16进制)
											</td>
										</tr>
										<tr>
											<td width="18%" class="tableTd" align="right">&nbsp;阀值(16的倍数:
												16~1000000):</td>
											<td width="32%" class="crons">&nbsp;
												<input name="limit_value" type="text" class="input_x">(Kbps)
											</td>
										</tr>
										<tr height="5">

										</tr>
										<tr>
											<td align="middle" class="crons" colspan="5">
												<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "buttons_apply", "button", "modify_bu", "checkup()");</script>
												&nbsp;&nbsp;&nbsp;

											</td>
										</tr>
										<tr height="5">

										</tr>
									</table>

									<!-- <tr>
													<td> -->
									<!-- <table border="0" cellspacing="0" cellpadding="0" width="100%"
															id="table61" class="tablebord"> -->
									<!-- <tr height="25">
																<td width="18%" class="crons">&nbsp;组播MAC地址</td>
																<td width="32%" class="crons">&nbsp;
																	<input name="appid" type="text"
																		class="input_x">(HHHH.HHHH.HHHH,16进制)
																</td>
																<td width="18%" class="crons">&nbsp;&nbsp;阀值(16的倍数:
																	16~1000000)</td>
																<td width="32%" class="crons">&nbsp;
																	<input name="limit_value" type="text"
																		class="input_x">(Kbps)
																</td>
															</tr> -->
									<!-- <tr height="25">
																<td colspan="4" align="middle" class="crons">
																	<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "buttons_apply", "button", "modify_bu", "checkup()");</script>
																	&nbsp;&nbsp;&nbsp;
																</td>
															</tr> -->
									<!-- <tr style="background-color: #fff;font-weight: normal;">
																<td colspan="4" style="font-weight: normal;"> -->
									<!-- <table id="table_port_vlan" border="0"
																		cellspacing="0" cellpadding="0" width="100%"
																		class="tablebord">
																		<tr align="center"  height="30" class="all_tables3 all_tables" style="background-color: #fff;">

																			<th class="td2" width="20%">序号</th>
																			<th class="td2" width="20%">
																				<font color="#333333"><b>&nbsp;MAC</b>
																				</font>
																			</th>
																			<th class="td2" width="20%">阀值</th>
																			<th class="td2" width="30%">
																				<font color="#333333"><b>&nbsp;
																						<script>writemsg(<% write(lang); %>, "删除");</script>
																					</b></font>
																			</th>
																		</tr>
																		<script>  <%  var errorcode; showMcastLimitPage("list"); %></script>
																	</table> -->
									<!-- </td>
															</tr> -->
									<!-- </table> -->
									<!-- </td>
												</tr> -->
									<!-- <tr>
													<td colspan="2" align="center" height="35">
														<script>writebutton(1,<% write(lang); %>, "首  页", "button", "button", "firstpage", "dofirst()");</script>
														<%showMcastLimitPage("pagebutton");%>
															&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
															<%showMcastLimitPage("pagenum");%>
																<%showMcastLimitPage("allpage");%>
													</td>
												</tr> -->


								</td>
							</tr>
						</table>
					</div>
					<input type="hidden" name="ltime" value=<% write(lltime); %>>
					<input type="hidden" name="lastts" value=<% write(serverts); %>>
					<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
					<input type="hidden" name="port_value" value="">
					<input type="hidden" name="appid_value" value="">
					<br>
					<br>
					<div class="formContain">
						<div class="">
							<font size="5" color="#404040">
								<div class="bot">信息展示</div>
							</font>
						</div>
						<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"
							class="tablebord">
							<tr align="center" height="30" class="all_tables3 all_tables"
								style="background-color: #fff;">

								<th class="td2" width="20%">序号</th>
								<th class="td2" width="20%">
									<font color="#333333"><b>&nbsp;MAC</b>
									</font>
								</th>
								<th class="td2" width="20%">阀值</th>
								<th class="td2" width="30%">
									<font color="#333333"><b>&nbsp;
											<script>writemsg(<% write(lang); %>, "删除");</script>
										</b></font>
								</th>
							</tr>
							<script>  <%  var errorcode; showMcastLimitPage("list"); %></script>
						</table>
					</div>
			</form>
			<form x-show="active==='tab3'" name="igmp_router" method="POST" action="/goform/AddIgmpRouter">
				<input type="hidden" name="left_menu_id" value="@left_menu_id#">
				<input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
				<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
				<input type="hidden" name="portNum" id="portNum">
				<input type="hidden" name="tabName" id="tabName">
				<div class="formContain">
					<table id="mainTblRouter" width="98%" height="100%" border="0" align="center" cellpadding="0"
						x-data="{portList:[]}"
						x-init="portList = JSON.parse(localStorage.getItem('portList')).map(item => item.portName)"
						cellspacing="0">
						<tr>
							<td>
								<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
									<tr>
										<td height="30px" colspan="11">
											<font size="5" color="#404040">
												<div class="bot">IGMP路由端口</div>
											</font>
										</td>

									</tr>
									<tr>
										<td width="15%" height="30" class="all_tables all_table3-right"
											style="border: none;">&nbsp;
											<script>writemsg(<% write(lang); %>, "VLAN:");</script>
										</td>
										<td align="left" class="crons" style="font-size: 16px;" colspan="10"> <input
												type="text" name="rang_vlan" id="rang_vlan">
										</td>
									</tr>
									<tr>
										<td width="15%" height="30" class="all_tables all_table3-right">&nbsp;
											<script>writemsg(<% write(lang); %>, "端口:");</script>
										</td>
										<!-- <td align="left" class="crons" style="font-size: 16px;">&nbsp;
																<%AppendOptionMirrored(); %>
															</td> -->
										<template x-for="(item,idx) in portList" :key="idx">
											<td class="all_tables all_table3-right" x-text="item"></td>
										</template>
									</tr>
									<tr>
										<td class="all_tables all_table3-right" style="font-size: 16px;">&nbsp;
											选择
										</td>
										<template x-for="(item,idx) in portList" :key="idx">
											<td align="center" class="all_tables all_table3-right">
												<input type="checkbox" name="checkbox_index" :value="item" :id="item" :
													onchange="addToPortRange(this)">

											</td>
										</template>
									</tr>

									<td :colspan="portList.length+1" align="center" class="crons"
										style="border-top:  1px solid #ddd; padding: 5px;">
										<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "添  加", "buttons_add", "button", "modify", "checkupRouter()");</script>
									</td>
								</table>







							</td>
						</tr>


					</table>
				</div>
				<input type="hidden" name="switch_port" value="26">
				<input type="hidden" name="h_mirror_enable" value="@h_mirror_enable#">
				<input name="s" value="@s#" type="hidden">
				<input type="hidden" name="h_capture_port" value="@h_capture_port#">
				<input type="hidden" name="h_mirror_capture_type" value="@h_mirror_capture_type#">
				<input type="hidden" name="loader" value="@loader#">
				<input type="hidden" name="todo" value="save">
				<input type="hidden" name="this_file" value="mirror.html">
				<input type="hidden" name="next_file" value="mirror.html">
				<input type="hidden" name="message" value="@msg_text#">
				<input type="hidden" name="mirror_table" value="@mirror_table#">
				<div class="formContain" style="margin-top: 10px;">
					<div>
						<font size="5" color="#404040">
							<div class="bot">信息展示</div>
						</font>
					</div>
					<table id="mirror_table3" width="100%" border="0" align="center" cellpadding="0" cellspacing="0"
						class="tablebord">
						<thead>
							<th align="center" class="td6" width="30%" style="font-size: 16px; font-weight: bold;">
								<b>&nbsp;
									<script>writemsg(<% write(lang); %>, "端口");</script>
								</b>
							</th>
							<th align="center" class="td6" width="40%" style="font-size: 16px; font-weight: bold;">
								<b>&nbsp;
									<script>writemsg(<% write(lang); %>, "VLAN");</script>
								</b>
							</th>
							<th align="center" class="td6" width="30%" style="font-size: 16px; font-weight: bold;">
								<b>&nbsp;
									<script>writemsg(<% write(lang); %>, "删除");</script>
								</b>
							</th>
						</thead>
						<script>

<% var errorcode; IgmpRouterShows(); %>

						</script>
					</table>
				</div>
			</form>


			<form id="form1" x-show="active=='tab4'" name="form1" method="post" action="" onSubmit=""
				class="formContain">
				<br>

				<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td height="30px">
							<font size="5" color="#404040">
								<div class="bot">IGMP统计</div>
							</font>
						</td>

					</tr>
				</table>




				<table width="100%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord"
					id="table21">
					<thead>
						<tr>
							<th width="16%" align="center" class="all_tables_list ">VLAN</th>
							<th width="14%" align="center" class="all_tables_list">v1 report</th>
							<th width="14%" align="center" class="all_tables_list">v2 report</th>
							<th width="14%" align="center" class="all_tables_list">v3 report</th>
							<th width="14%" align="center" class="all_tables_list">v2 leave</th>
							<th width="14%" align="center" class="all_tables_list">query</th>
							<th width="14%" align="center" class="all_tables_list">general query</th>
						</tr>
					</thead>

					<script language="javascript">
							writeLines();

					</script>
				</table>
				<span class="table_main1"></span>
			</form>



			<form name="macdel" method="POST" action="">
				<input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
				<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
				<input type="hidden" name="port_value" value="">
				<input type="hidden" name="vlan_value" value="">
				<input type="hidden" name="tabName" id="tabName">
			</form>
			<br>
			<br>
			<form id="formGroup" x-show="active=='tab4'" name="formGroup" method="post" action="" onSubmit=""
				class="formContain">


				<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td height="30px">
							<font size="5" color="#404040">
								<div class="bot">IGMP组信息</div>
							</font>
						</td>

					</tr>
				</table>

				<table width="100%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord"
					id="table51">
					<thead>
						<tr class="table_maintable">
							<th width="24%" align="center" class="all_tables3 all_tables">VLAN</th>
							<th width="26%" align="center" class="all_tables3 all_tables">Interface</th>
							<th width="25%" align="center" class="all_tables3 all_tables">Group Address</th>
						</tr>
					</thead>



					<script>
						writeLinesGroup();
					</script>
				</table>
			</form>

			<script>
				// changebgcolor();
				//changebgcolor2();
				// changebgcolor11();
				changebgcolor_name("table_igmp");
				changebgcolor21()
				// changebgcolor41();
				changebgcolor_mirror_table3();
				changebgcolor51();
				//changebgcolor61();
				changebgcolor_name("table_port_vlan");

				display();

			<% if (errorcode != "") { write_errorcode(errorcode); } %>
			</script>
			<br><br><br>

</body>

</html>