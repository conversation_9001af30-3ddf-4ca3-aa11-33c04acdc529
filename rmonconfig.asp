<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"RMON配置");</script></title>
<script language="javascript" >

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}


function checkvalue()
{
   var hid=document.webForm;
 	/*
	
    var lldptimer=document.webForm.lldptimer.vlaue;
	
  	if((lldptimer<=300) && (lldptimer>=5))
		{
			hid.submit();
		}
		else
		{
	   		alert(putmsg(<% write(lang); %>,"lldptimer的范围必须在5-300之间 且必须为数字"));
			return 0;
		} 
 */ 
    			hid.submit();

	return true;
}


</script>
</head>
<body  onload=""><br>

<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form  name="webForm" method="post" action="/goform/setRmonConfig">
<input type="hidden" name="flag">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<% var errorcode, rmonstatus; getrmonCfg(); %>
<table  id="table111"   width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" height="600" border="0" align="left" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" ><table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
      <tr>
        <td>
		
		<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
            <tr height="25">
              <td colspan="3" align="left" class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"RMON配置");</script></td>
            </tr>
        </table></td>
      </tr>
      
      <tr>
      
        <td><table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" id="table1" class="tablebord">




            <tr height="25">
              <td width="15%" align="left" class="crons">&nbsp;
                <script>writemsg(<% write(lang); %>,"RMON开关");</script></td>
              <td width="74%" align="left" class="crons"><input type="radio" name="rmonstatus" value="enable" <% if (rmonstatus=="enable") write("checked"); %>>
                Enable
                  <input type="radio" name="rmonstatus" value="disable" <% if (rmonstatus!="enable") write("checked"); %>>
                  Disable </td>
               </tr>
            
            <tr height="25">
              <td colspan="2" align="left" class="crons"> &nbsp;
                <div align="center">
                  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify2","checkvalue()");</script>
                </div></td>
</tr>
            
             
             
        </table></td>
      </tr>
    </table></td>
  </tr> 
  
</table>
</td></tr>
<tr><td>
</td></tr></table>
<script>
changebgcolor();

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
</form>
 
</body>
</html>

