<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"风暴抑制");</script></title>

<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript" type="text/JavaScript">


/*by LuoM  11-5-18*/
function DataScope(value,max,min)
{
	if(!isNaN(value) && value>=min && value<=max)
		return true;
	else
		return false;
}


function getRadioCheckedValue(radio_object)   // value of selected item, "" if none
{
	var index = 0;
	if (!radio_object)
		return "";
	var size = radio_object.length;
	if(isNaN(size)) 
	{
		if (radio_object.checked == true)
			return radio_object.value;
		else 
			return ""; 
	}
	for (var i = 0; i < size; i++)
	{
		if(!(radio_object[i])) 
			continue;
		if (radio_object[i].checked == true)
			return(radio_object[i].value);
	}
	if (radio_object.checked == true)
		return radio_object.value;
	else 
		return ""; 
}

function setDisabled(OnOffFlag,formFields)
{
	for (var i = 1; i < setDisabled.arguments.length; i++)
		setDisabled.arguments[i].disabled = OnOffFlag;
}

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

 function selectToAllx() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true; 
			 addToPortRangeX(objs[i]);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRangeX(objs[i]);
			}
             
        }
    } 
    
}


function addToPortRangeX(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var target = document.getElementById("port_range");
	var broadcast = document.getElementById("broadcast");
	var multicast = document.getElementById("multicast");
	var unknow = document.getElementById("unknow");
	
	var checkbox_broadcast = document.getElementById("checkbox_broadcast");
	var checkbox_multicast = document.getElementById("checkbox_multicast");
	var checkbox_unknow = document.getElementById("checkbox_unknow");
	
	
	var p = obj.value;
	var i;
	if(obj.checked){	
		target.value = target.value  + p + " ";
		
		//alert(trobj.cells[2].innerHTML);
		//alert(trobj.cells[3].innerHTML);
		//alert(trobj.cells[4].innerHTML);
		
		
		if(trobj.cells[2].innerHTML == "Disable")
			checkbox_broadcast.checked = true;
		else
			broadcast.value=trobj.cells[2].innerHTML;
			
		if(trobj.cells[3].innerHTML == "Disable")
			checkbox_multicast.checked = true;
		else
			multicast.value=trobj.cells[3].innerHTML;
			
		if(trobj.cells[4].innerHTML == "Disable")
			checkbox_unknow.checked = true;
		else
			unknow.value=trobj.cells[4].innerHTML;
		
		
		
	}else{
		target.value = target.value.replace(p+" ","");
	}

}


function set_storm_enable()
{
	var cf = document.forms[0];
	var dflag = (getRadioCheckedValue(cf.storm_enable) == "disable");
	
	if((dflag == true) || (arguments[0] == "1"))
	  dflag = true;
	else
		dflag = false;
		setDisabled(dflag, cf.control_rate[0], cf.control_rate[1], cf.control_rate[2],cf.control_rate[3],cf.control_rate[4],cf.control_rate[5],cf.control_pkts,cf.control_pkts2,cf.warning_type,cf.way[0],cf.way[1]);
		
}
function StormOut(portId,Broadcast,Multicast,Unknow)
{
    var narr=5;
    var tbtd;
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port").insertRow(-1);
	
    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");
	tbtr.setAttribute("id", "tr_"+portId);
	
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
        tbtr.appendChild(tbtd);
    }
	tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"checkbox_index\" value=\""+portId+"\" onclick=\"addToPortRangeX(this)\"/>";
    tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = Broadcast;
    tbtr.cells[3].innerHTML = Multicast;
    tbtr.cells[4].innerHTML = Unknow;
}



function saveData()
{
	var tf=document.stormseting;
	tf.action = "/goform/saveComm?name=storm";
	tf.submit();
}

function stormJudgment()
{
	var st=document.stormseting;
	var broadcast = document.getElementById("broadcast").value;
	var multicast = document.getElementById("multicast").value;
	var unknow = document.getElementById("unknow").value;
	
		var checkbox_broadcast = document.getElementById("checkbox_broadcast");
	var checkbox_multicast = document.getElementById("checkbox_multicast");
	var checkbox_unknow = document.getElementById("checkbox_unknow");
	
	
	

	 
		if((DataScope(broadcast,1000000,8)==true ||checkbox_broadcast.checked == true) && (DataScope(multicast,1000000,8)==true||checkbox_multicast.checked == true) &&( DataScope(unknow,1000000,8)==true||checkbox_unknow.checked == true))
		{
				if(checkbox_broadcast.checked == true)
					document.getElementById("broadcast").value= -1;
					
					if(checkbox_multicast.checked == true)
					document.getElementById("multicast").value= -1;
				if(checkbox_unknow.checked == true)
					document.getElementById("unknow").value= -1;
				
				
					//	alert(document.getElementById("broadcast").value);
					//	alert(document.getElementById("multicast").value);
					//	alert(document.getElementById("unknow").value);
		
			
			st.submit();
		}
		else
		{
			alert(putmsg(<% write(lang); %>,"取值范围为8-1000000!"));
		}



}

function DelStorm()
{
	var st=document.stormseting;
	st.action= "/goform/stormClean";
	st.submit();
}

function refreshpage()
{
  location.href='storm.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('storm',<% write(lang); %>);
}

function changebgcolor_port(){
 var tab = document.all.table_port;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

</script>
</head>

<body  >
<br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
	<form name="stormseting" method="POST" action="/goform/stromComm_kbits">
	<input type="hidden" name="ltime" value=<% write(lltime); %> >
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
    <input type="hidden" name="left_menu_id" value="@left_menu_id#">

<table  id="mainTbl" width="98%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr><td>
<table id="mainTbl" width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
              <tr height="25">
              <td colspan="2" align="left"   class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"风暴抑制");</script><th>
            </tr>
                  
        </table>
       </td></tr>
       <tr><td> 
         <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">
    
                  
     <tr height="25">
              <td align="left" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"端口范围");</script></td>
              <td width="81%" align="left" class="crons">&nbsp;
                <input name="port_range" type="text" class="input_x" id="port_range" readonly="true"/></td>
            </tr>         
      <tr height="25">
              <td width="20%" align="left" class="crons" >&nbsp;<script>writemsg(<% write(lang); %>,"广播数据包");</script></td>
              <td align="left" class="crons">&nbsp;<input type="checkbox" name="checkbox_broadcast"  id="checkbox_broadcast">&nbsp;
				<input id="broadcast" type="text" name="broadcast" class="input_board8"/> (fe:8-100000kbits, ge:8-1000000kbits)   </td>
            </tr>
			<tr height="25">
              <td width="20%" align="left" class="crons" >&nbsp;<script>writemsg(<% write(lang); %>,"多播数据包");</script></td>
              <td align="left" class="crons">&nbsp;<input type="checkbox" name="checkbox_multicast"  id="checkbox_multicast">&nbsp;
				<input id="multicast" type="text" name="multicast" class="input_board8"/> (fe:8-100000kbits, ge:8-1000000kbits)  </td>
            </tr>
			<tr height="25">
              <td width="20%" align="left" class="crons" >&nbsp;<script>writemsg(<% write(lang); %>,"未知单播包");</script></td>
              <td align="left" class="crons">&nbsp;<input type="checkbox" name="checkbox_unknow"  id="checkbox_unknow">&nbsp;
				<input id="unknow" type="text" name="unknow" class="input_board8"/>  (fe:8-100000kbits, ge:8-1000000kbits)    </td>
            </tr>
			<tr><td colspan="2" align="middle" class="crons"> 
			    <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","","stormJudgment()");</script>
			    <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"删  除","button","button","","DelStorm()");</script>
			</td></tr>
			
    </table></td></tr>
    <tr>
         <td height="8"></td>
      </tr>
	<tr>
        <td><table width="100%" height="35"  border="0" cellpadding="0" cellspacing="0"  class="tablebord" id="table_port">
            <tr height="25" align="center" class="crons">
              <th align="center" class="td2" width="10%" ><input type="checkbox" name="check_all" value="all" onClick="selectToAllx()" /></th>
			  <th align="center" class="td2" width="15%" >&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></td>
              <th align="center" class="td2" width="25%" >&nbsp;<script>writemsg(<% write(lang); %>,"广播数据包");</script></td>
              <th align="center" class="td2" width="25%" >&nbsp;<script>writemsg(<% write(lang); %>,"多播数据包");</script></td>
              <th align="center" class="td2" width="25%" >&nbsp;<script>writemsg(<% write(lang); %>,"未知单播包");</script></td>
            </tr>
	
            <script>
				  <%  var errorcode; stormShow();%>
				 
            </script>
        </table></td></tr>
    <tr>
        <td align="center" height="35">
          <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
          &nbsp;
          &nbsp;
</td>
      </tr> 
    </table>
 </td></tr> 
 
</table>
</td></tr>


</table>
<input type="hidden" name="h_storm_enable" value="@h_storm_enable#">
<input type="hidden" name="h_control_rate" value="@h_control_rate#">
<input type="hidden" name="control_type" value="@control_type#">
<input type="hidden" name="control_warn" value="@control_warn#">
<input type="hidden" name="loader" value="@loader#"> 
<input type="hidden" name="port_num" value="@port_num#">
 <input type="hidden" name="todo" value="save">
<input type="hidden" name="this_file" value="storm.html">
<input type="hidden" name="next_file" value="storm.html">
<input type="hidden" name="message" value="@msg_text#">
</form> 
<script>
changebgcolor();
changebgcolor_port();
<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>
 
</body>
</html>

