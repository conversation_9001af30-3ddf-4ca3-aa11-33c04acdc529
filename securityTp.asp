<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<script language="javascript" type="text/javascript" src="js/alpinejs.min.js" defer></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title><script>writemsg(<% write(lang); %>,"端口安全设置");</script></title>
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript">

function isValidString(str)
{
	var validc = "/'%`\"\\><";
	var a,c;
	for(i = 0 ; i < validc.length; i ++)
	{
		c = validc.substring(i, i + 1);
		if(str.indexOf(c) > -1)
		{
			return true;
		}
	}
	return false;
}


/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true;  
			 addToPortRange(objs[i]);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRange(objs[i]);
			}
             
        }
    } 
    
}

/*MTU judgment*/
function checking2()
{
	var port_range = document.getElementById("port_range").value;
	var port_speed = document.getElementById("port_speed");
	var tf=document.port_setting;
	var i,j;
	/*
	if(((port_range.indexOf("ge")>=0) && (port_range.indexOf("xe")>=0))||
		((port_range.indexOf("ge")>=0) && (port_range.indexOf("sa")>=0))||
		((port_range.indexOf("ge")>=0) && (port_range.indexOf("po")>=0))||
		((port_range.indexOf("xe")>=0) && (port_range.indexOf("sa")>=0))||
		((port_range.indexOf("xe")>=0) && (port_range.indexOf("po")>=0))||
		((port_range.indexOf("po")>=0) && (port_range.indexOf("sa")>=0)))
	{
        alert(putmsg(<% write(lang); %>,"不同类型的端口不能同时配置!"));
		return;
	}
	*/

	tf.submit();
}

/*display function*/
function P(portId, cspeed)
{

    var narr=3;
	var arr="";
	var speed="";
	var Desc="";
    var tbtd;
    var i;
    var opt;
    var tbtr = document.getElementById("table_port").insertRow(-1);

	
    tbtr.classname = "td7";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "td7");
    tbtr.setAttribute("className", "td7");
	tbtr.setAttribute("id", "tr_"+portId);

	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	

        tbtr.appendChild(tbtd);
    }

	/*display*/
	tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"checkbox_index\" value=\""+portId+"\" onchange=\"addToPortRange(this)\"/>";
	tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = cspeed;

}

/*
	Show all check true port, and will last a port data displayed
*/
function addToPortRange(obj){
	var trid="tr_"+obj.value;
	var nodeArray;
	var trobj = document.getElementById(trid);
	var target = document.getElementById("port_range");
	var port_speed = document.getElementById("port_speed");

	var p = obj.value;
	var i;

	if(obj.checked)
	{
		target.value = target.value  + p + " ";


		for(i=0;i<port_speed.options.length;i++)
		{
			if(port_speed.options[i].text==trobj.cells[2].innerHTML)
			{
				port_speed.options[i].selected=true;
				break;
			}
		}			
	}else{

		target.value = target.value.replace(p+" ","");
	}

}

function refreshpage()
{
  location.href='securityTp.asp?ltime='+<% write(lltime); %>;
}

function jsGetUrlParamActive() {
    let urlSearch = window.location.search
    const search = new URLSearchParams(urlSearch)
    const params = Object.fromEntries(search.entries())
    try {
        if (params.active) return params.active
    } catch { }
    return 'tab1'
}

function portChange2(row) {
	let object = {
		pageName: "securityTp.asp",
	}
	object.PortAuth = [{
		portName: row.portName,
		sofeMode: row.sofeMode=='NONE'?'none':'protocol-auth'
	}]
	var tf=document.auth_setting;
	tf.param2.value = JSON.stringify(object)
	tf.action = "/goform/jw_set_portAuthConfig"
	tf.submit();
	return true;
}

function setDevprotoStatus() {
	var tf = document.auth_setting;
	
	tf.action="/goform/jw_set_DevprotoStatus";
	tf.submit();
	return true;
}

function Manual_Authentication(row) {
	let object = {
		pageName: "securityTp.asp",
	}
	object.PortAuth = [{
		portName: row.portName,
	}]
	var tf=document.auth_setting;
	tf.param2.value = JSON.stringify(object)
	tf.action = "/goform/jw_set_ManualAuthConfig"
	tf.submit();
	return true;
}

</SCRIPT>
</head>

<body x-data="{active:'tab1'}" x-init="active=jsGetUrlParamActive()" onload=""><br>
<%  var authmode; checkCurMode(); %>
<% web_get_stat(); %>
<!-- page title -->
<table width="100%" align="center" border="0" cellspacing="0" cellpadding="0">
	<tr>
		<td height="30px">
			<font size="5" color="#404040">
				<div class="bot">端口安全设置</div>
			</font>
		</td>
	</tr>
</table>

<!-- port menu -->
<div>
	<ul class="tabmenu">
		<li id="tab1" :class="active=='tab1'?'tab':'tab-disable'">
			<a herf="#" x-on:click="active='tab1'">端口安全策略</a>
		</li>
		<li id="tab2" :class="active=='tab2'?'tab':'tab-disable'">
			<a herf="#" x-on:click="active='tab2'">设备安防协议认证</a>
		</li>
	</ul>   
</div>

<form x-show="active=='tab1'" name="port_setting" method="POST" action="" class="formContain">
	<input type="hidden" name="param1" id="param1" >
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
	<input type="hidden" name="lastts" value=<% write(serverts); %>>
	<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

	<script>
		checktop(<% write(lang); %>);
		retValue = <% var responseJsonStr; jw_get_portSafeConfig(); %>
		responseStr = <% write(responseJsonStr); %>;
		function portChange(row) {
			var obj={
				pageName: "securityTp.asp",
				PortSafe: [
					{
						portName: row.portName,
						sofeMode: row.sofeMode=='NONE'?'none':'static-mac'
					}
				]
			}
			var tf=document.port_setting;
			tf.param1.value = JSON.stringify(obj)
			tf.action = "/goform/jw_set_portSafeConfig"
			tf.submit();
			return true;
		}
	</script>

	<div x-data="{}">
		<table width="96%" class="tablebord" align="center" border="0" cellspacing="0" cellpadding="0" >
			<thead>
				<tr>
					<th class="all_tables">端口</th>
					<th class="all_tables">端口安全策略</th>
				</tr>
			</thead>
			<tbody>
				<template x-for="(row,rowIndex) in responseStr.PortSafe">
					<tr>
						<td style="font-weight: normal; text-align: center; height: 35px;" class="all_tables" x-text="row.portName"></td>
						<td style="font-weight: normal; text-align: center; height: 35px;" class="all_tables">
							<select x-model="row.sofeMode" @change="portChange(row)">
								<option value="NONE">NONE</option>
								<option value="STATIC_MAC">STATIC_MAC</option>
							</select>
						</td>
					</tr>
				</template>
			</tbody>
		</table>
	</div>
</form> 

<form x-show="active=='tab2'" name="auth_setting" method="POST" action="" class="formContain">
	<input type="hidden" name="param2" id="param2" >
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
	<input type="hidden" name="lastts" value=<% write(serverts); %>>
	<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

	<script><% var errorcode, l2pro, devprotoStatus; jw_get_DevprotoStatus(); %></script>
	<div style="display: flex;">
		<div width="20%" align="left" class="all_tables3 all_tables" style="border: none;text-align: left; height: 35px;">
			<script>writemsg(<% write(lang); %>, "全局使能:");</script>
		</div>
		<div width="40%" align="left" class="crons" colspan="2">
			<select name="devprotoStatus" id="devprotoStatus" @change="setDevprotoStatus()" style="width: 200px;">
				<option value="disabled" <% if (devprotoStatus=="disabled") write("selected"); %>>
					关闭
				</option>
				<option value="enabled" <% if (devprotoStatus=="enabled") write("selected"); %>>
					开启
				</option>
			</select>
		</div>
	</div>

	<script>
		retValue = <% var responseJsonProtoStr; jw_get_portAuthConfig(); %>
		responseProtoStr = <% write(responseJsonProtoStr); %>;
		const authData = {
            headers: ['端口', '协议认证启用', '手动认证'],
            PortAuth: [
                {
                    portName: null,
                    sofeMode: null,
                },
            ],
        selectedValues: {}
        };
        authData.PortAuth = [...responseProtoStr.PortAuth]
		console.log(authData);
	</script>

	<div x-data="{authData}">
	<table width="96%" class="tablebord" align="center" border="0" cellspacing="0" cellpadding="0" >
		<thead>
			<tr>
				<th class="all_tables">端口</th>
				<th class="all_tables">协议认证启用</th>
				<th class="all_tables">手动认证</th>
			</tr>
		</thead>
		<body>
			<template x-for="(row,rowIndex) in authData.PortAuth">
				<tr>
					<td style="font-weight: normal; text-align: center; height: 35px;" class="all_tables" x-text="row.portName"></td>
					<td style="font-weight: normal; text-align: center; height: 35px;" class="all_tables">
						<select x-model="row.sofeMode" @change="portChange2(row)">
							<option value="NONE">NONE</option>
							<option value="PROTOCOL_AUTH">PROTOCOL_AUTH</option>
						</select>
					</td>
					<td style="font-weight: normal; text-align: center;" class="all_tables" >
						<input type="button" name='button' id='button' class='buttons_apply' 
								x-bind:disabled="row.sofeMode == 'NONE'" value="手动认证" 
								x-bind:style="row.sofeMode == 'NONE' ? 'background-color: #ccc; cursor: not-allowed;' : ''"
								x-on:click="Manual_Authentication(row)">
					</td>
				</tr>
			</template>
		</body>
	</table>
	</div>
</form>
 
</body>

</html>



