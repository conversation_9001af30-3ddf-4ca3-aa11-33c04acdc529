<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<%  var authmode; checkCurMode(); %>

<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"GMRP");</script></title>
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript">

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function del_trust(portId)
{
	var hid = document.macdel;
	
		hid.port_value.value=portId;

	
	hid.action="/goform/QostrustDel"
	hid.submit();
	return 0;
}


function selectToAll() 
{  
		var cf = document.forms[0];
		var objs = document.getElementsByName("checkbox_index"); 
		var i;
		if(cf.check_all.checked == true)
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
		       if(objs[i].disabled==false && objs[i].checked==false){
		         objs[i].checked = true;  
			 		addToPortRange(objs[i]);
			 }
		    }
		}
		else
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
					if(objs[i].checked==true){
					objs[i].checked = false;  
					 addToPortRange(objs[i]);
			}
		         
		    }
		} 
    
}


function checkup()
{
	var tf = document.vlan_port;
	tf.action = "/goform/selQoSPolicyPort";
	tf.submit();
}

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var target = document.getElementById("port_range");
	var avid = document.getElementById("avid");
	var dvid = document.getElementById("dvid");
//	var porttype = document.getElementById("porttype");
	var filter = document.getElementById("filter");
	var type_value = document.getElementById("type_value");
	var filter_value = document.getElementById("filter_value");
	var vlan_port_id = document.getElementById("vlan_port_id");
	var p = obj.value;

	if(obj.checked){
		target.value =target.value + p + " ";
		vlan_port_id.value =vlan_port_id.value + p + " , ";
		
	

	}
	else
	{
		target.value = target.value.replace(p + " ","");
		vlan_port_id.value = vlan_port_id.value.replace(p + " , ","");
	}

}

function P(portId,policyId,trust)
{
    var narr=4;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+portId);	


    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		
	        tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
    tbtr.cells[0].abbr = portId;
    
   
	

		//tbtr.cells[0].innerHTML = "<input type=\"checkbox\" id=\"input_"+portId+"\" name=\"checkbox_index\" value=\""+portId+"\" onclick=\"addToPortRange(this)\"/>";
	
		tbtr.cells[0].innerHTML = portId;

    tbtr.cells[1].innerHTML = policyId;
    tbtr.cells[2].innerHTML = trust;

	if (<% write(authmode); %> == 1)
    	tbtr.cells[3].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick=del_trust('"+portId+"')>";
	else
		tbtr.cells[3].innerHTML = " ";
   // tbtr.cells[3].innerHTML = leaveall;
  //  tbtr.cells[4].innerHTML = enable;

}


function refreshpage()
{
  location.href='qostrust.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('vlan_port',<% write(lang); %>);
}

</script>
</head>

<body  ><br>
<% web_get_stat(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="vlan_port" method="POST" action="">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="vlan_port_id" id="vlan_port_id" >
<input type="hidden" name="flag" id="flag" >
<input type="hidden" name="type_value" id = "type_value"  value="@trunk_config#">
<input type="hidden" name="filter_value" id = "filter_value"  value="@trunk_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="pvid_config"  value="@pvid_config#">
<input type="hidden" name="egress_tagged" id="egress_tagged" value="@egress_tagged#">

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">

<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>

          	 
 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>QOS设置/端口信任模式</b></font></td></tr>
 </table>
 
<!--	 
    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
	     <tr height="25">
	       <td align="left" colspan="4"  class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"QoS端口信任模式");</script></td>
	     </tr>
        </table>
-->
        
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%" id="table1" class="tablebord">
     <tr height="25">
	     	  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"QoS策略");</script></td>
	     	  <td class="crons" colspan="3"><input name="qos_policy" type="text" class="input_x" id="qos_policy" >&nbsp;(留空则为默认策略)	     </td>
	     </tr>	
     <tr height="25">
	 	<td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"Trust dscp");</script></td>
	 	<td class="crons" colspan="3"><input type="checkbox" name="check_trust" checked="checked" />&nbsp;	     </td>
	 </tr>	
	                 
<tr>
<td width="15%" height="30"  rowspan="3" class="crons" >&nbsp;<script>writemsg(<% write(lang); %>,"端口范围");</script><input name="port_range" type="hidden" class="input_x" id="port_range" readonly="true"/></td>
<td align="left" class="crons">&nbsp;<% AppendOptionMirrored(); %>&nbsp;<input type="checkbox" name="check_all" value="all" onClick="selectToAll()" />All&nbsp;&nbsp;&nbsp;&nbsp;</td>
</tr>
		
    	     	   	 
	    <tr height="25">

	     	  <td colspan="4" align="middle" class="crons">
	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify","checkup()");</script>
&nbsp;&nbsp;&nbsp;</td>
	     </tr>	  
			 
   <tr>
	    	<td colspan="4">
	    		<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
	    		 	<tr align="center" height="25" class="crons">
	    		 		<th class="td2" width="10%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></b></font></th>
	    		 		<th class="td2" width="30%"><font color="#333333"><b>&nbsp;Dscp to CoS</b></font></th>
	    		 		<th class="td2" width="30%"><font color="#333333"><b>&nbsp;Trust</b></font></th>						
		    		 		<th class="td2" width="30%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"删除");</script></b></font></th>						
			
	    		 	</tr>
					<script>  <%  var errorcode; showQoSTrust(); %></script>
	    		</table>	    	</td>
   </tr>
     </table>
   </td></tr>
   <tr>
	  	   <td colspan="2" align="center" height="35">
	  	   <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
	  		</td>
   </tr>
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>

</form>  
<script>
changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>


	<form name="macdel" method="POST" action="">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

	<input type="hidden" name="port_value"  value="" >

</form>

</body>
</html>

