[
  {
    "clockType": 1,            // 时钟模式：0-OC（普通时钟），1-BC（边界时钟），2-TC（透明时钟），3-MIX-CLK（混合模式） --------- portType为MASTER时可以选择BC，MIX-CLK
    "pdelayTcEn": 1,            // Pdelay透传使能：1-启用（TC模式下透传Pdelay报文），0-禁用 --------- clockType为2，3时显示，默认0
    "delayValue": [100, -50, 20, -30],  // 补偿值：[RX-DELAY, TX-DELAY, RX-ASYM, TX-ASYM]（有符号整数，单位：纳秒） --------- portType为0(SLAVE)时显示
    

    //clockType 为TC模式时下列都隐藏
    // macAddr参数
    "dMacmode": [1, 0, 1, 0],   // MAC地址模式：0-COPY，1-CONFIG（控制是否手动配置） --------- clockType为2(TC)时隐藏
    "dMac": [                  // 目的MAC地址（4种包类型） --------- clockType为2(TC)时隐藏，clockType不为2时根据dMacmode为1显示
      [0x00, 0x11, 0x22, 0x33, 0x44, 0x55], // REQ包
      [0x00, 0xAA, 0xBB, 0xCC, 0xDD, 0xEE], // RESP包
      [0x11, 0x22, 0x33, 0x44, 0x55, 0x66], // PDELAY包
      [0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC]  // SYNC包
    ],
    // ipAddr参数 -- encapType为2，3时显示
    "dIpmode": [1, 1, 0, 0],    // IP地址模式：0-COPY，1-CONFIG（控制是否手动配置） --------- clockType为2(TC)时隐藏，clockType不为2时根据encapType为2，3显示
    "dIp": [********, ********, ********, ********], // 目的IP地址（点分十进制：********等） --------- clockType为2(TC)时隐藏，clockType不为2时根据encapType为2，3且dIpmode为1显示
    "unicastFlag": [1, 0, 1, 1],// 单播模式：0-MULTICAST，1-UNICAST，2-COPY（针对SYNC/ANNOUNCE/REQ/RESP包） --------- clockType为2(TC)时隐藏，clockType不为2时根据encapType为2，3显示，默认0
    

    // PTP基本参数
    "ptpHead": {               // --------- clockType为2(TC)时隐藏
      "domainNumber": 1,       // 域编号：取值范围[0-255]（标识时钟所属域） --------- clockType为2(TC)时隐藏
      "flagField": 0x123,      // 标志字段：取值范围[0-65535]（协议扩展标志） --------- clockType为2(TC)时隐藏
      "sourcePortIdentity": {  // 源端口标识 --------- clockType为2(TC)时隐藏
        "clock_id": [0x12345678, 0x87654321], // 时钟ID（8字节组合，高位在前） --------- clockType为2(TC)时隐藏
        "port_number": 100     // 端口号：取值范围[0-65535] --------- clockType为2(TC)时隐藏
      },
      "msgInterval": [10, 20, 30, 40, 50], // 消息间隔日志：[SYNC, ANNOUNCE, DELAY, PREQ, PRESP]（单位：日志级别） --------- clockType为2(TC)时隐藏
      "ttl": [64, 128]           // TTL配置：[PDELAY, NON_PDELAY]（数据包生存时间） --------- clockType为2(TC)时隐藏，clockType不为2时根据encapType为2，3显示
    },
    "selfPortIdentityValue": {  // 本端口标识（用于标识自身时钟） --------- clockType为2(TC)时隐藏
        "clock_id": [0x55555555, 0x66666666], // --------- clockType为2(TC)时隐藏
        "port_number": 400       // --------- clockType为2(TC)时隐藏
      },
    "announceReceiptTimeout": { // Announce超时配置 --------- clockType为2(TC)时隐藏，clockType不为2时根据portType为0(SLAVE)显示
        "timeoutSecond": 3,       // 超时秒数：取值范围[0-1023] --------- clockType为2(TC)时隐藏
        "timeoutMicrosecond": 500  // 超时微秒数：取值范围[0-1048576]（单位：1024ns） --------- clockType为2(TC)时隐藏
    },
    "txInterval": [             // 发送间隔配置（对应SYNC/ANNOUNCE/REQ包） --------- clockType为2(TC)时隐藏
        {
            "txIntervalSecond": 1,   // SYNC包秒数 --------- clockType为2(TC)时隐藏，clockType不为2时根据portType为1(MASTER)显示
            "txIntervalMicrosecond": 100 // SYNC包微秒数 --------- clockType为2(TC)时隐藏，clockType不为2时根据portType为1(MASTER)显示
        },
        {
            "txIntervalSecond": 2,   // ANNOUNCE包秒数 --------- clockType为2(TC)时隐藏，clockType不为2时根据portType为1(MASTER)显示
            "txIntervalMicrosecond": 200 // ANNOUNCE包微秒数 --------- clockType为2(TC)时隐藏，clockType不为2时根据portType为1(MASTER)显示
        },
        {
            "txIntervalSecond": 3,   // REQ包秒数 --------- clockType为2(TC)时隐藏，clockType不为2时根据portType为0(SLAVE)显示
            "txIntervalMicrosecond": 300 // REQ包微秒数 --------- clockType为2(TC)时隐藏，clockType不为2时根据portType为0(SLAVE)显示
        }
    ],


    //报文校验配置
    "rxBypassEn": 0,            // 接收透传使能：1-启用（直接转发PTP报文不处理），0-禁用 --------- clockType为2(TC)时隐藏
    "rxEncapType": 0,           // 接收封装类型：0-ETH，1-IPV4（检查报文封装格式） --------- clockType为2(TC)时隐藏
    "pktCheck": {               // 接收报文检查配置 --------- clockType为2(TC)时隐藏
      "rxVlanCheckEn": 1,       // VLAN检查使能：1-启用，0-禁用 --------- clockType为2(TC)时隐藏
      "rxVlanCheckValue": 100,  // VLAN值：取值范围[1-4094]（匹配报文VLAN标签） --------- clockType为2(TC)时隐藏
      "rxDipCheckEn": 1,        // 目的IP检查使能：1-启用，0-禁用 --------- clockType为2(TC)时隐藏，clockType不为2时根据rxEncapType为1显示
      "rxDipCheckValue": 0x0A000005, // 目的IP：********（点分十进制） --------- clockType为2(TC)时隐藏，clockType不为2时根据rxEncapType为1显示
      "rxDmacCheckEn": 1,       // 目的MAC检查使能：1-启用，0-禁用 --------- clockType为2(TC)时隐藏
      "rxDmacCheckValue": [0x00, 0x22, 0x44, 0x66, 0x88, 0xAA], // 目的MAC地址 --------- clockType为2(TC)时隐藏
      "domainCheckEn": [1, 1, 0], // 域名检查使能：[NON-REQ, REQ, ANNOUNCE]包（1-启用，0-禁用） --------- clockType为2(TC)时隐藏
      "domainValue": [1, 1, 0],   // 域名检查值：对应三种包类型的合法域编号 --------- clockType为2(TC)时隐藏
      "transportCheckEn": 1,     // 传输层检查使能：1-启用（检查协议类型） --------- clockType为2(TC)时隐藏
      "portIdentityCheckEn": [1, 0, 1], // 端口标识检查使能：[PDELAY, SYNC, ANNOUNCE]包 --------- clockType为2(TC)时隐藏
      "portIdentityValue": [     // 合法端口标识列表（对应检查使能的包类型） --------- clockType为2(TC)时隐藏
        {
          "clock_id": [0x11111111, 0x22222222], // --------- clockType为2(TC)时隐藏
          "port_number": 200       // --------- clockType为2(TC)时隐藏
        },
        {
          "clock_id": [0x33333333, 0x44444444], // --------- clockType为2(TC)时隐藏
          "port_number": 300       // --------- clockType为2(TC)时隐藏
        }
      ]
    },

    // 时间同步配置 --------master模式下有效,slave模式下隐藏
    "announce": {              // Announce消息参数配置 --------- clockType为2(TC)时隐藏，clockType不为2时根据portType为1(MASTER)显示
      "currentUtcOffset": 0,   // UTC偏移：取值范围[0-65535]（时钟与UTC的时间差） --------- clockType为2(TC)时隐藏
      "priority1": 10,         // 优先级1：取值范围[0-255]（主时钟优先级） --------- clockType为2(TC)时隐藏
      "priority2": 20,         // 优先级2：取值范围[0-255]（备用优先级） --------- clockType为2(TC)时隐藏
      "clockQuality": {        // 时钟质量参数 --------- clockType为2(TC)时隐藏
        "clockClass": 64,      // 时钟等级：取值范围[0-255]（如64表示普通时钟） --------- clockType为2(TC)时隐藏
        "clockAccuracy": 100,  // 时钟精度：取值范围[0-255]（单位：ppm） --------- clockType为2(TC)时隐藏
        "clockVariance": 200   // 时钟稳定度：取值范围[0-65535]（长期频率偏差） --------- clockType为2(TC)时隐藏
      },
      "identity1": 0x11111111, // 时钟ID高位：与identity2组成完整8字节ID --------- clockType为2(TC)时隐藏
      "identity2": 0x22222222, // 时钟ID低位 --------- clockType为2(TC)时隐藏
      "stepsRemoved": 5,       // 跳数：取值范围[0-65535]（距离时间源的路径长度） --------- clockType为2(TC)时隐藏
      "timeSource": 1          // 时间源：取值范围[0-255]（如1表示GPS） --------- clockType为2(TC)时隐藏
    }
  }
]
