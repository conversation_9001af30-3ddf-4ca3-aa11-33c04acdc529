<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<title>help</title>

<link href="css/global.css" rel="stylesheet" type="text/css">
<script language="JavaScript">
</script>
<script language="JavaScript" type="text/JavaScript">
</script>
<style type="text/css">
<!--
.STYLE1 {font-size: 14px}
.STYLE2 {font-size: 12px}
.STYLE3 {color: #FF0000}
-->
</style>
</head>

<body class="main">
    <table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">
        <tr><td>
            <table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" class="leftnav table1">
            <tr>
				<td valign="top" >

				    <!-- 具体内容 begin-->
				    <table width="90%"   border="0" align="center" cellpadding="4" cellspacing="1" class="cword09">    
							    <tr>
								   	<td> 		   	
							   	      <table width="100%" height="77" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09" id="sys_info">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="sysinfo">Basic Information</a></th>
								        </tr>
									    <tr>
									      <td height="50" valign="middle"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Display the information of Device,which is including Product Name,Version, OID,Baud Rate,MAC,Vendor Information,Copyright Information,Display the information of running state,which including running time,CPU Utilization,Memory Utilization,etc.</span></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="port">Port Management</a></th>
								        </tr>
									    <tr>
									      <td height="258" valign="middle"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Modify the configuration of port,which including Speed,Flow-control,Jumbo-frame and Description,etc. <br>
										  	  <br>
											  &nbsp;&nbsp; <strong>Port Range</strong>   The only way to choose the port is checking the checkbox.<br>
											  <br>
											  &nbsp;&nbsp; <strong>Port enable</strong>   Enable means no shutdown port, disable means shutdown port.<br>
											  <br>
											  &nbsp;&nbsp; <strong>Speed</strong> Configure the speed, duplex and negotiation of port.<br>
											  <br>
											  &nbsp;&nbsp; <strong>Flowcontrol</strong>  Enable and disable Flow-control function,complied IEEE802.3x standard. the default value is disable.<br>
											  <br>
											  &nbsp;&nbsp; <strong>Jumbo-frame</strong> Configure the MTU of port. the default value is 1500. <br>
											  <br>
											  &nbsp;&nbsp; <strong>Exit line_rate</strong> The max speed of egress port actual forward。<br>
											  <br>
											  &nbsp;&nbsp; <strong>Exit burst</strong> The max frame number of egress port a burst transmission contain.<br>
											  <br>
											  &nbsp;&nbsp; <strong>Entry line_rate</strong> The max speed of ingress port actual forward。<br>
											  <br>
											  &nbsp;&nbsp; <strong>Entry burst</strong>  The max frame number of ingress port a burst transmission contain.<br>
											  <br>
										      &nbsp;&nbsp; <strong>Description</strong>  Configure the description of port.	<br>
										      <br>
										      &nbsp;<span class="STYLE3">Notes：</span>Can't support manual input port name in the text box. Can't support configure Speed,Exit line_rate,Exit burst,Entry line_rate and Entry burst on aggregation Ports.</span>				  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="storm">Storm Control </a></th>
								        </tr>
									    <tr>
									      <td height="234"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Storm-control prevents broadcast storms, multicast storms and unidirectional storms on the network. <br>
										  <br>
											  &nbsp;&nbsp; <strong>Port Range</strong>   The only way to choose the port is checking the checkbox.<br>
											  <br>
											  &nbsp;&nbsp; <strong>Broadcast Traffic</strong>  Configure the broadcast traffic ratio of bandwidth for the checked ports,the available value is 0.00-100.00,the default value is 10.00%<br>
											  <br>
											  &nbsp;&nbsp; <strong>Multicast Traffic</strong>  Configure the Multicast traffic ratio of bandwidth for the checked ports,the available value is 0.00-100.00,the default value is 100.00%<br>
											  <br>
											  &nbsp;&nbsp; <strong>Unidirectional Unicast Traffic</strong>  Configure the unidirectional Unicast traffic ratio of bandwidth for the checked ports, the available value is 0.00-100.00, the default value is 10.00%<br>
											  <br>
											  &nbsp;<span class="STYLE3">Notes：</span>Delete means restore the default value. </span>									  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mirror">Port Mirror</a></th>
								        </tr>
									    <tr>
									      <td height="187"><span class="STYLE1">&nbsp;&nbsp;&nbsp;By configuring the port mirror function, network administrator can easily observe the packets that pass through the device in real time. The mirroring result serves as a basis for traffic detection,trouble shooting,and data analysis. <br>
										  <br>
										  &nbsp;&nbsp; <strong>Mirroring Port</strong>   Appoint the mirrored port,that is observed port.<br>
										  <br>
										  &nbsp;&nbsp; <strong>Monitor Port</strong>   Choose a mirror port,that is connecting a monitoring host.<br>
										  <br>
										  &nbsp;&nbsp; <strong>Direction</strong>    Choose the incoming traffic,outgoing traffic,or both incoming and outgoing traffic at an observed port.<br>
										  <br>
										  &nbsp;<span class="STYLE3">Notes:</span>Only allow to modify the directon selected. </span>								  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="sedtab">MAC address table</a></th>
								        </tr>
									    <tr>
										  <td height="180"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Device checks and records the source MAC address of data frames received on a port. Network administrator could get these information from Mac address table,including Source MAC address,VLAN ID and source Port.<br>
										  <br>
										  &nbsp;&nbsp; <strong>Aging time</strong>          Modify the aging time of MAC address table. The value 0 is available, and means no aging. the default value is 300s.<br>
										  <br>
										  &nbsp;&nbsp; <strong>Delete MAC address table</strong>     Network administrator could delete dynamic mac items by vlan, port or mac.<br>
										  <br>
										  &nbsp;<span class="STYLE3">Notes：</span>Only allow to delete dynamic mac items.</span>									  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="forfil">Static MAC and static MAC filter</a></th>
								        </tr>
									    <tr>
									      <td height="154">
											<span class="STYLE1">&nbsp;&nbsp; <strong>VLAN</strong>        Add the vlan of static mac item.<br>
											<br>
											&nbsp;&nbsp; <strong>MAC address</strong>    Add the mac address of static mac item.<br>
											<br>
											&nbsp;&nbsp; <strong>Functions</strong>     Must to choose forwarding port when add static mac items. If checking MAC filter function, there is no need to choose forwarding port.<br>
											<br>
											&nbsp;<span class="STYLE3">Notes：</span>MAC filter function will match source mac and direction mac of frame with configured mac. </span>										  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mac">MAC Banding </a></th>
								        </tr>
									    <tr>
									      <td height="148"><span class="STYLE1">&nbsp;&nbsp; <strong>Port</strong>    Configure the MAC banding to port.<br>
										  	  <br>	
											  &nbsp;&nbsp;<strong> MAC Address</strong>   Configure the Banding to MAC.<br>
											  <br>
											  &nbsp;&nbsp; <strong>VLAN</strong>        Configure the Banding to VLAN.<br>
											  <br>
											  &nbsp;<span class="STYLE3">Notes：</span> The port learning disable if only one binding mac item enable.</span>									  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
								<tr>
								<td> 		   	
									<table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
									<tr height="30">
										<th align="left" colspan="2">&nbsp;<a name="mac-bind">MAC Address Automatic Banding </a></th>
									</tr>
									<tr>
										<td height="148"><span class="STYLE1">&nbsp;&nbsp; <strong>Port</strong>    Configure the dynamic MAC banding to port.<br>
										<br>	
										&nbsp;&nbsp;<strong> VLAN ID</strong>   Configure the banding to VLAN.<br>
										<br>
										&nbsp;&nbsp; <strong>MAC Address</strong>        Configure the banding to dynamic MAC.<br>
										<br>
										&nbsp;<span class="STYLE3">Notes：</span> The port learning disable if only one binding mac item enable.The most number of dynamic MAC Address one port can banding is 64.

</span>									  
										</td>
									</tr>
									</table>							       
								</td>
								</tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mac_limit">MAC Learning Limit</a></th>
								        </tr>
									    <tr>
									      <td height="148"><span class="STYLE1">&nbsp;&nbsp; <strong>Port</strong>    Set mac address learning limit on ports.<br>
										  	  <br>	
											  &nbsp;&nbsp;<strong> Max Secure Addr</strong>   Set max values on interfaces study.<br>
											  <br>
											  &nbsp;&nbsp; <strong>Current Addr</strong>        Current number of study .<br>
										  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="vlan_fwd">VLAN Configuration</a></th>
								        </tr>
									    <tr>
										  <td height="196"><span class="STYLE1">&nbsp;&nbsp;&nbsp;A local area network (LAN) can be divided into several logical LANs. Each logical LAN is a broadcast domain,which is called a virtual LAN (VLAN).<br>
										  <br>
										  &nbsp;&nbsp; <strong>VLAN ID</strong> ID of VLAN.Can batch create or delete multiple continuous vlans,available - links VLAN ID. For example, "1-3" means vlan 1, vlan 2 and vlan 3. <br>
										  <br>
										  &nbsp;&nbsp; <strong>Name</strong>    Named VLAN and the default is 'VLANxxxx'.<br>
										  
										  <br>
										  &nbsp;<span class="STYLE3">Notes：</span>Administrator can configure up to 100 vlans at a time. </span>										  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
							   	 <td><table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
                                   <tr height="30">
                                     <th align="left" colspan="2">&nbsp;<a name="vlan_port">Port-Based VLAN</a></th>
                                   </tr>
                                   <tr>
                                     <td height="218"><span class="STYLE1">&nbsp;&nbsp; <strong>Port Range</strong> The only way to choose port you want to configure is to check the port in checkbox. <br>
                                           <br>
                                       &nbsp;&nbsp; <strong>Link-type</strong> Configure the port mode，the default value is access.<br>
                                       <br>
                                       &nbsp;&nbsp; <strong>Ingress Filter</strong> Enable or disable ingress filter.The default is disable. <br>
                                       <br>
                                       &nbsp;&nbsp; <strong>Allowed VLAN ID</strong> To configure allowed vlan-id for trunk port. <br>
                                       <br>
                                       &nbsp;&nbsp; <strong>Default VLAN ID</strong> To configure the default-vlan-id of port. The default-vlan-id must be 1 for trunk port. <br>
                                       <br>
                                       &nbsp;<span class="STYLE3">Notes：</span>To modify link-type first, when you want to configure Allowed VLAN and Default VLAN. </span> </td>
                                   </tr>
                                 </table></td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="vlan_mac">MAC-Based VLAN</a></th>
								        </tr>
									    <tr>
									      <td height="120"><span class="STYLE1">&nbsp;&nbsp; <strong>Rule ID</strong>      The rule-number of mac vlan is in the range of 1-999.<br>
										  <br>&nbsp;&nbsp; <strong>MAC address</strong>   To input a mac address. <br>
										  <br>&nbsp;&nbsp; <strong>VID</strong>         To configure vlan id that the mac address will belong to. </span>								  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="vlan_subnet">IP Subnet VLAN</a></th>
								        </tr>
									    <tr>
									      <td height="131"><span class="STYLE1">&nbsp;&nbsp;<strong> Rule ID</strong>       The rule-number of subnet vlan is in the range of 1000-1999.<br>
										  <br>&nbsp;&nbsp; <strong>IP address</strong>       To input a subnet. <br>
										  <br>&nbsp;&nbsp; <strong>VID</strong>           To configure vlan id that the subnet will belong to.</span>									  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="vlan_protocol">Protocol-Based VLAN </a></th>
								        </tr>
									    <tr>
									      <td height="158"><span class="STYLE1">&nbsp;&nbsp;<strong> Rule ID</strong>       The rule-number of protocol vlan is in the range of 2000-2099.<br>
											  <br>&nbsp;&nbsp; <strong>Protocol type</strong>      To input a protocol number, which is in the range of 0-65535.<br>
											  <br>&nbsp;&nbsp; <strong>ENCAP</strong>        The available value is ethv2,nosnapllc and snapllc. <br>
											  <br>&nbsp;&nbsp; <strong>VID</strong>       To configure vlan id that the protocol will belong to.
										  </span></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="vlan_classport">VLAN Rule Configuration</a></th>
								        </tr>
									    <tr>
									      <td height="91"><span class="STYLE1">&nbsp;&nbsp; <strong>Port</strong>         The choice to the configuration of the port.<br>
										  <br>&nbsp;&nbsp; <strong>MAC-Based VLAN</strong>    Corresponding to the MAC port, based on the VLAN.<br>
										  <br>&nbsp;&nbsp; <strong>Subnet VLAN</strong>   Corresponding port can make based on IP subnets VLAN.<br>
										  <br>&nbsp;&nbsp; <strong>Protocol VLAN</strong>    Corresponding port can make based on Ethernet protocol VLAN.(need Rule ID)。<br>
										  </span></td>
									    </tr>
								      </table>							       </td>
							   </tr>


							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="lacp_static">Static LACP Aggregation group</a></th>
								        </tr>
									    <tr>
									      <td height="159"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Link aggregation can aggregate multiple Ethernet ports together to form a logical aggregation group. To upper layer entities,all the physical links in an aggregation group are a single logical link. <br>
										  <br>&nbsp;&nbsp; <strong>Port list</strong>     Configure a port list which will be static-link-aggregation group member.  <br>
										  <br>&nbsp;&nbsp; <strong>Create link aggregation</strong>   To create link aggregation group ID. <br>
										  <br>&nbsp;<span class="STYLE3">Notes：</span>Link aggregation table could display and delete the aggregation group. </span>								  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="lacp_dynamic">LACP Aggregation Group</a></th>
								        </tr>
									    <tr>
									      <td height="175"><span class="STYLE1">&nbsp;&nbsp;&nbsp;The member port in one LACP Aggregation Group must be the same speed, duplen and vlan. <br>
										  <br>&nbsp;&nbsp; <strong>LACP</strong>        To configure the LACP mode, LACP ID of port. <br>
										  <br>&nbsp;&nbsp; <strong>LACP table</strong>    display and delete the LACP group. <br>
										  <br>&nbsp;&nbsp; <strong>Aggregation group information</strong>       Display the configuration of dynamic aggregation group, like the type of Load-balance.</span><br>
										  <br><span class="STYLE1"><span class="STYLE3">&nbsp;Notes：</span>LACP Aggregation Group list can view and delete relevant information.</span></td>
									    </tr>
								      </table>							       </td>
							   </tr><tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="lacp_ports">Port Information</a></th>
								        </tr>
									    <tr>
									      <td height="72"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Display the member information in the Link-aggregation group.</span><br>										  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mstpbasic">MSTP Basic Information</a></th>
								        </tr>
									    <tr>
										  <td height="118"><span class="STYLE1">&nbsp;&nbsp;&nbsp;The Spanning Tree Protocol (STP) and the Rapid Spanning Tree Protocol (RSTP) are link-layer management protocols and are mainly applied to LANs to prevent loops. STP blocks redundant links and trims a network into a tree topology free from loops. RSTP enhances STP. It provides fast transition of interfaces status to speed up network convergence. STP and RSTP prevent broadcast storms caused by loops and provide backup links for data forwarding. The Multiple Spanning Tree Protocol (MSTP) is developed based on STP and RSTP. MSTP divides a network into multiple regions. Based on VLAN tags, each region has several spanning trees that are independent of each other. As a result, the entire network is trimmed to a tree topology that is free from loops. Broadcast storms are thus prevented on the network. 
										  <br>&nbsp;&nbsp; Display the MSTP region information, instance-VLAN mapping, port-VLAN mapping.</span>								  </td>
									    </tr>
								      </table>							       </td>
							   </tr><tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mstpinfor">STP Information</a></th>
								        </tr>
									    <tr>
									      <td height="50"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Display the basic bridge information and port information.The default value is enable.</span><br></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mstpinstance">Instance Information</a></th>
								        </tr>
									    <tr>
									      <td height="57"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Display the Instance bridge information and port instance ID.</span><br></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mstpcfg">Spanning tree configuration</a></th>
								        </tr>
									    <tr>
									      <td height="158"><span class="STYLE1">&nbsp;&nbsp; <strong>MSTP enable</strong>      Enable/disable MSTP.<br>
										  <br>&nbsp;&nbsp; <strong>Instance configuration</strong>        Configure the VLAN and instance mapping relationships.<br>
										  <br>&nbsp;&nbsp; <strong> MST Configuration</strong>       Configure the MSTP global parameters.<br>
										  <br>&nbsp;&nbsp; <strong>Instance-VLAN mapping</strong>  Display the instance and VLAN mapping relationships.</span>									  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="mstpportcfg">Port configuration</a></th>
								        </tr>
									    <tr>
									      <td height="50"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Configure the MSTP correlation property, includes path cost, priority, portfast, Root guard etc.</span><br></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="eaps">EAPS management</a></th>
								        </tr>
									    <tr>
									      <td height="111"><span class="STYLE1">&nbsp;&nbsp;&nbsp;EAPS is link-layer management protocols and are mainly applied to LANs to prevent loops. EAPS prevent broadcast storms caused by loops and provide backup links for data forwarding.  The parameter of EAPS, include Ring ID, Mode, compatible with extreme, master port, subordinate port, Control VLAN, protocol VLAN, hello time, etc. <br>
                                           
                                          <br>&nbsp;<span class="STYLE3">Notes：</span>Not allow to enable a EAPS ring, but to complete all configuration. Must to disable ring when you want to modify some configuration of EAPS ring. </span>										  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="l3int">L3 Interface Configuration</a></th>
								        </tr>
									    <tr>
									      <td height="235"><span class="STYLE1">&nbsp;Configuration equipment related attributes three layers:
											 <br>&nbsp;&nbsp; <strong>VLAN ID</strong>         Set three layer interface corresponding VLAN ID.<br>
											 <br> &nbsp;&nbsp; <strong>IP Address</strong>          Set three layer interface corresponding IP address.<br>
											 <br> &nbsp;&nbsp; <strong>ARP Age Time</strong>     Set three layer interface ARP aging time.<br>
											 <br> &nbsp;&nbsp; <strong>State </strong>         Set three layer interface management state UP/DOWN.<br>
											 <br> &nbsp;&nbsp; <strong>Sub_IP Address</strong>        Three layer interface set sub_IP address.<br>
										  <br> 
										  <span class="STYLE3">&nbsp;Notes：</span>Need to create three layer interface and the configuration of the Lord IP address rear can configuration from IP address. </span>                                          </td>
									    </tr>
								      </table>							       </td>
							   </tr>

							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="staticarp">Static ARP</a></th>
								        </tr>
									    <tr>
									      <td height="152"><span class="STYLE1">&nbsp;Static ARP is refers to the IP address and MAC address fixed the mapping relationship will not be aging and dynamic deleted.<br>
										  <br>&nbsp;&nbsp; <strong>IP Address</strong>     A table in the corresponding ARP IP address.<br>
										  <br>&nbsp;&nbsp; <strong>MAC Address</strong>  A table in the corresponding ARP MAC address.<br>
										  <br>&nbsp;<span class="STYLE3">Notes：</span>Empty ARP table only delete a dynamic.</span>								  </td>
									    </tr>
								      </table>							       </td>
							   </tr>

							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="route">Routing Management </a></th>
								        </tr>
									    <tr>
									      <td height="125"><span class="STYLE1">&nbsp;&nbsp; <strong>Destination IP</strong>    The purpose of the static routing network segment.<br>
											<br>
											&nbsp;&nbsp; <strong>Next Hop</strong>  Static routing next jump address.  <br>
											<br>
										    <span class="STYLE3">&nbsp;Notes：</span>The router management can only add and remove static routing, but can check equipment all routing information.</span>										  </td>
									    </tr>
								      </table>							       </td>
							   </tr>

							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="ip">IP Address</a></th>
								        </tr>
									    <tr>
									      <td height="87"><span class="STYLE1">&nbsp;Set VLAN1.1 address three layer interface.<br>
										  <br>&nbsp;<span class="STYLE3">Notes：</span>Modify IP address can lead to break the current connection.</span>								  </td>
									    </tr>
								      </table>							       </td>
							   </tr>

							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="user">User management</a></th>
								        </tr>
									    <tr>
									      <td height="86"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Modify the current password.<br>
										  <br>&nbsp;<span class="STYLE3">Notes：</span>To modify the password of current logging user. </span>									  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="reset">Reboot</a></th>
								        </tr>
									    <tr>
									      <td height="93"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Reboot system checking a checkbox to reboot system with saved configuration. </span>								  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="snmp">SNMP Configuration</a></th>
								        </tr>
									    <tr>
										  <td height="237"><span class="STYLE1">&nbsp;&nbsp;&nbsp;The simple network management protocol (SNMP) is used for ensuring the transmission of the management information between any two network nodes. In this way, network administrators can easily retrieve and modify the information about any node on the network. In the meantime, they can locate faults promptly and implement the fault diagnosis, capacity planning and report generating.<br>
										  <br>&nbsp;&nbsp; <strong>Group name</strong>         Configure the SNMP community group name within the right of read-write or read only.<br>
										  <br>&nbsp;&nbsp; <strong>Administrator</strong>     add the contact information of network administrator.<br>
										  <br>&nbsp;&nbsp; <strong>SNMP traps</strong>    Enable/disable SNMP traps function.<br>
										  <br>&nbsp;&nbsp; <strong>Traps host</strong>      Configure IP address of trap host and corresponds to group community.<br>
										  <br>&nbsp;&nbsp; <strong>Device location</strong>        Configure the information of device location.</span><br>
										  <br>&nbsp;<span class="STYLE1"><span class="STYLE3">Notes：</span>The operation, to show and to delete SNMP Community or Trap table, is available. </span></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="sntp">System time</a></th>
								        </tr>
									    <tr>
									      <td height="73"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Configure the system time,time zone,SNTP preferred server,SNTP inferior server,etc.</span><br>
									      &nbsp;<span class="STYLE3">Notes:</span>If system not support RTC,can't save system time.</span>									  </td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="log">Log infomation</a></th>
								        </tr>
									    <tr>
									      <td height="71"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Display the current log information. </span><br></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="ping">Ping Diagnostics</a></th>
								        </tr>
									    <tr>
									      <td height="77"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Ping are used to test network connectivity.</span><br></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="tracert">Tracert</a></th>
								        </tr>
									    <tr>
									      <td height="72"><span class="STYLE1">&nbsp;&nbsp;&nbsp;Traceroute are used to test destination IP address information on the path.</span><br></td>
									    </tr>
								      </table>							       </td>
							   </tr>
							   <tr>
								   	<td> 		   	
							   	      <table id="sys_info" width="100%" border="0" align="center" cellpadding="0" cellspacing="1" class="cword09">
								        <tr height="30">
								          <th align="left" colspan="2">&nbsp;<a name="upgrade">File management</a></th>
								        </tr>
									    <tr>
									      <td height="159"><span class="STYLE1">&nbsp;&nbsp; <strong>Current version</strong>           Display the current version and backup the image file.<br>
										  <br>&nbsp;&nbsp; <strong> Software</strong>           Update the image file through WEB.<br>
										  <br>&nbsp;&nbsp; <strong>Update configure</strong>    Upload the configure file into device through web.<br> 
										  <br>&nbsp;&nbsp; <strong>Download configure file</strong>  Download the saved configure file from device through web.<br>
										  <br>&nbsp;&nbsp; <strong>Reply factory configuration</strong>      All configuration information will be returned to the factory situation, device will restart.<br>
										  <br>&nbsp;&nbsp; <strong>Save all configuration</strong>       Save all of your page configuration information.</span></td>
									    </tr>
								      </table>							       </td>
							   </tr>
				    </table>
					<!-- 具体内容 end -->

				</td>
            </tr>
        </td></tr>
        
    </table>
</body>
</html>
