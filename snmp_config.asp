<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>

<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"SNMP管理");
</script></title>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script src="js/alpinejs.min.js"  ></script>
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<% var errorcode, snmpenable, snmpversion, snmptrustip; getSnmpConfigInfo(); %>

<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<%  var authmode; checkCurMode(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 <link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"GMRP");</script></title>
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
function getUrlParamTab() {
        let urlSearch = window.location.search
        const search = new URLSearchParams(urlSearch)
        const params = Object.fromEntries(search.entries())
        try {
          if (params.tab) return params.tab
        } catch { }
        return 'tab1'
      }
</script>


<script language="JavaScript">
function changebgcolor(){
	var tab = document.all.table1;
	var len = tab.rows.length ;
	for (var i=0; i<len; i++)
	{
		var lencol = tab.rows[i].cells.length
		for (var j=0; j<lencol; j++)
		{
			if (j % 2 == 1){
				tab.rows[i].cells[j].className = "all_tables all_tables2" ;
			}
		else{
				tab.rows[i].cells[j].className = "all_tables all_tables5" ;
			}

		}

	}
}
function changebgcolor1(){
	var tab = document.all.table2;
	var len = tab.rows.length ;
	for (var i=0; i<len; i++)
	{
		var lencol = tab.rows[i].cells.length
		for (var j=0; j<lencol; j++)
		{
			if (j % 2 == 1){
				tab.rows[i].cells[j].className = "all_tables all_tables2" ;
			}
		else{
				tab.rows[i].cells[j].className = "all_tables all_tables5" ;
			}

		}

	}
}

function changebgcolor_name(value){

	var tab = document.getElementById(value);
	 var len = tab.rows.length ;
	 for (var i=0; i<len; i++)
	 {
	   var lencol = tab.rows[i].cells.length
	   for (var j=0; j<lencol; j++)
		{
		   if (j % 2 == 1){
   
			//    tab.rows[i].cells[j].className = "all_tables" ;
			 }
	   else{
			//    tab.rows[i].cells[j].className = "all_tables" ;
		   }
   
		}
	}
}
function IpCheck(ip_addr)
{
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])$/;
	if(myRE.test(ip_addr))
	{
        return true;
	}
	else
	{
		return false;
	}
}
//[a-z A-Z 0-9 ] 字线和数字
function check_ingress_str_format(str)
{
    var reg1 = /^([a-zA-Z]|\d)+$/;

    if(!reg1.exec(str))
        return false;

    return true;
}
function isValidString(str)
{
	var validc = "/'%`\"\\><";
	var a,c;
	for(i = 0 ; i < validc.length; i ++)
	{
		c = validc.substring(i, i + 1);
		if(str.indexOf(c) > -1)
		{
			return true;
		}
	}
	return false;
}
async function messageCheck(v)
{
    var hid=document.webForm;
	var dplen;
	var dpvalue;
	var reauthn;
	var reauthpd;

	//var name = prompt("操作认证-用户名", "");
	document.getElementById("reauthn").value = await userNamePrompt();
	// var pwd = prompt("操作认证-密码", "");
	// document.getElementById("reauthpd").value = pwd;
	document.getElementById("reauthpd").value = await testThePrompt();
	reauthn = document.getElementById("reauthn").value;
	reauthpd = document.getElementById("reauthpd").value;

	dplen = parseInt(reauthn.length/2);
	dpvalue = reauthn;
	reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

	dplen = parseInt(reauthpd.length/2);
	dpvalue = reauthpd;
	document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
	document.getElementById("reauthn").value = "XD!Zb3BjRCCoPExe";


    if(v==1)
    {
    	hid.flag.value=1;
		//hid.tabName.value = "tab2";
    	hid.submit();
		return true;
    }
    else if(v==2)
    {
	    hid.flag.value=2;
		//hid.tabName.value = "tab2";
			hid.submit();
			return true;
    }
    else if (v==3)
    {
    	if (0 != hid.trustIp.value.length )
		{
			if (true != IpCheck(hid.trustIp.value))
			{
				alert(putmsg(<% write(lang); %>,"Illegal IP address!"));
				return false;
			}
			if ("0.0.0.0" == hid.trustIp.value)
				hid.trustIp.value = "";
		}

		hid.flag.value=3;
		//hid.tabName.value = "tab2";
		hid.submit();
		return true;
    }

    return ;

}
// function DelSnmpCom(value)
// {
//     var hid=document.webForm;
//     	hid.comname.value=value;
// 		hid.flag.value=2;
// 		hid.submit();
// 		return true;
// }
function DelSnmpTrap(v1,v2)
{
    var hid=document.webForm;
		hid.flag.value=6;
		hid.trapiphid.value=v1;
		hid.trapnamehid.value=v2;
		hid.submit();
		return true;
}
function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm?name=snmp";
	tf.submit();
}
function refreshpage()
{
  location.href='snmp_config.asp?ltime='+<% write(lltime); %>;
}
function display()
{
 	var hid=document.webForm;

	tmp = "<% write(snmptrustip); %>";
	hid.trustIp.value = tmp.split(",")[0];
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

function writebutton2(priv,lang,key,bclass,btype,bname,benable,bfunc)
{
	var outputstr ="";
	if((priv == 1)||(priv == 5))
	  outputstr="<input class="+bclass+" id='"+bname+"' "+benable+" name='"+bname+"' type='"+btype+"' value='"+putmsg(lang,key)+"' onClick='return "+bfunc+";'>";
	else
		outputstr="&nbsp;";
	document.write(outputstr);

}
function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

var userPortStaList=[<%snmp_userSetShow();%>];

function userDelmac(i)
{

	var hid = document.formaa;
	
	hid.snmpuserName.value = userPortStaList[5*i];
	hid.snmpusergroupName.value = userPortStaList[5*i+1];

	hid.action="/goform/snmp_userSet";
	hid.tabName.value='tab4'
	hid.submit();
	return 0;

}

function userWriteLines()
{
	var j = 0;
	for(var i=0;i<userPortStaList.length/5;i++)
	{
		document.write("<tr  class='tables_all'>");
		document.write("<td  class='inputsyslog1'>"+userPortStaList[j]+"</td>");

		j++
		document.write("<td  class='inputsyslog1'>"+userPortStaList[j]+"</td>");
		j++;
		document.write("<td  class='inputsyslog1'>"+userPortStaList[j]+"</td>");
		j++;
		document.write("<td  class='inputsyslog1'>"+userPortStaList[j]+"</td>");
		j++;
		document.write("<td  class='inputsyslog1'>"+userPortStaList[j]+"</td>");
		j++;
		document.write("<td  class='inputsyslog1'>");

		if (<% write(authmode); %> == 1)
			document.write("      <input type='button' name='button"+i+"' id='button"+i+"' class='botton_under_line' value='删除' onclick='userDelmac("+i+")'  />");
			document.write("</td>");
			document.write("</tr>");

	}
}

/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true;  
			 addToPortRange(i);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRange(i);
			}
             
        }
    } 
    
}

function checkup()
{
}

function checking2()
{
	var tf=document.vlan_port;
	//var i,j;
	var auth_pro = document.getElementById("snmpauthProtocol").value;
	var priv_pro = document.getElementById("snmpprivProtocol").value;
	var auth_passwd = document.getElementById("snmpauthPassword").value;
	var cf_auth_passwd = document.getElementById("cfsnmpauthPassword").value;
	var priv_passwd = document.getElementById("snmpprivPassword").value;
	var cf_priv_passwd = document.getElementById("cfsnmpprivPassword").value;

	if ((auth_pro != 0) && ((auth_passwd == "") || (auth_passwd != cf_auth_passwd)))
	{
		alert("Inconsistent authentication password!");
		return false;
	}

	if ((priv_pro != 0 ) && ((priv_passwd == "") || (priv_passwd != cf_priv_passwd)))
	{
		alert("Inconsistent encryption password!");
		return false;
	}

	if (auth_pro != 0 || priv_pro == 0){

	}
		tf.tabName.value='tab4'
		tf.pathName.value='snmp_config.asp'
		tf.submit();
}
function AddOption(portname){
	var selectObject = document.getElementById("port_range");
	var y=document.createElement('option');
	y.text=portname;
	y.value = portname;
	try
	{
		selectObject.add(y,null); // standards compliant
	}
	catch(ex)
	{
		selectObject.add(y); // IE only
	}
}
function checkname(field,err_blank,err_space,err_invalid,flag)
{
	if ((field.value.indexOf(" ") >=0)&&(flag == 1))
   {
		alert(err_space);
		field.focus();
		field.select();
		return false;
	}

	left_trim(field);
	right_trim(field);

	if ((field.value =="" | field.value == null)&&(flag == 1))
	{
		
		alert(err_blank);
		field.focus();
		field.select();
		return false;
	}


	if (checkOtherChar(field.value, err_invalid)==false)
	{
		field.focus();
		field.select();
		return false;
	}
	return true;
}

function checkSub()
{
	if(!tdOIDCheck(document.getElementById("snmpsubTree").value))
	{
		alert("OID 错误");
		return;
	}
	if(document.getElementById("snmpsubTree").value.length > 128 ||document.getElementById("snmpsubTree").value.length == 0)
	{
		alert("OID 不能为空，长度也不能超过128");
		return;
	}
	if(!checkname(document.getElementById("snmpuserName"),"用户名为空","用户名错误","用户名错误",1))
	//if(!tdCheckASCIICode(document.getElementById("snmpuserName").value))
	{
		//alert("user Name 错误");
		return;
	}
	
	if(document.getElementById("snmpuserName").value.length > 32 || document.getElementById("snmpuserName").value.length == 0)
	{
		alert("用户名不能为空，长度也不能超过32");
		return;
	}

	document.vlan_port.submit();
}
function refreshpage_user()
{
  location.href='snmp_config.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('vlan_port',<% write(lang); %>);
}


//========================================================snmp grop====================
var groupPortStaList=[<%snmp_groupSetShow();%>];

function groupDelmac(i)
{
	var hid = document.snmpGroupDel;
	hid.snmpgroupName.value = groupPortStaList[5*i];
	hid.snmpsecLevel.value = groupPortStaList[5*i+4];
	hid.action="/goform/snmp_groupSet";
	hid.tabName.value='tab2'
	hid.submit();
	
	return 0;
}

function groupWriteLines()
{
	var j = 0;
	for(var i=0;i<groupPortStaList.length/5;i++)
	{
		document.write(" <tr  class='tables_all'>");
		document.write("    <td class='inputsyslog1'>"+groupPortStaList[j]+"</td>");
		j++
		document.write("    <td  class='inputsyslog1'>"+groupPortStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>"+groupPortStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>"+groupPortStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>"+groupPortStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>");
		if (<% write(authmode); %> == 1)
			document.write("      <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick='groupDelmac("+i+")'  />");
		document.write("      </td>");
		document.write("  </tr>");
	}
}
 
function checking21()
{
	var tf=document.snmpGroup;
	tf.tabName.value='tab2'
	tf.submit();
}

function changebgcolor31(){
 var tab = document.all.table31;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].className = "all_tables all_tables2" ;
		  }
	else{
			tab.rows[i].cells[j].className = "all_tables all_tables5" ;
		}

     }

  }
}



</script>


</head>
<body  onload="display()" x-data="{active:'tab1'}"  x-init="active=getUrlParamTab()"><br>

<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>

<form  name="webForm" method="post" action="/goform/setSnmpGlobalCfg" class="formContain">
<input type="hidden" name="flag">
<input type="hidden" name="comname">
<input type="hidden" name="trapiphid">
<input type="hidden" name="trapnamehid">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" id="reauthn" name="reauthn" value="">
<input type="hidden" id="reauthpd" name="reauthpd" value="">
<input type="hidden" id="tabName" name="tabName" >
<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="30px" ><font size="5" color="#404040"><div class="bot">SNMP设置</div></font></td></tr>
</table>

<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table1" class="tablebord">

    <tr height="25">
		<td width="30%" align="center" class="crons">&nbsp;SNMP使能:</td>

		<td width="40%" align="left" class="crons">&nbsp;
			<input type="hidden" name="snmpenable" value=<% write(snmpenable); %>>
			<input type="radio" name="trap" value="1" <% if (snmpenable==1) write("checked"); %>>开启
			<input type="radio" name="trap" value="0" <% if (snmpenable==0) write("checked"); %>>关闭
		</td>
		<td  align="center" class="crons">&nbsp;
			<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"应  用","buttons_apply","button","modify4","messageCheck(1)");</script>
		</td>
    </tr>

    <tr height="25">
		<td align="center" class="crons"><span class="td25">&nbsp;SNMP版本:</span></td>
		<td align="left" class="crons">&nbsp;
			<select	name="snmpver" size="1">
				<option	value="1" <% if (1 == snmpversion) write("selected"); %> >v1</option>
				<option	value="2" <% if (2 == snmpversion) write("selected"); %> >v2c</option>
				<option	value="3" <% if (3 == snmpversion) write("selected"); %> >v3</option>
				<option	value="4" <% if (4 == snmpversion) write("selected"); %> >all</option>
			</select>
		</td>
		<td  align="center" class="crons">&nbsp;
			<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"应  用","buttons_apply","button","modify5","messageCheck(2)");</script>
		</td>
    </tr>

	<tr height="25">
		<td align="center" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"管理站IP");</script>:</td>
		<td align="left" class="crons">&nbsp;
			<input type="text" name="trustIp" id="trustIp" class="input_board3">
		</td>

		<td  align="center" class="crons">&nbsp;
			<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"应  用","buttons_apply","button","modify5","messageCheck(3)");</script>
		</td>
	</tr>

</table>
</form>
<div style="margin-top: 15px;">
	<ul class="tabmenu">
	  <li  id="tab1" :class="active==='tab1'?'tab':'tab-disable'">
		<a herf="#" x-on:click="active='tab1'">视图</a>
	  </li>
	  <li  id="tab2" :class="active==='tab2'?'tab':'tab-disable'">
		<a herf="#" x-on:click="active='tab2'"> 群组</a>
	  </li>
	  <li  id="tab3" :class="active==='tab3'?'tab':'tab-disable'" >
		<a herf="#" x-on:click="active='tab3'">团体名称</a>
	  </li>
	  <li  id="tab3" :class="active==='tab4'?'tab':'tab-disable'" >
		<a herf="#" x-on:click="active='tab4'">用户名</a>
	  </li>
	</ul>
  </div>
<script>
changebgcolor();
// changebgcolor1();
<% if (errorcode=="3") { write("alert(putmsg(");write(lang);write(",'不具备执行该操作的权限!'));");  } %>
// <% if (errorcode!="") { if (errorcode!="3") { write_errorcode(errorcode); } } %>
</script>

<script language="JavaScript" type="text/JavaScript">
<% var Allipaddr,errorcode; %>


//[a-z A-Z 0-9 ] 字线和数字
function check_ingress_str_format(str) 
{
	var reg1 = /^([a-zA-Z]|\d)+$/; 
	
	if(!reg1.exec(str))
		return false;

	return true;
}


function isValidString(str)
{
	var validc = "/'%`\"\\><";
	var a,c;
	for(i = 0 ; i < validc.length; i ++)
	{
		c = validc.substring(i, i + 1);
		if(str.indexOf(c) > -1)
		{
			return true;
		}
	}
	return false;
}

/* by zjx  11-6-8 */
function IpCheck(ip_addr)
{
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])$/;
	if(myRE.test(ip_addr))
	{
		return true;
	}
	else
	{
		return false;
	}
}

function messageCheckCommunity(v)
{
	var hid=document.webCommunity;
	if(v==1)
	{
		var vie = hid.snmp_view.value;
		if(check_ingress_str_format(hid.community.value)!=true)
		{
			alert(putmsg(<% write(lang); %>,"团体名只能为字母和数字!"));
			return false;
		}
		if(hid.community.value.length>32)
		{
			alert(putmsg(<% write(lang); %>,"团体名长度不能大于32!"));
			return false;
		}
			else if(hid.community.value.length==0)
		{
			alert(putmsg(<% write(lang); %>,"团体名不能为空!"));
			return false;
		}
		hid.flag.value=1;
		hid.tabName.value="tab3";
		hid.submit();
		return true;
	}
	else if(v==2)
	{
		if(hid.community.value.length==0)
		{
			alert(putmsg(<% write(lang); %>,"团体名不能为空!"));
			return false;
		}
		hid.flag.value=2;
		hid.tabName.value="tab3";
		hid.submit();
		return true;
	}
	else if(v==3)
	{
		if(hid.admininfo.value.length>=128)
		{
			alert(putmsg(<% write(lang); %>,"管理员标识长度小于128!"));
			return false;
		}
		else if(isValidString(hid.admininfo.value))
		{
			alert(putmsg(<% write(lang); %>,"输入中发现非法字符!"));
			return false;
		}
		
		hid.flag.value=3;
		hid.tabName.value="tab3";
		hid.submit();
		return true;
	}
	else if(v==4)
	{
		hid.flag.value=4;
		hid.tabName="tab3";
		hid.submit();
		return true;
	}
	else if(v==5)
	{
		if(hid.trapsIp.value.length==0||hid.username.value.length==0)
				{
					alert(putmsg(<% write(lang); %>,"主机地址和团体名不能为空!"));
					return false;
				}
		else  if(check_ingress_str_format(hid.username.value)!=true)
					{
						alert(putmsg(<% write(lang); %>,"团体名只能为字母和数字!"));
						return false;
					}
		if(eval(document.webCommunity.trapval.value)!=1)
				{
					alert(putmsg(<% write(lang); %>,"此配置只有在traps功能打开的情况下有效!"));
					return false;
				}
		if(IpCheck(hid.trapsIp.value)!=true)
				{
					alert(putmsg(<% write(lang); %>,"IP地址的格式不合法!"));
					return false;
				}
				
		if(hid.username.value.length>32)
		{
			alert(putmsg(<% write(lang); %>,"团体名长度不能大于32!"));
			return false;
		}
				
		hid.flag.value=5;
		hid.tabName="tab3";
			hid.submit();
			return true;
	}
	else if(v==6)
	{
		if(hid.trapsIp.value.length==0)
		{
			alert(putmsg(<% write(lang); %>,"主机地址不能为空!"));
			return false;
		}
		if(eval(document.webCommunity.trapval.value)!=1)
		{
			alert(putmsg(<% write(lang); %>,"此配置只有在traps功能打开的情况下有效!"));
			return false;
		}
		hid.flag.value=6;
		hid.tabName="tab3";
		hid.submit();
		return true;
	}
	else if(v==7)
	{
		if(hid.devLocation.value.length==0)
		{
			alert(putmsg(<% write(lang); %>,"设备位置不能为空!"));
			return false;
		}
		else if(hid.devLocation.value.length > 256)
		{
			alert(putmsg(<% write(lang); %>,"设备位置长度不能大于256!"));
			return false;
		}
		else if(isValidString(hid.devLocation.value))
		{
			alert(putmsg(<% write(lang); %>,"输入中发现非法字符!"));
			return false;
		}
		hid.flag.value=7;
		hid.tabName="tab3";
		hid.submit();
		return true;
	}
	else if(v==8)
	{
		if(hid.devLocation.value.length==0)
		{
			alert(putmsg(<% write(lang); %>,"设备位置不能为空!"));
			return false;
		}
		else if(hid.devLocation.value.length>256)
		{
			alert(putmsg(<% write(lang); %>,"设备位置长度不能大于256!"));
			return false;
		}
		hid.flag.value=8;
		hid.tabName="tab3";
		hid.submit();
		return true;
	}

	return ;
	
}

function changebgcolor51(){
	var tab = document.all.table51;
	var len = tab.rows.length ;
	for (var i=0; i<len; i++)
	{
	var lencol = tab.rows[i].cells.length
	for (var j=0; j<lencol; j++)
		{
		if (j % 2 == 1){
			tab.rows[i].cells[j].className = "all_tables all_tables2" ;
			}
	else{
			tab.rows[i].cells[j].className = "all_tables all_tables5 btn3" ;
		}

		}

	}
}

function changebgcolor52(){
	var tab = document.all.table52;
	var len = tab.rows.length ;
	for (var i=0; i<len; i++)
	{
	var lencol = tab.rows[i].cells.length
	for (var j=0; j<lencol; j++)
		{
			tab.rows[0].cells[j].className = "all_tables all_tables3" ;
			if (j % 2 == 1){

				tab.rows[i].cells[j].className = "all_tables btn2" ;
			}
			else{
				tab.rows[i].cells[j].className = "all_tables btn2" ;
			}

		}

	}
}

function DelSnmpCom(value)
{
	var hid=document.webCommunity;
	hid.comname.value=value;
	hid.flag.value=2;
	hid.tabName.value="tab3";
	hid.submit();
	return true;
}

</script>


<script>
checktop(<% write(lang); %>);
</script>

<script language="JavaScript">

var viewPortStaList=[<%snmp_viewSetShow();%>];

function delView(i)
{
	var hid = document.snmp_view_del;
	hid.snmpviewName.value = viewPortStaList[3*i];
	hid.snmpsubTree.value = viewPortStaList[3*i+1];
	hid.action="/goform/snmp_viewSet";
	hid.submit();
	return 0;

}

function viewWriteLines()
{
	var j = 0;
	for(var i=0;i<viewPortStaList.length/3;i++)
	{
		document.write(" <tr  class='tables_all'>");
		document.write("    <td class='inputsyslog1'>"+viewPortStaList[j]+"</td>");
		j++
		document.write("    <td  class='inputsyslog1'>"+viewPortStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>"+viewPortStaList[j]+"</td>");
		j++;
		document.write("    <td  class='inputsyslog1'>");
		if (<% write(authmode); %> == 1)
			document.write("      <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick='delView("+i+")'  />");
		document.write("      </td>");
		document.write("  </tr>");
	}
}
	
function changebgcolor61(){
	var tab = document.all.table61;
	var len = tab.rows.length ;
	for (var i=0; i<len; i++)
	{
	var lencol = tab.rows[i].cells.length
	for (var j=0; j<lencol; j++)
		{
		if (j % 2 == 1){
			tab.rows[i].cells[j].className = "all_tables all_tables2" ;
			}
	else{
			tab.rows[i].cells[j].className = "all_tables all_tables5" ;
		}

		}

	}
}

function checking22()
{
	var tf=document.snmp_view;
	tf.submit();
}

</script>

<form  name="vlan_port" method="post" action="/goform/snmp_userSet" class="formContain" x-show="active==='tab4'">
<input name="del_flag" type="hidden" class="input_x"  value="0">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="snmpDeluserID" id="snmpDeluserID"/>
<input type="hidden" name="snmpDeluserOid" id="snmpDeluserOid">
<input type="hidden" name="tabName" id="tabName">
<input type="hidden" name="pathName" value="snmp_config">

<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="20px" ><font size="5" color="#404040"><div class="bot"> 用户名设置</div></font></td></tr>
</table>
<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" id="table2" class="tablebord">
<tbody>
	<tr height="25" class="table_maintable">
		<td width="10%" class="td25">用户名:</td>
		<td colspan="5"  class="crons"><input type="text" name="snmpuserName"  id="snmpuserName"  size="20" maxlength="31">(范围：1~31个字符) </td>
	</tr>

	<tr height="25">
		<td width="10%" class="td25">群组:</td>
		<td colspan="5" class="crons"><input type="text" name="snmpusergroupName"  id="snmpusergroupName" size="20" maxlength="32">(范围：1~32个字符) </td>
	</tr>

	<tr height="25" class="table_maintable">
		<td width="20%" class="td25">SNMP V3 加密:</td>
		<td colspan="5" class="crons"><span style="display:none"><input type="checkbox" name="snmpencrypt"  id="snmpencrypt" onclick="changeType()" value="ON" / >加密</span>&nbsp;</td>
	</tr>

	<tr height="25" class="table_maintable">
		<td width="10%" class="td25">鉴权方式:</td>
		<td width="15%" class="crons"><select NAME="snmpauthProtocol" id="snmpauthProtocol" SIZE="1">
			<option value="0">None</option>
			<option VALUE="1">MD5</option>
			<option VALUE="2">SHA</option>
			</select>
		</td>
		<td width="10%" class="td25">&nbsp;&nbsp;鉴权密码:</td>
		<td width="25%" class="crons"><input type="password" name="snmpauthPassword" id="snmpauthPassword" size="20" maxlength="16" /></td>
		<td width="10%" class="td25">&nbsp;&nbsp;确认鉴权密码:</td>
		<td width="25%" class="crons"><input type="password" name="cfsnmpauthPassword" id="cfsnmpauthPassword" size="20" maxlength="16" /></td>
	</tr>

	<tr height="25" class="table_maintable">
		<td width="10%" class="td25">加密方式:</td>
		<td width="15%" class="crons"><select name="snmpprivProtocol" id="snmpprivProtocol" size="1" >
			<option value="0">None</option>
			<option value="1">AES</option>
			<option value="2">DES</option>
		  	</select>
		</td>
		<td width="10%" class="td25">&nbsp;&nbsp;加密秘钥:</td>
		<td width="25%" class="crons"><input type="password" name="snmpprivPassword" id="snmpprivPassword" size="20" maxlength="10" /></td>
		<td width="10%" class="td25">&nbsp;&nbsp;确认加密秘钥:</td>
		<td width="25%" class="crons"><input type="password" name="cfsnmpprivPassword" id="cfsnmpprivPassword" size="20" maxlength="15" /></td>
	</tr>
  
	<tr height="25" class="table_maintable">
		<td colspan="6" class="crons" style="text-align: center;">
			<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"应  用","buttons_apply","button","modify","checking2()");</script>
		</td>
	</tr>
</table>

<br>
<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="20px" ><font size="5" color="#404040"><div class="bot">用户名列表</div></font></td></tr>
</table>

<table width="98%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table_port_vlan"  >
    <tr align="center" height=22>
      	<td width="13%"   nowrap class="all_tables_list"><span class="partition">用户名</span></td>
     	<td width="16%"   nowrap class="all_tables_list"><span class="partition">群组</span></td>
      	<td width="14%"   nowrap class="all_tables_list"><span class="partition">模式</span></td>
      	<td width="15%"   nowrap class="all_tables_list"><span class="td25 partition">鉴权方式</span></td>
      	<td width="15%"   nowrap class="all_tables_list"><span class="td25 partition">加密方式</span></td>
      	<td width="27%"   nowrap class="all_tables_list"><span class="partition">操作</span></td>
    </tr>
	<script language="javascript">
	userWriteLines();
	</script>
</table>
</form>

<form name="formaa" method="POST" action="">
<input name="snmpusergroupName" type="hidden" class="input_x"   >
<input name="snmpuserName" type="hidden" class="input_x"  >
<input name="del_flag" type="hidden" class="input_x"  value="1">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" id="tabName" name="tabName" >
</form>

<form  name="snmpGroup" method="post" action="/goform/snmp_groupSet" class="formContain" x-show="active==='tab2'">
	<input name="del_flag" type="hidden" class="input_x"  value="0">
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
	<input type="hidden" name="lastts" value=<% write(serverts); %>>
	<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
	
	<input type="hidden" name="snmpDelgroupID" id="snmpDelgroupID"/>
	<input type="hidden" name="snmpDelgroupOid" id="snmpDelgroupOid">
	<input type="hidden" name="tabName" id="tabName">
	
	 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
		<tr><td height="30px" ><font size="5" color="#404040"><div class="bot">群组设置</div></font></td></tr>
	 </table>
	
	
	<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table31" class="tablebord">
	  <TBODY>
		<TR height=22>
		  <TD width="15%" valign="top"><div align="left">&nbsp;&nbsp;<span class="td25">群组名:</span></div></TD>
		  <TD width="35%"  colspan="3"><span class="crons">
			<input type="text" name="snmpgroupName" id="snmpgroupName" size="20" maxlength="32" />
			(范围：1~32个字符) </span></TD>
		 
		</TR>
		<TR height=22>
		  <TD width="15%" valign="top">&nbsp;&nbsp;<span class="td25">只读视图:</span></TD>
		  <TD width="35%" ><span class="crons">
			<input type="text" name="snmpreadView" id="snmpreadView" size="20" maxlength="32" />
			(范围：1~32个字符) </span></TD>
		  <TD width="15%" >&nbsp;&nbsp;<span class="td25">读写视图:</span></TD>
		  <TD width="35%" ><span class="crons">
			<input type="text" name="snmpwriteView" id="snmpwriteView" size="20" maxlength="32" />
			(范围：1~32个字符) </span></TD>
		</TR>
		<TR height=22>
		  <TD width="15%" valign="top">&nbsp;&nbsp;<span class="td25">告警视图:</span></TD>
		  <TD width="35%" ><span class="crons">
			<input type="text" name="snmpnotifyView" id="snmpnotifyView" size="20" maxlength="32" />
			(范围：1~32个字符) </span></TD>
		  <TD width="15%" >&nbsp;&nbsp;<span class="td25">安全等级:</span></TD>
		  <TD width="35%" ><span class="crons">
			<select name="snmpsecLevel" id="snmpsecLevel" size="1">
			  <option value="auth"  >Auth</option>
			  <option value="noauth"  >Noauth</option>
			  <option value="priv"  >Priv</option>
			</select>
		  </span></TD>
		</TR>
		
		<tr height="25">
		  <td  colspan="4" align="center" class="crons" style="text-align: center;">&nbsp;
			 <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"提  交","buttons_apply","button","modify","checking21()");</script>
		  </td>
	  </tr> 
	</TABLE>
	
	
	<br>
	 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
		<tr><td height="20px" ><font size="5" color="#404040"><div class="bot">群组列表</div></font></td></tr>
	 </table>
	
	
	<table width="98%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table_snmp_grop"  >
		<TR align="center" height=22>
		  <TD   nowrap class="all_tables_list"><span class="partition">群组名</span></TD>
		  <TD   nowrap class="all_tables_list"><span class="partition">只读视图</span></TD>
		  <TD   nowrap class="all_tables_list"><span class="td25 partition">读写视图</span></TD>
		  <TD   nowrap class="all_tables_list"><span class="td25 partition">告警视图</span></TD>
		  <TD   nowrap class="all_tables_list"><span class="td25 partition">安全等级</span></TD>
		  <TD   nowrap class="all_tables_list"><span class="partition">删除</span></TD>
		</TR>
	  <script language="javascript">
	  groupWriteLines();
	  </script>
	  </table>
</form>

<form name="snmpGroupDel" method="POST" action="">
<input name="snmpsecLevel" type="hidden" class="input_x"  >
<input name="snmpgroupType" type="hidden" class="input_x"   >
<input name="snmpgroupName" type="hidden" class="input_x"  >
<input name="del_flag" type="hidden" class="input_x"  value="1">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" id="tabName" name="tabName" >
</form> 

<form  name="webCommunity" method="post" action="/goform/setSnmpCfgComm" class="formContain" x-show="active==='tab3'" >
	<input type="hidden" name="flag">
	<input type="hidden" name="comname">
	<input type="hidden" name="trapiphid">
	<input type="hidden" name="trapnamehid">
	<input type="hidden" name="ltime" value=<% write(lltime); %>>
	<input type="hidden" name="lastts" value=<% write(serverts); %>>
	<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
	<input type="hidden" name="tabName" id="tabName">
	
				
	
	 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
		<tr><td height="30px" ><font size="5"  color="#404040"><div class="bot">团体名设置</div></font></td></tr>
	 </table>
	 
	
	<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table51" class="tablebord">
	
		<tr height="25">
		  <td width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"团体名");</script>:</td>
		  <td width="25%" align="left" class="crons">&nbsp;
			  <input type="text" name="community" class="input_board3" value="">	               </td> 
		  <td width="15%" class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"权 限");</script>:</td>
			 <td width="23%" align="left" class="crons">&nbsp;
			  <input type="radio" name="rw" value="rw" checked>rw
			  <input type="radio" name="rw" value="ro" >ro	                 </td>
		</tr>
		<tr height="25" >
		  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"视图名:");</script>
			</td>
		  <td colspan="4" align="left" class="crons">&nbsp;
			  <input type="text" name="snmp_view" readonly="readonly" class="input_board3" value="all">                  </td>
	  </tr>
		<tr height="25">
		  <td  colspan="4" align="center" class="crons" style="text-align: center;">&nbsp;
		  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"添  加","button","button","modify1","messageCheckCommunity(1)");</script>	              </td>
	  </tr> 
	</table>
	
	<br>
	 <table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
		<tr><td height="20px" ><font size="5" color="#404040"><div class="bot">团体名列表</div></font></td></tr>
	 </table>
	
	
	<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0"  id="table52" class="tablebord">	        
		<% showSnmpComTable(); %>
	</table>
	
	
	<br><br>
</form>


<form  name="snmp_view" method="post" action="/goform/snmp_viewSet" class="formContain" x-show="active==='tab1'">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
		<input type="hidden" name="snmpDelViewID" id="snmpDelViewID"/>
		<input type="hidden" name="snmpDelViewOid" id="snmpDelViewOid">
		<input type="hidden" name="tabName" id="tabName">
	<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="30px" ><font size="5" color="#404040"><div class="bot">视图设置</div></font></td></tr>
	</table>
	

<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table61" class="tablebord">
	<TBODY>
	<TR height=22>
		<TD width="18%" valign="top"><div>&nbsp;&nbsp;<span class="td25">视图名:</span></div></TD>
		<TD width="32%" colspan="3" ><span class="crons">
		<input type="text" name="snmpviewName" id="snmpviewName" size="20" maxlength="32">
(范围：1~32个字符) </span></TD>
		
	</TR>
	<TR height=22>
		<TD valign="top">&nbsp;&nbsp;<span class="td25">子树 OID:</span></TD>
		<TD ><span class="crons">
		<input type="text" name="snmpsubTree" id="snmpsubTree" size="20" maxlength="128">

		</span></TD>
		<TD >&nbsp;&nbsp;<span class="td25">视图类型:</span></TD>
		<TD ><span class="crons">
		<select name="snmpviewType" id="snmpviewType" size="1">
			<option value="included"  >Included</option>
			<option value="excluded"  >Excluded</option>
		</select>
		</span></TD>
	</TR>
	
	<TR height=22>
		<TD colspan="4" align="center" style="text-align: center;">
		<script>writebutton(<% write(authmode); %>,<% write(lang); %>,"提 交","buttons_apply","button","aaa","checking22()");</script>
		</TD>
	</TR>    
</TABLE>


<br>
	<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td height="20px" ><font size="5" color="#404040"><div class="bot">视图列表</div></font></td></tr>
	</table>


<table width="98%" border="0" align="center" cellpadding=0 cellspacing=0 class="tablebord" id="table_snmp_view"  >
	<TR align="center" height=22>
		<TD width="20%"   nowrap class="all_tables_list"><span class="partition">视图名</span></TD>
		<TD width="22%"   nowrap class="all_tables_list"><span class="partition">子树</span></TD>
		<TD width="22%"   nowrap class="all_tables_list"><span class="partition">视图类型</span></TD>
		<TD width="36%"   nowrap class="all_tables_list"><span class="partition">删除</span></TD>
	</TR>
	<script language="javascript">
	viewWriteLines();
	</script>
	</table>
</form>
<form name="snmp_view_del" method="POST" action="">
<input name="snmpsubTree" type="hidden" class="input_x"  >
<input name="snmpviewType" type="hidden" class="input_x"   >
<input name="snmpviewName" type="hidden" class="input_x"  >
<input name="del_flag" type="hidden" class="input_x"  value="1">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

</form> 
	
<script>
changebgcolor();
 changebgcolor1();
changebgcolor_name("table_port_vlan");
changebgcolor31();
changebgcolor_name("table_snmp_grop");
changebgcolor51();
changebgcolor52();
changebgcolor61();
changebgcolor_name("table_snmp_view");
<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

<br><br>
</body>
</html>