<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<script src="js/alpinejs.min.js"></script>
		<meta http-equiv="Content-Type" content="text/html; charset=utf8">
		<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
		<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
		<title>&nbsp;
			<script>writemsg(<% write(lang); %>, "Alarm");</script>
		</title>

		<link href="css/display_web.css" rel="stylesheet" type="text/css" />
		<% var alarmportSta, errorcode,alarmcfg,alarmportratecfg;getAlarmInfo();getAlarmPortrate(); %>

			<script language="JavaScript" type="text/JavaScript">

function changebgcolor11(){
 var tab = document.all.table11;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}
function changebgcolor12(){
	
 var tab = document.getElementById("table12");
 if(tab.rows.length ){
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
}

function alarmJudgment()
{
	if(document.getElementById("c1").checked == true)//端口报警
	{
		document.forms[0].para1.value = "enable";
	}
	else
	{
		document.forms[0].para1.value = "disable";
	}
	if(document.getElementById("d1").checked == true)//电源告警
	{
		document.forms[0].para2.value = "enable";
	}
	else
	{
		document.forms[0].para2.value = "disable";
	}
	if(document.getElementById("e1").checked == true)//温度高景
	{
		document.forms[0].para3.value = "enable";
	}
	else
	{
		document.forms[0].para3.value = "disable";
	}
			
	if(document.forms[0].alarmLow.value >0||document.forms[0].alarmLow.value < -40)
	{
		alert(putmsg("ch","低温告警值的范围只能在 -40 到 0之间，且只能为数字!"));
		return false;
	}
			
	if(document.forms[0].alarmwarnLow.value >0||document.forms[0].alarmwarnLow.value < -40)
	{
		alert(putmsg("ch","低温预警值的范围只能在 -40 到 0之间，且只能为数字!"));
		return false;
	}

	var aL = parseInt(document.forms[0].alarmLow.value);
	var wL = parseInt(document.forms[0].alarmwarnLow.value);
	if(aL >= wL)
	{
		alert(putmsg("ch","低温预警值必须大于低温告警值!"));
		return false;
	}

	if(document.forms[0].alarmHigh.value >85||document.forms[0].alarmHigh.value < 0)
	{
		alert(putmsg("ch","高温告警值的范围只能在 0 到 85之间，且只能为数字!"));
		return false;
	}

	if(document.forms[0].alarmwarnHigh.value >85||document.forms[0].alarmwarnHigh.value < 0)
	{
		alert(putmsg("ch","高温预警值的范围只能在 0 到 85之间，且只能为数字!"));
		return false;
	}

	var wH = parseInt(document.forms[0].alarmwarnHigh.value);
	var aH = parseInt(document.forms[0].alarmHigh.value);
	if(wH >= aH)
	{
		alert(putmsg("ch","高温预警值必须小于高温告警值!"));
		return false;
	}
		
		
		document.forms[0].para4.value = document.forms[0].alarmLow.value;
		document.forms[0].para5.value = document.forms[0].alarmHigh.value;
		
		document.forms[0].para6.value = document.forms[0].alarmwarnLow.value;
		document.forms[0].para7.value = document.forms[0].alarmwarnHigh.value;


	if(document.getElementById("pm1").checked == true)
		document.forms[0].para8.value = "single";
	else
		document.forms[0].para8.value = "double";
	if(document.getElementById("mc1").checked == true)
		document.forms[0].para9.value = "enable";
	else
		document.forms[0].para9.value = "disable";

		document.forms[0].submit();
		return true;   
}

// function changeMAC(val){
// 	document.forms[0].para9.value=val;
// 	document.forms[0].submit();
// }
function changePort(idx){
	let Dom=document.querySelectorAll('#portAlarm');
	document.forms[0].para1.value=Dom[idx].value;
	document.forms[0].submit();
		return true;   
}
function refreshpage()
{
  location.href='alarm.asp?ltime='+<% write(lltime); %>;
}

/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
	
    if (cf.check_all.checked == true)
    {
        for (i = 0; i < objs.length; i++) 
        {    
           if (objs[i].disabled==false && objs[i].checked==false)
		   {
           		objs[i].checked = true;  
				addToPortRange(objs[i]);
			}
        }
    }
    else
    {
        for (i = 0; i < objs.length; i++) 
        {    
			if (objs[i].checked==true)
			{
				objs[i].checked = false;  
			 	addToPortRange(objs[i]);
			}
             
        }
    } 
    
}

function isAllSelected() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
	var notAll_flag = 0;
	

    for (i = 0; i < objs.length; i++) 
    {    
		if (objs[i].checked==false)
		{
			notAll_flag = 1;
			break;
		}
    }

	if (!notAll_flag)
	{
		cf.check_all.checked = true;
	}
}



// function addToPortRange(obj){
// 	var target = document.getElementById("port_range");
// 	var p = obj.value;

// 	if(obj.checked)
// 	{
// 		target.value =target.value + p + "=";
// 	}
// 	else
// 	{
// 		target.value = target.value.replace(p + "=","");
// 	}
// }

// function port_range_init(list)
// {
// 	var target = document.getElementById("port_range");

// 	target.value = list;
// }

function isIE() {
    if(!!window.ActiveXObject || "ActiveXObject" in window){
      return true;
    }else{
      return false;
　　 }
}

function displaytype(id)
{
	document.getElementById(id).style="text-align:center";
	if(isIE())
	{
	document.getElementById(id).style.display="";
	document.getElementById(id).style.textAlign="center"
	}
}
function display()
{
	return
	tmp = "<% write(alarmcfg); %>";
	array_cfg = tmp.split(",");
	
	if(array_cfg[0]=="enable")
	  document.getElementById("c1").checked = true;
	else
		document.getElementById("c2").checked = true;

	if(array_cfg[1]=="enable")
	  document.getElementById("d1").checked = true;
	else
		document.getElementById("d2").checked = true;

	if(array_cfg[2]=="enable")
	  document.getElementById("e1").checked = true;
	else
		document.getElementById("e2").checked = true;

		
	  document.forms[0].alarmwarnLow.value = array_cfg[3];
	  document.forms[0].alarmwarnHigh.value = array_cfg[4];
		
//	if(array_cfg[3] != -100)
	  document.forms[0].alarmLow.value = array_cfg[5];

//	if(array_cfg[4] != 100)
	  document.forms[0].alarmHigh.value = array_cfg[6];
		
	  document.forms[0].currentTem.value = array_cfg[7];
		
 		
	if(array_cfg[8]=="single")
	  document.getElementById("pm1").checked = true;
	else
		document.getElementById("pm2").checked = true;

	if(array_cfg[9]=="enable")
	  document.getElementById("mc1").checked = true;
	else
		document.getElementById("mc2").checked = true;

	if(array_cfg[8]!="none")
	{
		displaytype("trid1");
		displaytype("trid2");
	}
	if(array_cfg[7]!="255")
	{
		displaytype("trid3");
		displaytype("trid4");
		displaytype("trid5");
		displaytype("trid6");
	}
	
	tmp = "<% write(alarmportratecfg); %>";

	// if(tmp == "enable")
	// 	document.getElementById("portm1").checked = true;
	// else
	// 	document.getElementById("portm2").checked = true;
	
}

//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++alarmport

function changebgcolor21(){
 var tab = document.all.table21;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}
function changebgcolor22(){
 var tab = document.all.table22;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}

function changebgcolor_port(){
 var tab = document.all.table_port;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}


// function alarmportrateJudgment()
// {
// 	if(document.getElementById("portm1").checked == true)
// 		document.forms["webFormPortrate"].para1.value = "enable";
// 	else
// 		document.forms["webFormPortrate"].para1.value = "disable";

// 	document.forms["webFormPortrate"].submit();
// 	return true;   
// }

function checking2()
{
	var port_range_portrate = document.getElementById("port_range_portrate").value;
	var rate_threshold = document.getElementById("rate_threshold");
 	var hid=document.webFormPortrate;
	var i,j;

	
	if(rate_threshold < 0 || rate_threshold > 100)
	{
        alert(putmsg(<% write(lang); %>,"Valid percentage(%) 0-100!"));
		return;
	}

    hid.action="/goform/setAlarmPortrateThreshold";
	hid.submit();
}

function addToPortRange(obj)
{

	var trid="input_ge"+obj.value;
	var nodeArray;
	var trobj = document.getElementById(trid);
	var target = document.getElementById("port_range_portrate");
	var rate_threshold = document.getElementById("rate_threshold");

	var p = obj.value;
	var i;

	if (obj.checked)
	{
		// target.value = target.value  + p + " ";

		// for (i=0; i<rate_threshold.options.length; i++)
		// {
		// 	if (rate_threshold.options[i].text==trobj.cells[2].innerHTML)
		// 	{
		// 		rate_threshold.options[i].selected=true;
		// 		break;
		// 	}
		// }			
	}
	else
	{

		// target.value = target.value.replace(p+" ","");
	}

}

/*display function*/
function P(portId, threshold)
{

    var narr=3;
	var arr="";
	var speed="";
	var Desc="";
    var tbtd;
    var i;
    var opt;
    var tbtr = document.getElementById("table_port").insertRow(-1);

	
    tbtr.classname = "td7";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "td7");
    tbtr.setAttribute("className", "td7");
	tbtr.setAttribute("id", "tr_"+portId);

	/*Cycle respectively and setting attributes*/
    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	

        tbtr.appendChild(tbtd);
    }

	/*display*/
	tbtr.cells[0].innerHTML = "<input type=\"checkbox\" name=\"port_checkbox_index\" value=\""+portId+"\" onchange=\"addToPortRange(this)\"/>";
	tbtr.cells[1].innerHTML = portId;
    tbtr.cells[2].innerHTML = threshold;

}


//snmp ==============================================


function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}
function changebgcolor2(){
 var tab = document.all.table2;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor3(){
 var tab = document.getElementById('table3');
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		  }
	else{
			// tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables all_tables3" ;
		}

     }

  }
}



//[a-z A-Z 0-9 ] 字线和数字
function check_ingress_str_format(str)
{
    var reg1 = /^([a-zA-Z]|\d)+$/;

    if(!reg1.exec(str))
        return false;

    return true;
}

function isValidString(str)
{
	var validc = "/'%`\"\\><";
	var a,c;
	for(i = 0 ; i < validc.length; i ++)
	{
		c = validc.substring(i, i + 1);
		if(str.indexOf(c) > -1)
		{
			return true;
		}
	}
	return false;
}

/* by zjx  11-6-8 */
function IpCheck(ip_addr)
{
	var myRE = /^(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])$/;
	if(myRE.test(ip_addr))
	{
        return true;
	}
	else
	{
		return false;
	}
}

function messageCheck(v)
{
    var hid=document.webForm;
    if(v==1)
    {
    	if(check_ingress_str_format(hid.community.value)!=true)
    	{
			alert(putmsg(<% write(lang); %>,"团体名只能为字母和数字!"));
			return false;
    	}
    	if(hid.community.value.length>32)
    	{
			alert(putmsg(<% write(lang); %>,"团体名长度不能大于32!"));
			return false;
		}
	 	else if(hid.community.value.length==0)
	    {
	    	alert(putmsg(<% write(lang); %>,"团体名不能为空!"));
	    	return false;
	    }
	    hid.flag.value=1;
		hid.tabName.value='tab3'
		hid.submit();
		return true;
    }
    else if(v==2)
    {
    	if(hid.community.value.length==0)
	    {
	    	alert(putmsg(<% write(lang); %>,"团体名不能为空!"));
	    	return false;
	    }
	    hid.flag.value=2;
		hid.tabName.value='tab3'
		hid.submit();
		return true;
    }
    else if(v==3)
    {
		if(hid.admininfo.value.length>=128)
		{
			alert(putmsg(<% write(lang); %>,"管理员标识长度小于128!"));
			return false;
		}
		else if(isValidString(hid.admininfo.value))
		{
			alert(putmsg(<% write(lang); %>,"输入中发现非法字符!"));
			return false;
		}

    	hid.flag.value=3;
		hid.tabName.value='tab3'
    	hid.submit();
		return true;
    }
    else if(v==4)
    {
    	hid.flag.value=4;
		hid.tabName.value='tab3'
    	hid.submit();
		return true;
    }
    else if(v==5)
    {
    	if(hid.trapsIp.value.length==0||hid.username.value.length==0)
				{
					alert(putmsg(<% write(lang); %>,"主机地址和团体名不能为空!"));
					return false;
				}
		else  if(check_ingress_str_format(hid.username.value)!=true)
		     	{
		 			alert(putmsg(<% write(lang); %>,"团体名只能为字母和数字!"));
		 			return false;
		     	}
		if(eval(document.webForm.trapval.value)!=1)
				{
					alert(putmsg(<% write(lang); %>,"此配置只有在traps功能打开的情况下有效!"));
					return false;
				}
	
		if(IpCheck(hid.trapsIp.value)!=true)
				{
					alert(putmsg(<% write(lang); %>,"IP地址的格式不合法!"));
					return false;
				}
				

		if(hid.username.value.length>32)
		{
			alert(putmsg(<% write(lang); %>,"团体名长度不能大于32!"));
			return false;
		}

	    hid.flag.value=5;
		hid.tabName.value='tab3'
			hid.submit();
			return true;
    }
    else if(v==6)
    {
    	if(hid.trapsIp.value.length==0)
		{
			alert(putmsg(<% write(lang); %>,"主机地址不能为空!"));
			return false;
		}
		if(eval(document.webForm.trapval.value)!=1)
		{
			alert(putmsg(<% write(lang); %>,"此配置只有在traps功能打开的情况下有效!"));
			return false;
		}
	    hid.flag.value=6;
		hid.tabName.value='tab3'
		hid.submit();
		return true;
    }
    else if(v==7)
    {
    	if(hid.devLocation.value.length==0)
		{
			alert(putmsg(<% write(lang); %>,"设备位置不能为空!"));
			return false;
		}
		else if(hid.devLocation.value.length > 256)
		{
			alert(putmsg(<% write(lang); %>,"设备位置长度不能大于256!"));
			return false;
		}
		else if(isValidString(hid.devLocation.value))
		{
			alert(putmsg(<% write(lang); %>,"输入中发现非法字符!"));
			return false;
		}
	    hid.flag.value=7;
		hid.tabName.value='tab3'
		hid.submit();
		return true;
    }
    else if(v==8)
    {
    	if(hid.devLocation.value.length==0)
		{
			alert(putmsg(<% write(lang); %>,"设备位置不能为空!"));
			return false;
		}
		else if(hid.devLocation.value.length>256)
		{
			alert(putmsg(<% write(lang); %>,"设备位置长度不能大于256!"));
			return false;
		}
	    hid.flag.value=8;
		hid.tabName.value='tab3'
		hid.submit();
		return true;
    }
	else if (v == 9)
    {
    	if(hid.engineid.value.length==0)
		{
			alert(putmsg(<% write(lang); %>,"引擎ID不能为空!"));
			return false;
		}
		else if (hid.engineid.value.length < 16 || hid.engineid.value.length > 64)
		{
			alert(putmsg(<% write(lang); %>,"引擎ID: 偶数个十六进制字符，偶数取值范围: 16~64!"));
			return false;
		}
		else if (hid.engineid.value.length%2)
		{
			alert(putmsg(<% write(lang); %>,"引擎ID: 偶数个十六进制字符，偶数取值范围: 16~64!"));
			return false;
		}

	    hid.flag.value=9;
		hid.tabName.value='tab3'
		hid.submit();
		return true;
    }
	else if (v == 10)
    {
	    hid.flag.value = 10;
		hid.tabName.value='tab3'
		hid.submit();
		return true;
    }

    return ;

}
function DelSnmpCom(value)
{
    var hid=document.webForm;
    	hid.comname.value=value;
		hid.flag.value=2;
		hid.submit();
		return true;
}
function DelSnmpTrap(v1,v2,v3)
{
    var hid=document.webForm;

	hid.flag.value=6;
	if (v1 == "v1")
		hid.trapverhid.value="1";
	else if (v1 == "v2c")
		hid.trapverhid.value="2c";
	else
		hid.trapverhid.value="3";

	hid.trapiphid.value=v2;
	hid.trapnamehid.value=v3;
	hid.tabName.value='tab3'
	hid.submit();
	return true;
}
function checkData()
{
	var tf=document.webForm;
	tf.action = "/goform/saveComm?name=snmp";
	tf.submit();
}
function getUrlParamTab() {
        let urlSearch = window.location.search
        const search = new URLSearchParams(urlSearch)
		
        const params = Object.fromEntries(search.entries())
		
        try {
          if (params.tab) return params.tab
        } catch { }
        return 'tab1'
      }
</script>
</head>

<body onload="display();" x-data="{active:'tab1'}" x-init="active=getUrlParamTab()"><br>
	<% web_get_stat(); %>
		<% var authmode; checkCurMode(); %>
			<script>
				checktop(<% write(lang); %>);

				retValue = <% var responseJsonStr; jw_get_portLimitConfig(); %>
					AlarmPortrate = <% write(responseJsonStr); %>;
				// 告警配置
				retValue1 = <% var responseJsonStr; jw_get_trapAlarmConfig(); %>
					alarmList = <% write(responseJsonStr); %>;
				const appData = {

					PortLimit: [
						{
							portName: '',
							portRate: '',

						},

					],


				};

				//   console.log(alarmList,'alarmList')
				appData.PortLimit = [...AlarmPortrate.PortLimit]
				appData.enFlag = AlarmPortrate.enFlag
				Alpine.data('myApp', () => ({
					...appData,

				}));
				Alpine.data('myAlarm', () => ({
					portAlarm: alarmList.portAlarm,
					macChange: alarmList.macChange,
					alarmPort: alarmList.alarmPort,
				}));


				function changePortLimit(row, idx) {
					var hid = document.webFormPortrate;
					var obj = {
						pageName: 'alarm.asp',
						enFlag: appData.enFlag,
						PortLimit: [{
							portName: row.portName,
							portRate: Number(row.portRate)
						}]
					}
					hid.param1.value = JSON.stringify(obj);
					hid.action = "/goform/jw_set_portLimitConfig";
					hid.submit();
				}

				function changeOpen(val) {
					var hid = document.webFormPortrate;
					var obj = {
						pageName: 'alarm.asp',
						pagePath: `alarm.asp?ltime=${<% write(lltime); %>}& tab=tab2`,
			enFlag:val,
			PortLimit:[]
		}
			hid.param1.value = JSON.stringify(obj);
			hid.action="/goform/jw_set_portLimitConfig"; 
			hid.submit();
	  }
	  //告警管理
	  
	  function  cahngeAlarm() {
		var hid=document.webFormAlarm;
		
		var obj={
			pageName:'alarm.asp',
			portAlarm:alarmList.portAlarm,
			macChange:alarmList.macChange,
			alarmPort:alarmList.alarmPort.map((item)=>{
				return {
					portName:item.portName,
					portStat:Number(item.portStat)
				}
			})
		}
		hid.param1.value = JSON.stringify(obj);
		hid.action="/goform/jw_set_trapAlarmConfig";
		hid.submit();
		
	  }
	  function changeMAC(val) {
		var hid=document.webFormAlarm;
		
		var obj={
			pageName:'alarm.asp',
			portAlarm:alarmList.portAlarm,
			macChange:val,
			alarmPort:alarmList.alarmPort.map((item)=>{
				return {
					portName:item.portName,
					portStat:Number(item.portStat)
				}
			})
		}
		hid.param1.value = JSON.stringify(obj);
		hid.action="/goform/jw_set_trapAlarmConfig";
		hid.submit();
	  }
	  function cahngePortAlarm(val) {
		var hid=document.webFormAlarm;
		
		var obj={
			pageName:'alarm.asp',
			portAlarm:val,
			macChange:alarmList.macChange,
			alarmPort:alarmList.alarmPort.map((item)=>{
				return {
					portName:item.portName,
					portStat:Number(item.portStat)
				}
			})
		}
		hid.param1.value = JSON.stringify(obj);
		hid.action="/goform/jw_set_trapAlarmConfig";
		hid.submit();
	  }
			</script>
			<div>
				<ul class="tabmenu">
					<li id="tab1" :class="active==='tab1'?'tab':'tab-disable'">
						<a herf="#" x-on:click="active='tab1'">TRAP 安全设置</a>
					</li>
					<li id="tab2" :class="active==='tab2'?'tab':'tab-disable'">
						<a herf="#" x-on:click="active='tab2'">TRAP 端口流量越限</a>
					</li>
					<li id="tab3" :class="active==='tab3'?'tab':'tab-disable'" @click="">
						<a herf="#" x-on:click="active='tab3'">TRAP SNMP设置</a>
					</li>
				</ul>
			</div>
			<form x-show="active==='tab1'" name="webFormAlarm" method="post" action="/goform/setAlarm"
				class="formContain">

				<input type="hidden" name="para1">
				<input type="hidden" name="para2">
				<input type="hidden" name="para3">
				<input type="hidden" name="para4">
				<input type="hidden" name="para5">
				<input type="hidden" name="para6">
				<input type="hidden" name="para7">
				<input type="hidden" name="para8">
				<input type="hidden" name="para9">

				<input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
				<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
				<input type="hidden" name="param1" id="param1">
				<div x-data="myAlarm">


					<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="30px" colspan="2">
								<font size="5" color="#404040">
									<div class="bot">告警管理</div>
								</font>
							</td>
						</tr>

						<tr height="25">
							<td align="right" class="tableTd" width="40%">&nbsp;mac地址配置变更告警（5分钟后自动清除告警）&nbsp:</td>
							<td align="center" class="crons">&nbsp;

								<select x-model="macChange" @change="changeMAC(macChange)">
									<option value="enable">开启</option>
									<option value="disable">关闭</option>
								</select>
							</td>
						</tr>
						<tr height="10">
							<td>

							</td>
						</tr>
						<tr height="25">
							<td align="right" class="tableTd" width="30%">&nbsp;端口告警 &nbsp:</td>
							<td align="center" class="crons">&nbsp;
								<select x-model="portAlarm" @change="cahngePortAlarm(portAlarm)">
									<option value="enable">开启</option>
									<option value="disable">关闭</option>
								</select>
								<!-- <input type="radio" name="mstpswitch" value="enable" checked id="c1">Enable
							<input type="radio" name="mstpswitch" value="disable" id="c2">Disable -->
							</td>
						</tr>
						<tr height="10">

						</tr>
					</table>
					<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="tablebord">
						<tr height="45" style="background-color: #fff;">
							<td class="tableTd" align="center">
								端口
							</td>
							<td class="tableTd" align="center">
								是否开启
							</td>
						</tr>
						<template x-for="(row,index) in alarmPort" :key="index+1">
							<tr>
								<td x-text="row.portName" align="center" class="all_tables"></td>
								<td align="center" class="all_tables">
									<select x-model="row.portStat" @change="cahngeAlarm">
										<option value="1">开启</option>
										<option value="0">关闭</option>
									</select>
								</td>
							</tr>
						</template>
					</table>

					<!-- <table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
					<tr>
						<td>
							<table width="100%" border="0"   align="center" cellpadding="0" cellspacing="0" id="table11"
								class="tablebord">
								<tr height="25" id="trid1" style="display:none">
									<td width="15%" align="left" class="crons">&nbsp;电源告警模式 &nbsp:</td>
									<td width="25%" colspan="3" align="left" class="crons">&nbsp;
										<input type="radio" name="powermode" value="single" checked id="pm1">Single
										<input type="radio" name="powermode" value="double" id="pm2">Double
									</td>
								</tr>

								<tr height="25" id="trid2" style="display:none">
									<td width="15%" align="left" class="crons">&nbsp;电源告警 &nbsp:</td>
									<td width="25%" colspan="3" align="left" class="crons">&nbsp;
										<input type="radio" name="rw" value="rw" checked id="d1">Enable
										<input type="radio" name="rw" value="ro" id="d2">Disable
									</td>
								</tr> -->

					<!-- <tr height="25">
									<td align="left" class="crons">&nbsp;mac地址配置变更告警（5分钟后自动清除告警）&nbsp:</td>
									<td colspan="3" align="left" class="crons">&nbsp;
										<input type="radio" name="macChange" value="enable" checked id="mc1">Enable
										<input type="radio" name="macChange" value="disable" id="mc2">Disable
										&nbsp;&nbsp;
									</td>
								</tr> -->

					<!-- <tr height="25">
									<td align="left" class="crons">&nbsp;端口告警 &nbsp:</td>
									<td colspan="3" align="left" class="crons">&nbsp;
										<input type="radio" name="mstpswitch" value="enable" checked id="c1">Enable
										<input type="radio" name="mstpswitch" value="disable" id="c2">Disable
									</td>
								</tr> -->

					<!-- <tr height="25">
									<td colspan="4" align="left" class="crons">&nbsp;
										<script>writemsg(<% write(lang); %>, "端口选择");</script>:
										&nbsp;全选<input type="checkbox" name="check_all" value="all"
											onClick="selectToAll()" />
									</td>

								</tr>
								<tr height="25" id="trid3" style="display:none">
									<td align="left" class="crons">&nbsp;温度告警:</td>
									<td colspan="3" align="left" class="crons">&nbsp;
										<input name="mstpswitch22" type="radio" value="enable" checked id="e1">Enable
										<input type="radio" name="
				" value="disable" id="e2">Disable
									</td>
								</tr>

								<tr height="25" id="trid4" style="display:none">
									<td align="left" class="crons">&nbsp;当前温度:</td>
									<td colspan="3" align="left" class="crons">&nbsp;
										<input type="text" name="currentTem" readonly="readonly"
											class="input_board3">&nbsp;℃
									</td>
								</tr>

								<tr height="25" id="trid5" style="display:none">
									<td align="left" class="crons">&nbsp;低温预警值:</td>
									<td colspan="1" align="left" class="crons">&nbsp;
										<input type="text" name="alarmwarnLow" class="input_board3">&nbsp;℃
									</td>

									<td align="left" class="crons">&nbsp;高温预警值:</td>
									<td colspan="1" align="left" class="crons">&nbsp;
										<input type="text" name="alarmwarnHigh" class="input_board3">&nbsp;℃
									</td>
								</tr>

								<tr height="25" id="trid6" style="display:none">
									<td align="left" class="crons">&nbsp;低温告警值:</td>
									<td colspan="1" align="left" class="crons">&nbsp;
										<input type="text" name="alarmLow" class="input_board3">&nbsp;℃
									</td>

									<td align="left" class="crons">&nbsp;高温告警值:</td>
									<td colspan="1" align="left" class="crons">&nbsp;
										<input type="text" name="alarmHigh" class="input_board3">&nbsp;℃
									</td>
								</tr> -->
					<!-- <tr height="25">
									<td colspan="4" align="left" class="crons">&nbsp;<% AppendOptionPortAlarm(); %>
											<input name="port_range" type="hidden" class="input_x" id="port_range"
												readonly="true" />
											</td>
								</tr>
								<tr height="25">
									<td colspan="4" align="center" class="crons">
										<div align="center">
											<script>writebutton(<% write(authmode); %>, "ch", "应  用", "buttons_apply", "button", "", "alarmJudgment()");</script>
											&nbsp;&nbsp;&nbsp;
										</div>
									</td>
								</tr>

							</table> -->
					<!-- </td>
					</tr>



				</table> -->
				</div>
			</form>
			<div x-show="active==='tab1'" class="Tablelist formContain" style="margin-top: 20px;">
				<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td height="30px">
							<font size="5" color="#404040">
								<div class="bot">
									<script>writemsg(<% write(lang); %>, "告警清单");</script>
								</div>
							</font>
						</td>
					</tr>
				</table>
				<table width="100%" border="0" height="100" align="center" cellpadding="0" cellspacing="0" id="table12"
					class="tablebord">
					<tr>
						<td>
							<% showAlarmInfo(); %>
						</td>
					</tr>
				</table>
			</div>


			<form x-show="active==='tab2'" name="webFormPortrate" method="post" action="/goform/setAlarmPortrate"
				class="formContain">

				<input type="hidden" name="param1">

				<input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
				<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

				<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td height="30px">
							<font size="5" color="#404040">
								<div class="bot">端口流量越限</div>
							</font>
						</td>
					</tr>
				</table>


				<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">







					<tr>
						<td>

							<div x-data="myApp">
								<div class=""> <span style="font-size: 16px;font-weight: bold;">
										是否开启:</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <select
										style="width: 200px;text-align: center;margin-bottom: 10px;" id="portrate_mode"
										x-model="enFlag" @change="changeOpen(enFlag)">
										<option value="enable">开启</option>
										<option value="disable">关闭</option>
									</select>
								</div>

								<br>
								<table width="100%" height="36" border="0" cellpadding="0" cellspacing="0"
									class="tablebord" id="table_port">
									<thead height="30" align="center" class="td7">
										<!-- <th align="center" class="td2" width="20%"></th> -->
										<th align="center" class="td2" width="30%">
											<script>writemsg(<% write(lang); %>, "端口");</script>
										</th>
										<th align="center" class="td2" width="50%">
											<script>writemsg(<% write(lang); %>, "流量阈值(%)");</script>
										</th>

									</thead>
									<template x-for="(row,idx) in PortLimit" :key="idx">
										<tr height="30" align="center" class="td7">

											<td align="center" class="td2" x-text="row.portName"
												style="font-weight: normal;">
											</td>
											<td align="center" class="td2" style="font-weight: normal;">
												<input type="text" class="input_board3" x-model="row.portRate"
													@change="changePortLimit(row)">
											</td>

										</tr>
									</template>
									<!-- <script>
				                 <% var errorcode; alarmPortrateShow();%>
								</script> -->
								</table>
							</div>

						</td>
					</tr>

					<tr>
						<td align="center" height="35">
							<table>
								<tr>
									<br />
									<script>writebutton(1,<% write(lang); %>, "刷  新", "button", "button", "Refresh", "refreshpage()");</script>
									&nbsp;
									&nbsp;
								</tr>
							</table>
						</td>
					</tr>
				</table>

			</form>
			<form x-show="active==='tab3'" name="webForm" method="post" action="/goform/setSnmpCfg" class="formContain">
				<input type="hidden" name="flag">
				<input type="hidden" name="comname">
				<input type="hidden" name="trapiphid">
				<input type="hidden" name="trapnamehid">
				<input type="hidden" name="trapverhid">
				<input type="hidden" name="ltime" value=<% write(lltime); %>>
				<input type="hidden" name="lastts" value=<% write(serverts); %>>
				<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
				<input type="hidden" name="tabName" id="tabName">
				<% var errorcode,devLocation,engineid,adminInfo,trapen;getSnmpInfo(); %>




					<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="30px">
								<font size="5" color="#404040">
									<div class="bot">traps主机</div>
								</font>
							</td>

						</tr>
					</table>

					<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" id="table1"
						class="tablebord">

						<tr height="25" style="display:none">
							<td width="13%" align="left" class="crons">&nbsp;
								<script>writemsg(<% write(lang); %>, "团体名");</script>:
							</td>
							<td width="17%" align="left" class="crons">&nbsp;
								<input type="text" name="community" class="input_board3" value="">
							</td>
							<td width="16%" align="left" class="crons">&nbsp;
								<script>writemsg(<% write(lang); %>, "权 限");</script>:
							</td>
							<td colspan="3" align="left" class="crons">&nbsp;
								<input type="radio" name="rw" value="rw" checked>Read_Write
								<input type="radio" name="rw" value="ro">Read_Only
							</td>
							<td width="13%" align="left" class="crons">
								<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "添  加", "buttons_add", "button", "modify1", "messageCheck(1)");</script>
							</td>
						</tr>

						<tr height="25" style="display:none">
							<td align="left" class="crons">&nbsp;
								<script>writemsg(<% write(lang); %>, "管理员标识");</script>:
							</td>
							<td colspan="5" align="left" class="crons">&nbsp;
								<input type="text" name="admininfo" class="input_board3" value=<% write(adminInfo); %>>
							</td>
							<td align="left" class="crons">
								<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "buttons_apply", "button", "modify3", "messageCheck(3)");</script>
							</td>
						</tr>

						<tr height="25">
							<td align="left" class="crons">&nbsp;SNMP Traps:</td>
							<td align="left" class="crons">&nbsp;
								<input type="hidden" name="trapval" value=<% write(trapen); %>>
								<input type="radio" name="trap" value="1" <% if (trapen==1) write("checked"); %>>开启
								<input type="radio" name="trap" value="0" <% if (trapen==0) write("checked"); %>
								>关闭
							</td>
							<td colspan="4" align="left" class="crons">&nbsp;</td>
							<td align="left" class="crons">
								<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "buttons_apply", "button", "modify4", "messageCheck(4)");</script>
							</td>
						</tr>

						<tr height="25">
							<td align="left" class="crons">&nbsp;
								<script>writemsg(<% write(lang); %>, "引擎ID");</script>:
							</td>

							<td align="left" class="crons">
								<input type="text" name="engineid" size="26" class="input_board3" value=<%
									write(engineid); %>>
							</td>

							<td colspan="4" align="left" class="crons">&nbsp;( 偶数个十六进制字符, 偶数取值范围16~64 )</td>

							<td align="left" class="crons">
								<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "buttons_apply", "button", "modify9", "messageCheck(9)");</script>
								<div style="margin-top: 5px;">
									<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "缺  省", "buttons_apply", "button", "modify10", "messageCheck(10)");</script>
								</div>

							</td>
						</tr>

						<tr height="25">
							<td align="left" class="crons">&nbsp;
								<script>writemsg(<% write(lang); %>, "traps主机");</script>:
							</td>
							<td colspan="1" align="left" class="crons">
								<input type="text" name="trapsIp" size="26" class="input_board3">
							</td>
							<td align="left" class="crons">&nbsp;
								<script>writemsg(<% write(lang); %>, "团体名/用户");</script>:
							</td>
							<td width="16%" colspan="1" align="left" class="crons">&nbsp;
								<input type="text" name="username" class="input_board3">
								&nbsp;&nbsp;
							</td>
							<td width="11%" align="left" class="crons"><span class="td25">SNMP版本</span></td>
							<td width="11%" align="left" class="crons"><select name="snmpver" size="1">
									<option value="1">V1</option>
									<option value="2" selected>V2c</option>
									<option value="3">V3</option>
								</select></td>
							<td align="left" class="crons" style="text-align: left;">
								<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "添  加", "buttons_add", "button", "modify1", "messageCheck(5)");</script>
							</td>
						</tr>

						<tr height="25">
							<td align="left" class="crons">&nbsp;
								<script>writemsg(<% write(lang); %>, "设备位置");</script>:
							</td>
							<td align="left" class="crons">
								<input type="text" name="devLocation" size="26" class="input_board3" value=<%
									write(devLocation); %>>
							</td>
							<td colspan="4" align="left" class="crons">&nbsp;</td>
							<td align="left" class="crons">
								<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "buttons_apply", "button", "modify7", "messageCheck(7)");</script>
								<div style="margin-top: 5px;">
									<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "删  除", "buttons_del", "button", "modify8", "messageCheck(8)");</script>
								</div>

							</td>
						</tr>
					</table>





					<script>
						changebgcolor();
						
	
	<% if (errorcode != "") { write_errorcode(errorcode); } %>
					</script>
					<br><br>
			</form>
			<br>
			<div x-show="active==='tab3'" class="formContain">
				<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td height="30px">
							<font size="5" color="#404040">
								<div class="bot">traps列表</div>
							</font>
						</td>

					</tr>
				</table>
				<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" id="table3"
					class="tablebord">

					<% showSnmpTrapTable(); %>
				</table>
			</div>




			<script>
					//changebgcolor11();
				changebgcolor12();
			//	isAllSelected();
				changebgcolor3();
				//changebgcolor21();
				// changebgcolor22();
				changebgcolor_port();

				var alarmportStaList = <% write("\""); write(alarmportSta); write("\""); %>;
				// port_range_init(alarmportStaList);
				
<% if (errorcode != "") { write_errorcode(errorcode); } %>
			</script>
			<br>
			<br>

</html>