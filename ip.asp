<HTML>

<HEAD>
	<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
		<TITLE>page</TITLE>
		<META http-equiv=Content-Type content="text/html; charset=utf8">
		<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
		<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
		<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
		<script language="javascript" type="text/javascript" src="/js/jquery.min.js"></script>
		<link href="css/display_web.css" rel="stylesheet" type="text/css" />
		<script>
<% var outbandip, Allipaddr, errorcode; getIpAddr(); %>

				function showHelp(helpname, lang) {
					var tmp = lang + "_help.html#" + helpname;
					window.open(tmp);
				}
			function tdIpCheck(textValue) {
				re1 = /(\d+)(\W)(\d+)(\W)(\d+)(\W)(\d+)/
				Check = textValue.search(re1);
				if (Check == -1) {
					return false;
				}
				else {
					ipSplit = textValue.split('.');
					if (ipSplit.length != 4) {
						return false;
					}

					for (i = 0; i < ipSplit.length; i++) {
						if (isNaN(ipSplit[i])) return false;
						if (ipSplit[i].length > 1 && ipSplit[i].charAt(0) == "0") return false;
					}

					for (i = 0; i < ipSplit.length; i++) {
						if (ipSplit[i] > 255) {
							return false;
						}
						if (ipSplit[i] < 0) {
							return false;
						}
					}
					if ((ipSplit[0] == 255) && (ipSplit[1] == 255) && (ipSplit[2] == 255) && (ipSplit[3] == 255)) {
						return false;
					}

					if ((ipSplit[0] == 0) || (ipSplit[0] == 127) || (ipSplit[3] == 0) || (ipSplit[3] == 255)) {
						return false;
					}

					if (ipSplit[0] >= 224) {
						return false;
					}
					return true;
				}
			}

			function _checkIput_fomartIP(ip) {
				return (parseInt(ip) + 256).toString(2).substring(1); //格式化输出(补零) 
			}

			function tdSubMaskCheck(textValue) {
				re1 = /(\d+)(\W)(\d+)(\W)(\d+)(\W)(\d+)/
				Check = textValue.search(re1);
				if (Check == -1) {
					return false;
				}
				else {
					ipSplit = textValue.split('.');
					if (ipSplit.length != 4) {
						return false;
					}

					for (i = 0; i < ipSplit.length; i++) {
						if (isNaN(ipSplit[i])) return false;
						if (ipSplit[i].length > 1 && ipSplit[i].charAt(0) == '0') return false;
					}

					for (i = 0; i < ipSplit.length; i++) {
						if (ipSplit[i] > 255) {
							return false;
						}
						if (ipSplit[i] < 0) {
							return false;
						}
					}

					var ip_binary = _checkIput_fomartIP(ipSplit[0]) + _checkIput_fomartIP(ipSplit[1]) + _checkIput_fomartIP(ipSplit[2]) + _checkIput_fomartIP(ipSplit[3]);
					if (-1 != ip_binary.indexOf("01") || ip_binary.charAt(0) == '0') {
						return false;
					}
					return true;
				}
			}

			function changebgcolor() {
				var tab = document.all.table1;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {
							tab.rows[i].cells[j].className = "all_tables all_tables2";
						}
						else {
							tab.rows[i].cells[j].className = "all_tables all_tables1";
						}

					}

				}

			}

			function changebgcolor2() {
				var tab = document.all.table2;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {
							tab.rows[i].cells[j].className = "all_tables all_tables2";
						}
						else {
							tab.rows[i].cells[j].className = "all_tables all_tables1";
						}
					}
				}
			}

			function changebgcolor3() {
				var tab = document.all.table3;
				var len = tab.rows.length;
				for (var i = 0; i < len; i++) {
					var lencol = tab.rows[i].cells.length
					for (var j = 0; j < lencol; j++) {
						if (j % 2 == 1) {
							tab.rows[i].cells[j].className = "all_tables all_tables2";
						}
						else {
							tab.rows[i].cells[j].className = "all_tables all_tables1";
						}
					}
				}
			}
			var lastIP = "";
			function display() {
				var hid = document.webForm;
				allvalue = "<% write(Allipaddr); %>";
				var hid1 = document.webForm1;
				hid1.manage_vlan.value = allvalue.split(",")[0];
				hid.ipaddr.value = allvalue.split(",")[1].split("/")[0];
				lastIP = hid.ipaddr.value
				hid.ipsubnet.value = getSubnetMask(allvalue.split(",")[1].split("/")[1])
				hid.gateway.value = allvalue.split(",")[2];
				const form = document.querySelector('#webFormId');
				// const input = document.querySelector('input');

				form.addEventListener('submit', (event) => {
					event.preventDefault(); // 阻止表单的默认提交行为
				});

				// input.addEventListener('keydown', (event) => {
				// 	if (event.keyCode === 13) { // 如果按下了回车键
				// 		event.preventDefault(); // 阻止默认的提交行为
				// 	}
				// });

			}
			//网络位数转二进制 24 返回*************
			function getSubnetMask(networkBits) {
				const binaryMask = "1".repeat(networkBits).padEnd(32, "0"); // 生成对应位数的二进制子网掩码
				const decimalMask = [];

				for (let i = 0; i < 4; i++) {
					const octet = binaryMask.substring(i * 8, (i + 1) * 8); // 每个八位二进制数作为一个八进制数
					const decimalOctet = parseInt(octet, 2); // 将八位二进制数转换为十进制数
					decimalMask.push(decimalOctet); // 将每个八进制数添加到结果数组中
				}

				return decimalMask.join('.'); // 将结果数组以点分十进制表示法连接起来
			}

			function convertMaskToBinary(mask) {
				var binaryMask = [];
				var maskArray = mask.split(".");
				for (var i in maskArray) {
					var octet = parseInt(maskArray[i]).toString(2);
					binaryMask.push(("00000000" + octet).substr(-8));
				}
				return binaryMask.join("");
			}
			function getNetworkBits(subnetMask) {
				const binaryMask = convertMaskToBinary(subnetMask); // 将十进制子网掩码转换为二进制字符串
				let networkBits = 0;

				for (let i = 0; i < binaryMask.length; i++) {
					if (binaryMask[i] === '1') {
						networkBits++;
					} else {
						break; // 遇到第一个0位时停止计数
					}
				}

				return networkBits;
			}
			function isSameSubnet(ipAddress, gateway, subnetMask) {
				// 将IP地址和网关转换为数字
				const ip = ipToNumber(ipAddress);
				const gw = ipToNumber(gateway);

				// 将子网掩码转换为数字
				const subnet = ipToNumber(subnetMask);

				// 计算网络地址
				const network = ip & subnet;

				// 判断IP地址和网关是否在同一个网段中
				return (network === (gw & subnet));
			}

			// 辅助函数：将IP地址转换为数字
			function ipToNumber(ipAddress) {
				const parts = ipAddress.split('.');
				return (parseInt(parts[0], 10) << 24) +
					(parseInt(parts[1], 10) << 16) +
					(parseInt(parts[2], 10) << 8) +
					parseInt(parts[3], 10);
			}
			function validateIPAddress(ipAddress) {
				const ipRegex = /^([0-9]{1,3}\.){3}[0-9]{1,3}$/;
				return ipRegex.test(ipAddress);
			}
		async	function messageCheck() {
				var hid = document.webForm;
				var dplen;
				var dpvalue;
				var reauthn;
				var reauthpd;

				if (hid.ipsubnet.value == '') {
					alert("子网掩码不能为空")
					return false
				}
				if ((tdIpCheck(hid.gateway.value) == false) && (hid.gateway.value != "")) {
					alert("网关输入非法！格式：A.B.C.D");
					return false;
				}

				if ((tdSubMaskCheck(hid.ipsubnet.value) == false) && (hid.ipsubnet.value != "")) {
					alert("子网掩码输入非法！格式：A.B.C.D");
					return false;
				}

				if (validateIPAddress(hid.ipaddr.value) == false) {
					alert("IP地址不符合");
					return false;
				}
				if (isSameSubnet(hid.ipaddr.value, hid.gateway.value, hid.ipsubnet.value) == false) {
					alert("IP地址和网关不在同一个网段中");
					return false;
				}

				// IpCheckAndMask(hid.ipaddr.value) != false
				if (true) {
					if (confirm("此操作会导致当前连接断掉,确定是否修改?")) {
						//var name = prompt("操作认证-用户名", "");
						document.getElementById("reauthn").value = await userNamePrompt();
						// var pwd = prompt("操作认证-密码", "");
						// document.getElementById("reauthpd").value = pwd;
						document.getElementById("reauthpd").value = await testThePrompt();
						reauthn = document.getElementById("reauthn").value;
						reauthpd = document.getElementById("reauthpd").value;
						
						dplen = parseInt(reauthn.length / 2);
						dpvalue = reauthn;
						reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

						dplen = parseInt(reauthpd.length / 2);
						dpvalue = reauthpd;
						document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
						document.getElementById("reauthn").value = "mo6RlYrwxRel3PDh";
						if (lastIP != hid.ipaddr.value) {
							let ip = hid.ipaddr.value
							hid.ipaddr.value = hid.ipaddr.value + '/' + getNetworkBits(hid.ipsubnet.value)


							hid.submit();
							hid.ipaddr.value = ip
							setTimeout(() => {
								window.top.location.href = 'http://' + ip + '/logout.asp'
							}, 500);

						} else {
							hid.ipaddr.value = hid.ipaddr.value + '/' + getNetworkBits(hid.ipsubnet.value)
							hid.submit();
						}

						return true;
					}
					else
						return false;
				}
				else {
					alert("IP地址输入非法！格式：A.B.C.D/M");
					return false;
				}
				return false;
			}

			function messageCheck2() {
				var hid = document.webForm;
				var dplen;
				var dpvalue;
				var reauthn;
				var reauthpd;
				// IpCheckAndMask(hid.mmsa.value) != false
				if (true) {
					if (confirm("此操作会导致当前连接断掉,确定是否修改?")) {
						var name = prompt("操作认证-用户名", "");
						document.getElementById("reauthn").value = name;
						var pwd = prompt("操作认证-密码", "");
						document.getElementById("reauthpd").value = pwd;


						reauthn = document.getElementById("reauthn").value;
						reauthpd = document.getElementById("reauthpd").value;

						dplen = parseInt(reauthn.length / 2);
						dpvalue = reauthn;
						reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

						dplen = parseInt(reauthpd.length / 2);
						dpvalue = reauthpd;
						document.getElementById("reauthpd").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
						document.getElementById("reauthn").value = "1gA9TASCOvB#10ac";


						hid.action = "/goform/setOutbandIp";
						hid.submit();
						return true;
					}
					else
						return false;
				}
				else {
					alert("IP地址输入非法！格式：A.B.C.D/M");
					return false;
				}

				return false;
			}

			function checkData() {
				var tf = document.webForm;
				tf.action = "/goform/saveComm?name=ip";
				tf.submit();
			}

			function showHelpinfo() {
				showHelp('ip',<% write(lang); %>);
			}

			function updataTime() {
				var tf = document.webForm;
				var agetime = document.getElementById("age_time");
				if (agetime.value == "") {
					alert(putmsg(<% write(lang); %>, "没有输入老化时间，请输入数字!"));
					return;
				}
				if (!isNaN(agetime.value)) {
					if ((agetime.value >= 10 && agetime.value <= 32766) || agetime.value == 0) {
						tf.action = "/goform/UpTime";
						tf.submit();
					}
					else {
						alert(putmsg(<% write(lang); %>, "输入的数必须在10-32766之间,或为 0 "));
					}
				}
				else {
					alert(putmsg(<% write(lang); %>, "输入必须为数字!"));
				}
			}
		async	function updataVlan() {
				var reauthn;
				var reauthpd;
				var dplen;
				var dpvalue;
				var hid = document.webForm1;
				var vid = document.getElementById("manage_vlan").value;

				if ((vid > 4094) && (vid < 1)) {
					alert(putmsg(<% write(lang); %>, "VID的范围必须在2-4094之间"));
				}
				else {
					if (confirm("此操作会导致当前连接断掉,确定是否修改?")) {
						//var name = prompt("操作认证-用户名", "");
						document.getElementById("reauthn1").value = await userNamePrompt();
						// var pwd = prompt("操作认证-密码", "");
						// document.getElementById("reauthpd1").value = pwd;
						document.getElementById("reauthpd1").value = await testThePrompt();
						reauthn = document.getElementById("reauthn1").value;
						reauthpd = document.getElementById("reauthpd1").value;

						dplen = parseInt(reauthn.length / 2);
						dpvalue = reauthn;
						reauthn = dplen + ":3F2e6855" + dpvalue.substring(0, dplen)
							+ reauthn.length + ":E26bE809" + dpvalue.substring(dplen);

						dplen = parseInt(reauthpd.length / 2);
						dpvalue = reauthpd;
						document.getElementById("reauthpd1").value = reauthn + dplen + ":0D2E16CE" + dpvalue.substring(0, dplen)
							+ reauthpd.length + ":E95ca4DA" + dpvalue.substring(dplen);
						document.getElementById("reauthn1").value = "mo6RlYrwxRel3PDh";
						hid.manage_vlan.value = vid;


						hid.submit();

						return true;
					}
					else
						return false;

				}
			}

		</script>
</HEAD>

<BODY onload=display()>
	<br>
	<% web_get_stat(); %>
		<% var authmode; checkCurMode(); %>
			<script>
				checktop(<% write(lang); %>);

			</script>
			<form name="webForm1" method="post" action="/goform/setManageVlan" id="webFormId">
				<div id="view_help" style="display:none">
					<input type="hidden" name="ltime" value=<% write(lltime); %>>
					<input type="hidden" name="lastts" value=<% write(serverts); %>>
					<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
					<input type="hidden" id="reauthn1" name="reauthn" value="">
					<input type="hidden" id="reauthpd1" name="reauthpd" value="">

				</div>

				<div class="formContain">
					<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="30px">
								<font size="5" color="#404040">
									<div class="bot">管理VLAN配置信息</div>
								</font>
							</td>
						</tr>
					</table>
					</tr>
					</table>
					<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table2" class="tablebord">
						<TBODY>
							<TR height=22>
								<TD width="42%" valign="top">&nbsp;&nbsp;
									<script>writemsg(<% write(lang); %>, "管理VLAN");</script>:
								</TD>
								<td><input type="text" name="manage_vlan" id="manage_vlan">&nbsp;&nbsp;(1 -
									4094)</td>
							</TR>
							<tr>
								<td colspan="2" style="text-align: center;">
									<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "button", "button", "det_arp", "updataVlan()");</script>
								</td>
							</tr>
					</table>
					</td>
					</tr>
					</TABLE>
				</div>
			</form>
			<form name="webForm" method="post" action="/goform/setIpAddr">
				<div id="view_help" style="display:none">
					<input type="hidden" name="ltime" value=<% write(lltime); %>>
					<input type="hidden" name="lastts" value=<% write(serverts); %>>
					<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
					<input type="hidden" id="reauthn" name="reauthn" value="">
					<input type="hidden" id="reauthpd" name="reauthpd" value="">
					<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 class="tablebord" bgcolor="efefef">
					</TABLE>
				</div>

				<br><br>
				<div class="formContain">
					<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="30px">
								<font size="5" color="#404040">
									<div class="bot">网络配置信息</div>
								</font>
							</td>
						</tr>
					</table>
					</tr>
					</table>
					<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table3" class="tablebord">
						<TBODY>
							<TR height=22>
								<TD width="42%" valign="top">&nbsp;&nbsp;IP:</TD>
								<td><span class="crons">
										<input type="text" name="ipaddr" id="ipaddr" value="">&nbsp;必填
									</span></td>
							</TR>
							<TR height=22>
								<TD width="42%" valign="top">&nbsp;&nbsp;子网掩码:</TD>
								<td><span class="crons">
										<input type="text" name="ipsubnet" id="ipsubnet" value="">&nbsp;必填
									</span></td>
							</TR>
							<TR height=22>
								<TD valign="top">&nbsp;&nbsp;
									<script>writemsg(<% write(lang); %>, "网关");</script>:
								</TD>
								<td><input type="text" name="gateway" id="gateway" value=""></td>
							</TR>
							<TR height=22>
								<TD colspan="2" valign="top">
									<div align="center" class="btn">
										<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "buttons_apply", "button", "aaa", "messageCheck()");</script>
									</div>
								</TD>
							</TR>
					</table>
					</td>
					</tr>
					</TABLE>
				</div>
				<br>
				<br>
				<div class="formContain">
					<table width="98%" align="center" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td height="30px">
								<font size="5" color="#404040">
									<div class="bot">MAC老化时间配置信息</div>
								</font>
							</td>
						</tr>
					</table>
					</tr>
					</table>
					<TABLE width="98%" align="center" cellpadding=0 cellspacing=0 id="table1" class="tablebord">
						<TBODY>
							<tr>
								<td width="42%" height="30px" align="left" class="crons">&nbsp;
									<script>writemsg(<% write(lang); %>, "MAC老化时间");</script>:
								</td>
								<td colspan="4" align="left" class="crons">
									&nbsp;<input type="text" name="age_time" id="age_time"
										value="<% AgeShow();%>">&nbsp;单位为秒(s)
								</td>
							</tr>
							<tr>
								<td colspan="2">
									<div align="center" class="btn">

										<script>writebutton(<% write(authmode); %>,<% write(lang); %>, "应  用", "button", "button", "det_arp", "updataTime()");</script>
									</div>

								</td>
							</tr>
					</table>
					</td>
					</tr>
					</TABLE>
				</div>


				<script>
					changebgcolor();
					changebgcolor2();
					changebgcolor3();
			<% if (errorcode == "3") { write("alert(putmsg("); write(lang); write(",'密码校验失败!'));"); } %>
			<% if (errorcode != "") { if (errorcode != "3") { write_errorcode(errorcode); } } %>
				</script>

			</form>
</BODY>

</HTML>