<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="/js/func/msg_rtl.js"></script>
<title>&nbsp;<script>writemsg(<% write(lang); %>,"vlan");</script></title>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="javascript" src="js/InputCheck_web.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/linux.js"></script>
<script language="javascript" type="text/javascript" src="/js/func/func.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
 
<script language="JavaScript">

function display(){
/*
var wValue = document.getElementById("qos_cfg").value.split(',');

	if(wValue[0] == "disable")
	{
	
		document.getElementById("modify_bu").disabled = true;
		alert("QoS未开启!");
	}
	*/
}


function del_vlan(vlanid,aclname,classname,policyname)
{
		
		//alert(vlanid);
		//alert(aclname);
		//alert(classname);
		//alert(policyname);
		
		var hid = document.macdel;
		
		hid.port_value.value=vlanid;
		hid.acl_value.value=aclname;
		hid.class_value.value=classname;
		hid.policy_value.value=policyname;
		
		
		hid.action="/goform/delBroadcastLimit"
		hid.submit();
		return 0;
}


function selectToAll() 
{  
		var cf = document.forms[0];
		var objs = document.getElementsByName("checkbox_index"); 
		var i;
		if(cf.check_all.checked == true)
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
		       if(objs[i].disabled==false && objs[i].checked==false){
		         objs[i].checked = true;  
			 		addToPortRange(objs[i]);
			 }
		    }
		}
		else
		{
		    for(i = 0; i < objs.length; i++) 
		    {    
					if(objs[i].checked==true){
					objs[i].checked = false;  
					 addToPortRange(objs[i]);
			}
		         
		    }
		} 
    
}


function checkup()
{
	var tf = document.vlan_port;
	tf.action = "/goform/selBroadcastLimit";
	tf.submit();
}

function addToPortRange(obj){
	var trid="tr_"+obj.value; 
	var trobj = document.getElementById(trid);
	var target = document.getElementById("port_range");
	var avid = document.getElementById("avid");
	var dvid = document.getElementById("dvid");
	var porttype = document.getElementById("porttype");
	var filter = document.getElementById("filter");
	var type_value = document.getElementById("type_value");
	var filter_value = document.getElementById("filter_value");
	var vlan_port_id = document.getElementById("vlan_port_id");
	var p = obj.value;

	if(obj.checked){
		target.value =target.value + p + " ";
		vlan_port_id.value =vlan_port_id.value + p + " , ";
		
	

	}
	else
	{
		target.value = target.value.replace(p + " ","");
		vlan_port_id.value = vlan_port_id.value.replace(p + " , ","");
	}

}

function P(vlanid,avg,aclname,classname,policyname)
{
    var narr=3;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+vlanid);	


    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		
	        tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
   // tbtr.cells[0].abbr = vlanid;
    
   
	

		//tbtr.cells[0].innerHTML = "<input type=\"checkbox\" id=\"input_"+vlanid+"\" name=\"checkbox_index\" value=\""+vlanid+"\" onclick=\"addToPortRange(this)\"/>";
	
		tbtr.cells[0].innerHTML = vlanid;

    tbtr.cells[1].innerHTML = avg;
    //tbtr.cells[2].innerHTML = vlanid;


		tbtr.cells[2].innerHTML = " <input type='button' name='button3' id='button3' class='botton_under_line' value='删除' onclick=del_vlan('"+vlanid+"','"+aclname+"','"+classname+"','"+policyname+"')>";
}


function refreshpage()
{
  location.href='vlan_limit.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('vlan_port',<% write(lang); %>);
}

function AddOption(portname){

	var selectObject = document.getElementById("rang_monitor");
	var y=document.createElement('option');
  	y.text=portname;
	y.value = portname;
	try
    {
    selectObject.add(y,null); // standards compliant
    }
  	catch(ex)
    {
   	selectObject.add(y); // IE only
  	}
}


</script>
</head>

<body    onload="display()" ><br>

<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="vlan_port" method="POST" action="">

	                  <input type="hidden" name="qos_cfg"  id="qos_cfg"   value="">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="vlan_port_id" id="vlan_port_id" >
<input type="hidden" name="flag" id="flag" >
<input type="hidden" name="type_value" id = "type_value"  value="@trunk_config#">
<input type="hidden" name="filter_value" id = "filter_value"  value="@trunk_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="pvid_config"  value="@pvid_config#">
<input type="hidden" name="egress_tagged" id="egress_tagged" value="@egress_tagged#">

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">

<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>
    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
	     <tr height="25">
	       <td align="left" colspan="4"  class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"VLAN广播限制");</script></td>
	     </tr>
        </table>
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%" id="table1" class="tablebord">

	
	     	     <tr height="25">
	     	  <td width="18%" class="crons">&nbsp;vlanid</td>
	     	  <td width="33%" class="crons" >&nbsp;
	     	    <input name="vlanid" type="text" class="input_x" ></td>
	     	  <td width="11%" class="crons">&nbsp;阀值(8的倍数)</td>
	     	  <td width="38%" class="crons" >&nbsp;
	     	    <input name="limit_value" type="text" class="input_x" >(Kbps)	     	    </td>
     	    	      </tr>	
		     	     <tr height="25" style="display:none">
	     	  <td class="crons">&nbsp;端口</td>
	     	  <td class="crons"  colspan="3">&nbsp;<select id="rang_monitor" name="rang_monitor"></select>
	 	<script>
						<% AppendOptionForfil(); %>	
					</script>				 
				 </td>
	     	    	     </tr>	     	 
	    <tr height="25">

	     	  <td colspan="4" align="middle" class="crons">
	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify_bu","checkup()");</script>
&nbsp;&nbsp;&nbsp;</td>
	     </tr>	  
			 
   <tr>
	    	<td colspan="4">
	    		<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
	    		 	<tr align="center" height="25" class="crons">
	    		 		<th class="td2" width="25%"><font color="#333333"><b>&nbsp;vlanid</b></font></th>
	    		 		<th class="td2" width="25%">阀值</th>						
	    		 		<th class="td2" width="50%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"删除");</script></b></font></th>						
	    		 	</tr>
					<script>  <%  var errorcode; showvlanLimit(); %></script>
	    		</table>	    	</td>
   </tr>
     </table>
   </td></tr>
   <tr>
	  	   <td colspan="2" align="center" height="35">
	  	   <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
	  	   <script>writebutton(1,<% write(lang); %>,"帮  助","button","button","Help","showHelpinfo()");</script>
	  		</td>
   </tr>
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>

</form>  
<script>
changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

<form name="macdel" method="POST" action="">
	    <input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>

	<input type="hidden" name="port_value"  value="" >
	<input type="hidden" name="acl_value"  value="" >
	

	<input type="hidden" name="class_value"  value="" >
	<input type="hidden" name="policy_value"  value="" >
	
	

	

</form>
</body>
</html>

