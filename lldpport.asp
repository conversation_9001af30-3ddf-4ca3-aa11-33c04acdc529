<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<% var lltime,lang,serverts, alpha; getltime_lanflag(); %>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<script language="javascript" type="text/javascript" src="js/func/msg_rtl.js"></script>
<script language="javascript" type="text/javascript" src="js/func/logintime.js"></script>
<link href="css/display_web.css" rel="stylesheet" type="text/css" />
<title>&nbsp;<script>writemsg(<% write(lang); %>,"GMRP");</script></title>
<script language="JavaScript">
var boardType = <% getSysCfg(); %>;
</script>
<script language="JavaScript">

function showHelp(helpname,lang) 
{
	var tmp=lang+"_help.html#"+helpname;
	window.open(tmp);
}

function changebgcolor_name(value){

 var tab = document.getElementById(value);
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}

function changebgcolor(){
 var tab = document.all.table1;
  var len = tab.rows.length ;
  for (var i=0; i<len; i++)
  {
	var lencol = tab.rows[i].cells.length
    for (var j=0; j<lencol; j++)
     {
        if (j % 2 == 1){

			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		  }
	else{
			tab.rows[i].cells[j].bgColor = "efefef";
			tab.rows[i].cells[j].className = "all_tables" ;
		}

     }

  }
}
 
var portStaList=[<%LldpPortShow();%>];

function writeLines()
{
var j = 0;
for(var i=0;i<portStaList.length/2;i++)
{
document.write(" <tr  class='tables_all'>");


document.write("    <td class='inputsyslog1'><input type='checkbox' name='checkbox_index' onchange=addToPortRange('"+i+"') ></td>");

document.write("    <td class='inputsyslog1'>"+portStaList[j]+"</td>");

j++
document.write("    <td  class='inputsyslog1'>"+portStaList[j]+"</td>");
j++;





document.write("  </tr>");

}
}
 
 
 
/*select ALL*/
function selectToAll() 
{  
    var cf = document.forms[0];
    var objs = document.getElementsByName("checkbox_index"); 
	var i;
    if(cf.check_all.checked == true)
    {
        for(i = 0; i < objs.length; i++) 
        {    
           if(objs[i].disabled==false && objs[i].checked==false){
             objs[i].checked = true;  
			 addToPortRange(i);
			 }
        }
    }
    else
    {
        for(i = 0; i < objs.length; i++) 
        {    
			if(objs[i].checked==true){
			objs[i].checked = false;  
			 addToPortRange(i);
			}
             
        }
    } 
    
}


function checkup()
{
	//var tf = document.vlan_port;
	//tf.action = "/goform/selLldpPort";
	//tf.submit();
}


function checking2()
{
 	var port_range = document.getElementById("port_range").value;
 
  	var checkbox_index = document.getElementsByName("checkbox_index");
	var table_port = document.getElementById("table_port");
	var tf=document.vlan_port;
	var i,j;


	 
			tf.submit();
 

	//if(port_txrate.length==0)port_txrate="-";
	////if(port_txburst.length==0)port_txburst="-";
	//if(port_rxrate.length==0)port_rxrate="-";	    
	//if(port_rxburst.length==0)port_rxburst="-";	
	 

}

function addToPortRange(index)
{
	//alert(index);

	
	var target = document.getElementById("port_range");
//	var port_enable = document.getElementById("port_enable");
//	var port_speed = document.getElementById("port_speed");
//	var port_flow_t = document.getElementById("port_flow_t");
//	var port_flow_r = document.getElementById("port_flow_r");
//	var port_mtu = document.getElementById("port_mtu");
//	var port_description=document.getElementById("port_description");

     var objs = document.getElementsByName("checkbox_index"); 

	if(objs[index].checked){

		target.value = target.value  + portStaList[2*index] + " ";
		
 
	}else{

		target.value = target.value.replace(portStaList[2*index]+" ","");
	}

}

function P(portId,enable)
{
    var narr=2;
    var tbtd;
    var eport="";
    var gport="";
    var i;
    var opt;
    var gtrunk=0;
    var tbtr = document.getElementById("table_port_vlan").insertRow(-1);

    tbtr.classname = "crons";
	tbtr.height = "30";

    tbtr.setAttribute("height", "30");
    tbtr.setAttribute("class", "crons");
    tbtr.setAttribute("className", "crons");	
	tbtr.setAttribute("id", "tr_"+portId);	


    for(i=0;i<narr;i++)
    {
        tbtd = document.createElement("td");
	 
        tbtd.align = "center";
	    tbtd.setAttribute("class", "td2");	
		tbtd.setAttribute("className", "td2");	
		
	        tbtr.appendChild(tbtd);
    }
	            
    tbtr.cells[0].abbr = 0;
    tbtr.cells[0].abbr = portId;
    
   
	

		//tbtr.cells[0].innerHTML = "<input type=\"checkbox\" id=\"input_"+portId+"\" name=\"checkbox_index\" value=\""+portId+"\" onclick=\"addToPortRange(this)\"/>";
	
		tbtr.cells[0].innerHTML = portId;
    tbtr.cells[1].innerHTML = enable;


}


function refreshpage()
{
  location.href='lldpport.asp?ltime='+<% write(lltime); %>;
}

function showHelpinfo()
{
   showHelp('vlan_port',<% write(lang); %>);
}

</script>
</head>

<body  ><br>
<% web_get_stat(); %>
<%  var authmode; checkCurMode(); %>
<script>
checktop(<% write(lang); %>);
</script>
<form name="vlan_port" method="POST" action="/goform/PortLldpChange">
<input type="hidden" name="left_menu_id" value="@left_menu_id#">
<input type="hidden" name="trunk_config"  value="@trunk_config#">
<input type="hidden" name="vlan_port_id" id="vlan_port_id" >
<input type="hidden" name="flag" id="flag" >
<input type="hidden" name="type_value" id = "type_value"  value="@trunk_config#">
<input type="hidden" name="filter_value" id = "filter_value"  value="@trunk_config#">
<input type="hidden" name="ltime" value=<% write(lltime); %>>
<input type="hidden" name="lastts" value=<% write(serverts); %>>
<input type="hidden" name="alpha" id="alpha" value=<% write(alpha); %>>
<input type="hidden" name="pvid_config"  value="@pvid_config#">
<input type="hidden" name="egress_tagged" id="egress_tagged" value="@egress_tagged#">

<table id="mainTbl" width="100%" height="100%" border="0" align="center" cellpadding="0" cellspacing="0">

<tr><td>
<table width="100%" height="600" border="0" align="center" cellpadding="0" cellspacing="0" >
  <tr>
    <td valign="top" >
     <table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" class="cword09">
     	 <tr><td>

 <table width="100%" align="center" border="0" cellspacing="0" cellpadding="0" >
	<tr><td bgcolor="#3cc4c4" height="30px" ><font size="3" color="#FFFFFF"><b>LLDP设置/LLDP端口</b></font></td></tr>
 </table>
 
<!--
    	  <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" bordercolor="#000000" bgcolor="#FFFFFF" class="mainHeader" >
	     <tr height="25">
	       <td align="left" colspan="4"  class="Tablelist">&nbsp;<script>writemsg(<% write(lang); %>,"LLDP端口");</script></td>
	     </tr>
        </table>
-->
        
       </td></tr>
             
   <tr><td>  	
   	 <table  border="0" cellspacing="0" cellpadding="0" width="100%" id="table1" class="tablebord">

		<tr height="30">
              <td align="left" class="td7">&nbsp;<script>writemsg(<% write(lang); %>,"端口范围");</script></td>
              <td width="81%" align="left" class="td7">&nbsp;
                <input name="port_range" type="text" class="input_x" id="port_range" readonly="true"/></td>
            </tr>
	     <tr height="25">
	     	  <td class="crons">&nbsp;<script>writemsg(<% write(lang); %>,"端口状态");</script></td>
	     	  <td class="crons" colspan="3">&nbsp;
	     	    <select name="porttype" id="porttype" class="select1"  >
                  <option value="txrx" >All</option>
                  <option value="rx" >RX</option>
                  <option value="tx" >TX</option>
                  <option value="disable" >Disable</option>
                </select>                </td>
	     </tr>		 
	    <tr height="25">

	     	  <td colspan="4" align="middle" class="crons">
	     	  <script>writebutton(<% write(authmode); %>,<% write(lang); %>,"修  改","button","button","modify","checking2()");</script>
&nbsp;&nbsp;&nbsp;</td>
	     </tr>	  
			 
   <tr>
	    	<td colspan="4">
	    		<table id="table_port_vlan" border="0" cellspacing="0" cellpadding="0" width="100%"   class="tablebord">
	    		 	<tr align="center" height="25" class="crons">
	              <th width="13%" height="13" align="center" class="td2"><input type="checkbox" name="check_all" value="all" onClick="selectToAll()" /></th>
    		 		<th class="td2" width="20%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"端口");</script></b></font></th>
	    		 		<th class="td2" width="70%"><font color="#333333"><b>&nbsp;<script>writemsg(<% write(lang); %>,"端口状态");</script></b></font></th>						
	    		 	</tr>

           <script language="javascript">
writeLines();

</script>

	    		</table>	    	</td>
   </tr>
     </table>
   </td></tr>
   <tr>
	  	   <td colspan="2" align="center" height="35">
	  	   <script>writebutton(1,<% write(lang); %>,"刷  新","button","button","Refresh","refreshpage()");</script>
	  		</td>
   </tr>
  
   </table>
 </td></tr> 
 
</table>
</td></tr>
</table>

</form>  
<script>
changebgcolor();
changebgcolor_name("table_port_vlan");

<% if (errorcode!="") { write_errorcode(errorcode); } %>
</script>

</body>
</html>

